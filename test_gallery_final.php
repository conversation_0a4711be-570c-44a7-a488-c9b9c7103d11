<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\Business;
use App\Models\User;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Final Gallery System Test...\n\n";

try {
    // Test route generation
    echo "✅ Testing route generation:\n";
    echo "Gallery index: " . route('owner.gallery.index') . "\n";
    echo "Gallery categories index: " . route('owner.gallery.categories.index') . "\n";
    echo "Gallery categories create: " . route('owner.gallery.categories.create') . "\n";
    echo "Gallery create: " . route('owner.gallery.create') . "\n";
    
    // Find a business owner
    $owner = User::whereHas('ownedBusinesses')->first();
    
    if (!$owner) {
        echo "\n❌ No business owner found. Please create a business owner first.\n";
        exit(1);
    }
    
    $business = $owner->ownedBusinesses()->first();
    echo "\n✅ Using business: {$business->name} (ID: {$business->id})\n";
    echo "✅ Owner: {$owner->name} (ID: {$owner->id})\n";
    
    // Test gallery categories relationship
    echo "\n✅ Testing gallery categories relationship:\n";
    $categoriesCount = $business->galleryCategories()->count();
    echo "Gallery categories count: {$categoriesCount}\n";
    
    $categoriesWithCount = $business->galleryCategories()->withCount('images')->get();
    echo "Categories with image count:\n";
    foreach ($categoriesWithCount as $cat) {
        echo "- {$cat->name}: {$cat->images_count} images\n";
    }
    
    // Test gallery images relationship
    echo "\n✅ Testing gallery images relationship:\n";
    $imagesCount = $business->galleryImages()->count();
    echo "Gallery images count: {$imagesCount}\n";
    
    $activeImagesCount = $business->activeGalleryImages()->count();
    echo "Active gallery images count: {$activeImagesCount}\n";
    
    $featuredImagesCount = $business->featuredGalleryImages()->count();
    echo "Featured gallery images count: {$featuredImagesCount}\n";
    
    // Test view files exist
    echo "\n✅ Testing view files:\n";
    $viewFiles = [
        'resources/views/owner/gallery/index.blade.php',
        'resources/views/owner/gallery/create.blade.php',
        'resources/views/owner/gallery/edit.blade.php',
        'resources/views/owner/gallery/show.blade.php',
        'resources/views/owner/gallery/categories/index.blade.php',
        'resources/views/owner/gallery/categories/create.blade.php',
        'resources/views/owner/gallery/categories/edit.blade.php',
        'resources/views/owner/gallery/categories/show.blade.php',
    ];
    
    foreach ($viewFiles as $file) {
        if (file_exists($file)) {
            echo "✅ {$file} exists\n";
        } else {
            echo "❌ {$file} missing\n";
        }
    }
    
    echo "\n🎉 Gallery system test completed successfully!\n";
    echo "The 'Manage Categories' button should now work correctly.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
