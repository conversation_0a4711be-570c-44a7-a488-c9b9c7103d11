<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\Business;
use App\Models\BusinessGalleryCategory;
use App\Models\User;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Gallery Categories functionality...\n\n";

try {
    // Find a business owner
    $owner = User::whereHas('ownedBusinesses')->first();
    
    if (!$owner) {
        echo "No business owner found. Creating test data...\n";
        
        // Create a test user
        $owner = User::create([
            'name' => 'Test Owner',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'owner',
            'email_verified_at' => now(),
        ]);
        
        // Create a test business
        $business = Business::create([
            'owner_id' => $owner->id,
            'name' => 'Test Business',
            'slug' => 'test-business-' . time(),
            'description' => 'Test business for gallery categories',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'is_active' => true,
        ]);
    } else {
        $business = $owner->ownedBusinesses()->first();
    }
    
    echo "Using business: {$business->name} (ID: {$business->id})\n";
    echo "Owner: {$owner->name} (ID: {$owner->id})\n\n";
    
    // Test gallery categories relationship
    echo "Testing galleryCategories relationship...\n";
    $categoriesCount = $business->galleryCategories()->count();
    echo "Current gallery categories count: {$categoriesCount}\n";
    
    // Test creating a category
    echo "\nCreating a test category...\n";
    $category = $business->galleryCategories()->create([
        'name' => 'Test Category ' . time(),
        'slug' => 'test-category-' . time(),
        'description' => 'Test category for gallery functionality',
        'color' => '#007bff',
        'is_active' => true,
        'sort_order' => 0
    ]);
    
    echo "Category created successfully!\n";
    echo "- ID: {$category->id}\n";
    echo "- Name: {$category->name}\n";
    echo "- Slug: {$category->slug}\n";
    echo "- Business ID: {$category->business_id}\n";
    
    // Test the relationship from category to business
    echo "\nTesting category->business relationship...\n";
    $categoryBusiness = $category->business;
    echo "Category belongs to business: {$categoryBusiness->name}\n";
    
    // Test withCount query
    echo "\nTesting withCount query...\n";
    $categoriesWithCount = $business->galleryCategories()->withCount('images')->get();
    echo "Categories with image count:\n";
    foreach ($categoriesWithCount as $cat) {
        echo "- {$cat->name}: {$cat->images_count} images\n";
    }
    
    // Test ordered scope
    echo "\nTesting ordered scope...\n";
    $orderedCategories = $business->galleryCategories()->ordered()->get();
    echo "Ordered categories:\n";
    foreach ($orderedCategories as $cat) {
        echo "- {$cat->name} (sort_order: {$cat->sort_order})\n";
    }
    
    // Clean up test category
    echo "\nCleaning up test category...\n";
    $category->delete();
    echo "Test category deleted.\n";
    
    echo "\n✅ Gallery Categories functionality test completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
}
