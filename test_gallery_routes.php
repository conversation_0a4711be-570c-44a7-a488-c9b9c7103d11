<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Business;
use App\Models\BusinessGalleryImage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Gallery Routes and Functionality\n";
echo "=======================================\n";

// Get the test user and business
$user = User::where('email', '<EMAIL>')->first();
$business = Business::where('slug', 'test-business')->first();

if (!$user || !$business) {
    echo "Test user or business not found. Please run the setup scripts first.\n";
    exit;
}

// Simulate authentication
Auth::login($user);

echo "User: {$user->name} (ID: {$user->id})\n";
echo "Business: {$business->name} (ID: {$business->id})\n";

// Get a test image
$image = $business->galleryImages()->first();
if (!$image) {
    echo "No test images found. Please run create_test_gallery_images.php first.\n";
    exit;
}

echo "Test Image: {$image->title} (ID: {$image->id})\n";
echo "Current featured status: " . ($image->is_featured ? 'true' : 'false') . "\n\n";

// Test the controller methods
use App\Http\Controllers\Owner\GalleryController;

try {
    $controller = new GalleryController(app(\App\Services\GalleryImageService::class));
    
    echo "Testing toggleFeatured method...\n";
    $response = $controller->toggleFeatured($image);
    
    if ($response instanceof \Illuminate\Http\JsonResponse) {
        $data = $response->getData(true);
        echo "Response: " . json_encode($data) . "\n";
        echo "Status Code: " . $response->getStatusCode() . "\n";
        
        // Refresh the image to see the change
        $image->refresh();
        echo "New featured status: " . ($image->is_featured ? 'true' : 'false') . "\n\n";
    } else {
        echo "Unexpected response type: " . get_class($response) . "\n";
    }
    
    echo "Testing destroy method...\n";
    
    // Create a temporary image for deletion test
    $tempImage = BusinessGalleryImage::create([
        'business_id' => $business->id,
        'filename' => 'temp-test-image.jpg',
        'original_name' => 'Temp Test Image.jpg',
        'path' => "gallery/business-{$business->id}/temp-test-image.jpg",
        'mime_type' => 'image/jpeg',
        'file_size' => 1024000,
        'width' => 800,
        'height' => 600,
        'title' => 'Temp Test Image',
        'alt_text' => 'Temp Test Image',
        'description' => 'Temporary image for deletion test',
        'tags' => ['test'],
        'is_featured' => false,
        'is_active' => true,
        'sort_order' => 999,
        'uploaded_at' => now(),
    ]);
    
    echo "Created temporary image: {$tempImage->title} (ID: {$tempImage->id})\n";
    
    $destroyResponse = $controller->destroy($tempImage);
    
    if ($destroyResponse instanceof \Illuminate\Http\RedirectResponse) {
        echo "Destroy method returned redirect response (expected)\n";
        echo "Target URL: " . $destroyResponse->getTargetUrl() . "\n";
        
        // Check if the image was actually deleted
        $deletedImage = BusinessGalleryImage::find($tempImage->id);
        if (!$deletedImage) {
            echo "✓ Image successfully deleted from database\n";
        } else {
            echo "✗ Image still exists in database\n";
        }
    } else {
        echo "Unexpected response type: " . get_class($destroyResponse) . "\n";
    }
    
} catch (Exception $e) {
    echo "Error testing controller methods: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nRoute testing completed!\n";

// Test route generation
echo "\nTesting route generation:\n";
echo "Toggle featured route: " . route('owner.gallery.toggle-featured', $image->id) . "\n";
echo "Destroy route: " . route('owner.gallery.destroy', $image->id) . "\n";
