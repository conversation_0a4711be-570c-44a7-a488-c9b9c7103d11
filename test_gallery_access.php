<?php

require_once 'vendor/autoload.php';

use App\Models\Business;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Gallery Access\n";
echo "======================\n";

// Get the test user
$user = User::where('email', '<EMAIL>')->first();

if (!$user) {
    echo "Test user not found.\n";
    exit;
}

echo "User: {$user->name} (ID: {$user->id})\n";

// Check user's businesses
$businesses = $user->ownedBusinesses()->get();
echo "User owns {$businesses->count()} businesses:\n";

foreach ($businesses as $business) {
    echo "- {$business->name} (ID: {$business->id}, Slug: {$business->slug})\n";
    echo "  Gallery images: {$business->galleryImages()->count()}\n";
    echo "  Gallery categories: {$business->galleryCategories()->count()}\n";
}

// Test the getUserBusiness method from the controller
echo "\nTesting getUserBusiness method...\n";

// Simulate authentication
Auth::login($user);

// Import the controller
use App\Http\Controllers\Owner\GalleryController;

try {
    $controller = new GalleryController(app(\App\Services\GalleryImageService::class));
    
    // Use reflection to access the protected method
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getUserBusiness');
    $method->setAccessible(true);
    
    $userBusiness = $method->invoke($controller);
    
    if ($userBusiness) {
        echo "getUserBusiness returned: {$userBusiness->name} (ID: {$userBusiness->id})\n";
        echo "Gallery images in this business: {$userBusiness->galleryImages()->count()}\n";
    } else {
        echo "getUserBusiness returned null\n";
    }
    
} catch (Exception $e) {
    echo "Error testing getUserBusiness: " . $e->getMessage() . "\n";
}

echo "\nTest completed!\n";
