<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule the reminder processing command to run every minute
Schedule::command('reminders:process')->everyMinute();

// Security monitoring tasks
Schedule::command('security:monitor --scan')
    ->everyFifteenMinutes()
    ->withoutOverlapping()
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/security-monitoring.log'));

// Data retention policies (weekly)
Schedule::command('security:monitor --retention')
    ->weekly()
    ->sundays()
    ->at('02:00')
    ->withoutOverlapping()
    ->appendOutputTo(storage_path('logs/data-retention.log'));

// Full security monitoring (daily)
Schedule::command('security:monitor --all')
    ->daily()
    ->at('01:00')
    ->withoutOverlapping()
    ->appendOutputTo(storage_path('logs/security-full.log'));
