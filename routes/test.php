<?php

use App\Models\Business;
use App\Models\Service;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Support\Facades\Route;

// Test route to verify booking views work
Route::get('/test-booking-views', function () {
    try {
        // Test data
        $businesses = Business::all();
        $business = $businesses->first();
        $services = $business ? Service::where('business_id', $business->id)->get() : collect();
        $customers = User::all();

        // Test create view
        $createView = view('admin.bookings.create', compact('businesses', 'services', 'customers', 'business'));

        // Test if we have any bookings for show/edit views
        $booking = Booking::first();

        $result = [
            'create_view' => 'OK - Create view compiled successfully',
            'businesses_count' => $businesses->count(),
            'services_count' => $services->count(),
            'customers_count' => $customers->count(),
        ];

        if ($booking) {
            $booking->load(['business', 'branch', 'customer', 'bookingServices.service', 'payments', 'reminders']);
            $showView = view('admin.bookings.show', compact('booking'));
            $editView = view('admin.bookings.edit', compact('booking', 'businesses', 'services', 'customers'));

            $result['show_view'] = 'OK - Show view compiled successfully';
            $result['edit_view'] = 'OK - Edit view compiled successfully';
            $result['booking_id'] = $booking->id;
        } else {
            $result['show_view'] = 'SKIP - No bookings found';
            $result['edit_view'] = 'SKIP - No bookings found';
        }

        return response()->json($result);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ], 500);
    }
});

// Test route to create a sample booking
Route::get('/test-create-booking', function () {
    try {
        $business = Business::first();
        $service = Service::first();
        $user = User::first();

        if (!$business || !$service || !$user) {
            return response()->json(['error' => 'Missing test data. Please run seeders first.']);
        }

        $booking = Booking::create([
            'business_id' => $business->id,
            'customer_id' => $user->id,
            'customer_name' => $user->name,
            'customer_email' => $user->email,
            'customer_phone' => '+1234567890',
            'start_datetime' => now()->addDay(),
            'end_datetime' => now()->addDay()->addHour(),
            'total_duration_minutes' => 60,
            'participant_count' => 1,
            'subtotal' => $service->base_price,
            'tax_amount' => 0,
            'discount_amount' => 0,
            'total_amount' => $service->base_price,
            'deposit_amount' => 0,
            'paid_amount' => 0,
            'status' => 'pending',
            'payment_status' => 'pending',
            'notes' => 'Test booking created for testing views',
        ]);

        // Create booking service
        $booking->bookingServices()->create([
            'service_id' => $service->id,
            'quantity' => 1,
            'unit_price' => $service->base_price,
            'total_price' => $service->base_price,
            'duration_minutes' => $service->duration_minutes,
            'start_datetime' => $booking->start_datetime,
            'end_datetime' => $booking->end_datetime,
        ]);

        return response()->json([
            'message' => 'Test booking created successfully',
            'booking_id' => $booking->id,
            'booking_number' => $booking->booking_number,
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ], 500);
    }
});

// Test route to verify service views work
Route::get('/test-service-views', function () {
    try {
        // Test data
        $businesses = Business::all();
        $business = $businesses->first();
        $categories = collect(); // Service categories would be loaded based on business
        $resources = collect(); // Resources would be loaded based on business

        // Test create view
        $createView = view('admin.services.create', compact('businesses', 'categories', 'resources', 'business'));

        // Test if we have any services for show/edit views
        $service = Service::first();

        $result = [
            'create_view' => 'OK - Create view compiled successfully',
            'businesses_count' => $businesses->count(),
            'categories_count' => $categories->count(),
            'resources_count' => $resources->count(),
        ];

        if ($service) {
            $service->load(['business', 'category', 'resources.resourceType', 'addons', 'bookingServices.booking', 'waitingLists']);
            $showView = view('admin.services.show', compact('service'));
            $editView = view('admin.services.edit', compact('service', 'businesses', 'categories', 'resources'));

            $result['show_view'] = 'OK - Show view compiled successfully';
            $result['edit_view'] = 'OK - Edit view compiled successfully';
            $result['service_id'] = $service->id;
            $result['service_name'] = $service->name;
        } else {
            $result['show_view'] = 'SKIP - No services found';
            $result['edit_view'] = 'SKIP - No services found';
        }

        return response()->json($result);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ], 500);
    }
});

// Test route to verify business views work
Route::get('/test-business-views', function () {
    try {
        // Test data
        $categories = collect(); // Business categories would be loaded
        $tags = collect(); // Business tags would be loaded

        // Test create view
        $createView = view('admin.businesses.create', compact('categories', 'tags'));

        // Test if we have any businesses for show/edit views
        $business = Business::first();

        $result = [
            'create_view' => 'OK - Create view compiled successfully',
            'categories_count' => $categories->count(),
            'tags_count' => $tags->count(),
        ];

        if ($business) {
            $business->load(['owner', 'categories', 'tags', 'branches', 'services', 'bookings', 'resources']);
            $showView = view('admin.businesses.show', compact('business'));
            $editView = view('admin.businesses.edit', compact('business', 'categories', 'tags'));

            $result['show_view'] = 'OK - Show view compiled successfully';
            $result['edit_view'] = 'OK - Edit view compiled successfully';
            $result['business_id'] = $business->id;
            $result['business_name'] = $business->name;
        } else {
            $result['show_view'] = 'SKIP - No businesses found';
            $result['edit_view'] = 'SKIP - No businesses found';
        }

        return response()->json($result);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ], 500);
    }
});

// Comprehensive test for all admin views
Route::get('/test-all-views', function () {
    try {
        $results = [];

        // Test booking views
        $businesses = Business::all();
        $business = $businesses->first();
        $services = $business ? Service::where('business_id', $business->id)->get() : collect();
        $customers = User::all();

        view('admin.bookings.create', compact('businesses', 'services', 'customers', 'business'));
        $results['booking_create'] = 'OK';

        $booking = Booking::first();
        if ($booking) {
            $booking->load(['business', 'branch', 'customer', 'bookingServices.service', 'payments', 'reminders']);
            view('admin.bookings.show', compact('booking'));
            view('admin.bookings.edit', compact('booking', 'businesses', 'services', 'customers'));
            $results['booking_show'] = 'OK';
            $results['booking_edit'] = 'OK';
        }

        // Test service views
        $categories = collect();
        $resources = collect();

        view('admin.services.create', compact('businesses', 'categories', 'resources', 'business'));
        $results['service_create'] = 'OK';

        $service = Service::first();
        if ($service) {
            $service->load(['business', 'category', 'resources.resourceType', 'addons', 'bookingServices.booking', 'waitingLists']);
            view('admin.services.show', compact('service'));
            view('admin.services.edit', compact('service', 'businesses', 'categories', 'resources'));
            $results['service_show'] = 'OK';
            $results['service_edit'] = 'OK';
        }

        // Test business views
        $businessCategories = collect();
        $businessTags = collect();

        view('admin.businesses.create', ['categories' => $businessCategories, 'tags' => $businessTags]);
        $results['business_create'] = 'OK';

        if ($business) {
            $business->load(['owner', 'categories', 'tags', 'branches', 'services', 'bookings', 'resources']);
            view('admin.businesses.show', compact('business'));
            view('admin.businesses.edit', ['business' => $business, 'categories' => $businessCategories, 'tags' => $businessTags]);
            $results['business_show'] = 'OK';
            $results['business_edit'] = 'OK';
        }

        $results['summary'] = 'All admin views compiled successfully!';
        $results['total_tests'] = count(array_filter($results, function($v) { return $v === 'OK'; }));

        return response()->json($results);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
        ], 500);
    }
});

// Test route to verify calendar views work
Route::get('/test-calendar-views', function () {
    try {
        // Test data
        $businesses = Business::all();
        $business = $businesses->first();
        $view = 'month';
        $date = now()->format('Y-m-d');

        // Test calendar index view
        $indexView = view('admin.calendar.index', compact('businesses', 'business', 'view', 'date'));

        $result = [
            'calendar_index' => 'OK - Calendar index view compiled successfully',
            'businesses_count' => $businesses->count(),
        ];

        // Test quick booking form view if we have business and service
        if ($business) {
            $service = Service::where('business_id', $business->id)->where('is_active', true)->first();
            if ($service) {
                $startDateTime = now()->addDay();
                $quickBookingView = view('admin.calendar.quick-booking-form', [
                    'business' => $business,
                    'service' => $service,
                    'startDateTime' => $startDateTime,
                ]);
                $result['quick_booking_form'] = 'OK - Quick booking form view compiled successfully';
                $result['service_name'] = $service->name;
            } else {
                $result['quick_booking_form'] = 'SKIP - No active services found';
            }
        } else {
            $result['quick_booking_form'] = 'SKIP - No businesses found';
        }

        // Test booking modal view if we have a booking
        $booking = Booking::with(['business', 'branch', 'customer', 'bookingServices.service', 'payments'])->first();
        if ($booking) {
            $bookingModalView = view('admin.calendar.booking-modal', compact('booking'));
            $result['booking_modal'] = 'OK - Booking modal view compiled successfully';
            $result['booking_id'] = $booking->id;
        } else {
            $result['booking_modal'] = 'SKIP - No bookings found';
        }

        return response()->json($result);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
        ], 500);
    }
});

// Test route to verify owner views work
Route::get('/test-owner-views', function () {
    try {
        $results = [];

        // Test all owner views that should exist
        $ownerViews = [
            'owner.bookings.index',
            'owner.bookings.create',
            'owner.check-in.index',
            'owner.waiting-lists.index',
            'owner.calendar.index',
            'owner.customers.index',
            'owner.notifications.index',
            'owner.email-templates.index',
            'owner.sms-settings.index',
            'owner.analytics.index',
            'owner.reports.bookings',
            'owner.reports.revenue',
            'owner.reports.customers',
            'owner.reports.resources',
            'owner.revenue.index',
            'owner.payments.index',
            'owner.pricing.index',
            'owner.profile.index',
            'owner.security.index',
            'owner.subscription.index',
            // Services views
            'owner.services.index',
            'owner.services.create',
            'owner.service-categories.index',
            // Resources views
            'owner.resources.index',
            'owner.resources.create',
            'owner.resource-types.index',
            // Schedule views
            'owner.schedule.today',
        ];

        foreach ($ownerViews as $viewName) {
            try {
                view($viewName);
                $results[$viewName] = 'OK';
            } catch (\Exception $e) {
                $results[$viewName] = 'ERROR: ' . $e->getMessage();
            }
        }

        $successCount = count(array_filter($results, function($v) { return $v === 'OK'; }));
        $totalCount = count($results);

        $results['summary'] = "Successfully compiled {$successCount}/{$totalCount} owner views";

        return response()->json($results);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ], 500);
    }
});