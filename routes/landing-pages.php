<?php

use App\Http\Controllers\BusinessLandingController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Landing Page Routes
|--------------------------------------------------------------------------
|
| These routes handle the public-facing landing pages for businesses.
| They support both subdomain and path-based routing for flexibility.
|
*/

// Subdomain routing for businesses (e.g., business.bookkei.com)
Route::domain('{business}.bookkei.com')->group(function () {
    // Main landing page
    Route::get('/', [BusinessLandingController::class, 'index'])->name('landing-page.subdomain.index');

    // Services pages
    Route::get('/services', [BusinessLandingController::class, 'services'])->name('landing-page.subdomain.services');
    Route::get('/services/{service_slug}', [BusinessLandingController::class, 'serviceDetail'])->name('landing-page.subdomain.service');

    // Booking pages
    Route::get('/booking', [BusinessLandingController::class, 'booking'])->name('landing-page.subdomain.booking');
    Route::get('/booking/{service_id}', [BusinessLandingController::class, 'bookService'])->name('landing-page.subdomain.book-service');

    // Additional pages
    Route::get('/about', [BusinessLandingController::class, 'about'])->name('landing-page.subdomain.about');
    Route::get('/contact', [BusinessLandingController::class, 'contact'])->name('landing-page.subdomain.contact');
    Route::get('/gallery', [BusinessLandingController::class, 'gallery'])->name('landing-page.subdomain.gallery');
    Route::get('/team', [BusinessLandingController::class, 'team'])->name('landing-page.subdomain.team');

    // API endpoints for booking
    Route::prefix('api')->group(function () {
        Route::get('/availability', [BusinessLandingController::class, 'getAvailability'])->name('landing-page.subdomain.api.availability');
        Route::post('/book', [BusinessLandingController::class, 'createBooking'])->name('landing-page.subdomain.api.book');
        Route::get('/services/{service_id}/details', [BusinessLandingController::class, 'getServiceDetails'])->name('landing-page.subdomain.api.service-details');
    });
});

// Path-based routing for businesses (e.g., bookkei.com/business-name)
Route::prefix('/')->group(function () {
    // Main landing page
    Route::get('/{business_slug}', [BusinessLandingController::class, 'index'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.index');

    // Services pages
    Route::get('/{business_slug}/services', [BusinessLandingController::class, 'services'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.services');

    Route::get('/{business_slug}/services/{service_slug}', [BusinessLandingController::class, 'serviceDetail'])
        ->where(['business_slug' => '[a-z0-9\-]+', 'service_slug' => '[a-z0-9\-]+'])
        ->name('landing-page.service');

    // Booking pages
    Route::get('/{business_slug}/booking', [BusinessLandingController::class, 'booking'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.booking');

    Route::get('/{business_slug}/booking/{service_id}', [BusinessLandingController::class, 'bookService'])
        ->where(['business_slug' => '[a-z0-9\-]+', 'service_id' => '[0-9]+'])
        ->name('landing-page.book-service');

    // Additional pages
    Route::get('/{business_slug}/about', [BusinessLandingController::class, 'about'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.about');

    Route::get('/{business_slug}/contact', [BusinessLandingController::class, 'contact'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.contact');

    Route::get('/{business_slug}/gallery', [BusinessLandingController::class, 'gallery'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.gallery');

    Route::get('/{business_slug}/team', [BusinessLandingController::class, 'team'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.team');

    // Service category pages
    Route::get('/{business_slug}/category/{category_slug}', [BusinessLandingController::class, 'serviceCategory'])
        ->where(['business_slug' => '[a-z0-9\-]+', 'category_slug' => '[a-z0-9\-]+'])
        ->name('landing-page.category');

    // Special offers and packages
    Route::get('/{business_slug}/offers', [BusinessLandingController::class, 'offers'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.offers');

    Route::get('/{business_slug}/packages', [BusinessLandingController::class, 'packages'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.packages');

    // API endpoints for booking and interactions
    Route::prefix('/{business_slug}/api')->where(['business_slug' => '[a-z0-9\-]+'])->group(function () {
        Route::get('/availability', [BusinessLandingController::class, 'getAvailability'])->name('landing-page.api.availability');
        Route::post('/book', [BusinessLandingController::class, 'createBooking'])->name('landing-page.api.book');
        Route::get('/services/{service_id}/details', [BusinessLandingController::class, 'getServiceDetails'])->name('landing-page.api.service-details');
        Route::post('/contact', [BusinessLandingController::class, 'submitContact'])->name('landing-page.api.contact');
        Route::post('/newsletter', [BusinessLandingController::class, 'subscribeNewsletter'])->name('landing-page.api.newsletter');
        Route::get('/search', [BusinessLandingController::class, 'searchServices'])->name('landing-page.api.search');
    });

    // SEO-friendly URLs for service packages
    Route::get('/{business_slug}/package/{package_slug}', [BusinessLandingController::class, 'packageDetail'])
        ->where(['business_slug' => '[a-z0-9\-]+', 'package_slug' => '[a-z0-9\-]+'])
        ->name('landing-page.package');

    // Staff member pages (if enabled)
    Route::get('/{business_slug}/staff/{staff_slug}', [BusinessLandingController::class, 'staffMember'])
        ->where(['business_slug' => '[a-z0-9\-]+', 'staff_slug' => '[a-z0-9\-]+'])
        ->name('landing-page.staff');

    // Location-specific pages (for multi-location businesses)
    Route::get('/{business_slug}/location/{location_slug}', [BusinessLandingController::class, 'location'])
        ->where(['business_slug' => '[a-z0-9\-]+', 'location_slug' => '[a-z0-9\-]+'])
        ->name('landing-page.location');

    // Reviews and testimonials
    Route::get('/{business_slug}/reviews', [BusinessLandingController::class, 'reviews'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.reviews');

    // Blog/Articles (if enabled)
    Route::get('/{business_slug}/blog', [BusinessLandingController::class, 'blog'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.blog');

    Route::get('/{business_slug}/blog/{article_slug}', [BusinessLandingController::class, 'blogArticle'])
        ->where(['business_slug' => '[a-z0-9\-]+', 'article_slug' => '[a-z0-9\-]+'])
        ->name('landing-page.blog.article');

    // Sitemap and SEO
    Route::get('/{business_slug}/sitemap.xml', [BusinessLandingController::class, 'sitemap'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.sitemap');

    Route::get('/{business_slug}/robots.txt', [BusinessLandingController::class, 'robots'])
        ->where('business_slug', '[a-z0-9\-]+')
        ->name('landing-page.robots');
});

// Custom domain routing (for premium businesses)
Route::group(['middleware' => 'custom.domain'], function () {
    Route::get('/', [BusinessLandingController::class, 'customDomainIndex'])->name('landing-page.custom.index');
    Route::get('/services', [BusinessLandingController::class, 'customDomainServices'])->name('landing-page.custom.services');
    Route::get('/services/{service_slug}', [BusinessLandingController::class, 'customDomainServiceDetail'])->name('landing-page.custom.service');
    Route::get('/booking', [BusinessLandingController::class, 'customDomainBooking'])->name('landing-page.custom.booking');
    Route::get('/booking/{service_id}', [BusinessLandingController::class, 'customDomainBookService'])->name('landing-page.custom.book-service');
});
