<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Business;

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Fixing User-Business Relationship\n";
echo "=================================\n";

// Get the test user
$user = User::where('email', '<EMAIL>')->first();
$business = Business::where('slug', 'test-business')->first();

if (!$user || !$business) {
    echo "Test user or business not found.\n";
    exit;
}

echo "User: {$user->name} (ID: {$user->id})\n";
echo "Business: {$business->name} (ID: {$business->id})\n";

// Check current relationship
echo "Current business owner_id: {$business->owner_id}\n";
echo "User owns businesses: " . $user->ownedBusinesses()->count() . "\n";

// Fix the relationship if needed
if ($business->owner_id !== $user->id) {
    echo "Fixing business ownership...\n";
    $business->owner_id = $user->id;
    $business->save();
    echo "Business ownership updated.\n";
} else {
    echo "Business ownership is correct.\n";
}

// Verify the relationship
$user->refresh();
$business->refresh();

echo "\nAfter fix:\n";
echo "Business owner_id: {$business->owner_id}\n";
echo "User owns businesses: " . $user->ownedBusinesses()->count() . "\n";

// List all businesses owned by the user
$ownedBusinesses = $user->ownedBusinesses()->get();
foreach ($ownedBusinesses as $ownedBusiness) {
    echo "- {$ownedBusiness->name} (ID: {$ownedBusiness->id})\n";
}

echo "\nRelationship fixed successfully!\n";
