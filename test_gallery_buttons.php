<?php

require_once 'vendor/autoload.php';

use App\Models\Business;
use App\Models\BusinessGalleryImage;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Gallery Button Functionality\n";
echo "====================================\n";

// Get the test user and business
$user = User::where('email', '<EMAIL>')->first();
$business = Business::where('slug', 'test-business')->first();

if (!$user || !$business) {
    echo "Test user or business not found.\n";
    exit;
}

// Simulate authentication
Auth::login($user);

echo "User: {$user->name} (ID: {$user->id})\n";
echo "Business: {$business->name} (ID: {$business->id})\n";

// Get test images
$images = $business->galleryImages()->get();
echo "Found {$images->count()} gallery images\n\n";

if ($images->count() === 0) {
    echo "No images found. Please run create_test_gallery_images.php first.\n";
    exit;
}

// Test toggle featured functionality
$testImage = $images->first();
echo "Testing toggle featured on image: {$testImage->title} (ID: {$testImage->id})\n";
echo "Current featured status: " . ($testImage->is_featured ? 'true' : 'false') . "\n";

// Test the toggle method directly
$originalStatus = $testImage->is_featured;
$result = $testImage->toggleFeatured();
$testImage->refresh();

echo "Toggle result: " . ($result ? 'success' : 'failed') . "\n";
echo "New featured status: " . ($testImage->is_featured ? 'true' : 'false') . "\n";
echo "Status changed: " . ($originalStatus !== $testImage->is_featured ? 'yes' : 'no') . "\n\n";

// Test toggle active functionality
echo "Testing toggle active on image: {$testImage->title} (ID: {$testImage->id})\n";
echo "Current active status: " . ($testImage->is_active ? 'true' : 'false') . "\n";

$originalActiveStatus = $testImage->is_active;
$activeResult = $testImage->toggleActive();
$testImage->refresh();

echo "Toggle result: " . ($activeResult ? 'success' : 'failed') . "\n";
echo "New active status: " . ($testImage->is_active ? 'true' : 'false') . "\n";
echo "Status changed: " . ($originalActiveStatus !== $testImage->is_active ? 'yes' : 'no') . "\n\n";

// Test controller methods
echo "Testing controller methods...\n";

// Import the controller
use App\Http\Controllers\Owner\GalleryController;

try {
    $controller = new GalleryController(app(\App\Services\GalleryImageService::class));
    
    // Test toggleFeatured method
    echo "Testing toggleFeatured controller method...\n";
    $response = $controller->toggleFeatured($testImage);
    $responseData = $response->getData(true);
    echo "Response: " . json_encode($responseData) . "\n";
    
    // Test toggleActive method
    echo "Testing toggleActive controller method...\n";
    $response = $controller->toggleActive($testImage);
    $responseData = $response->getData(true);
    echo "Response: " . json_encode($responseData) . "\n";
    
} catch (Exception $e) {
    echo "Controller test failed: " . $e->getMessage() . "\n";
}

echo "\nTest completed!\n";
