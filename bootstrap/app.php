<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'configure.owner' => App\Http\Middleware\ConfigureOwnerPanel::class,
            'business.owner' => App\Http\Middleware\EnsureBusinessOwnership::class,
            'hierarchical.role' => App\Http\Middleware\HierarchicalRoleMiddleware::class,
            '2fa.required' => App\Http\Middleware\TwoFactorAuthMiddleware::class,
            'business.isolation' => App\Http\Middleware\BusinessDataIsolationMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
