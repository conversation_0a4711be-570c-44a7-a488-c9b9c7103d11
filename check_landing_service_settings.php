<?php
require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;

echo "Checking landing_service_settings table:\n";

if (Schema::hasTable('landing_service_settings')) {
    echo "✓ Table exists\n\n";

    echo "Columns:\n";
    $columns = Schema::getColumnListing('landing_service_settings');
    foreach ($columns as $column) {
        echo "- $column\n";
    }
} else {
    echo "✗ Table does not exist\n";
}
