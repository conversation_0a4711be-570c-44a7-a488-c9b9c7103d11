@extends('owner.layouts.app')

@section('title', 'Customer Tags')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Customer Tags</h1>
            <p class="text-muted">Manage customer categorization tags</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <div class="btn-group" role="group">
                    @if($tags->count() === 0)
                        <button class="btn btn-info" onclick="createDefaults()">
                            <i class="fas fa-magic mr-2"></i>
                            Create Defaults
                        </button>
                    @endif
                    <button class="btn btn-primary" onclick="showCreateModal()">
                        <i class="fas fa-plus mr-2"></i>
                        Add Tag
                    </button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    @if($tags->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-tags mr-2"></i>
                    Customer Tags ({{ $tags->count() }} tags)
                </h3>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Tag</th>
                                <th>Description</th>
                                <th>Customers</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tags as $tag)
                                <tr data-tag-id="{{ $tag->id }}">
                                    <td>
                                        <span class="badge mr-2" style="background-color: {{ $tag->color }}; color: white; font-size: 14px;">
                                            {{ $tag->name }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $tag->description ?: 'No description' }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ number_format($tag->assignments_count) }}</strong> customers
                                        @if($tag->assignments_count > 0)
                                            <br><small class="text-muted">
                                                <a href="#" onclick="viewTagStats({{ $tag->id }})" class="text-info">
                                                    View details
                                                </a>
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $tag->is_active ? 'badge-success' : 'badge-secondary' }}">
                                            {{ $tag->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-info" onclick="editTag({{ $tag->id }})" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm {{ $tag->is_active ? 'btn-warning' : 'btn-success' }}"
                                                    onclick="toggleTagStatus({{ $tag->id }})"
                                                    title="{{ $tag->is_active ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas {{ $tag->is_active ? 'fa-pause' : 'fa-play' }}"></i>
                                            </button>
                                            @if($tag->assignments_count > 0)
                                                <button class="btn btn-sm btn-secondary" onclick="exportTag({{ $tag->id }})" title="Export">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            @endif
                                            <button class="btn btn-sm btn-danger" onclick="deleteTag({{ $tag->id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @else
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-tags fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">No Customer Tags</h4>
                <p class="text-muted mb-4">Create tags to categorize and organize your customers effectively.</p>
                <div class="btn-group" role="group">
                    <button class="btn btn-info" onclick="createDefaults()">
                        <i class="fas fa-magic mr-2"></i>
                        Create Default Tags
                    </button>
                    <button class="btn btn-primary" onclick="showCreateModal()">
                        <i class="fas fa-plus mr-2"></i>
                        Create Custom Tag
                    </button>
                </div>
            </div>
        </div>
    @endif

    {{-- Create/Edit Tag Modal --}}
    <div class="modal fade" id="tagModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tagModalTitle">Create Tag</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="tagForm">
                    @csrf
                    <input type="hidden" id="tag_id" name="tag_id">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="tag_name">Tag Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="tag_name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="tag_color">Color <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="color" class="form-control" id="tag_color" name="color" value="#007bff" required>
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="fas fa-palette"></i>
                                    </span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Choose a color to represent this tag</small>
                        </div>
                        <div class="form-group">
                            <label for="tag_description">Description</label>
                            <textarea class="form-control" id="tag_description" name="description" rows="3"
                                      placeholder="Optional description for this tag"></textarea>
                        </div>
                        <div class="form-group" id="tag_status_group" style="display: none;">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="tag_is_active" name="is_active" checked>
                                <label class="custom-control-label" for="tag_is_active">
                                    Active (customers can be assigned this tag)
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            <span id="tagSubmitText">Create Tag</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Tag Stats Modal --}}
    <div class="modal fade" id="tagStatsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Tag Statistics</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="tagStatsContent">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Loading statistics...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
<script>
let isEditMode = false;

// Create/Edit Tag Functions
function showCreateModal() {
    isEditMode = false;
    $('#tagModalTitle').text('Create Tag');
    $('#tagSubmitText').text('Create Tag');
    $('#tag_status_group').hide();
    $('#tagForm')[0].reset();
    $('#tag_color').val('#007bff');
    $('#tagModal').modal('show');
}

function editTag(tagId) {
    isEditMode = true;
    $('#tagModalTitle').text('Edit Tag');
    $('#tagSubmitText').text('Update Tag');
    $('#tag_status_group').show();

    // Find tag data from the table
    const row = $(`tr[data-tag-id="${tagId}"]`);
    const tagName = row.find('.badge').text().trim();

    // You would typically fetch this data via AJAX, but for now we'll use a simple approach
    $('#tag_id').val(tagId);
    $('#tag_name').val(tagName);
    $('#tagModal').modal('show');
}

$('#tagForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.html();

    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...');

    const url = isEditMode
        ? `/owner/customer-tags/${$('#tag_id').val()}`
        : '{{ route("owner.customer-tags.store") }}';

    const method = isEditMode ? 'PUT' : 'POST';

    if (isEditMode) {
        formData.append('_method', 'PUT');
    }

    $.ajax({
        url: url,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#tagModal').modal('hide');
                location.reload(); // Refresh to show changes
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            if (response?.errors) {
                Object.values(response.errors).forEach(errors => {
                    errors.forEach(error => toastr.error(error));
                });
            } else {
                toastr.error(response?.message || 'Failed to save tag');
            }
        },
        complete: function() {
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
});

// Tag Management Functions
function toggleTagStatus(tagId) {
    $.ajax({
        url: `/owner/customer-tags/${tagId}/toggle-status`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                location.reload();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to update tag status');
        }
    });
}

function deleteTag(tagId) {
    if (confirm('Are you sure you want to delete this tag? This will remove it from all customers.')) {
        $.ajax({
            url: `/owner/customer-tags/${tagId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response?.message || 'Failed to delete tag');
            }
        });
    }
}

function viewTagStats(tagId) {
    $('#tagStatsModal').modal('show');

    $.ajax({
        url: `/owner/customer-tags/${tagId}/stats`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                let content = `
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Customers</span>
                                    <span class="info-box-number">${response.stats.total_customers}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-user-plus"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Recent (30 days)</span>
                                    <span class="info-box-number">${response.stats.recent_assignments}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                if (response.stats.customers.length > 0) {
                    content += `
                        <h6>Customers with this tag:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Email</th>
                                        <th>Assigned</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    response.stats.customers.forEach(customer => {
                        content += `
                            <tr>
                                <td>${customer.name}</td>
                                <td>${customer.email}</td>
                                <td>${customer.assigned_at}</td>
                            </tr>
                        `;
                    });

                    content += `
                                </tbody>
                            </table>
                        </div>
                    `;
                }

                $('#tagStatsContent').html(content);
            } else {
                $('#tagStatsContent').html('<div class="alert alert-danger">Failed to load statistics</div>');
            }
        },
        error: function() {
            $('#tagStatsContent').html('<div class="alert alert-danger">Failed to load statistics</div>');
        }
    });
}

function exportTag(tagId) {
    window.location.href = `/owner/customer-tags/${tagId}/export`;
}

function createDefaults() {
    if (confirm('This will create default customer tags for your business. Continue?')) {
        $.ajax({
            url: '{{ route("owner.customer-tags.create-defaults") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response?.message || 'Failed to create default tags');
            }
        });
    }
}
</script>
@stop
