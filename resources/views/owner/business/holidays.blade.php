@extends('owner.layouts.app')

@section('title', 'Holidays & Closures')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Holidays & Closures</h1>
            <p class="text-muted mb-0">Manage your business holidays and temporary closures.</p>
        </div>
        <div>
            <a href="{{ route('owner.business.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Overview
            </a>
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addHolidayModal">
                <i class="fas fa-plus mr-1"></i>
                Add Holiday
            </button>
        </div>
    </div>
@stop

@section('content')
    {{-- Success/Error Messages --}}
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle mr-2"></i>
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-calendar-times text-danger mr-2"></i>
                        Scheduled Holidays & Closures
                    </h5>
                </div>
                <div class="widget-body">
                    @if(count($holidays) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Holiday Name</th>
                                        <th>Date(s)</th>
                                        <th>Type</th>
                                        <th>Recurring</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($holidays as $holiday)
                                        <tr>
                                            <td>
                                                <strong>{{ $holiday->name }}</strong>
                                                @if($holiday->description)
                                                    <br><small class="text-muted">{{ $holiday->description }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($holiday->start_date->format('Y-m-d') !== $holiday->end_date->format('Y-m-d'))
                                                    {{ $holiday->start_date->format('M j, Y') }} -
                                                    {{ $holiday->end_date->format('M j, Y') }}
                                                @else
                                                    {{ $holiday->start_date->format('M j, Y') }}
                                                @endif
                                            </td>
                                            <td>
                                                @if($holiday->is_recurring)
                                                    <span class="badge badge-info">
                                                        <i class="fas fa-calendar-alt mr-1"></i>
                                                        {{ ucfirst($holiday->recurrence_type ?? 'Yearly') }}
                                                    </span>
                                                @else
                                                    <span class="badge badge-warning">Business Closure</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($holiday->is_recurring)
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-sync-alt mr-1"></i>
                                                        {{ ucfirst($holiday->recurrence_type ?? 'Yearly') }}
                                                    </span>
                                                @else
                                                    <span class="badge badge-secondary">One-time</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary"
                                                            onclick="editHoliday({{ $holiday->id }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger"
                                                            onclick="deleteHoliday({{ $holiday->id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No holidays scheduled</h5>
                            <p class="text-muted">Add holidays and closures to help customers plan their visits.</p>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addHolidayModal">
                                <i class="fas fa-plus mr-1"></i>
                                Add Your First Holiday
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {{-- Quick Actions --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-magic text-primary mr-2"></i>
                        Quick Add
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="list-group list-group-flush">
                        <button type="button" class="list-group-item list-group-item-action" onclick="addCommonHolidays()">
                            <i class="fas fa-flag-usa text-primary mr-2"></i>
                            Add Common US Holidays
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="addChristmasWeek()">
                            <i class="fas fa-tree text-success mr-2"></i>
                            Add Christmas Week Closure
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="addSummerBreak()">
                            <i class="fas fa-sun text-warning mr-2"></i>
                            Add Summer Break
                        </button>
                    </div>
                </div>
            </div>

            {{-- Holiday Tips --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-lightbulb text-warning mr-2"></i>
                        Tips
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="callout callout-info">
                        <h6><i class="fas fa-info-circle mr-1"></i> Customer Notifications</h6>
                        <p class="mb-0 small">Customers will be automatically notified about closures when they try to book during holiday periods.</p>
                    </div>

                    <div class="callout callout-warning">
                        <h6><i class="fas fa-calendar-alt mr-1"></i> Recurring Holidays</h6>
                        <p class="mb-0 small">Set holidays as recurring to automatically apply them every year without manual updates.</p>
                    </div>

                    <div class="callout callout-success">
                        <h6><i class="fas fa-clock mr-1"></i> Advance Notice</h6>
                        <p class="mb-0 small">Add holidays well in advance to give customers time to plan alternative dates.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Add Holiday Modal --}}
    <div class="modal fade" id="addHolidayModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Holiday / Closure</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="holidayForm" method="POST" action="{{ route('owner.business.holidays.store') }}">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="holiday_name">Holiday Name</label>
                            <input type="text" class="form-control" id="holiday_name" name="name" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_recurring" name="is_recurring" value="1">
                                <label class="form-check-label" for="is_recurring">
                                    Recurring annually
                                </label>
                            </div>
                        </div>

                        <div class="form-group" id="recurrence_type_group" style="display: none;">
                            <label for="recurrence_type">Recurrence Type</label>
                            <select class="form-control" id="recurrence_type" name="recurrence_type">
                                <option value="yearly">Yearly</option>
                                <option value="monthly">Monthly</option>
                                <option value="weekly">Weekly</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="description">Description (Optional)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Add any additional notes about this holiday or closure..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Holiday</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Edit Holiday Modal --}}
    <div class="modal fade" id="editHolidayModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Holiday / Closure</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="editHolidayForm" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="edit_holiday_name">Holiday Name</label>
                            <input type="text" class="form-control" id="edit_holiday_name" name="name" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_start_date">Start Date</label>
                                    <input type="date" class="form-control" id="edit_start_date" name="start_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_end_date">End Date</label>
                                    <input type="date" class="form-control" id="edit_end_date" name="end_date">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_is_recurring" name="is_recurring" value="1">
                                <label class="form-check-label" for="edit_is_recurring">
                                    Recurring annually
                                </label>
                            </div>
                        </div>

                        <div class="form-group" id="edit_recurrence_type_group" style="display: none;">
                            <label for="edit_recurrence_type">Recurrence Type</label>
                            <select class="form-control" id="edit_recurrence_type" name="recurrence_type">
                                <option value="yearly">Yearly</option>
                                <option value="monthly">Monthly</option>
                                <option value="weekly">Weekly</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_description">Description (Optional)</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3" placeholder="Add any additional notes about this holiday or closure..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Holiday</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
        // Holiday data for editing
        const holidays = @json($holidays);

        $(document).ready(function() {
            // Set minimum date to today for new holidays
            const today = new Date().toISOString().split('T')[0];
            $('#start_date').attr('min', today);

            // Handle recurring checkbox changes
            $('#is_recurring').change(function() {
                if ($(this).is(':checked')) {
                    $('#recurrence_type_group').show();
                } else {
                    $('#recurrence_type_group').hide();
                }
            });

            $('#edit_is_recurring').change(function() {
                if ($(this).is(':checked')) {
                    $('#edit_recurrence_type_group').show();
                } else {
                    $('#edit_recurrence_type_group').hide();
                }
            });

            // Handle end date validation
            $('#start_date').change(function() {
                const startDate = $(this).val();
                $('#end_date').attr('min', startDate);
                if ($('#end_date').val() && $('#end_date').val() < startDate) {
                    $('#end_date').val(startDate);
                }
            });

            $('#edit_start_date').change(function() {
                const startDate = $(this).val();
                $('#edit_end_date').attr('min', startDate);
                if ($('#edit_end_date').val() && $('#edit_end_date').val() < startDate) {
                    $('#edit_end_date').val(startDate);
                }
            });
        });

        function editHoliday(id) {
            const holiday = holidays.find(h => h.id === id);
            if (!holiday) {
                alert('Holiday not found');
                return;
            }

            // Populate the edit form
            $('#edit_holiday_name').val(holiday.name);
            $('#edit_start_date').val(holiday.start_date);
            $('#edit_end_date').val(holiday.end_date);
            $('#edit_description').val(holiday.description || '');

            if (holiday.is_recurring) {
                $('#edit_is_recurring').prop('checked', true);
                $('#edit_recurrence_type_group').show();
                $('#edit_recurrence_type').val(holiday.recurrence_type || 'yearly');
            } else {
                $('#edit_is_recurring').prop('checked', false);
                $('#edit_recurrence_type_group').hide();
            }

            // Set form action
            const updateUrl = "{{ route('owner.business.holidays.update', ':id') }}".replace(':id', id);
            $('#editHolidayForm').attr('action', updateUrl);

            // Show modal
            $('#editHolidayModal').modal('show');
        }

        function deleteHoliday(id) {
            if (confirm('Are you sure you want to delete this holiday? This action cannot be undone.')) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = "{{ route('owner.business.holidays.destroy', ':id') }}".replace(':id', id);

                // Add CSRF token
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                // Add method override
                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';
                form.appendChild(methodField);

                document.body.appendChild(form);
                form.submit();
            }
        }

        function addCommonHolidays() {
            if (confirm('This will add common US holidays for the current year. Continue?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = "{{ route('owner.business.holidays.import-common') }}";

                // Add CSRF token
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                document.body.appendChild(form);
                form.submit();
            }
        }

        function addChristmasWeek() {
            // Pre-fill the form with Christmas week data
            const currentYear = new Date().getFullYear();
            $('#holiday_name').val('Christmas Week Closure');
            $('#start_date').val(`${currentYear}-12-23`);
            $('#end_date').val(`${currentYear}-12-31`);
            $('#description').val('Business closed for Christmas and New Year holidays');
            $('#addHolidayModal').modal('show');
        }

        function addSummerBreak() {
            // Pre-fill the form with summer break data
            const currentYear = new Date().getFullYear();
            $('#holiday_name').val('Summer Break');
            $('#start_date').val(`${currentYear}-07-01`);
            $('#end_date').val(`${currentYear}-07-15`);
            $('#description').val('Annual summer break closure');
            $('#addHolidayModal').modal('show');
        }

        // Reset form when modal is closed
        $('#addHolidayModal').on('hidden.bs.modal', function() {
            $('#holidayForm')[0].reset();
            $('#recurrence_type_group').hide();
        });

        $('#editHolidayModal').on('hidden.bs.modal', function() {
            $('#editHolidayForm')[0].reset();
            $('#edit_recurrence_type_group').hide();
        });
    </script>
@stop
