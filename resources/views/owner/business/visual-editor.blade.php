@extends('owner.layouts.app')

@section('title', 'Visual Page Builder')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Visual Page Builder</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('owner.business.index') }}">Business</a></li>
                        <li class="breadcrumb-item active">Visual Editor</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar with Components -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-layer-group mr-2"></i>
                                Page Sections
                            </h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="section-components">
                                <!-- Hero Section -->
                                <div class="component-item" draggable="true" data-component="hero">
                                    <div class="component-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>Hero Section</h6>
                                        <small>Main banner with call-to-action</small>
                                    </div>
                                </div>

                                <!-- About Section -->
                                <div class="component-item" draggable="true" data-component="about">
                                    <div class="component-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>About Section</h6>
                                        <small>Business information and story</small>
                                    </div>
                                </div>

                                <!-- Services Section -->
                                <div class="component-item" draggable="true" data-component="services">
                                    <div class="component-icon">
                                        <i class="fas fa-concierge-bell"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>Services Section</h6>
                                        <small>Display your services catalog</small>
                                    </div>
                                </div>

                                <!-- Team Section -->
                                <div class="component-item" draggable="true" data-component="team">
                                    <div class="component-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>Team Section</h6>
                                        <small>Meet your team members</small>
                                    </div>
                                </div>

                                <!-- Testimonials Section -->
                                <div class="component-item" draggable="true" data-component="testimonials">
                                    <div class="component-icon">
                                        <i class="fas fa-quote-left"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>Testimonials</h6>
                                        <small>Customer reviews and feedback</small>
                                    </div>
                                </div>

                                <!-- Gallery Section -->
                                <div class="component-item" draggable="true" data-component="gallery">
                                    <div class="component-icon">
                                        <i class="fas fa-images"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>Gallery Section</h6>
                                        <small>Showcase your work</small>
                                    </div>
                                </div>

                                <!-- Contact Section -->
                                <div class="component-item" draggable="true" data-component="contact">
                                    <div class="component-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>Contact Section</h6>
                                        <small>Contact form and information</small>
                                    </div>
                                </div>

                                <!-- Booking Section -->
                                <div class="component-item" draggable="true" data-component="booking">
                                    <div class="component-icon">
                                        <i class="fas fa-calendar-plus"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>Booking Section</h6>
                                        <small>Online appointment booking</small>
                                    </div>
                                </div>

                                <!-- Custom Content -->
                                <div class="component-item" draggable="true" data-component="custom">
                                    <div class="component-icon">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="component-info">
                                        <h6>Custom Content</h6>
                                        <small>Add custom HTML/text</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Settings -->
                    <div class="card" id="section-settings" style="display: none;">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-cog mr-2"></i>
                                Section Settings
                            </h3>
                        </div>
                        <div class="card-body" id="settings-content">
                            <!-- Dynamic settings content will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Main Editor Area -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-desktop mr-2"></i>
                                Page Preview
                            </h3>
                            <div class="card-tools">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="desktop-view">
                                        <i class="fas fa-desktop"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="tablet-view">
                                        <i class="fas fa-tablet-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="mobile-view">
                                        <i class="fas fa-mobile-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="page-builder-canvas" id="canvas">
                                <div class="drop-zone" id="drop-zone">
                                    <div class="drop-zone-placeholder">
                                        <i class="fas fa-plus-circle fa-3x text-muted"></i>
                                        <h4 class="text-muted mt-3">Drag sections here to build your page</h4>
                                        <p class="text-muted">Start by dragging a Hero section from the left panel</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="row">
                                <div class="col-6">
                                    <button type="button" class="btn btn-secondary" id="preview-btn">
                                        <i class="fas fa-eye mr-1"></i>
                                        Preview
                                    </button>
                                </div>
                                <div class="col-6 text-right">
                                    <button type="button" class="btn btn-success" id="save-btn">
                                        <i class="fas fa-save mr-1"></i>
                                        Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Properties Panel -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-palette mr-2"></i>
                                Design Settings
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Theme Selection -->
                            <div class="form-group">
                                <label>Page Theme</label>
                                <select class="form-control" id="page-theme">
                                    <option value="modern">Modern</option>
                                    <option value="classic">Classic</option>
                                    <option value="minimal">Minimal</option>
                                    <option value="creative">Creative</option>
                                </select>
                            </div>

                            <!-- Color Scheme -->
                            <div class="form-group">
                                <label>Primary Color</label>
                                <input type="color" class="form-control" id="primary-color" value="#007bff">
                            </div>

                            <div class="form-group">
                                <label>Secondary Color</label>
                                <input type="color" class="form-control" id="secondary-color" value="#6c757d">
                            </div>

                            <!-- Typography -->
                            <div class="form-group">
                                <label>Font Family</label>
                                <select class="form-control" id="font-family">
                                    <option value="Inter">Inter</option>
                                    <option value="Roboto">Roboto</option>
                                    <option value="Open Sans">Open Sans</option>
                                    <option value="Lato">Lato</option>
                                    <option value="Poppins">Poppins</option>
                                </select>
                            </div>

                            <!-- Layout Options -->
                            <div class="form-group">
                                <label>Container Width</label>
                                <select class="form-control" id="container-width">
                                    <option value="container">Standard (1140px)</option>
                                    <option value="container-fluid">Full Width</option>
                                    <option value="container-sm">Small (540px)</option>
                                    <option value="container-lg">Large (1200px)</option>
                                </select>
                            </div>

                            <!-- Animation Settings -->
                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="enable-animations" checked>
                                    <label class="custom-control-label" for="enable-animations">
                                        Enable Animations
                                    </label>
                                </div>
                            </div>

                            <!-- SEO Settings -->
                            <hr>
                            <h6>SEO Settings</h6>

                            <div class="form-group">
                                <label>Page Title</label>
                                <input type="text" class="form-control" id="seo-title" maxlength="60">
                                <small class="form-text text-muted">
                                    <span id="title-count">0</span>/60 characters
                                </small>
                            </div>

                            <div class="form-group">
                                <label>Meta Description</label>
                                <textarea class="form-control" id="seo-description" rows="3" maxlength="160"></textarea>
                                <small class="form-text text-muted">
                                    <span id="description-count">0</span>/160 characters
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Section Templates (Hidden) -->
<div id="section-templates" style="display: none;">
    <!-- Hero Section Template -->
    <div class="section-template" data-type="hero">
        <div class="section-wrapper hero-section" data-section="hero">
            <div class="section-controls">
                <button class="btn btn-sm btn-primary edit-section"><i class="fas fa-edit"></i></button>
                <button class="btn btn-sm btn-danger delete-section"><i class="fas fa-trash"></i></button>
                <button class="btn btn-sm btn-secondary move-up"><i class="fas fa-arrow-up"></i></button>
                <button class="btn btn-sm btn-secondary move-down"><i class="fas fa-arrow-down"></i></button>
            </div>
            <div class="section-content">
                <div class="hero-preview">
                    <h1>Your Business Name</h1>
                    <p>Welcome to our amazing business</p>
                    <button class="btn btn-primary">Book Now</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Services Section Template -->
    <div class="section-template" data-type="services">
        <div class="section-wrapper services-section" data-section="services">
            <div class="section-controls">
                <button class="btn btn-sm btn-primary edit-section"><i class="fas fa-edit"></i></button>
                <button class="btn btn-sm btn-danger delete-section"><i class="fas fa-trash"></i></button>
                <button class="btn btn-sm btn-secondary move-up"><i class="fas fa-arrow-up"></i></button>
                <button class="btn btn-sm btn-secondary move-down"><i class="fas fa-arrow-down"></i></button>
            </div>
            <div class="section-content">
                <div class="services-preview">
                    <h2>Our Services</h2>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="service-card">
                                <h5>Service 1</h5>
                                <p>Service description</p>
                                <span class="price">$50</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="service-card">
                                <h5>Service 2</h5>
                                <p>Service description</p>
                                <span class="price">$75</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="service-card">
                                <h5>Service 3</h5>
                                <p>Service description</p>
                                <span class="price">$100</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('css')
<style>
.page-builder-canvas {
    min-height: 600px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    position: relative;
}

.drop-zone {
    min-height: 600px;
    padding: 20px;
}

.drop-zone-placeholder {
    text-align: center;
    padding: 100px 20px;
}

.drop-zone.drag-over {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.component-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    cursor: grab;
    transition: all 0.3s ease;
}

.component-item:hover {
    background-color: #f8f9fa;
}

.component-item:active {
    cursor: grabbing;
}

.component-icon {
    width: 40px;
    height: 40px;
    background: #007bff;
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.component-info h6 {
    margin: 0;
    font-weight: 600;
}

.component-info small {
    color: #6c757d;
}

.section-wrapper {
    position: relative;
    margin-bottom: 20px;
    border: 2px solid transparent;
    border-radius: 8px;
    background: white;
    padding: 20px;
    transition: all 0.3s ease;
}

.section-wrapper:hover {
    border-color: #007bff;
}

.section-wrapper.selected {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
}

.section-controls {
    position: absolute;
    top: -15px;
    right: 10px;
    display: none;
    gap: 5px;
}

.section-wrapper:hover .section-controls,
.section-wrapper.selected .section-controls {
    display: flex;
}

.hero-preview {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.services-preview {
    padding: 40px 20px;
}

.service-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.price {
    font-size: 1.25rem;
    font-weight: bold;
    color: #28a745;
}

/* Responsive preview modes */
.canvas-tablet {
    max-width: 768px;
    margin: 0 auto;
}

.canvas-mobile {
    max-width: 375px;
    margin: 0 auto;
}

/* Drag and drop styles */
.dragging {
    opacity: 0.5;
}

.drop-indicator {
    height: 4px;
    background: #007bff;
    margin: 10px 0;
    border-radius: 2px;
}
</style>
@endsection

@section('js')
<script src="{{ asset('js/visual-editor.js') }}"></script>
@endsection
