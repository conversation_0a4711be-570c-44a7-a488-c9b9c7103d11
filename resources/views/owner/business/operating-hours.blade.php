@extends('owner.layouts.app')

@section('title', 'Operating Hours')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Operating Hours</h1>
            <p class="text-muted mb-0">Manage your business operating hours and schedule.</p>
        </div>
        <div>
            <a href="{{ route('owner.business.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Overview
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-clock text-primary mr-2"></i>
                        Weekly Operating Hours
                    </h5>
                </div>
                <div class="widget-body">
                    <form action="{{ route('owner.business.operating-hours.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
                            <div class="row mb-3 align-items-center">
                                <div class="col-md-2">
                                    <label class="form-label fw-bold">{{ ucfirst($day) }}</label>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               id="is_open_{{ $day }}"
                                               name="hours[{{ $day }}][is_open]"
                                               value="1"
                                               {{ $operatingHours[$day]['is_open'] ? 'checked' : '' }}
                                               onchange="toggleHours('{{ $day }}')">
                                        <label class="form-check-label" for="is_open_{{ $day }}">
                                            Open
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <input type="time"
                                           class="form-control"
                                           id="open_time_{{ $day }}"
                                           name="hours[{{ $day }}][open_time]"
                                           value="{{ $operatingHours[$day]['open_time'] }}"
                                           {{ !$operatingHours[$day]['is_open'] ? 'disabled' : '' }}>
                                </div>
                                <div class="col-md-1 text-center">
                                    <span class="text-muted">to</span>
                                </div>
                                <div class="col-md-3">
                                    <input type="time"
                                           class="form-control"
                                           id="close_time_{{ $day }}"
                                           name="hours[{{ $day }}][close_time]"
                                           value="{{ $operatingHours[$day]['close_time'] }}"
                                           {{ !$operatingHours[$day]['is_open'] ? 'disabled' : '' }}>
                                </div>
                                <div class="col-md-1">
                                    @if(!$operatingHours[$day]['is_open'])
                                        <span class="badge badge-secondary">Closed</span>
                                    @endif
                                </div>
                            </div>
                            <input type="hidden" name="hours[{{ $day }}][day]" value="{{ $day }}">
                        @endforeach

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i>
                                Save Operating Hours
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="copyToAll()">
                                <i class="fas fa-copy mr-1"></i>
                                Copy Monday to All Days
                            </button>
                        </div>

                        @if(session('success'))
                            <div class="alert alert-success mt-3">
                                <i class="fas fa-check-circle mr-1"></i>
                                {{ session('success') }}
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="alert alert-danger mt-3">
                                <h6><i class="fas fa-exclamation-triangle mr-1"></i> Validation Errors:</h6>
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {{-- Quick Actions --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-cogs text-success mr-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="list-group list-group-flush">
                        <button type="button" class="list-group-item list-group-item-action" onclick="setStandardHours()">
                            <i class="fas fa-business-time text-primary mr-2"></i>
                            Set Standard Business Hours (9 AM - 5 PM)
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="setExtendedHours()">
                            <i class="fas fa-clock text-warning mr-2"></i>
                            Set Extended Hours (8 AM - 8 PM)
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="setWeekendClosed()">
                            <i class="fas fa-calendar-times text-danger mr-2"></i>
                            Close Weekends
                        </button>
                    </div>
                </div>
            </div>

            {{-- Current Schedule Preview --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-eye text-info mr-2"></i>
                        Schedule Preview
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="schedule-preview">
                        @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-bold">{{ ucfirst($day) }}</span>
                                <span class="text-muted" id="preview_{{ $day }}">
                                    @if($operatingHours[$day]['is_open'])
                                        {{ $operatingHours[$day]['open_time'] }} - {{ $operatingHours[$day]['close_time'] }}
                                    @else
                                        Closed
                                    @endif
                                </span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
        // Add form submission handler for debugging
        $('form').on('submit', function(e) {
            console.log('Form submitted!');
            console.log('Form data:', $(this).serialize());

            // Check if any days are open
            let hasOpenDays = false;
            $('input[type="checkbox"]:checked').each(function() {
                hasOpenDays = true;
            });

            if (!hasOpenDays) {
                if (!confirm('All days are set to closed. Are you sure you want to save this?')) {
                    e.preventDefault();
                    return false;
                }
            }
        });

        // Show success message if present
        @if(session('success'))
            $(document).ready(function() {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: '{{ session("success") }}',
                    confirmButtonColor: '#28a745',
                    timer: 3000,
                    timerProgressBar: true
                });
            });
        @endif

        function toggleHours(day) {
            const isOpen = document.getElementById('is_open_' + day).checked;
            const openTime = document.getElementById('open_time_' + day);
            const closeTime = document.getElementById('close_time_' + day);
            const preview = document.getElementById('preview_' + day);

            openTime.disabled = !isOpen;
            closeTime.disabled = !isOpen;

            if (!isOpen) {
                openTime.value = '';
                closeTime.value = '';
                preview.textContent = 'Closed';
            } else {
                openTime.value = '09:00';
                closeTime.value = '17:00';
                preview.textContent = '09:00 - 17:00';
            }
        }

        function copyToAll() {
            const mondayOpen = document.getElementById('is_open_monday').checked;
            const mondayOpenTime = document.getElementById('open_time_monday').value;
            const mondayCloseTime = document.getElementById('close_time_monday').value;

            ['tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].forEach(day => {
                document.getElementById('is_open_' + day).checked = mondayOpen;
                document.getElementById('open_time_' + day).value = mondayOpenTime;
                document.getElementById('close_time_' + day).value = mondayCloseTime;
                document.getElementById('open_time_' + day).disabled = !mondayOpen;
                document.getElementById('close_time_' + day).disabled = !mondayOpen;

                const preview = document.getElementById('preview_' + day);
                if (mondayOpen) {
                    preview.textContent = mondayOpenTime + ' - ' + mondayCloseTime;
                } else {
                    preview.textContent = 'Closed';
                }
            });
        }

        function setStandardHours() {
            ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'].forEach(day => {
                document.getElementById('is_open_' + day).checked = true;
                document.getElementById('open_time_' + day).value = '09:00';
                document.getElementById('close_time_' + day).value = '17:00';
                document.getElementById('open_time_' + day).disabled = false;
                document.getElementById('close_time_' + day).disabled = false;
                document.getElementById('preview_' + day).textContent = '09:00 - 17:00';
            });
        }

        function setExtendedHours() {
            ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].forEach(day => {
                document.getElementById('is_open_' + day).checked = true;
                document.getElementById('open_time_' + day).value = '08:00';
                document.getElementById('close_time_' + day).value = '20:00';
                document.getElementById('open_time_' + day).disabled = false;
                document.getElementById('close_time_' + day).disabled = false;
                document.getElementById('preview_' + day).textContent = '08:00 - 20:00';
            });
        }

        function setWeekendClosed() {
            ['saturday', 'sunday'].forEach(day => {
                document.getElementById('is_open_' + day).checked = false;
                document.getElementById('open_time_' + day).value = '';
                document.getElementById('close_time_' + day).value = '';
                document.getElementById('open_time_' + day).disabled = true;
                document.getElementById('close_time_' + day).disabled = true;
                document.getElementById('preview_' + day).textContent = 'Closed';
            });
        }
    </script>
@stop
