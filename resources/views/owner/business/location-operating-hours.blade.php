@extends('owner.layouts.app')

@section('title', 'Operating Hours - ' . $location->name)

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Operating Hours</h1>
            <p class="text-muted mb-0">Manage operating hours for {{ $location->name }}</p>
        </div>
        <div>
            <a href="{{ route('owner.business.locations') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Locations
            </a>
        </div>
    </div>
@stop

@section('content')
    {{-- Success/Error Messages --}}
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle mr-2"></i>
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2"></i>
                        Weekly Operating Hours
                    </h3>
                    <div class="card-tools">
                        <form method="POST" action="{{ route('owner.business.locations.copy-business-hours', $location) }}" style="display: inline;">
                            @csrf
                            <button type="submit" class="btn btn-sm btn-outline-primary" onclick="return confirm('This will overwrite current hours with your business default hours. Continue?')">
                                <i class="fas fa-copy mr-1"></i>
                                Copy Business Hours
                            </button>
                        </form>
                    </div>
                </div>
                <form method="POST" action="{{ route('owner.business.locations.operating-hours.update', $location) }}">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        @php
                            $dayNames = [
                                'sunday' => 'Sunday',
                                'monday' => 'Monday',
                                'tuesday' => 'Tuesday',
                                'wednesday' => 'Wednesday',
                                'thursday' => 'Thursday',
                                'friday' => 'Friday',
                                'saturday' => 'Saturday'
                            ];
                        @endphp

                        @foreach($dayNames as $dayKey => $dayName)
                            @php
                                $dayData = $operatingHours[$dayKey] ?? ['day' => $dayKey, 'is_open' => false, 'open_time' => '', 'close_time' => '', 'break_times' => []];
                            @endphp
                            <div class="row mb-3 pb-3 border-bottom">
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="font-weight-bold">{{ $dayName }}</label>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input day-toggle"
                                                   id="is_open_{{ $dayKey }}"
                                                   name="hours[{{ $loop->index }}][is_open]"
                                                   value="1"
                                                   {{ $dayData['is_open'] ? 'checked' : '' }}
                                                   data-day="{{ $dayKey }}">
                                            <label class="form-check-label" for="is_open_{{ $dayKey }}">
                                                Open
                                            </label>
                                        </div>
                                        <input type="hidden" name="hours[{{ $loop->index }}][day]" value="{{ $dayKey }}">
                                    </div>
                                </div>
                                <div class="col-md-10">
                                    <div class="time-inputs" id="time_inputs_{{ $dayKey }}" style="{{ $dayData['is_open'] ? '' : 'display: none;' }}">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="open_time_{{ $dayKey }}">Open Time</label>
                                                    <input type="time" class="form-control"
                                                           id="open_time_{{ $dayKey }}"
                                                           name="hours[{{ $loop->index }}][open_time]"
                                                           value="{{ $dayData['open_time'] }}">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="close_time_{{ $dayKey }}">Close Time</label>
                                                    <input type="time" class="form-control"
                                                           id="close_time_{{ $dayKey }}"
                                                           name="hours[{{ $loop->index }}][close_time]"
                                                           value="{{ $dayData['close_time'] }}">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Break Times (Optional)</label>
                                                    <div class="break-times-container" id="break_times_{{ $dayKey }}">
                                                        @if(!empty($dayData['break_times']))
                                                            @foreach($dayData['break_times'] as $breakIndex => $breakTime)
                                                                <div class="row mb-2 break-time-row">
                                                                    <div class="col-5">
                                                                        <input type="time" class="form-control form-control-sm"
                                                                               name="hours[{{ $loop->parent->index }}][break_times][{{ $breakIndex }}][start]"
                                                                               value="{{ $breakTime['start'] ?? '' }}"
                                                                               placeholder="Break start">
                                                                    </div>
                                                                    <div class="col-5">
                                                                        <input type="time" class="form-control form-control-sm"
                                                                               name="hours[{{ $loop->parent->index }}][break_times][{{ $breakIndex }}][end]"
                                                                               value="{{ $breakTime['end'] ?? '' }}"
                                                                               placeholder="Break end">
                                                                    </div>
                                                                    <div class="col-2">
                                                                        <button type="button" class="btn btn-sm btn-outline-danger remove-break">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary add-break" data-day="{{ $dayKey }}" data-index="{{ $loop->index }}">
                                                        <i class="fas fa-plus mr-1"></i>
                                                        Add Break
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i>
                            Save Operating Hours
                        </button>
                        <a href="{{ route('owner.business.locations') }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-4">
            {{-- Location Info --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        Location Details
                    </h3>
                </div>
                <div class="card-body">
                    <h5>{{ $location->name }}</h5>
                    @if($location->is_main_branch)
                        <span class="badge badge-primary mb-2">Primary Location</span>
                    @endif

                    <div class="mb-2">
                        <i class="fas fa-map-marker-alt text-muted mr-2"></i>
                        <span>{{ $location->full_address }}</span>
                    </div>

                    @if($location->phone)
                        <div class="mb-2">
                            <i class="fas fa-phone text-muted mr-2"></i>
                            <span>{{ $location->phone }}</span>
                        </div>
                    @endif

                    @if($location->email)
                        <div class="mb-2">
                            <i class="fas fa-envelope text-muted mr-2"></i>
                            <span>{{ $location->email }}</span>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <button type="button" class="list-group-item list-group-item-action" onclick="copyFromTemplate('standard')">
                            <i class="fas fa-business-time text-primary mr-2"></i>
                            Set Standard Hours (9 AM - 5 PM)
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="copyFromTemplate('extended')">
                            <i class="fas fa-clock text-info mr-2"></i>
                            Set Extended Hours (8 AM - 8 PM)
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="copyFromTemplate('weekend')">
                            <i class="fas fa-calendar-weekend text-warning mr-2"></i>
                            Set Weekend Only
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="clearAllHours()">
                            <i class="fas fa-times-circle text-danger mr-2"></i>
                            Clear All Hours
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
        $(document).ready(function() {
            // Toggle time inputs when day is opened/closed
            $('.day-toggle').on('change', function() {
                const day = $(this).data('day');
                const timeInputs = $('#time_inputs_' + day);

                if ($(this).is(':checked')) {
                    timeInputs.show();
                } else {
                    timeInputs.hide();
                    // Clear time values when closing
                    timeInputs.find('input[type="time"]').val('');
                }
            });

            // Add break time functionality
            $('.add-break').on('click', function() {
                const day = $(this).data('day');
                const index = $(this).data('index');
                const container = $('#break_times_' + day);
                const breakCount = container.find('.break-time-row').length;

                const breakHtml = `
                    <div class="row mb-2 break-time-row">
                        <div class="col-5">
                            <input type="time" class="form-control form-control-sm"
                                   name="hours[${index}][break_times][${breakCount}][start]"
                                   placeholder="Break start">
                        </div>
                        <div class="col-5">
                            <input type="time" class="form-control form-control-sm"
                                   name="hours[${index}][break_times][${breakCount}][end]"
                                   placeholder="Break end">
                        </div>
                        <div class="col-2">
                            <button type="button" class="btn btn-sm btn-outline-danger remove-break">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;

                container.append(breakHtml);
            });

            // Remove break time functionality
            $(document).on('click', '.remove-break', function() {
                $(this).closest('.break-time-row').remove();
            });

            // Form validation
            $('form').on('submit', function(e) {
                let hasError = false;

                $('.day-toggle:checked').each(function() {
                    const day = $(this).data('day');
                    const openTime = $(`#open_time_${day}`).val();
                    const closeTime = $(`#close_time_${day}`).val();

                    if (!openTime || !closeTime) {
                        alert(`Please set both open and close times for ${day.charAt(0).toUpperCase() + day.slice(1)}`);
                        hasError = true;
                        return false;
                    }

                    if (openTime >= closeTime) {
                        alert(`Close time must be after open time for ${day.charAt(0).toUpperCase() + day.slice(1)}`);
                        hasError = true;
                        return false;
                    }
                });

                if (hasError) {
                    e.preventDefault();
                }
            });
        });

        // Template functions
        function copyFromTemplate(template) {
            const templates = {
                'standard': {
                    'monday': { open: '09:00', close: '17:00' },
                    'tuesday': { open: '09:00', close: '17:00' },
                    'wednesday': { open: '09:00', close: '17:00' },
                    'thursday': { open: '09:00', close: '17:00' },
                    'friday': { open: '09:00', close: '17:00' },
                    'saturday': { open: '', close: '' },
                    'sunday': { open: '', close: '' }
                },
                'extended': {
                    'monday': { open: '08:00', close: '20:00' },
                    'tuesday': { open: '08:00', close: '20:00' },
                    'wednesday': { open: '08:00', close: '20:00' },
                    'thursday': { open: '08:00', close: '20:00' },
                    'friday': { open: '08:00', close: '20:00' },
                    'saturday': { open: '10:00', close: '18:00' },
                    'sunday': { open: '', close: '' }
                },
                'weekend': {
                    'monday': { open: '', close: '' },
                    'tuesday': { open: '', close: '' },
                    'wednesday': { open: '', close: '' },
                    'thursday': { open: '', close: '' },
                    'friday': { open: '', close: '' },
                    'saturday': { open: '10:00', close: '18:00' },
                    'sunday': { open: '10:00', close: '18:00' }
                }
            };

            if (confirm(`Apply ${template} hours template? This will overwrite current settings.`)) {
                const templateData = templates[template];

                Object.keys(templateData).forEach(day => {
                    const dayData = templateData[day];
                    const checkbox = $(`#is_open_${day}`);
                    const openTime = $(`#open_time_${day}`);
                    const closeTime = $(`#close_time_${day}`);

                    if (dayData.open && dayData.close) {
                        checkbox.prop('checked', true);
                        openTime.val(dayData.open);
                        closeTime.val(dayData.close);
                        $(`#time_inputs_${day}`).show();
                    } else {
                        checkbox.prop('checked', false);
                        openTime.val('');
                        closeTime.val('');
                        $(`#time_inputs_${day}`).hide();
                    }

                    // Clear break times
                    $(`#break_times_${day}`).empty();
                });
            }
        }

        function clearAllHours() {
            if (confirm('Clear all operating hours? This will set all days to closed.')) {
                $('.day-toggle').prop('checked', false);
                $('input[type="time"]').val('');
                $('.time-inputs').hide();
                $('.break-times-container').empty();
            }
        }
    </script>
@stop