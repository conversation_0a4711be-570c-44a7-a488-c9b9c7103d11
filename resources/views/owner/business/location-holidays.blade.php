@extends('owner.layouts.app')

@section('title', 'Holidays & Closures - ' . $location->name)

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Holidays & Closures</h1>
            <p class="text-muted mb-0">Manage holidays and closures for {{ $location->name }}</p>
        </div>
        <div>
            <a href="{{ route('owner.business.locations') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Locations
            </a>
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addHolidayModal">
                <i class="fas fa-plus mr-1"></i>
                Add Holiday
            </button>
        </div>
    </div>
@stop

@section('content')
    {{-- Success/Error Messages --}}
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('info'))
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle mr-2"></i>
            {{ session('info') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle mr-2"></i>
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-times text-danger mr-2"></i>
                        Scheduled Holidays & Closures
                    </h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <form method="POST" action="{{ route('owner.business.locations.copy-business-holidays', $location) }}" style="display: inline;">
                                @csrf
                                <button type="submit" class="btn btn-sm btn-outline-primary" onclick="return confirm('Copy all business holidays to this location? Existing holidays will not be duplicated.')">
                                    <i class="fas fa-copy mr-1"></i>
                                    Copy Business Holidays
                                </button>
                            </form>
                            <button type="button" class="btn btn-sm btn-outline-secondary" data-toggle="modal" data-target="#importHolidaysModal">
                                <i class="fas fa-download mr-1"></i>
                                Import Common Holidays
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($holidays->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Holiday Name</th>
                                        <th>Date(s)</th>
                                        <th>Type</th>
                                        <th>Recurring</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($holidays as $holiday)
                                        <tr>
                                            <td>
                                                <strong>{{ $holiday->name }}</strong>
                                                @if($holiday->description)
                                                    <br><small class="text-muted">{{ $holiday->description }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($holiday->start_date->format('Y-m-d') !== $holiday->end_date->format('Y-m-d'))
                                                    {{ $holiday->start_date->format('M j, Y') }} - {{ $holiday->end_date->format('M j, Y') }}
                                                    <br><small class="text-muted">{{ $holiday->start_date->diffInDays($holiday->end_date) + 1 }} days</small>
                                                @else
                                                    {{ $holiday->start_date->format('M j, Y') }}
                                                    <br><small class="text-muted">{{ $holiday->start_date->format('l') }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($holiday->start_date->format('Y-m-d') !== $holiday->end_date->format('Y-m-d'))
                                                    <span class="badge badge-warning">Multi-day</span>
                                                @else
                                                    <span class="badge badge-info">Single day</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($holiday->is_recurring)
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-redo mr-1"></i>
                                                        {{ ucfirst($holiday->recurrence_type) }}
                                                    </span>
                                                @else
                                                    <span class="badge badge-secondary">One-time</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" onclick="editHoliday({{ $holiday->id }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteHoliday({{ $holiday->id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No holidays scheduled</h5>
                            <p class="text-muted">Add holidays and closures specific to this location.</p>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addHolidayModal">
                                <i class="fas fa-plus mr-1"></i>
                                Add Your First Holiday
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {{-- Location Info --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        Location Details
                    </h3>
                </div>
                <div class="card-body">
                    <h5>{{ $location->name }}</h5>
                    @if($location->is_main_branch)
                        <span class="badge badge-primary mb-2">Primary Location</span>
                    @endif

                    <div class="mb-2">
                        <i class="fas fa-map-marker-alt text-muted mr-2"></i>
                        <span>{{ $location->full_address }}</span>
                    </div>

                    @if($location->phone)
                        <div class="mb-2">
                            <i class="fas fa-phone text-muted mr-2"></i>
                            <span>{{ $location->phone }}</span>
                        </div>
                    @endif

                    @if($location->email)
                        <div class="mb-2">
                            <i class="fas fa-envelope text-muted mr-2"></i>
                            <span>{{ $location->email }}</span>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Holiday Statistics --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Holiday Statistics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $holidays->count() }}</h4>
                                <p class="text-muted mb-0">Total Holidays</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $holidays->where('is_recurring', true)->count() }}</h4>
                            <p class="text-muted mb-0">Recurring</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-info">{{ $holidays->where('start_date', '>=', now())->count() }}</h4>
                            <p class="text-muted mb-0">Upcoming</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ $holidays->where('start_date', '<', now())->count() }}</h4>
                            <p class="text-muted mb-0">Past</p>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('owner.business.locations.operating-hours', $location) }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-clock text-primary mr-2"></i>
                            Manage Operating Hours
                        </a>
                        <button type="button" class="list-group-item list-group-item-action" onclick="addChristmasWeek()">
                            <i class="fas fa-tree text-success mr-2"></i>
                            Add Christmas Week Closure
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="addSummerClosure()">
                            <i class="fas fa-sun text-warning mr-2"></i>
                            Add Summer Closure
                        </button>
                        <button type="button" class="list-group-item list-group-item-action" onclick="clearPastHolidays()">
                            <i class="fas fa-broom text-danger mr-2"></i>
                            Clear Past Holidays
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Add Holiday Modal --}}
    <div class="modal fade" id="addHolidayModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Holiday</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST" action="{{ route('owner.business.locations.holidays.store', $location) }}">
                    @csrf
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="holiday_name">Holiday Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="holiday_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="holiday_start_date">Start Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="holiday_start_date" name="start_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="holiday_end_date">End Date (Optional)</label>
                                    <input type="date" class="form-control" id="holiday_end_date" name="end_date">
                                    <small class="form-text text-muted">Leave empty for single-day holiday</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="holiday_is_recurring" name="is_recurring" value="1">
                                        <label class="form-check-label" for="holiday_is_recurring">
                                            Recurring Holiday
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="recurrence_type_group" style="display: none;">
                            <label for="holiday_recurrence_type">Recurrence Type</label>
                            <select class="form-control" id="holiday_recurrence_type" name="recurrence_type">
                                <option value="">Select recurrence type</option>
                                <option value="yearly">Yearly</option>
                                <option value="monthly">Monthly</option>
                                <option value="weekly">Weekly</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="holiday_description">Description (Optional)</label>
                            <textarea class="form-control" id="holiday_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Holiday</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Edit Holiday Modal --}}
    <div class="modal fade" id="editHolidayModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Holiday</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="editHolidayForm" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_holiday_name">Holiday Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="edit_holiday_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_holiday_start_date">Start Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="edit_holiday_start_date" name="start_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_holiday_end_date">End Date (Optional)</label>
                                    <input type="date" class="form-control" id="edit_holiday_end_date" name="end_date">
                                    <small class="form-text text-muted">Leave empty for single-day holiday</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="edit_holiday_is_recurring" name="is_recurring" value="1">
                                        <label class="form-check-label" for="edit_holiday_is_recurring">
                                            Recurring Holiday
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="edit_recurrence_type_group" style="display: none;">
                            <label for="edit_holiday_recurrence_type">Recurrence Type</label>
                            <select class="form-control" id="edit_holiday_recurrence_type" name="recurrence_type">
                                <option value="">Select recurrence type</option>
                                <option value="yearly">Yearly</option>
                                <option value="monthly">Monthly</option>
                                <option value="weekly">Weekly</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_holiday_description">Description (Optional)</label>
                            <textarea class="form-control" id="edit_holiday_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Holiday</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Import Holidays Modal --}}
    <div class="modal fade" id="importHolidaysModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Import Common Holidays</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST" action="{{ route('owner.business.locations.import-common-holidays', $location) }}">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="import_year">Year</label>
                            <select class="form-control" id="import_year" name="year">
                                @for($year = date('Y'); $year <= date('Y') + 2; $year++)
                                    <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>{{ $year }}</option>
                                @endfor
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="import_country">Country</label>
                            <select class="form-control" id="import_country" name="country">
                                <option value="US">United States</option>
                                <option value="CA">Canada</option>
                                <option value="UK">United Kingdom</option>
                                <option value="AU">Australia</option>
                                <option value="OTHER">Other/Generic</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            This will import common holidays for the selected country and year. Existing holidays will not be duplicated.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Import Holidays</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
        // Store holiday data for editing
        const holidayData = @json($holidays);

        $(document).ready(function() {
            // Toggle recurrence type visibility
            $('#holiday_is_recurring, #edit_holiday_is_recurring').on('change', function() {
                const isEdit = $(this).attr('id').includes('edit_');
                const targetGroup = isEdit ? '#edit_recurrence_type_group' : '#recurrence_type_group';
                const targetSelect = isEdit ? '#edit_holiday_recurrence_type' : '#holiday_recurrence_type';

                if ($(this).is(':checked')) {
                    $(targetGroup).show();
                    $(targetSelect).prop('required', true);
                } else {
                    $(targetGroup).hide();
                    $(targetSelect).prop('required', false).val('');
                }
            });

            // Set minimum date to today for new holidays
            const today = new Date().toISOString().split('T')[0];
            $('#holiday_start_date').attr('min', today);

            // Update end date minimum when start date changes
            $('#holiday_start_date, #edit_holiday_start_date').on('change', function() {
                const isEdit = $(this).attr('id').includes('edit_');
                const endDateField = isEdit ? '#edit_holiday_end_date' : '#holiday_end_date';
                $(endDateField).attr('min', $(this).val());
            });

            // Form validation
            $('form').on('submit', function(e) {
                const form = $(this);
                const startDate = form.find('input[name="start_date"]').val();
                const endDate = form.find('input[name="end_date"]').val();
                const isRecurring = form.find('input[name="is_recurring"]').is(':checked');
                const recurrenceType = form.find('select[name="recurrence_type"]').val();

                if (endDate && endDate < startDate) {
                    e.preventDefault();
                    alert('End date must be on or after the start date.');
                    return false;
                }

                if (isRecurring && !recurrenceType) {
                    e.preventDefault();
                    alert('Please select a recurrence type for recurring holidays.');
                    return false;
                }

                // Disable submit button to prevent double submission
                form.find('button[type="submit"]').prop('disabled', true).text('Processing...');
            });

            // Reset forms when modals are hidden
            $('#addHolidayModal').on('hidden.bs.modal', function() {
                $('#addHolidayModal form')[0].reset();
                $('#recurrence_type_group').hide();
                $('#addHolidayModal button[type="submit"]').prop('disabled', false).text('Add Holiday');
            });

            $('#editHolidayModal').on('hidden.bs.modal', function() {
                $('#editHolidayForm')[0].reset();
                $('#edit_recurrence_type_group').hide();
                $('#editHolidayModal button[type="submit"]').prop('disabled', false).text('Update Holiday');
            });
        });

        function editHoliday(id) {
            // Find the holiday data
            const holiday = holidayData.find(h => h.id === id);
            if (!holiday) {
                alert('Holiday not found');
                return;
            }

            // Populate the edit form
            $('#edit_holiday_name').val(holiday.name);
            $('#edit_holiday_start_date').val(holiday.start_date);
            $('#edit_holiday_end_date').val(holiday.end_date !== holiday.start_date ? holiday.end_date : '');
            $('#edit_holiday_description').val(holiday.description || '');

            // Handle recurring checkbox and type
            if (holiday.is_recurring) {
                $('#edit_holiday_is_recurring').prop('checked', true);
                $('#edit_recurrence_type_group').show();
                $('#edit_holiday_recurrence_type').val(holiday.recurrence_type).prop('required', true);
            } else {
                $('#edit_holiday_is_recurring').prop('checked', false);
                $('#edit_recurrence_type_group').hide();
                $('#edit_holiday_recurrence_type').prop('required', false);
            }

            // Set the form action
            $('#editHolidayForm').attr('action', `{{ route('owner.business.locations.holidays.update', [$location, '']) }}/${id}`);

            // Show the modal
            $('#editHolidayModal').modal('show');
        }

        function deleteHoliday(id) {
            const holiday = holidayData.find(h => h.id === id);
            const holidayName = holiday ? holiday.name : 'this holiday';

            if (confirm(`Are you sure you want to delete "${holidayName}"? This action cannot be undone.`)) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `{{ route('owner.business.locations.holidays.destroy', [$location, '']) }}/${id}`;

                // Add CSRF token
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                // Add method override for DELETE
                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';
                form.appendChild(methodField);

                // Submit the form
                document.body.appendChild(form);
                form.submit();
            }
        }

        function addChristmasWeek() {
            const currentYear = new Date().getFullYear();

            // Pre-fill the form with Christmas week data
            $('#holiday_name').val('Christmas Week Closure');
            $('#holiday_start_date').val(`${currentYear}-12-23`);
            $('#holiday_end_date').val(`${currentYear}-12-31`);
            $('#holiday_description').val('Annual Christmas and New Year closure');
            $('#holiday_is_recurring').prop('checked', true);
            $('#recurrence_type_group').show();
            $('#holiday_recurrence_type').val('yearly').prop('required', true);

            // Show the modal
            $('#addHolidayModal').modal('show');
        }

        function addSummerClosure() {
            const currentYear = new Date().getFullYear();

            // Pre-fill the form with summer closure data
            $('#holiday_name').val('Summer Closure');
            $('#holiday_start_date').val(`${currentYear}-08-01`);
            $('#holiday_end_date').val(`${currentYear}-08-15`);
            $('#holiday_description').val('Annual summer vacation closure');
            $('#holiday_is_recurring').prop('checked', true);
            $('#recurrence_type_group').show();
            $('#holiday_recurrence_type').val('yearly').prop('required', true);

            // Show the modal
            $('#addHolidayModal').modal('show');
        }

        function clearPastHolidays() {
            const pastHolidays = holidayData.filter(h => new Date(h.start_date) < new Date());

            if (pastHolidays.length === 0) {
                alert('No past holidays to clear.');
                return;
            }

            if (confirm(`This will delete ${pastHolidays.length} past holiday(s). This action cannot be undone. Continue?`)) {
                // Delete each past holiday
                pastHolidays.forEach(holiday => {
                    deleteHoliday(holiday.id);
                });
            }
        }
    </script>
@stop