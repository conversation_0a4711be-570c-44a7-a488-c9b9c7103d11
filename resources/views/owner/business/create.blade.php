@extends('owner.layouts.app')

@section('title', 'Create Your Business')

@section('content_header')
    <h1>Create Your Business</h1>
    <p class="text-muted">Set up your business and create your personalized landing page.</p>
@stop

@section('content')
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <!-- Progress Steps -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="progress-steps">
                                        <div class="step active" data-step="1">
                                            <div class="step-number">1</div>
                                            <div class="step-title">Business Info</div>
                                        </div>
                                        <div class="step" data-step="2">
                                            <div class="step-number">2</div>
                                            <div class="step-title">Landing Page</div>
                                        </div>
                                        <div class="step" data-step="3">
                                            <div class="step-number">3</div>
                                            <div class="step-title">Service Display</div>
                                        </div>
                                        <div class="step" data-step="4">
                                            <div class="step-number">4</div>
                                            <div class="step-title">SEO Settings</div>
                                        </div>
                                        <div class="step" data-step="5">
                                            <div class="step-number">5</div>
                                            <div class="step-title">Review</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="businessCreationForm" action="{{ route('owner.business.store') }}" method="POST">
                        @csrf

                        <!-- Step 1: Business Information -->
                        <div class="card card-primary step-content" id="step-1">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-building mr-2"></i>
                                    Business Information
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">Business Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                   id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="category_id">Business Category <span class="text-danger">*</span></label>
                                            <select class="form-control @error('category_id') is-invalid @enderror"
                                                    id="category_id" name="category_id" required>
                                                <option value="">Select a category</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('category_id')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="description">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description" name="description" rows="3">{{ old('description') }}</textarea>
                                    @error('description')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone">Phone</label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                                   id="phone" name="phone" value="{{ old('phone') }}">
                                            @error('phone')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email">Email</label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                                   id="email" name="email" value="{{ old('email') }}">
                                            @error('email')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="website">Website</label>
                                    <input type="url" class="form-control @error('website') is-invalid @enderror"
                                           id="website" name="website" value="{{ old('website') }}">
                                    @error('website')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="timezone">Timezone <span class="text-danger">*</span></label>
                                            <select class="form-control @error('timezone') is-invalid @enderror"
                                                    id="timezone" name="timezone" required>
                                                <option value="America/New_York" {{ old('timezone') == 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                                <option value="America/Chicago" {{ old('timezone') == 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                                <option value="America/Denver" {{ old('timezone') == 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                                <option value="America/Los_Angeles" {{ old('timezone') == 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                            </select>
                                            @error('timezone')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="currency">Currency <span class="text-danger">*</span></label>
                                            <select class="form-control @error('currency') is-invalid @enderror"
                                                    id="currency" name="currency" required>
                                                <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>USD ($)</option>
                                                <option value="EUR" {{ old('currency') == 'EUR' ? 'selected' : '' }}>EUR (€)</option>
                                                <option value="GBP" {{ old('currency') == 'GBP' ? 'selected' : '' }}>GBP (£)</option>
                                                <option value="CAD" {{ old('currency') == 'CAD' ? 'selected' : '' }}>CAD ($)</option>
                                            </select>
                                            @error('currency')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="language">Language <span class="text-danger">*</span></label>
                                            <select class="form-control @error('language') is-invalid @enderror"
                                                    id="language" name="language" required>
                                                <option value="en" {{ old('language') == 'en' ? 'selected' : '' }}>English</option>
                                                <option value="es" {{ old('language') == 'es' ? 'selected' : '' }}>Spanish</option>
                                                <option value="fr" {{ old('language') == 'fr' ? 'selected' : '' }}>French</option>
                                                <option value="de" {{ old('language') == 'de' ? 'selected' : '' }}>German</option>
                                            </select>
                                            @error('language')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="button" class="btn btn-primary next-step" data-next="2">
                                    Next: Landing Page <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                                <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>

                        <!-- Step 2: Landing Page Configuration -->
                        <div class="card card-info step-content" id="step-2" style="display: none;">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-globe mr-2"></i>
                                    Landing Page Setup
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle mr-2"></i>
                                            Create a professional landing page for your business that customers can visit to learn about your services and book appointments.
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="landing_page_slug">Custom URL Slug <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">bookkei.com/</span>
                                                </div>
                                                <input type="text" class="form-control @error('landing_page_slug') is-invalid @enderror"
                                                       id="landing_page_slug" name="landing_page_slug" value="{{ old('landing_page_slug') }}"
                                                       placeholder="your-business-name" required>
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-outline-secondary" id="check-slug">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">
                                                Only lowercase letters, numbers, and hyphens allowed. Minimum 3 characters.
                                            </small>
                                            <div id="slug-feedback" class="mt-1"></div>
                                            @error('landing_page_slug')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="domain_type">URL Type <span class="text-danger">*</span></label>
                                            <select class="form-control @error('domain_type') is-invalid @enderror"
                                                    id="domain_type" name="domain_type" required>
                                                <option value="subdirectory" {{ old('domain_type') == 'subdirectory' ? 'selected' : '' }}>
                                                    Subdirectory (bookkei.com/your-business) - Free
                                                </option>
                                                <option value="subdomain" {{ old('domain_type') == 'subdomain' ? 'selected' : '' }}>
                                                    Subdomain (your-business.bookkei.com) - Premium
                                                </option>
                                                <option value="custom" {{ old('domain_type') == 'custom' ? 'selected' : '' }}>
                                                    Custom Domain (www.yourbusiness.com) - Enterprise
                                                </option>
                                            </select>
                                            @error('domain_type')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row" id="custom-domain-row" style="display: none;">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="custom_domain">Custom Domain</label>
                                            <input type="text" class="form-control @error('custom_domain') is-invalid @enderror"
                                                   id="custom_domain" name="custom_domain" value="{{ old('custom_domain') }}"
                                                   placeholder="www.yourbusiness.com">
                                            <small class="form-text text-muted">
                                                Enter your custom domain. You'll need to configure DNS settings after creation.
                                            </small>
                                            @error('custom_domain')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="page_title">Landing Page Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('page_title') is-invalid @enderror"
                                                   id="page_title" name="page_title" value="{{ old('page_title') }}" required>
                                            <small class="form-text text-muted">
                                                This will be displayed as the main heading on your landing page.
                                            </small>
                                            @error('page_title')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="landing_page_theme">Theme <span class="text-danger">*</span></label>
                                            <select class="form-control @error('landing_page_theme') is-invalid @enderror"
                                                    id="landing_page_theme" name="landing_page_theme" required>
                                                <option value="default" {{ old('landing_page_theme') == 'default' ? 'selected' : '' }}>Default - Clean & Professional</option>
                                                <option value="modern" {{ old('landing_page_theme') == 'modern' ? 'selected' : '' }}>Modern - Contemporary & Sleek</option>
                                                <option value="elegant" {{ old('landing_page_theme') == 'elegant' ? 'selected' : '' }}>Elegant - Sophisticated & Refined</option>
                                                <option value="minimal" {{ old('landing_page_theme') == 'minimal' ? 'selected' : '' }}>Minimal - Simple & Clean</option>
                                                <option value="creative" {{ old('landing_page_theme') == 'creative' ? 'selected' : '' }}>Creative - Bold & Artistic</option>
                                            </select>
                                            @error('landing_page_theme')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="page_description">Landing Page Description</label>
                                    <textarea class="form-control @error('page_description') is-invalid @enderror"
                                              id="page_description" name="page_description" rows="3">{{ old('page_description') }}</textarea>
                                    <small class="form-text text-muted">
                                        A brief description that will appear below your main heading.
                                    </small>
                                    @error('page_description')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="booking_enabled" name="booking_enabled" value="1" {{ old('booking_enabled', true) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="booking_enabled">
                                            Enable Online Booking
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        Allow customers to book appointments directly from your landing page.
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="button" class="btn btn-secondary prev-step" data-prev="1">
                                    <i class="fas fa-arrow-left mr-1"></i> Previous
                                </button>
                                <button type="button" class="btn btn-info next-step" data-next="3">
                                    Next: Service Display <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Service Display -->
                        <div class="card card-warning step-content" id="step-3" style="display: none;">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-cogs mr-2"></i>
                                    Service Display Configuration
                                </h3>
                            </div>
                            <div class="card-body">
                                                 <div class="row">
                     <div class="col-md-12">
                         <div class="alert alert-warning">
                             <i class="fas fa-lightbulb mr-2"></i>
                             Configure how services are displayed on your landing page. We'll automatically create sample services based on your business category and set up advanced service features.
                         </div>
                     </div>
                 </div>

                 <!-- Service Display Layout Configuration -->
                 <div class="row">
                     <div class="col-md-6">
                         <div class="form-group">
                             <label for="service_display_type">Service Display Layout <span class="text-danger">*</span></label>
                             <select class="form-control @error('service_display_type') is-invalid @enderror"
                                     id="service_display_type" name="service_display_type" required>
                                 <option value="grid" {{ old('service_display_type', 'grid') == 'grid' ? 'selected' : '' }}>Grid Layout - Cards in rows</option>
                                 <option value="list" {{ old('service_display_type') == 'list' ? 'selected' : '' }}>List Layout - Detailed rows</option>
                                 <option value="carousel" {{ old('service_display_type') == 'carousel' ? 'selected' : '' }}>Carousel - Sliding display</option>
                                 <option value="masonry" {{ old('service_display_type') == 'masonry' ? 'selected' : '' }}>Masonry - Pinterest style</option>
                                 <option value="featured" {{ old('service_display_type') == 'featured' ? 'selected' : '' }}>Featured Services - Highlight top services</option>
                             </select>
                             @error('service_display_type')
                                 <span class="invalid-feedback">{{ $message }}</span>
                             @enderror
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <label for="service_count">Services on Homepage <span class="text-danger">*</span></label>
                             <input type="number" class="form-control @error('service_count') is-invalid @enderror"
                                    id="service_count" name="service_count" value="{{ old('service_count', 6) }}" min="3" max="20" required>
                             <small class="form-text text-muted">Number of services to display on your landing page homepage</small>
                             @error('service_count')
                                 <span class="invalid-feedback">{{ $message }}</span>
                             @enderror
                         </div>
                     </div>
                 </div>

                 <!-- Grid Configuration -->
                 <div class="row" id="grid-config" style="display: none;">
                     <div class="col-md-6">
                         <div class="form-group">
                             <label for="grid_columns">Grid Columns</label>
                             <select class="form-control" id="grid_columns" name="grid_columns">
                                 <option value="2" {{ old('grid_columns') == '2' ? 'selected' : '' }}>2 Columns</option>
                                 <option value="3" {{ old('grid_columns', '3') == '3' ? 'selected' : '' }}>3 Columns</option>
                                 <option value="4" {{ old('grid_columns') == '4' ? 'selected' : '' }}>4 Columns</option>
                             </select>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <label for="service_card_style">Card Style</label>
                             <select class="form-control" id="service_card_style" name="service_card_style">
                                 <option value="modern" {{ old('service_card_style', 'modern') == 'modern' ? 'selected' : '' }}>Modern - Clean shadows</option>
                                 <option value="classic" {{ old('service_card_style') == 'classic' ? 'selected' : '' }}>Classic - Traditional borders</option>
                                 <option value="minimal" {{ old('service_card_style') == 'minimal' ? 'selected' : '' }}>Minimal - Simple design</option>
                                 <option value="creative" {{ old('service_card_style') == 'creative' ? 'selected' : '' }}>Creative - Artistic style</option>
                             </select>
                         </div>
                     </div>
                 </div>

                 <div class="form-group">
                     <label for="service_display_theme">Service Display Style <span class="text-danger">*</span></label>
                     <select class="form-control @error('service_display_theme') is-invalid @enderror"
                             id="service_display_theme" name="service_display_theme" required>
                         <option value="modern" {{ old('service_display_theme', 'modern') == 'modern' ? 'selected' : '' }}>Modern - Clean and contemporary</option>
                         <option value="default" {{ old('service_display_theme') == 'default' ? 'selected' : '' }}>Default - Professional and classic</option>
                         <option value="elegant" {{ old('service_display_theme') == 'elegant' ? 'selected' : '' }}>Elegant - Sophisticated and refined</option>
                         <option value="minimal" {{ old('service_display_theme') == 'minimal' ? 'selected' : '' }}>Minimal - Simple and clean</option>
                         <option value="creative" {{ old('service_display_theme') == 'creative' ? 'selected' : '' }}>Creative - Bold and artistic</option>
                     </select>
                     @error('service_display_theme')
                         <span class="invalid-feedback">{{ $message }}</span>
                     @enderror
                 </div>

                 <!-- Service Information Display -->
                 <div class="row">
                     <div class="col-md-12">
                         <h5><i class="fas fa-info-circle mr-2"></i>Service Information Display</h5>
                         <p class="text-muted">Choose what information to show for each service on your landing page</p>
                     </div>
                 </div>

                 <div class="row">
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="show_service_pricing" name="show_service_pricing" value="1" {{ old('show_service_pricing', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="show_service_pricing">
                                     Show Service Pricing
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="show_service_duration" name="show_service_duration" value="1" {{ old('show_service_duration', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="show_service_duration">
                                     Show Service Duration
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="show_service_descriptions" name="show_service_descriptions" value="1" {{ old('show_service_descriptions', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="show_service_descriptions">
                                     Show Service Descriptions
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="show_service_images" name="show_service_images" value="1" {{ old('show_service_images', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="show_service_images">
                                     Show Service Images
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="show_service_categories" name="show_service_categories" value="1" {{ old('show_service_categories', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="show_service_categories">
                                     Show Service Categories
                                 </label>
                             </div>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_service_search" name="enable_service_search" value="1" {{ old('enable_service_search', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_service_search">
                                     Enable Service Search
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_service_filtering" name="enable_service_filtering" value="1" {{ old('enable_service_filtering', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_service_filtering">
                                     Enable Service Filtering
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="quick_booking_enabled" name="quick_booking_enabled" value="1" {{ old('quick_booking_enabled', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="quick_booking_enabled">
                                     Enable Quick Booking
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="show_availability_status" name="show_availability_status" value="1" {{ old('show_availability_status', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="show_availability_status">
                                     Show Availability Status
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="show_reviews_rating" name="show_reviews_rating" value="1" {{ old('show_reviews_rating', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="show_reviews_rating">
                                     Show Reviews & Ratings
                                 </label>
                             </div>
                         </div>
                     </div>
                 </div>

                 <!-- Advanced Service Features -->
                 <div class="row mt-4">
                     <div class="col-md-12">
                         <h5><i class="fas fa-rocket mr-2"></i>Advanced Service Features</h5>
                         <p class="text-muted">Enable advanced features for better customer experience and SEO</p>
                     </div>
                 </div>

                 <div class="row">
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_service_seo" name="enable_service_seo" value="1" {{ old('enable_service_seo', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_service_seo">
                                     Enable Service SEO
                                 </label>
                             </div>
                             <small class="form-text text-muted">Individual SEO pages for each service</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="generate_service_sitemap" name="generate_service_sitemap" value="1" {{ old('generate_service_sitemap', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="generate_service_sitemap">
                                     Generate Service Sitemap
                                 </label>
                             </div>
                             <small class="form-text text-muted">Automatic sitemap for search engines</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="group_by_category" name="group_by_category" value="1" {{ old('group_by_category', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="group_by_category">
                                     Group Services by Category
                                 </label>
                             </div>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="track_service_analytics" name="track_service_analytics" value="1" {{ old('track_service_analytics', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="track_service_analytics">
                                     Track Service Analytics
                                 </label>
                             </div>
                             <small class="form-text text-muted">Monitor service performance and popularity</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="mobile_optimized" name="mobile_optimized" value="1" {{ old('mobile_optimized', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="mobile_optimized">
                                     Mobile Optimized Display
                                 </label>
                             </div>
                             <small class="form-text text-muted">Responsive design for mobile devices</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="show_booking_calendar" name="show_booking_calendar" value="1" {{ old('show_booking_calendar', false) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="show_booking_calendar">
                                     Show Booking Calendar
                                 </label>
                             </div>
                             <small class="form-text text-muted">Display availability calendar on service pages</small>
                         </div>
                     </div>
                 </div>

                 <!-- Booking Configuration -->
                 <div class="row mt-4">
                     <div class="col-md-12">
                         <h5><i class="fas fa-calendar-check mr-2"></i>Booking Configuration</h5>
                         <p class="text-muted">Configure how booking works on your landing page</p>
                     </div>
                 </div>

                 <div class="row">
                     <div class="col-md-6">
                         <div class="form-group">
                             <label for="booking_button_text">Booking Button Text</label>
                             <input type="text" class="form-control" id="booking_button_text" name="booking_button_text"
                                    value="{{ old('booking_button_text', 'Book Now') }}" maxlength="50">
                             <small class="form-text text-muted">Text displayed on booking buttons</small>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <label for="booking_button_style">Booking Button Style</label>
                             <select class="form-control" id="booking_button_style" name="booking_button_style">
                                 <option value="primary" {{ old('booking_button_style', 'primary') == 'primary' ? 'selected' : '' }}>Primary - Blue</option>
                                 <option value="secondary" {{ old('booking_button_style') == 'secondary' ? 'selected' : '' }}>Secondary - Gray</option>
                                 <option value="success" {{ old('booking_button_style') == 'success' ? 'selected' : '' }}>Success - Green</option>
                                 <option value="warning" {{ old('booking_button_style') == 'warning' ? 'selected' : '' }}>Warning - Orange</option>
                                 <option value="outline" {{ old('booking_button_style') == 'outline' ? 'selected' : '' }}>Outline - Transparent</option>
                             </select>
                         </div>
                     </div>
                 </div>

                 <!-- Advanced Service Management -->
                 <div class="row mt-4">
                     <div class="col-md-12">
                         <h5><i class="fas fa-cogs mr-2"></i>Advanced Service Management</h5>
                         <p class="text-muted">Configure advanced service features for your landing page</p>
                     </div>
                 </div>

                 <div class="row">
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_service_packages" name="enable_service_packages" value="1" {{ old('enable_service_packages', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_service_packages">
                                     Enable Service Packages
                                 </label>
                             </div>
                             <small class="form-text text-muted">Allow bundled services with package pricing</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_add_on_services" name="enable_add_on_services" value="1" {{ old('enable_add_on_services', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_add_on_services">
                                     Enable Add-on Services
                                 </label>
                             </div>
                             <small class="form-text text-muted">Complementary services and upgrades</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_staff_assignments" name="enable_staff_assignments" value="1" {{ old('enable_staff_assignments', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_staff_assignments">
                                     Enable Staff Assignments
                                 </label>
                             </div>
                             <small class="form-text text-muted">Assign specific staff to services</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_seasonal_services" name="enable_seasonal_services" value="1" {{ old('enable_seasonal_services', false) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_seasonal_services">
                                     Enable Seasonal Services
                                 </label>
                             </div>
                             <small class="form-text text-muted">Time-limited or seasonal offerings</small>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_service_prerequisites" name="enable_service_prerequisites" value="1" {{ old('enable_service_prerequisites', false) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_service_prerequisites">
                                     Enable Service Prerequisites
                                 </label>
                             </div>
                             <small class="form-text text-muted">Requirements or preparations needed</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_equipment_requirements" name="enable_equipment_requirements" value="1" {{ old('enable_equipment_requirements', false) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_equipment_requirements">
                                     Enable Equipment Requirements
                                 </label>
                             </div>
                             <small class="form-text text-muted">Special equipment or facility needs</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_service_faqs" name="enable_service_faqs" value="1" {{ old('enable_service_faqs', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_service_faqs">
                                     Enable Service FAQs
                                 </label>
                             </div>
                             <small class="form-text text-muted">Common questions and answers per service</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_before_after_gallery" name="enable_before_after_gallery" value="1" {{ old('enable_before_after_gallery', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_before_after_gallery">
                                     Enable Before/After Gallery
                                 </label>
                             </div>
                             <small class="form-text text-muted">Visual service results showcase</small>
                         </div>
                     </div>
                 </div>

                 <!-- Visual Page Builder Configuration -->
                 <div class="row mt-4">
                     <div class="col-md-12">
                         <h5><i class="fas fa-paint-brush mr-2"></i>Visual Page Builder</h5>
                         <p class="text-muted">Configure the drag-and-drop visual editor for your landing page</p>
                     </div>
                 </div>

                 <div class="row">
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_visual_editor" name="enable_visual_editor" value="1" {{ old('enable_visual_editor', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_visual_editor">
                                     Enable Visual Page Builder
                                 </label>
                             </div>
                             <small class="form-text text-muted">Drag-and-drop interface for organizing sections</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_live_preview" name="enable_live_preview" value="1" {{ old('enable_live_preview', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_live_preview">
                                     Enable Live Preview
                                 </label>
                             </div>
                             <small class="form-text text-muted">Immediate visualization of changes</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_modular_sections" name="enable_modular_sections" value="1" {{ old('enable_modular_sections', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_modular_sections">
                                     Enable Modular Sections
                                 </label>
                             </div>
                             <small class="form-text text-muted">About us, services, team, testimonials, contact</small>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_wysiwyg_editor" name="enable_wysiwyg_editor" value="1" {{ old('enable_wysiwyg_editor', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_wysiwyg_editor">
                                     Enable WYSIWYG Editor
                                 </label>
                             </div>
                             <small class="form-text text-muted">Real-time content editing</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_multimedia_management" name="enable_multimedia_management" value="1" {{ old('enable_multimedia_management', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_multimedia_management">
                                     Enable Multimedia Management
                                 </label>
                             </div>
                             <small class="form-text text-muted">Image/video upload and organization</small>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="enable_pre_designed_blocks" name="enable_pre_designed_blocks" value="1" {{ old('enable_pre_designed_blocks', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="enable_pre_designed_blocks">
                                     Enable Pre-designed Blocks
                                 </label>
                             </div>
                             <small class="form-text text-muted">Ready components for different industries</small>
                         </div>
                     </div>
                 </div>

                 <!-- Default Sections Configuration -->
                 <div class="row mt-4">
                     <div class="col-md-12">
                         <h5><i class="fas fa-layer-group mr-2"></i>Default Page Sections</h5>
                         <p class="text-muted">Choose which sections to include by default on your landing page</p>
                     </div>
                 </div>

                 <div class="row">
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="include_hero_section" name="include_hero_section" value="1" {{ old('include_hero_section', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="include_hero_section">
                                     Hero Section
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="include_about_section" name="include_about_section" value="1" {{ old('include_about_section', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="include_about_section">
                                     About Us Section
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="include_services_section" name="include_services_section" value="1" {{ old('include_services_section', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="include_services_section">
                                     Services Section
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="include_team_section" name="include_team_section" value="1" {{ old('include_team_section', false) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="include_team_section">
                                     Team Section
                                 </label>
                             </div>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="include_testimonials_section" name="include_testimonials_section" value="1" {{ old('include_testimonials_section', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="include_testimonials_section">
                                     Testimonials Section
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="include_gallery_section" name="include_gallery_section" value="1" {{ old('include_gallery_section', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="include_gallery_section">
                                     Gallery Section
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="include_contact_section" name="include_contact_section" value="1" {{ old('include_contact_section', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="include_contact_section">
                                     Contact Section
                                 </label>
                             </div>
                         </div>
                         <div class="form-group">
                             <div class="custom-control custom-switch">
                                 <input type="checkbox" class="custom-control-input" id="include_booking_section" name="include_booking_section" value="1" {{ old('include_booking_section', true) ? 'checked' : '' }}>
                                 <label class="custom-control-label" for="include_booking_section">
                                     Booking Section
                                 </label>
                             </div>
                         </div>
                     </div>
                 </div>
                            </div>
                            <div class="card-footer">
                                <button type="button" class="btn btn-secondary prev-step" data-prev="2">
                                    <i class="fas fa-arrow-left mr-1"></i> Previous
                                </button>
                                <button type="button" class="btn btn-warning next-step" data-next="4">
                                    Next: SEO Settings <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 4: SEO Settings -->
                        <div class="card card-warning step-content" id="step-4" style="display: none;">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-search mr-2"></i>
                                    SEO Optimization
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="alert alert-warning">
                                            <i class="fas fa-lightbulb mr-2"></i>
                                            Optimize your landing page and services for search engines to help customers find your business online. This includes both general business SEO and service-specific optimization.
                                        </div>
                                    </div>
                                </div>

                                <!-- Basic SEO Settings -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <h5><i class="fas fa-search mr-2"></i>Basic SEO Settings</h5>
                                        <p class="text-muted">Configure the main SEO settings for your landing page</p>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="meta_title">SEO Title</label>
                                    <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                                           id="meta_title" name="meta_title" value="{{ old('meta_title') }}" maxlength="60">
                                    <small class="form-text text-muted">
                                        <span id="meta-title-count">0</span>/60 characters. This appears in search engine results.
                                    </small>
                                    @error('meta_title')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="meta_description">SEO Description</label>
                                    <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                              id="meta_description" name="meta_description" rows="3" maxlength="160">{{ old('meta_description') }}</textarea>
                                    <small class="form-text text-muted">
                                        <span id="meta-description-count">0</span>/160 characters. This appears below your title in search results.
                                    </small>
                                    @error('meta_description')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="meta_keywords">Keywords</label>
                                    <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror"
                                           id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords') }}">
                                    <small class="form-text text-muted">
                                        Separate keywords with commas (e.g., salon, haircut, beauty, spa).
                                    </small>
                                    @error('meta_keywords')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Advanced SEO Features -->
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <h5><i class="fas fa-chart-line mr-2"></i>Advanced SEO Features</h5>
                                        <p class="text-muted">Enable advanced SEO features for better search engine visibility</p>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="enable_open_graph" name="enable_open_graph" value="1" {{ old('enable_open_graph', true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="enable_open_graph">
                                                    Enable Open Graph Tags
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Better social media sharing</small>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="enable_twitter_cards" name="enable_twitter_cards" value="1" {{ old('enable_twitter_cards', true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="enable_twitter_cards">
                                                    Enable Twitter Cards
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Enhanced Twitter sharing</small>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="enable_schema_markup" name="enable_schema_markup" value="1" {{ old('enable_schema_markup', true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="enable_schema_markup">
                                                    Enable Schema Markup
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Structured data for search engines</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="enable_local_seo" name="enable_local_seo" value="1" {{ old('enable_local_seo', true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="enable_local_seo">
                                                    Enable Local SEO
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Location-based search optimization</small>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="enable_amp" name="enable_amp" value="1" {{ old('enable_amp', false) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="enable_amp">
                                                    Enable AMP Pages
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Accelerated Mobile Pages for faster loading</small>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="enable_lazy_loading" name="enable_lazy_loading" value="1" {{ old('enable_lazy_loading', true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="enable_lazy_loading">
                                                    Enable Lazy Loading
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Improve page loading speed</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Analytics Integration -->
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <h5><i class="fas fa-analytics mr-2"></i>Analytics Integration</h5>
                                        <p class="text-muted">Connect analytics tools to track your landing page performance</p>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="google_analytics_id">Google Analytics ID</label>
                                            <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id"
                                                   value="{{ old('google_analytics_id') }}" placeholder="G-XXXXXXXXXX">
                                            <small class="form-text text-muted">Your Google Analytics tracking ID</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="google_tag_manager_id">Google Tag Manager ID</label>
                                            <input type="text" class="form-control" id="google_tag_manager_id" name="google_tag_manager_id"
                                                   value="{{ old('google_tag_manager_id') }}" placeholder="GTM-XXXXXXX">
                                            <small class="form-text text-muted">Your Google Tag Manager container ID</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="facebook_pixel_id">Facebook Pixel ID</label>
                                            <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id"
                                                   value="{{ old('facebook_pixel_id') }}" placeholder="123456789012345">
                                            <small class="form-text text-muted">Your Facebook Pixel ID for ad tracking</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch mt-4">
                                                <input type="checkbox" class="custom-control-input" id="enable_conversion_tracking" name="enable_conversion_tracking" value="1" {{ old('enable_conversion_tracking', true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="enable_conversion_tracking">
                                                    Enable Conversion Tracking
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Track bookings and other conversions</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="button" class="btn btn-secondary prev-step" data-prev="3">
                                    <i class="fas fa-arrow-left mr-1"></i> Previous
                                </button>
                                <button type="button" class="btn btn-warning next-step" data-next="5">
                                    Next: Review <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 5: Review -->
                        <div class="card card-success step-content" id="step-5" style="display: none;">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-check mr-2"></i>
                                    Review & Create
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle mr-2"></i>
                                            Review your business information and landing page settings before creating your business.
                                        </div>
                                    </div>
                                </div>

                                <div id="review-content">
                                    <!-- Review content will be populated by JavaScript -->
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="terms_accepted" name="terms_accepted" required>
                                        <label class="custom-control-label" for="terms_accepted">
                                            I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="button" class="btn btn-secondary prev-step" data-prev="4">
                                    <i class="fas fa-arrow-left mr-1"></i> Previous
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-rocket mr-1"></i> Create Business & Landing Page
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
@stop

@section('css')
<style>
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 60%;
    width: 80%;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step.active:not(:last-child)::after,
.step.completed:not(:last-child)::after {
    background-color: #007bff;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.step.active .step-number {
    background-color: #007bff;
    color: white;
}

.step.completed .step-number {
    background-color: #28a745;
    color: white;
}

.step-title {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    text-align: center;
}

.step.active .step-title {
    color: #007bff;
    font-weight: 600;
}

.step.completed .step-title {
    color: #28a745;
    font-weight: 600;
}

.step-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slug-available {
    color: #28a745;
}

.slug-unavailable {
    color: #dc3545;
}

.theme-preview {
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-preview:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.theme-preview.selected {
    border-color: #007bff;
    background-color: rgba(0,123,255,0.1);
}
</style>
@endsection

@section('js')
<script>
$(document).ready(function() {
    let currentStep = 1;
    const totalSteps = 5;

    // Auto-generate slug from business name
    $('#name').on('input', function() {
        const businessName = $(this).val();
        const slug = businessName.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');

        $('#landing_page_slug').val(slug);
        $('#page_title').val(businessName);

        if (slug.length >= 3) {
            checkSlugAvailability(slug);
        }
    });

    // Auto-generate meta title and description
    $('#page_title').on('input', function() {
        const title = $(this).val();
        if (!$('#meta_title').val()) {
            $('#meta_title').val(title);
            updateCharCount('#meta_title', '#meta-title-count');
        }
    });

    $('#page_description').on('input', function() {
        const description = $(this).val();
        if (!$('#meta_description').val()) {
            $('#meta_description').val(description);
            updateCharCount('#meta_description', '#meta-description-count');
        }
    });

    // Character counters
    $('#meta_title').on('input', function() {
        updateCharCount(this, '#meta-title-count');
    });

    $('#meta_description').on('input', function() {
        updateCharCount(this, '#meta-description-count');
    });

    function updateCharCount(input, counter) {
        const length = $(input).val().length;
        $(counter).text(length);

        const maxLength = $(input).attr('maxlength');
        if (length > maxLength * 0.9) {
            $(counter).addClass('text-warning');
        } else {
            $(counter).removeClass('text-warning');
        }
    }

    // Domain type change handler
    $('#domain_type').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom-domain-row').show();
            $('#custom_domain').prop('required', true);
        } else {
            $('#custom-domain-row').hide();
            $('#custom_domain').prop('required', false);
        }
    });

    // Service display type change handler
    $('#service_display_type').on('change', function() {
        const displayType = $(this).val();
        if (displayType === 'grid' || displayType === 'masonry') {
            $('#grid-config').show();
        } else {
            $('#grid-config').hide();
        }
    });

    // Initialize grid config visibility
    if ($('#service_display_type').val() === 'grid' || $('#service_display_type').val() === 'masonry') {
        $('#grid-config').show();
    }

    // Slug checker
    $('#check-slug, #landing_page_slug').on('click input', function() {
        const slug = $('#landing_page_slug').val();
        if (slug.length >= 3) {
            checkSlugAvailability(slug);
        }
    });

    function checkSlugAvailability(slug) {
        if (slug.length < 3) {
            showSlugFeedback('Slug must be at least 3 characters long.', false);
            return;
        }

        if (!/^[a-z0-9-]+$/.test(slug)) {
            showSlugFeedback('Only lowercase letters, numbers, and hyphens allowed.', false);
            return;
        }

        // Simulate API call (replace with actual endpoint)
        $.get('/owner/landing-page/check-slug', { slug: slug })
            .done(function(response) {
                showSlugFeedback(response.message, response.available);
            })
            .fail(function() {
                showSlugFeedback('Error checking slug availability.', false);
            });
    }

    function showSlugFeedback(message, available) {
        const feedbackDiv = $('#slug-feedback');
        feedbackDiv.removeClass('slug-available slug-unavailable');
        feedbackDiv.addClass(available ? 'slug-available' : 'slug-unavailable');
        feedbackDiv.html('<i class="fas fa-' + (available ? 'check' : 'times') + ' mr-1"></i>' + message);
    }

    // Step navigation
    $('.next-step').on('click', function() {
        const nextStep = parseInt($(this).data('next'));
        if (validateStep(currentStep)) {
            goToStep(nextStep);
        }
    });

    $('.prev-step').on('click', function() {
        const prevStep = parseInt($(this).data('prev'));
        goToStep(prevStep);
    });

    function goToStep(step) {
        // Hide current step
        $('#step-' + currentStep).hide();
        $('.step[data-step="' + currentStep + '"]').removeClass('active').addClass('completed');

        // Show new step
        currentStep = step;
        $('#step-' + currentStep).show();
        $('.step[data-step="' + currentStep + '"]').addClass('active').removeClass('completed');

        // Update review content if going to step 5
        if (currentStep === 5) {
            updateReviewContent();
        }

        // Scroll to top
        $('html, body').animate({ scrollTop: 0 }, 300);
    }

    function validateStep(step) {
        let isValid = true;
        const stepElement = $('#step-' + step);

        // Clear previous validation
        stepElement.find('.is-invalid').removeClass('is-invalid');

        // Validate required fields in current step
        stepElement.find('input[required], select[required], textarea[required]').each(function() {
            if (!$(this).val() || ($(this).attr('type') === 'checkbox' && !$(this).is(':checked'))) {
                $(this).addClass('is-invalid');
                isValid = false;
            }
        });

        // Step-specific validation
        if (step === 2) {
            const slug = $('#landing_page_slug').val();
            if (slug.length < 3 || !/^[a-z0-9-]+$/.test(slug)) {
                $('#landing_page_slug').addClass('is-invalid');
                isValid = false;
            }
        }

        if (!isValid) {
            toastr.error('Please fill in all required fields correctly.');
        }

        return isValid;
    }

    function updateReviewContent() {
        const reviewHtml = `
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-building mr-2"></i>Business Information</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Name:</strong></td><td>${$('#name').val()}</td></tr>
                        <tr><td><strong>Category:</strong></td><td>${$('#category_id option:selected').text()}</td></tr>
                        <tr><td><strong>Description:</strong></td><td>${$('#description').val() || 'Not provided'}</td></tr>
                        <tr><td><strong>Phone:</strong></td><td>${$('#phone').val() || 'Not provided'}</td></tr>
                        <tr><td><strong>Email:</strong></td><td>${$('#email').val() || 'Not provided'}</td></tr>
                        <tr><td><strong>Website:</strong></td><td>${$('#website').val() || 'Not provided'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5><i class="fas fa-globe mr-2"></i>Landing Page</h5>
                    <table class="table table-sm">
                        <tr><td><strong>URL:</strong></td><td>bookkei.com/${$('#landing_page_slug').val()}</td></tr>
                        <tr><td><strong>Title:</strong></td><td>${$('#page_title').val()}</td></tr>
                        <tr><td><strong>Theme:</strong></td><td>${$('#landing_page_theme option:selected').text()}</td></tr>
                        <tr><td><strong>Booking:</strong></td><td>${$('#booking_enabled').is(':checked') ? 'Enabled' : 'Disabled'}</td></tr>
                    </table>

                    <h5><i class="fas fa-cogs mr-2"></i>Service Display</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Layout:</strong></td><td>${$('#service_display_type option:selected').text()}</td></tr>
                        <tr><td><strong>Count:</strong></td><td>${$('#service_count').val()}</td></tr>
                        <tr><td><strong>Card Style:</strong></td><td>${$('#service_card_style option:selected').text()}</td></tr>
                        <tr><td><strong>Show Pricing:</strong></td><td>${$('#show_service_pricing').is(':checked') ? 'Yes' : 'No'}</td></tr>
                        <tr><td><strong>Quick Booking:</strong></td><td>${$('#quick_booking_enabled').is(':checked') ? 'Enabled' : 'Disabled'}</td></tr>
                        <tr><td><strong>Service SEO:</strong></td><td>${$('#enable_service_seo').is(':checked') ? 'Enabled' : 'Disabled'}</td></tr>
                    </table>

                    <h5><i class="fas fa-search mr-2"></i>SEO & Analytics</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Open Graph:</strong></td><td>${$('#enable_open_graph').is(':checked') ? 'Enabled' : 'Disabled'}</td></tr>
                        <tr><td><strong>Schema Markup:</strong></td><td>${$('#enable_schema_markup').is(':checked') ? 'Enabled' : 'Disabled'}</td></tr>
                        <tr><td><strong>Analytics:</strong></td><td>${$('#google_analytics_id').val() ? 'Configured' : 'Not configured'}</td></tr>
                        <tr><td><strong>Service Analytics:</strong></td><td>${$('#track_service_analytics').is(':checked') ? 'Enabled' : 'Disabled'}</td></tr>
                    </table>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5><i class="fas fa-search mr-2"></i>SEO Settings</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Meta Title:</strong></td><td>${$('#meta_title').val() || 'Auto-generated'}</td></tr>
                        <tr><td><strong>Meta Description:</strong></td><td>${$('#meta_description').val() || 'Auto-generated'}</td></tr>
                        <tr><td><strong>Keywords:</strong></td><td>${$('#meta_keywords').val() || 'None'}</td></tr>
                    </table>
                </div>
            </div>
        `;

        $('#review-content').html(reviewHtml);
    }

    // Initialize character counters
    updateCharCount('#meta_title', '#meta-title-count');
    updateCharCount('#meta_description', '#meta-description-count');

    // Initialize domain type
    $('#domain_type').trigger('change');
});
</script>
@endsection