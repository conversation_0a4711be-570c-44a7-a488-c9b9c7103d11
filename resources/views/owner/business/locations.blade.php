@extends('owner.layouts.app')

@section('title', 'Business Locations')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Business Locations</h1>
            <p class="text-muted mb-0">Manage your business locations and branches.</p>
        </div>
        <div>
            <a href="{{ route('owner.business.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Overview
            </a>
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addLocationModal">
                <i class="fas fa-plus mr-1"></i>
                Add Location
            </button>
        </div>
    </div>
@stop

@section('content')
    {{-- Success/Error Messages --}}
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle mr-2"></i>
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-map-marker-alt text-success mr-2"></i>
                        Your Locations
                    </h5>
                </div>
                <div class="widget-body">
                    @if($locations->count() > 0)
                        @foreach($locations as $location)
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="d-flex align-items-center mb-2">
                                                <h5 class="mb-0 mr-2">{{ $location->name }}</h5>
                                                @if($location->is_main_branch)
                                                    <span class="badge badge-primary">Primary Location</span>
                                                @endif
                                            </div>

                                            <div class="mb-2">
                                                <i class="fas fa-map-marker-alt text-muted mr-2"></i>
                                                <span>{{ $location->full_address }}</span>
                                            </div>

                                            @if($location->phone)
                                                <div class="mb-2">
                                                    <i class="fas fa-phone text-muted mr-2"></i>
                                                    <span>{{ $location->phone }}</span>
                                                </div>
                                            @endif

                                            @if($location->email)
                                                <div class="mb-2">
                                                    <i class="fas fa-envelope text-muted mr-2"></i>
                                                    <span>{{ $location->email }}</span>
                                                </div>
                                            @endif

                                            @if($location->manager_name)
                                                <div class="mb-2">
                                                    <i class="fas fa-user text-muted mr-2"></i>
                                                    <span>Manager: {{ $location->manager_name }}</span>
                                                    @if($location->manager_phone)
                                                        <small class="text-muted">({{ $location->manager_phone }})</small>
                                                    @endif
                                                </div>
                                            @endif

                                            <div class="mb-2">
                                                <i class="fas fa-clock text-muted mr-2"></i>
                                                <span class="text-muted">{{ $location->operating_hours_summary }}</span>
                                            </div>

                                            <div class="mb-2">
                                                <i class="fas fa-calendar-times text-muted mr-2"></i>
                                                <span class="text-muted">{{ $location->holidays_summary }}</span>
                                            </div>

                                            <div class="mt-3">
                                                <div class="btn-group btn-group-sm mb-2">
                                                    <button type="button" class="btn btn-outline-primary"
                                                            onclick="editLocation({{ $location->id }})">
                                                        <i class="fas fa-edit mr-1"></i>
                                                        Edit
                                                    </button>
                                                    <a href="{{ route('owner.business.locations.operating-hours', $location) }}" class="btn btn-outline-secondary">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        Operating Hours
                                                    </a>
                                                    <a href="{{ route('owner.business.locations.holidays', $location) }}" class="btn btn-outline-warning">
                                                        <i class="fas fa-calendar-times mr-1"></i>
                                                        Holidays
                                                    </a>
                                                    @if($location->latitude && $location->longitude)
                                                        <button type="button" class="btn btn-outline-info"
                                                                onclick="viewOnMap({{ $location->latitude }}, {{ $location->longitude }})">
                                                            <i class="fas fa-map mr-1"></i>
                                                            View on Map
                                                        </button>
                                                    @endif
                                                </div>
                                                @if(!$location->is_main_branch)
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-success"
                                                                onclick="setPrimary({{ $location->id }})">
                                                            <i class="fas fa-star mr-1"></i>
                                                            Set as Primary
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger"
                                                                onclick="deleteLocation({{ $location->id }})">
                                                            <i class="fas fa-trash mr-1"></i>
                                                            Delete
                                                        </button>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="location-map-placeholder bg-light rounded d-flex align-items-center justify-content-center"
                                                 style="height: 150px;">
                                                <div class="text-center text-muted">
                                                    <i class="fas fa-map fa-2x mb-2"></i>
                                                    <div>Map Preview</div>
                                                    @if($location->latitude && $location->longitude)
                                                        <small>{{ $location->latitude }}, {{ $location->longitude }}</small>
                                                    @else
                                                        <small>No coordinates</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No locations added</h5>
                            <p class="text-muted">Add your business locations to help customers find you.</p>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addLocationModal">
                                <i class="fas fa-plus mr-1"></i>
                                Add Your First Location
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {{-- Location Stats --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-chart-bar text-info mr-2"></i>
                        Location Stats
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $locations->count() }}</h4>
                                <p class="text-muted mb-0">Total Locations</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $locations->where('is_main_branch', true)->count() }}</h4>
                            <p class="text-muted mb-0">Primary Location</p>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Location Tips --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-lightbulb text-warning mr-2"></i>
                        Location Tips
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="callout callout-info">
                        <h6><i class="fas fa-star mr-1"></i> Primary Location</h6>
                        <p class="mb-0 small">Your primary location appears first in search results and customer communications.</p>
                    </div>

                    <div class="callout callout-success">
                        <h6><i class="fas fa-map mr-1"></i> Accurate Addresses</h6>
                        <p class="mb-0 small">Ensure addresses are accurate for GPS navigation and delivery services.</p>
                    </div>

                    <div class="callout callout-warning">
                        <h6><i class="fas fa-phone mr-1"></i> Contact Information</h6>
                        <p class="mb-0 small">Add location-specific phone numbers for better customer service.</p>
                    </div>
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-cogs text-primary mr-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="list-group list-group-flush">
                        <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="importFromGoogle()">
                            <div>
                                <i class="fab fa-google text-danger mr-2"></i>
                                Import from Google My Business
                                <small class="d-block text-muted">Search and import your business locations</small>
                            </div>
                            <i class="fas fa-chevron-right text-muted"></i>
                        </button>
                        <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="bulkUpdate()">
                            <div>
                                <i class="fas fa-edit text-primary mr-2"></i>
                                Bulk Update Hours
                                <small class="d-block text-muted">Update operating hours for multiple locations</small>
                            </div>
                            <i class="fas fa-chevron-right text-muted"></i>
                        </button>
                        <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="exportLocations()">
                            <div>
                                <i class="fas fa-download text-success mr-2"></i>
                                Export Location Data
                                <small class="d-block text-muted">Download location data in CSV, Excel, or PDF format</small>
                            </div>
                            <i class="fas fa-chevron-right text-muted"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Add Location Modal --}}
    <div class="modal fade" id="addLocationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Location</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="locationForm" method="POST" action="{{ route('owner.business.locations.store') }}">
                    @csrf
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location_name">Location Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="location_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location_phone">Phone Number</label>
                                    <input type="tel" class="form-control" id="location_phone" name="phone">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="location_address">Street Address <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location_address" name="address" required>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="location_city">City <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="location_city" name="city" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="location_state">State/Province</label>
                                    <input type="text" class="form-control" id="location_state" name="state">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="location_postal_code">ZIP/Postal Code</label>
                                    <input type="text" class="form-control" id="location_postal_code" name="postal_code">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location_country">Country <span class="text-danger">*</span></label>
                                    <select class="form-control" id="location_country" name="country" required>
                                        <option value="">Select Country</option>
                                        <option value="United States">United States</option>
                                        <option value="Canada">Canada</option>
                                        <option value="United Kingdom">United Kingdom</option>
                                        <option value="Australia">Australia</option>
                                        <option value="Germany">Germany</option>
                                        <option value="France">France</option>
                                        <option value="Spain">Spain</option>
                                        <option value="Italy">Italy</option>
                                        <option value="Netherlands">Netherlands</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location_email">Email</label>
                                    <input type="email" class="form-control" id="location_email" name="email">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location_lat">Latitude (Optional)</label>
                                    <input type="number" step="any" class="form-control" id="location_lat" name="latitude">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location_lng">Longitude (Optional)</label>
                                    <input type="number" step="any" class="form-control" id="location_lng" name="longitude">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="location_description">Description</label>
                            <textarea class="form-control" id="location_description" name="description" rows="3"></textarea>
                        </div>

                        <hr>
                        <h6>Manager Information (Optional)</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="manager_name">Manager Name</label>
                                    <input type="text" class="form-control" id="manager_name" name="manager_name">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="manager_email">Manager Email</label>
                                    <input type="email" class="form-control" id="manager_email" name="manager_email">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="manager_phone">Manager Phone</label>
                                    <input type="tel" class="form-control" id="manager_phone" name="manager_phone">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Location</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Edit Location Modal --}}
    <div class="modal fade" id="editLocationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Location</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="editLocationForm" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_location_name">Location Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="edit_location_name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_location_phone">Phone Number</label>
                                    <input type="tel" class="form-control" id="edit_location_phone" name="phone">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="edit_location_address">Street Address <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_location_address" name="address" required>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="edit_location_city">City <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="edit_location_city" name="city" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="edit_location_state">State/Province</label>
                                    <input type="text" class="form-control" id="edit_location_state" name="state">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="edit_location_postal_code">ZIP/Postal Code</label>
                                    <input type="text" class="form-control" id="edit_location_postal_code" name="postal_code">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_location_country">Country <span class="text-danger">*</span></label>
                                    <select class="form-control" id="edit_location_country" name="country" required>
                                        <option value="">Select Country</option>
                                        <option value="United States">United States</option>
                                        <option value="Canada">Canada</option>
                                        <option value="United Kingdom">United Kingdom</option>
                                        <option value="Australia">Australia</option>
                                        <option value="Germany">Germany</option>
                                        <option value="France">France</option>
                                        <option value="Spain">Spain</option>
                                        <option value="Italy">Italy</option>
                                        <option value="Netherlands">Netherlands</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_location_email">Email</label>
                                    <input type="email" class="form-control" id="edit_location_email" name="email">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_location_lat">Latitude (Optional)</label>
                                    <input type="number" step="any" class="form-control" id="edit_location_lat" name="latitude">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_location_lng">Longitude (Optional)</label>
                                    <input type="number" step="any" class="form-control" id="edit_location_lng" name="longitude">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="edit_location_description">Description</label>
                            <textarea class="form-control" id="edit_location_description" name="description" rows="3"></textarea>
                        </div>

                        <hr>
                        <h6>Manager Information (Optional)</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="edit_manager_name">Manager Name</label>
                                    <input type="text" class="form-control" id="edit_manager_name" name="manager_name">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="edit_manager_email">Manager Email</label>
                                    <input type="email" class="form-control" id="edit_manager_email" name="manager_email">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="edit_manager_phone">Manager Phone</label>
                                    <input type="tel" class="form-control" id="edit_manager_phone" name="manager_phone">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Location</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
        // Store location data for editing
        const locationData = @json($locations);

        function editLocation(id) {
            // Find the location data
            const location = locationData.find(loc => loc.id === id);
            if (!location) {
                alert('Location not found');
                return;
            }

            // Populate the edit form
            $('#edit_location_name').val(location.name);
            $('#edit_location_phone').val(location.phone || '');
            $('#edit_location_address').val(location.address);
            $('#edit_location_city').val(location.city);
            $('#edit_location_state').val(location.state || '');
            $('#edit_location_postal_code').val(location.postal_code || '');
            $('#edit_location_country').val(location.country);
            $('#edit_location_email').val(location.email || '');
            $('#edit_location_lat').val(location.latitude || '');
            $('#edit_location_lng').val(location.longitude || '');
            $('#edit_location_description').val(location.description || '');
            $('#edit_manager_name').val(location.manager_name || '');
            $('#edit_manager_email').val(location.manager_email || '');
            $('#edit_manager_phone').val(location.manager_phone || '');

            // Set the form action
            $('#editLocationForm').attr('action', `{{ url('owner/business/locations') }}/${id}`);

            // Show the modal
            $('#editLocationModal').modal('show');
        }

        function deleteLocation(id) {
            if (confirm('Are you sure you want to delete this location? This action cannot be undone.')) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `{{ url('owner/business/locations') }}/${id}`;

                // Add CSRF token
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                // Add method override for DELETE
                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';
                form.appendChild(methodField);

                // Submit the form
                document.body.appendChild(form);
                form.submit();
            }
        }

        function setPrimary(id) {
            if (confirm('Set this as your primary location?')) {
                // Create a form to submit the set primary request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `{{ url('owner/business/locations') }}/${id}/set-primary`;

                // Add CSRF token
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                // Submit the form
                document.body.appendChild(form);
                form.submit();
            }
        }

        function viewOnMap(lat, lng) {
            window.open(`https://www.google.com/maps?q=${lat},${lng}`, '_blank');
        }

        function importFromGoogle() {
            $('#googleImportModal').modal('show');
        }

        function bulkUpdate() {
            // Check if there are locations to update
            @if($locations->count() === 0)
                alert('No locations available for bulk update. Please add locations first.');
                return;
            @endif

            $('#bulkUpdateModal').modal('show');
        }

        function exportLocations() {
            $('#exportModal').modal('show');
        }

        // Handle form submissions
        $(document).ready(function() {
            // Add validation feedback
            $('form').on('submit', function() {
                $(this).find('button[type="submit"]').prop('disabled', true).text('Processing...');
            });

            // Reset forms when modals are hidden
            $('#addLocationModal').on('hidden.bs.modal', function() {
                $('#locationForm')[0].reset();
                $('#locationForm button[type="submit"]').prop('disabled', false).text('Add Location');
            });

            $('#editLocationModal').on('hidden.bs.modal', function() {
                $('#editLocationForm')[0].reset();
                $('#editLocationForm button[type="submit"]').prop('disabled', false).text('Update Location');
            });

            // Google Import functionality
            $('#googleSearchForm').on('submit', function(e) {
                e.preventDefault();
                searchGoogleLocations();
            });

            // Bulk Update functionality
            $('#bulkUpdateForm').on('submit', function(e) {
                e.preventDefault();
                submitBulkUpdate();
            });
        });

        // Google My Business functions
        function searchGoogleLocations() {
            const form = $('#googleSearchForm');
            const submitBtn = form.find('button[type="submit"]');
            const originalText = submitBtn.html();

            // Show loading state
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Searching...');

            const formData = new FormData(form[0]);
            formData.append('search', '1');

            $.ajax({
                url: '{{ route("owner.business.locations.import-google") }}',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success && response.locations.length > 0) {
                        displayGoogleLocations(response.locations);
                        showGoogleResultsStep();
                    } else {
                        showAlert('warning', 'No locations found. Please try a different search term.');
                    }
                },
                error: function(xhr) {
                    const message = xhr.responseJSON?.message || 'Search failed. Please try again.';
                    showAlert('error', message);
                },
                complete: function() {
                    // Reset button state
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
        }

        function displayGoogleLocations(locations) {
            let html = '';
            locations.forEach(function(location, index) {
                html += `
                    <div class="card mb-2">
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="selected_locations[]"
                                       value="${index}" id="google_location_${index}">
                                <label class="form-check-label" for="google_location_${index}">
                                    <strong>${location.name}</strong><br>
                                    <small class="text-muted">${location.address}, ${location.city}, ${location.state}</small><br>
                                    ${location.phone ? `<small>Phone: ${location.phone}</small><br>` : ''}
                                    ${location.website ? `<small>Website: ${location.website}</small>` : ''}
                                </label>
                            </div>
                        </div>
                    </div>
                `;
            });
            $('#google-locations-list').html(html);
            window.googleLocationsData = locations;
        }

        function showGoogleSearchStep() {
            $('#google-search-step').show();
            $('#google-results-step').hide();
        }

        function showGoogleResultsStep() {
            $('#google-search-step').hide();
            $('#google-results-step').show();
        }

        function importSelectedLocations() {
            const selectedIndexes = [];
            $('input[name="selected_locations[]"]:checked').each(function() {
                selectedIndexes.push(parseInt($(this).val()));
            });

            if (selectedIndexes.length === 0) {
                showAlert('warning', 'Please select at least one location to import.');
                return;
            }

            const selectedLocations = selectedIndexes.map(index => window.googleLocationsData[index]);
            const importBtn = $('button[onclick="importSelectedLocations()"]');
            const originalText = importBtn.html();

            // Show loading state
            importBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Importing...');

            $.ajax({
                url: '{{ route("owner.business.locations.import-google") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    selected_locations: selectedLocations
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        setTimeout(() => window.location.reload(), 1500);
                    } else {
                        showAlert('error', response.message);
                        importBtn.prop('disabled', false).html(originalText);
                    }
                },
                error: function(xhr) {
                    const message = xhr.responseJSON?.message || 'Import failed. Please try again.';
                    showAlert('error', message);
                    importBtn.prop('disabled', false).html(originalText);
                }
            });
        }

        // Helper function to show alerts
        function showAlert(type, message) {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;

            // Remove existing alerts
            $('.alert').remove();

            // Add new alert at the top of the page
            $('main .container-fluid').prepend(alertHtml);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                $('.alert').alert('close');
            }, 5000);
        }

        // Bulk Update functions
        function toggleBulkDayInputs(day) {
            const isOpen = $(`#bulk_${day}_open`).is(':checked');
            $(`#bulk_${day}_open_time`).prop('disabled', !isOpen);
            $(`#bulk_${day}_close_time`).prop('disabled', !isOpen);

            if (!isOpen) {
                $(`#bulk_${day}_open_time`).val('');
                $(`#bulk_${day}_close_time`).val('');
            }
        }

        function submitBulkUpdate() {
            const selectedLocations = [];
            $('input[name="location_ids[]"]:checked').each(function() {
                selectedLocations.push($(this).val());
            });

            if (selectedLocations.length === 0) {
                showAlert('warning', 'Please select at least one location to update.');
                return;
            }

            const submitBtn = $('#bulkUpdateForm button[type="submit"]');
            const originalText = submitBtn.html();

            // Show loading state
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Updating...');

            const formData = $('#bulkUpdateForm').serialize();

            $.ajax({
                url: '{{ route("owner.business.locations.bulk-update-hours") }}',
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        $('#bulkUpdateModal').modal('hide');
                        setTimeout(() => window.location.reload(), 1500);
                    } else {
                        showAlert('error', response.message);
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                },
                error: function(xhr) {
                    const errors = xhr.responseJSON?.errors;
                    if (errors) {
                        let errorMessage = 'Validation errors:<br>';
                        Object.keys(errors).forEach(key => {
                            errorMessage += `• ${errors[key][0]}<br>`;
                        });
                        showAlert('error', errorMessage);
                    } else {
                        const message = xhr.responseJSON?.message || 'Update failed. Please try again.';
                        showAlert('error', message);
                    }
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
        }
    </script>

    {{-- Google My Business Import Modal --}}
    <div class="modal fade" id="googleImportModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fab fa-google text-danger mr-2"></i>
                        Import from Google My Business
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="google-search-step">
                        <h6>Step 1: Search for your business</h6>
                        <form id="googleSearchForm">
                            @csrf
                            <div class="form-group">
                                <label for="google_business_name">Business Name</label>
                                <input type="text" class="form-control" id="google_business_name" name="business_name"
                                       value="{{ $locations->first()->business->name ?? '' }}" required>
                            </div>
                            <div class="form-group">
                                <label for="google_address">Address (Optional)</label>
                                <input type="text" class="form-control" id="google_address" name="address"
                                       placeholder="Enter address to narrow search">
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search mr-1"></i>
                                Search Locations
                            </button>
                        </form>
                    </div>

                    <div id="google-results-step" style="display: none;">
                        <h6>Step 2: Select locations to import</h6>
                        <div id="google-locations-list"></div>
                        <div class="mt-3">
                            <button type="button" class="btn btn-secondary" onclick="showGoogleSearchStep()">
                                <i class="fas fa-arrow-left mr-1"></i>
                                Back to Search
                            </button>
                            <button type="button" class="btn btn-success" onclick="importSelectedLocations()">
                                <i class="fas fa-download mr-1"></i>
                                Import Selected
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        This feature searches for your business locations using Google's database and imports basic information including address, phone, and operating hours.
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Bulk Update Hours Modal --}}
    <div class="modal fade" id="bulkUpdateModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit text-primary mr-2"></i>
                        Bulk Update Operating Hours
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="bulkUpdateForm">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Select Locations to Update</label>
                            <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                @foreach($locations as $location)
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="location_ids[]"
                                               value="{{ $location->id }}" id="bulk_location_{{ $location->id }}">
                                        <label class="form-check-label" for="bulk_location_{{ $location->id }}">
                                            {{ $location->name }}
                                            <small class="text-muted d-block">{{ $location->address }}, {{ $location->city }}</small>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            <small class="form-text text-muted">Select all locations you want to update with the same hours.</small>
                        </div>

                        <div class="form-group">
                            <label>Operating Hours</label>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Day</th>
                                            <th>Open</th>
                                            <th>Opening Time</th>
                                            <th>Closing Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                            $days = [
                                                'monday' => 'Monday',
                                                'tuesday' => 'Tuesday',
                                                'wednesday' => 'Wednesday',
                                                'thursday' => 'Thursday',
                                                'friday' => 'Friday',
                                                'saturday' => 'Saturday',
                                                'sunday' => 'Sunday'
                                            ];
                                        @endphp
                                        @foreach($days as $dayKey => $dayName)
                                            <tr>
                                                <td>{{ $dayName }}</td>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input day-toggle" type="checkbox"
                                                               name="hours[{{ $loop->index }}][is_open]" value="1"
                                                               id="bulk_{{ $dayKey }}_open"
                                                               onchange="toggleBulkDayInputs('{{ $dayKey }}')">
                                                        <input type="hidden" name="hours[{{ $loop->index }}][day]" value="{{ $dayKey }}">
                                                    </div>
                                                </td>
                                                <td>
                                                    <input type="time" class="form-control form-control-sm"
                                                           name="hours[{{ $loop->index }}][open_time]"
                                                           id="bulk_{{ $dayKey }}_open_time" disabled>
                                                </td>
                                                <td>
                                                    <input type="time" class="form-control form-control-sm"
                                                           name="hours[{{ $loop->index }}][close_time]"
                                                           id="bulk_{{ $dayKey }}_close_time" disabled>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            This will overwrite the current operating hours for all selected locations.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i>
                            Update Hours
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Export Modal --}}
    <div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-download text-success mr-2"></i>
                        Export Location Data
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="exportForm" action="{{ route('owner.business.locations.export') }}" method="GET">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="export_format">Export Format</label>
                            <select class="form-control" id="export_format" name="format" required>
                                <option value="">Select format...</option>
                                <option value="csv">CSV (Comma Separated Values)</option>
                                <option value="excel">Excel Spreadsheet</option>
                                <option value="pdf">PDF Report</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Include Additional Data</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="include_hours" value="1" id="include_hours">
                                <label class="form-check-label" for="include_hours">
                                    Operating Hours
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="include_holidays" value="1" id="include_holidays">
                                <label class="form-check-label" for="include_holidays">
                                    Holidays & Closures
                                </label>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            Export includes all location details: name, address, contact information, and manager details.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-download mr-1"></i>
                            Export Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop
