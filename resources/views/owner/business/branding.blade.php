@extends('owner.layouts.app')

@section('title', 'Branding & Themes')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Branding & Themes</h1>
            <p class="text-muted mb-0">Customize your business appearance and branding.</p>
        </div>
        <div>
            <a href="{{ route('owner.business.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Overview
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-palette text-primary mr-2"></i>
                        Brand Colors & Logo
                    </h5>
                </div>
                <div class="widget-body">
                    <form action="{{ route('owner.business.branding.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        {{-- Logo Upload --}}
                        <div class="form-group">
                            <label for="logo">Business Logo</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input @error('logo') is-invalid @enderror" id="logo" name="logo" accept="image/*">
                                        <label class="custom-file-label" for="logo">Choose logo file...</label>
                                        @error('logo')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        Recommended: PNG or JPG, max 2MB, square format (500x500px)
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <div class="logo-preview border rounded p-3 text-center bg-light">
                                        @if($brandingSettings['logo_url'])
                                            <img src="{{ asset('storage/' . $brandingSettings['logo_url']) }}" alt="Current Logo" class="img-fluid" style="max-height: 100px;">
                                        @else
                                            <div class="text-muted">
                                                <i class="fas fa-image fa-2x mb-2"></i>
                                                <div>No logo uploaded</div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Color Scheme --}}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="primary_color">Primary Color</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control @error('primary_color') is-invalid @enderror" id="primary_color" name="primary_color"
                                               value="{{ old('primary_color', $brandingSettings['primary_color']) }}" style="height: 38px;">
                                        <div class="input-group-append">
                                            <input type="text" class="form-control" id="primary_color_hex"
                                                   value="{{ old('primary_color', $brandingSettings['primary_color']) }}" readonly>
                                        </div>
                                    </div>
                                    @error('primary_color')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Used for buttons, links, and highlights</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="secondary_color">Secondary Color</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control @error('secondary_color') is-invalid @enderror" id="secondary_color" name="secondary_color"
                                               value="{{ old('secondary_color', $brandingSettings['secondary_color']) }}" style="height: 38px;">
                                        <div class="input-group-append">
                                            <input type="text" class="form-control" id="secondary_color_hex"
                                                   value="{{ old('secondary_color', $brandingSettings['secondary_color']) }}" readonly>
                                        </div>
                                    </div>
                                    @error('secondary_color')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Used for secondary elements and borders</small>
                                </div>
                            </div>
                        </div>

                        {{-- Theme Selection --}}
                        <div class="form-group">
                            <label>Theme Preference</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card theme-option {{ $brandingSettings['theme'] === 'light' ? 'border-primary' : '' }}"
                                         onclick="selectTheme('light')">
                                        <div class="card-body text-center">
                                            <i class="fas fa-sun fa-2x text-warning mb-2"></i>
                                            <h6>Light Theme</h6>
                                            <small class="text-muted">Clean and bright appearance</small>
                                            <input type="radio" name="theme" value="light"
                                                   {{ $brandingSettings['theme'] === 'light' ? 'checked' : '' }} style="display: none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card theme-option {{ $brandingSettings['theme'] === 'dark' ? 'border-primary' : '' }}"
                                         onclick="selectTheme('dark')">
                                        <div class="card-body text-center">
                                            <i class="fas fa-moon fa-2x text-info mb-2"></i>
                                            <h6>Dark Theme</h6>
                                            <small class="text-muted">Modern dark appearance</small>
                                            <input type="radio" name="theme" value="dark"
                                                   {{ $brandingSettings['theme'] === 'dark' ? 'checked' : '' }} style="display: none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card theme-option {{ $brandingSettings['theme'] === 'auto' ? 'border-primary' : '' }}"
                                         onclick="selectTheme('auto')">
                                        <div class="card-body text-center">
                                            <i class="fas fa-adjust fa-2x text-secondary mb-2"></i>
                                            <h6>Auto Theme</h6>
                                            <small class="text-muted">Follows system preference</small>
                                            <input type="radio" name="theme" value="auto"
                                                   {{ $brandingSettings['theme'] === 'auto' ? 'checked' : '' }} style="display: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Font Selection --}}
                        <div class="form-group">
                            <label for="font_family">Font Family</label>
                            <select class="form-control @error('font_family') is-invalid @enderror" id="font_family" name="font_family">
                                <option value="Inter" {{ old('font_family', $brandingSettings['font_family']) === 'Inter' ? 'selected' : '' }}>Inter (Modern)</option>
                                <option value="Roboto" {{ old('font_family', $brandingSettings['font_family']) === 'Roboto' ? 'selected' : '' }}>Roboto (Clean)</option>
                                <option value="Open Sans" {{ old('font_family', $brandingSettings['font_family']) === 'Open Sans' ? 'selected' : '' }}>Open Sans (Friendly)</option>
                                <option value="Lato" {{ old('font_family', $brandingSettings['font_family']) === 'Lato' ? 'selected' : '' }}>Lato (Professional)</option>
                                <option value="Poppins" {{ old('font_family', $brandingSettings['font_family']) === 'Poppins' ? 'selected' : '' }}>Poppins (Rounded)</option>
                            </select>
                            @error('font_family')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i>
                                Save Branding Settings
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
                                <i class="fas fa-undo mr-1"></i>
                                Reset to Defaults
                            </button>
                        </div>

                        @if(session('success'))
                            <div class="alert alert-success mt-3">
                                <i class="fas fa-check-circle mr-1"></i>
                                {{ session('success') }}
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="alert alert-danger mt-3">
                                <h6><i class="fas fa-exclamation-triangle mr-1"></i> Validation Errors:</h6>
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {{-- Live Preview --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-eye text-info mr-2"></i>
                        Live Preview
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="preview-container border rounded p-3" id="brandingPreview">
                        <div class="preview-header mb-3" style="background-color: {{ $brandingSettings['primary_color'] }}; color: white; padding: 10px; border-radius: 4px;">
                            <h6 class="mb-0">Your Business Name</h6>
                        </div>

                        <div class="preview-content">
                            <button class="btn btn-sm mb-2" id="previewButton"
                                    style="background-color: {{ $brandingSettings['primary_color'] }}; color: white; border: none;">
                                Book Now
                            </button>

                            <div class="preview-text" style="font-family: {{ $brandingSettings['font_family'] }};">
                                <p class="mb-2">Welcome to our booking system!</p>
                                <small class="text-muted" style="color: {{ $brandingSettings['secondary_color'] }} !important;">
                                    This is how your branding will appear to customers.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Color Presets --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-swatchbook text-warning mr-2"></i>
                        Color Presets
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="row">
                        <div class="col-6 mb-2">
                            <button type="button" class="btn btn-block color-preset"
                                    onclick="applyColorPreset('#007bff', '#6c757d')"
                                    style="background: linear-gradient(45deg, #007bff, #6c757d); color: white;">
                                Blue Professional
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button type="button" class="btn btn-block color-preset"
                                    onclick="applyColorPreset('#28a745', '#20c997')"
                                    style="background: linear-gradient(45deg, #28a745, #20c997); color: white;">
                                Green Fresh
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button type="button" class="btn btn-block color-preset"
                                    onclick="applyColorPreset('#dc3545', '#fd7e14')"
                                    style="background: linear-gradient(45deg, #dc3545, #fd7e14); color: white;">
                                Red Energy
                            </button>
                        </div>
                        <div class="col-6 mb-2">
                            <button type="button" class="btn btn-block color-preset"
                                    onclick="applyColorPreset('#6f42c1', '#e83e8c')"
                                    style="background: linear-gradient(45deg, #6f42c1, #e83e8c); color: white;">
                                Purple Creative
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Branding Tips --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-lightbulb text-warning mr-2"></i>
                        Branding Tips
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="callout callout-info">
                        <h6><i class="fas fa-palette mr-1"></i> Color Psychology</h6>
                        <p class="mb-0 small">Blue conveys trust, green suggests growth, red creates urgency, and purple implies creativity.</p>
                    </div>

                    <div class="callout callout-success">
                        <h6><i class="fas fa-image mr-1"></i> Logo Guidelines</h6>
                        <p class="mb-0 small">Use a square logo for best results. Ensure it's readable at small sizes.</p>
                    </div>

                    <div class="callout callout-warning">
                        <h6><i class="fas fa-mobile-alt mr-1"></i> Mobile Friendly</h6>
                        <p class="mb-0 small">Test your branding on mobile devices to ensure good visibility.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
        // Form submission handler for debugging and validation
        $('form').on('submit', function(e) {
            console.log('Branding form submitted!');
            console.log('Form data:', $(this).serialize());

            // Basic validation
            let isValid = true;
            const primaryColor = $('#primary_color').val();
            const secondaryColor = $('#secondary_color').val();

            // Validate hex color format
            const hexPattern = /^#[0-9A-Fa-f]{6}$/;
            if (primaryColor && !hexPattern.test(primaryColor)) {
                isValid = false;
                alert('Primary color must be a valid hex color (e.g., #28a745)');
            }

            if (secondaryColor && !hexPattern.test(secondaryColor)) {
                isValid = false;
                alert('Secondary color must be a valid hex color (e.g., #6c757d)');
            }

            if (!isValid) {
                e.preventDefault();
                return false;
            }
        });

        // Show success message if present
        @if(session('success'))
            $(document).ready(function() {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: '{{ session("success") }}',
                    confirmButtonColor: '#28a745',
                    timer: 3000,
                    timerProgressBar: true
                });
            });
        @endif

        // Update hex values when color inputs change
        $('#primary_color').on('input', function() {
            $('#primary_color_hex').val($(this).val());
            updatePreview();
        });

        $('#secondary_color').on('input', function() {
            $('#secondary_color_hex').val($(this).val());
            updatePreview();
        });

        $('#font_family').on('change', function() {
            updatePreview();
        });

        function selectTheme(theme) {
            $('.theme-option').removeClass('border-primary');
            $('.theme-option input[value="' + theme + '"]').prop('checked', true);
            $('.theme-option input[value="' + theme + '"]').closest('.theme-option').addClass('border-primary');
        }

        function applyColorPreset(primary, secondary) {
            $('#primary_color').val(primary);
            $('#primary_color_hex').val(primary);
            $('#secondary_color').val(secondary);
            $('#secondary_color_hex').val(secondary);
            updatePreview();
        }

        function updatePreview() {
            const primaryColor = $('#primary_color').val();
            const secondaryColor = $('#secondary_color').val();
            const fontFamily = $('#font_family').val();

            $('.preview-header').css('background-color', primaryColor);
            $('#previewButton').css('background-color', primaryColor);
            $('.preview-text').css('font-family', fontFamily);
            $('.preview-text small').css('color', secondaryColor + ' !important');
        }

        function resetToDefaults() {
            if (confirm('Reset all branding settings to defaults?')) {
                $('#primary_color').val('#28a745');
                $('#primary_color_hex').val('#28a745');
                $('#secondary_color').val('#6c757d');
                $('#secondary_color_hex').val('#6c757d');
                $('#font_family').val('Inter');
                selectTheme('light');
                updatePreview();
            }
        }

        // File input label update
        $('.custom-file-input').on('change', function() {
            const fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName);
        });
    </script>
@stop
