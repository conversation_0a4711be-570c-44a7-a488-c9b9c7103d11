@extends('owner.layouts.app')

@section('title', 'General Business Information')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>General Business Information</h1>
            <p class="text-muted mb-0">Update your business details and contact information.</p>
        </div>
        <div>
            <a href="{{ route('owner.business.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Overview
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-info-circle text-info mr-2"></i>
                        Business Details
                    </h5>
                </div>
                <div class="widget-body">
                    <form action="{{ route('owner.business.general.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Business Name <span class="text-danger">*</span></label>
                                    <input type="text"
                                           class="form-control @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name', $business['name']) }}"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel"
                                           class="form-control @error('phone') is-invalid @enderror"
                                           id="phone"
                                           name="phone"
                                           value="{{ old('phone', $business['phone']) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email"
                                           class="form-control @error('email') is-invalid @enderror"
                                           id="email"
                                           name="email"
                                           value="{{ old('email', $business['email']) }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="website" class="form-label">Website URL</label>
                                    <input type="url"
                                           class="form-control @error('website') is-invalid @enderror"
                                           id="website"
                                           name="website"
                                           value="{{ old('website', $business['website']) }}"
                                           placeholder="https://www.example.com">
                                    @error('website')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="address" class="form-label">Business Address</label>
                            <div class="input-group">
                                <textarea class="form-control bg-light"
                                          id="address"
                                          rows="2"
                                          readonly
                                          placeholder="Address is managed through Business Locations">{{ $business['address'] }}</textarea>
                                <div class="input-group-append">
                                    <a href="{{ route('owner.business.locations') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        Manage Locations
                                    </a>
                                </div>
                            </div>
                            <small class="form-text text-muted">Business addresses are managed in the <a href="{{ route('owner.business.locations') }}">Locations</a> section.</small>
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Business Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description"
                                      name="description"
                                      rows="4"
                                      placeholder="Describe your business, services, and what makes you unique">{{ old('description', $business['description']) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">This description may be displayed to customers when they book appointments.</small>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="timezone" class="form-label">Timezone</label>
                                    <select class="form-control @error('timezone') is-invalid @enderror"
                                            id="timezone"
                                            name="timezone">
                                        <option value="UTC" {{ old('timezone', $business['timezone']) == 'UTC' ? 'selected' : '' }}>UTC</option>
                                        <option value="America/New_York" {{ old('timezone', $business['timezone']) == 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                        <option value="America/Chicago" {{ old('timezone', $business['timezone']) == 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                        <option value="America/Denver" {{ old('timezone', $business['timezone']) == 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                        <option value="America/Los_Angeles" {{ old('timezone', $business['timezone']) == 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                        <option value="Europe/London" {{ old('timezone', $business['timezone']) == 'Europe/London' ? 'selected' : '' }}>London</option>
                                        <option value="Europe/Paris" {{ old('timezone', $business['timezone']) == 'Europe/Paris' ? 'selected' : '' }}>Paris</option>
                                        <option value="Asia/Tokyo" {{ old('timezone', $business['timezone']) == 'Asia/Tokyo' ? 'selected' : '' }}>Tokyo</option>
                                    </select>
                                    @error('timezone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="currency" class="form-label">Currency</label>
                                    <select class="form-control @error('currency') is-invalid @enderror"
                                            id="currency"
                                            name="currency">
                                        <option value="USD" {{ old('currency', $business['currency']) == 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                        <option value="EUR" {{ old('currency', $business['currency']) == 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                        <option value="GBP" {{ old('currency', $business['currency']) == 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                        <option value="CAD" {{ old('currency', $business['currency']) == 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                                        <option value="AUD" {{ old('currency', $business['currency']) == 'AUD' ? 'selected' : '' }}>AUD - Australian Dollar</option>
                                        <option value="JPY" {{ old('currency', $business['currency']) == 'JPY' ? 'selected' : '' }}>JPY - Japanese Yen</option>
                                    </select>
                                    @error('currency')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="language" class="form-label">Language</label>
                                    <select class="form-control @error('language') is-invalid @enderror"
                                            id="language"
                                            name="language">
                                        <option value="en" {{ old('language', $business['language']) == 'en' ? 'selected' : '' }}>English</option>
                                        <option value="es" {{ old('language', $business['language']) == 'es' ? 'selected' : '' }}>Spanish</option>
                                        <option value="fr" {{ old('language', $business['language']) == 'fr' ? 'selected' : '' }}>French</option>
                                        <option value="de" {{ old('language', $business['language']) == 'de' ? 'selected' : '' }}>German</option>
                                        <option value="it" {{ old('language', $business['language']) == 'it' ? 'selected' : '' }}>Italian</option>
                                        <option value="pt" {{ old('language', $business['language']) == 'pt' ? 'selected' : '' }}>Portuguese</option>
                                    </select>
                                    @error('language')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save mr-1"></i>
                                Update Business Information
                            </button>
                            <button type="reset" class="btn btn-secondary ml-2">
                                <i class="fas fa-undo mr-1"></i>
                                Reset Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {{-- Help & Tips --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-lightbulb text-warning mr-2"></i>
                        Tips & Best Practices
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="callout callout-info">
                        <h6><i class="fas fa-phone mr-1"></i> Phone Number</h6>
                        <p class="mb-0">Use a format that customers can easily recognize and dial, such as (*************.</p>
                    </div>

                    <div class="callout callout-success">
                        <h6><i class="fas fa-globe mr-1"></i> Website</h6>
                        <p class="mb-0">Include your full website URL starting with https:// to ensure customers can find you online.</p>
                    </div>

                    <div class="callout callout-warning">
                        <h6><i class="fas fa-edit mr-1"></i> Description</h6>
                        <p class="mb-0">Write a compelling description that highlights your unique services and value proposition.</p>
                    </div>
                </div>
            </div>

            {{-- Business Preview --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-eye text-primary mr-2"></i>
                        Customer Preview
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="text-center mb-3">
                        <div class="bg-light rounded p-3">
                            <h5 class="mb-1" id="preview-name">{{ $business['name'] }}</h5>
                            <p class="text-muted mb-2" id="preview-description">{{ $business['description'] ?: 'No description provided yet.' }}</p>
                            <div class="d-flex justify-content-center">
                                <div class="text-warning">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= floor($business['rating']))
                                            <i class="fas fa-star"></i>
                                        @elseif($i <= $business['rating'])
                                            <i class="fas fa-star-half-alt"></i>
                                        @else
                                            <i class="far fa-star"></i>
                                        @endif
                                    @endfor
                                </div>
                                <small class="ml-2 text-muted">({{ $business['total_reviews'] }} reviews)</small>
                            </div>
                        </div>
                    </div>
                    <small class="text-muted">This is how your business appears to customers when they search for services.</small>
                </div>
            </div>

            {{-- Recent Changes --}}
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-history text-secondary mr-2"></i>
                        Recent Changes
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="timeline">
                        <div class="time-label">
                            <span class="bg-success">Today</span>
                        </div>

                        <div>
                            <i class="fas fa-info-circle bg-info"></i>
                            <div class="timeline-item">
                                <h3 class="timeline-header">
                                    Business information viewed
                                </h3>
                                <div class="timeline-body">
                                    You accessed the business information page.
                                </div>
                                <div class="timeline-footer">
                                    <small class="text-muted">Just now</small>
                                </div>
                            </div>
                        </div>

                        <div>
                            <i class="fas fa-edit bg-warning"></i>
                            <div class="timeline-item">
                                <h3 class="timeline-header">
                                    Last update
                                </h3>
                                <div class="timeline-body">
                                    Business description was updated.
                                </div>
                                <div class="timeline-footer">
                                    <small class="text-muted">2 days ago</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
        $(document).ready(function() {
            // Live preview updates
            $('#name').on('input', function() {
                $('#preview-name').text($(this).val() || '{{ $business["name"] }}');
            });

            $('#description').on('input', function() {
                const description = $(this).val() || 'No description provided yet.';
                $('#preview-description').text(description);
            });

            // Form submission handler with enhanced validation
            $('form').on('submit', function(e) {
                console.log('Form submitted!');
                console.log('Form data:', $(this).serialize());

                const requiredFields = $(this).find('[required]');
                let isValid = true;

                requiredFields.each(function() {
                    if (!$(this).val().trim()) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                        if (!$(this).next('.invalid-feedback').length) {
                            $(this).after('<div class="invalid-feedback">This field is required.</div>');
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        $(this).next('.invalid-feedback').remove();
                    }
                });

                // Additional validation for URL format
                const websiteField = $('#website');
                if (websiteField.val() && !isValidUrl(websiteField.val())) {
                    isValid = false;
                    websiteField.addClass('is-invalid');
                    if (!websiteField.next('.invalid-feedback').length) {
                        websiteField.after('<div class="invalid-feedback">Please enter a valid URL (e.g., https://www.example.com)</div>');
                    }
                }

                // Additional validation for email format
                const emailField = $('#email');
                if (emailField.val() && !isValidEmail(emailField.val())) {
                    isValid = false;
                    emailField.addClass('is-invalid');
                    if (!emailField.next('.invalid-feedback').length) {
                        emailField.after('<div class="invalid-feedback">Please enter a valid email address</div>');
                    }
                }

                if (!isValid) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: 'Please fix the errors and try again.',
                        confirmButtonColor: '#28a745'
                    });
                }
            });

            // Helper functions for validation
            function isValidUrl(string) {
                try {
                    new URL(string);
                    return true;
                } catch (_) {
                    return false;
                }
            }

            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            // Reset form confirmation
            $('button[type="reset"]').on('click', function(e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Reset Form?',
                    text: 'This will undo all your changes. Are you sure?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#6c757d',
                    cancelButtonColor: '#28a745',
                    confirmButtonText: 'Yes, reset it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $('form')[0].reset();
                        // Reset preview
                        $('#preview-name').text('{{ $business["name"] }}');
                        $('#preview-description').text('{{ $business["description"] ?: "No description provided yet." }}');
                    }
                });
            });
        });

        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: '{{ session("success") }}',
                confirmButtonColor: '#28a745',
                timer: 3000,
                timerProgressBar: true
            });
        @endif
    </script>
@stop
