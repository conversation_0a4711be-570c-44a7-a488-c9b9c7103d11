@extends('owner.layouts.app')

@section('title', 'Business Overview')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Business Overview</h1>
            <p class="text-muted mb-0">Manage your business information and view key performance metrics.</p>
        </div>
        <div>
            <a href="{{ route('owner.business.general') }}" class="btn btn-success">
                <i class="fas fa-edit mr-1"></i>
                Edit Business Info
            </a>
        </div>
    </div>
@stop

@section('content')
    {{-- Business Information Card --}}
    <div class="row">
        <div class="col-lg-8">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-building text-success mr-2"></i>
                        Business Information
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Name:</dt>
                                <dd class="col-sm-8">{{ $business['name'] }}</dd>

                                <dt class="col-sm-4">Category:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge badge-primary">{{ $business['category'] }}</span>
                                </dd>

                                <dt class="col-sm-4">Phone:</dt>
                                <dd class="col-sm-8">
                                    <a href="tel:{{ $business['phone'] }}">{{ $business['phone'] }}</a>
                                </dd>

                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8">
                                    <a href="mailto:{{ $business['email'] }}">{{ $business['email'] }}</a>
                                </dd>

                                <dt class="col-sm-4">Website:</dt>
                                <dd class="col-sm-8">
                                    <a href="{{ $business['website'] }}" target="_blank">{{ $business['website'] }}</a>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Established:</dt>
                                <dd class="col-sm-8">{{ \Carbon\Carbon::parse($business['established'])->format('F j, Y') }}</dd>

                                <dt class="col-sm-4">Rating:</dt>
                                <dd class="col-sm-8">
                                    <div class="d-flex align-items-center">
                                        <span class="h5 mb-0 mr-2">{{ $business['rating'] }}</span>
                                        <div class="text-warning">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= floor($business['rating']))
                                                    <i class="fas fa-star"></i>
                                                @elseif($i <= $business['rating'])
                                                    <i class="fas fa-star-half-alt"></i>
                                                @else
                                                    <i class="far fa-star"></i>
                                                @endif
                                            @endfor
                                        </div>
                                        <small class="ml-2 text-muted">({{ $business['total_reviews'] }} reviews)</small>
                                    </div>
                                </dd>

                                <dt class="col-sm-4">Address:</dt>
                                <dd class="col-sm-8">{{ $business['address'] }}</dd>
                            </dl>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Description:</h6>
                            <p class="text-muted">{{ $business['description'] }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Quick Actions --}}
        <div class="col-lg-4">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-cogs text-primary mr-2"></i>
                        Quick Settings
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('owner.business.general') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-info-circle text-info mr-2"></i>
                                General Information
                            </div>
                            <i class="fas fa-chevron-right"></i>
                        </a>

                        <a href="{{ route('owner.business.operating-hours') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-clock text-warning mr-2"></i>
                                Operating Hours
                            </div>
                            <i class="fas fa-chevron-right"></i>
                        </a>

                        <a href="{{ route('owner.business.holidays') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-calendar-times text-danger mr-2"></i>
                                Holidays & Closures
                            </div>
                            <i class="fas fa-chevron-right"></i>
                        </a>

                        <a href="{{ route('owner.business.locations') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-map-marker-alt text-success mr-2"></i>
                                Locations
                            </div>
                            <i class="fas fa-chevron-right"></i>
                        </a>

                        <a href="{{ route('owner.business.branding') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-palette text-purple mr-2"></i>
                                Branding & Themes
                            </div>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Business Statistics --}}
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-chart-bar text-info mr-2"></i>
                        Business Statistics
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-concierge-bell"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Services</span>
                                    <span class="info-box-number">{{ $businessStats['total_services'] }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-tools"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Resources</span>
                                    <span class="info-box-number">{{ $businessStats['total_resources'] }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-users"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Staff Members</span>
                                    <span class="info-box-number">{{ $businessStats['total_staff'] }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger">
                                    <i class="fas fa-calendar-check"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Monthly Bookings</span>
                                    <span class="info-box-number">{{ $businessStats['monthly_bookings'] }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Performance Metrics --}}
    <div class="row">
        <div class="col-lg-6">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-tachometer-alt text-success mr-2"></i>
                        Performance Metrics
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-success">${{ number_format($businessStats['monthly_revenue'], 0) }}</h3>
                                <p class="text-muted mb-0">Monthly Revenue</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-info">{{ $businessStats['customer_satisfaction'] }}%</h3>
                                <p class="text-muted mb-0">Customer Satisfaction</p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-warning">{{ $businessStats['repeat_customers'] }}%</h3>
                                <p class="text-muted mb-0">Repeat Customers</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-primary">{{ $businessStats['average_service_time'] }} min</h3>
                                <p class="text-muted mb-0">Avg. Service Time</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-lightbulb text-warning mr-2"></i>
                        Business Insights
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="callout callout-info">
                        <h6><i class="fas fa-calendar-day mr-1"></i> Peak Day</h6>
                        <p class="mb-0">Your busiest day is <strong>{{ $businessStats['peak_day'] }}</strong></p>
                    </div>

                    <div class="callout callout-warning">
                        <h6><i class="fas fa-clock mr-1"></i> Busiest Hour</h6>
                        <p class="mb-0">Most bookings occur at <strong>{{ $businessStats['busiest_hour'] }}</strong></p>
                    </div>

                    <div class="callout callout-success">
                        <h6><i class="fas fa-trending-up mr-1"></i> Recommendation</h6>
                        <p class="mb-0">Consider extending hours on {{ $businessStats['peak_day'] }} to accommodate more customers.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
