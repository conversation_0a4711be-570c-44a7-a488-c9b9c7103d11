@extends('owner.layouts.app')

@section('title', 'Service Display Preview')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Service Display Preview</h1>
                <p class="text-muted">Preview how your services will appear on your landing page</p>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('owner.service-display.index') }}">Service Display</a></li>
                    <li class="breadcrumb-item active">Preview</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <!-- Preview Controls -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">Preview Controls</h5>
                                <p class="text-muted mb-0">View how your services will appear to customers</p>
                            </div>
                            <div class="btn-group">
                                <a href="{{ route('owner.service-display.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left mr-2"></i>Back to Settings
                                </a>
                                <button type="button" class="btn btn-info" id="toggle-mobile-view">
                                    <i class="fas fa-mobile-alt mr-2"></i>Mobile View
                                </button>
                                @if($business->landingPage && $business->landingPage->is_published)
                                    <a href="{{ route('landing-page.show', $business->landingPage->custom_slug) }}"
                                       target="_blank" class="btn btn-success">
                                        <i class="fas fa-external-link-alt mr-2"></i>View Live
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Container -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-eye mr-2"></i>Service Display Preview
                        </h3>
                        <div class="card-tools">
                            <span class="badge badge-info">{{ $settings->layout_type }} Layout</span>
                            <span class="badge badge-secondary">{{ $services->count() }} Services</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="preview-container" class="preview-container">
                            @if($services->count() > 0)
                                @if($settings->group_by_category && $servicesByCategory->count() > 1)
                                    <!-- Grouped by Category -->
                                    @foreach($servicesByCategory as $categoryName => $categoryServices)
                                        <div class="service-category-section mb-5">
                                            @if($settings->show_categories)
                                                <h4 class="category-title mb-3">{{ $categoryName }}</h4>
                                            @endif

                                            <div class="services-container layout-{{ $settings->layout_type }}">
                                                @include('owner.service-display.partials.service-grid', ['services' => $categoryServices])
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <!-- All Services Together -->
                                    <div class="services-container layout-{{ $settings->layout_type }}">
                                        @include('owner.service-display.partials.service-grid', ['services' => $services])
                                    </div>
                                @endif
                            @else
                                <!-- No Services -->
                                <div class="text-center py-5">
                                    <i class="fas fa-concierge-bell fa-4x text-muted mb-3"></i>
                                    <h4 class="text-muted">No Services to Display</h4>
                                    <p class="text-muted">Create and configure services to see them in the preview.</p>
                                    <a href="{{ route('owner.services.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus mr-2"></i>Create Your First Service
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Settings Info -->
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle mr-2"></i>Current Settings
                        </h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Layout Type:</strong></td>
                                <td>{{ ucfirst($settings->layout_type) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Grid Columns:</strong></td>
                                <td>{{ $settings->grid_columns }}</td>
                            </tr>
                            <tr>
                                <td><strong>Card Style:</strong></td>
                                <td>{{ ucfirst($settings->service_card_style) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Services Displayed:</strong></td>
                                <td>{{ $settings->homepage_display_count }}</td>
                            </tr>
                            <tr>
                                <td><strong>Service Order:</strong></td>
                                <td>{{ ucfirst(str_replace('_', ' ', $settings->service_order)) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Group by Category:</strong></td>
                                <td>{{ $settings->group_by_category ? 'Yes' : 'No' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-toggle-on mr-2"></i>Display Features
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="feature-list">
                                    <div class="feature-item">
                                        <i class="fas fa-{{ $settings->show_pricing ? 'check text-success' : 'times text-muted' }} mr-2"></i>
                                        Pricing
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-{{ $settings->show_duration ? 'check text-success' : 'times text-muted' }} mr-2"></i>
                                        Duration
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-{{ $settings->show_description ? 'check text-success' : 'times text-muted' }} mr-2"></i>
                                        Description
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-{{ $settings->show_images ? 'check text-success' : 'times text-muted' }} mr-2"></i>
                                        Images
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-list">
                                    <div class="feature-item">
                                        <i class="fas fa-{{ $settings->enable_quick_booking ? 'check text-success' : 'times text-muted' }} mr-2"></i>
                                        Quick Booking
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-{{ $settings->enable_filtering ? 'check text-success' : 'times text-muted' }} mr-2"></i>
                                        Filtering
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-{{ $settings->enable_search ? 'check text-success' : 'times text-muted' }} mr-2"></i>
                                        Search
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-{{ $settings->show_availability_status ? 'check text-success' : 'times text-muted' }} mr-2"></i>
                                        Availability
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
/* Preview Container Styles */
.preview-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    min-height: 400px;
}

/* Layout Styles */
.layout-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat({{ $settings->grid_columns }}, 1fr);
}

.layout-list .service-card {
    display: flex;
    margin-bottom: 1rem;
}

.layout-list .service-card .service-image {
    width: 150px;
    flex-shrink: 0;
}

.layout-list .service-card .service-content {
    flex: 1;
    padding-left: 1rem;
}

.layout-carousel {
    display: flex;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 1rem;
}

.layout-carousel .service-card {
    min-width: 300px;
    flex-shrink: 0;
}

.layout-masonry {
    columns: {{ $settings->grid_columns }};
    column-gap: 1.5rem;
}

.layout-masonry .service-card {
    break-inside: avoid;
    margin-bottom: 1.5rem;
}

/* Service Card Styles */
.service-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.service-card.style-modern {
    border: none;
}

.service-card.style-classic {
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.service-card.style-minimal {
    box-shadow: none;
    border: 1px solid #e9ecef;
}

.service-card.style-creative {
    border-radius: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.service-image {
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.service-image-placeholder {
    color: #6c757d;
    font-size: 3rem;
}

.service-content {
    padding: 1.5rem;
}

.service-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.service-card.style-creative .service-title {
    color: white;
}

.service-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.service-card.style-creative .service-description {
    color: rgba(255,255,255,0.9);
}

.service-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.service-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #28a745;
}

.service-card.style-creative .service-price {
    color: #ffd700;
}

.service-duration {
    color: #6c757d;
    font-size: 0.9rem;
}

.service-card.style-creative .service-duration {
    color: rgba(255,255,255,0.8);
}

.service-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-book {
    flex: 1;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.category-title {
    color: #2c3e50;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.feature-item {
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

/* Mobile View */
.mobile-view .layout-grid {
    grid-template-columns: 1fr;
}

.mobile-view .layout-masonry {
    columns: 1;
}

.mobile-view .layout-carousel .service-card {
    min-width: 280px;
}

/* Responsive */
@media (max-width: 768px) {
    .layout-grid {
        grid-template-columns: 1fr !important;
    }

    .layout-masonry {
        columns: 1 !important;
    }

    .layout-list .service-card {
        flex-direction: column;
    }

    .layout-list .service-card .service-image {
        width: 100%;
    }

    .layout-list .service-card .service-content {
        padding-left: 0;
        padding-top: 1rem;
    }
}
</style>
@stop

@section('js')
<script>
$(document).ready(function() {
    // Mobile view toggle
    $('#toggle-mobile-view').click(function() {
        const container = $('#preview-container');
        const button = $(this);

        if (container.hasClass('mobile-view')) {
            container.removeClass('mobile-view');
            button.html('<i class="fas fa-mobile-alt mr-2"></i>Mobile View');
            button.removeClass('btn-warning').addClass('btn-info');
        } else {
            container.addClass('mobile-view');
            button.html('<i class="fas fa-desktop mr-2"></i>Desktop View');
            button.removeClass('btn-info').addClass('btn-warning');
        }
    });

    // Simulate booking button clicks
    $('.btn-book').click(function(e) {
        e.preventDefault();
        const serviceName = $(this).closest('.service-card').find('.service-title').text();
        alert(`Booking simulation for: ${serviceName}\n\nThis would redirect to the booking page in the live version.`);
    });
});
</script>
@stop
