@extends('owner.layouts.app')

@section('title', 'Service Display Settings')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Service Display Settings</h1>
                <p class="text-muted">Configure how your services appear on your landing page</p>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('owner.landing-page.index') }}">Landing Page</a></li>
                    <li class="breadcrumb-item active">Service Display</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <!-- Quick Actions -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">Service Display Management</h5>
                                <p class="text-muted mb-0">Control how your services are presented to customers</p>
                            </div>
                            <div class="btn-group">
                                <a href="{{ route('owner.service-display.preview') }}" class="btn btn-info">
                                    <i class="fas fa-eye mr-2"></i>Preview
                                </a>
                                <a href="{{ route('owner.service-display.analytics') }}" class="btn btn-success">
                                    <i class="fas fa-chart-bar mr-2"></i>Analytics
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Display Settings -->
            <div class="col-md-8">
                <form action="{{ route('owner.service-display.update') }}" method="POST">
                    @csrf
                    @method('PUT')

                    <!-- Layout Configuration -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-th mr-2"></i>Layout Configuration
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="layout_type">Layout Type</label>
                                        <select name="layout_type" id="layout_type" class="form-control">
                                            <option value="grid" {{ $settings->layout_type == 'grid' ? 'selected' : '' }}>Grid</option>
                                            <option value="list" {{ $settings->layout_type == 'list' ? 'selected' : '' }}>List</option>
                                            <option value="carousel" {{ $settings->layout_type == 'carousel' ? 'selected' : '' }}>Carousel</option>
                                            <option value="masonry" {{ $settings->layout_type == 'masonry' ? 'selected' : '' }}>Masonry</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="grid_columns">Grid Columns</label>
                                        <select name="grid_columns" id="grid_columns" class="form-control">
                                            <option value="1" {{ $settings->grid_columns == 1 ? 'selected' : '' }}>1 Column</option>
                                            <option value="2" {{ $settings->grid_columns == 2 ? 'selected' : '' }}>2 Columns</option>
                                            <option value="3" {{ $settings->grid_columns == 3 ? 'selected' : '' }}>3 Columns</option>
                                            <option value="4" {{ $settings->grid_columns == 4 ? 'selected' : '' }}>4 Columns</option>
                                            <option value="6" {{ $settings->grid_columns == 6 ? 'selected' : '' }}>6 Columns</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="service_card_style">Card Style</label>
                                        <select name="service_card_style" id="service_card_style" class="form-control">
                                            <option value="modern" {{ $settings->service_card_style == 'modern' ? 'selected' : '' }}>Modern</option>
                                            <option value="classic" {{ $settings->service_card_style == 'classic' ? 'selected' : '' }}>Classic</option>
                                            <option value="minimal" {{ $settings->service_card_style == 'minimal' ? 'selected' : '' }}>Minimal</option>
                                            <option value="creative" {{ $settings->service_card_style == 'creative' ? 'selected' : '' }}>Creative</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="homepage_display_count">Services to Display</label>
                                        <input type="number" name="homepage_display_count" id="homepage_display_count"
                                               class="form-control" value="{{ $settings->homepage_display_count }}"
                                               min="1" max="20">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Display Options -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-cog mr-2"></i>Display Options
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Service Information</h6>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_pricing" id="show_pricing" class="form-check-input"
                                               value="1" {{ $settings->show_pricing ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_pricing">Show Pricing</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_duration" id="show_duration" class="form-check-input"
                                               value="1" {{ $settings->show_duration ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_duration">Show Duration</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_description" id="show_description" class="form-check-input"
                                               value="1" {{ $settings->show_description ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_description">Show Description</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_images" id="show_images" class="form-check-input"
                                               value="1" {{ $settings->show_images ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_images">Show Images</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_categories" id="show_categories" class="form-check-input"
                                               value="1" {{ $settings->show_categories ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_categories">Show Categories</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Additional Features</h6>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_service_icons" id="show_service_icons" class="form-check-input"
                                               value="1" {{ $settings->show_service_icons ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_service_icons">Show Service Icons</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_availability_status" id="show_availability_status" class="form-check-input"
                                               value="1" {{ $settings->show_availability_status ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_availability_status">Show Availability Status</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_special_offers" id="show_special_offers" class="form-check-input"
                                               value="1" {{ $settings->show_special_offers ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_special_offers">Show Special Offers</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_reviews_rating" id="show_reviews_rating" class="form-check-input"
                                               value="1" {{ $settings->show_reviews_rating ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_reviews_rating">Show Reviews & Rating</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Service Organization -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-sort mr-2"></i>Service Organization
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="service_order">Service Order</label>
                                        <select name="service_order" id="service_order" class="form-control">
                                            <option value="manual" {{ $settings->service_order == 'manual' ? 'selected' : '' }}>Manual Order</option>
                                            <option value="alphabetical" {{ $settings->service_order == 'alphabetical' ? 'selected' : '' }}>Alphabetical</option>
                                            <option value="price_low_high" {{ $settings->service_order == 'price_low_high' ? 'selected' : '' }}>Price: Low to High</option>
                                            <option value="price_high_low" {{ $settings->service_order == 'price_high_low' ? 'selected' : '' }}>Price: High to Low</option>
                                            <option value="duration" {{ $settings->service_order == 'duration' ? 'selected' : '' }}>Duration</option>
                                            <option value="popularity" {{ $settings->service_order == 'popularity' ? 'selected' : '' }}>Popularity</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check mt-4">
                                            <input type="checkbox" name="group_by_category" id="group_by_category" class="form-check-input"
                                                   value="1" {{ $settings->group_by_category ? 'checked' : '' }}>
                                            <label class="form-check-label" for="group_by_category">Group by Category</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Interactive Features -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-mouse-pointer mr-2"></i>Interactive Features
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" name="enable_filtering" id="enable_filtering" class="form-check-input"
                                               value="1" {{ $settings->enable_filtering ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_filtering">Enable Filtering</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="enable_sorting" id="enable_sorting" class="form-check-input"
                                               value="1" {{ $settings->enable_sorting ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_sorting">Enable Sorting</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="enable_search" id="enable_search" class="form-check-input"
                                               value="1" {{ $settings->enable_search ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_search">Enable Search</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" name="enable_quick_booking" id="enable_quick_booking" class="form-check-input"
                                               value="1" {{ $settings->enable_quick_booking ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_quick_booking">Enable Quick Booking</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="show_booking_calendar" id="show_booking_calendar" class="form-check-input"
                                               value="1" {{ $settings->show_booking_calendar ? 'checked' : '' }}>
                                        <label class="form-check-label" for="show_booking_calendar">Show Booking Calendar</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="booking_button_style">Booking Button Style</label>
                                        <select name="booking_button_style" id="booking_button_style" class="form-control">
                                            <option value="primary" {{ $settings->booking_button_style == 'primary' ? 'selected' : '' }}>Primary</option>
                                            <option value="secondary" {{ $settings->booking_button_style == 'secondary' ? 'selected' : '' }}>Secondary</option>
                                            <option value="outline" {{ $settings->booking_button_style == 'outline' ? 'selected' : '' }}>Outline</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="booking_button_text">Booking Button Text</label>
                                        <input type="text" name="booking_button_text" id="booking_button_text"
                                               class="form-control" value="{{ $settings->booking_button_text }}"
                                               placeholder="Book Now">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO & Analytics -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-search mr-2"></i>SEO & Analytics
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" name="enable_service_seo" id="enable_service_seo" class="form-check-input"
                                               value="1" {{ $settings->enable_service_seo ? 'checked' : '' }}>
                                        <label class="form-check-label" for="enable_service_seo">Enable Service SEO</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="generate_service_sitemap" id="generate_service_sitemap" class="form-check-input"
                                               value="1" {{ $settings->generate_service_sitemap ? 'checked' : '' }}>
                                        <label class="form-check-label" for="generate_service_sitemap">Generate Service Sitemap</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input type="checkbox" name="track_service_analytics" id="track_service_analytics" class="form-check-input"
                                               value="1" {{ $settings->track_service_analytics ? 'checked' : '' }}>
                                        <label class="form-check-label" for="track_service_analytics">Track Service Analytics</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="mobile_optimized" id="mobile_optimized" class="form-check-input"
                                               value="1" {{ $settings->mobile_optimized ? 'checked' : '' }}>
                                        <label class="form-check-label" for="mobile_optimized">Mobile Optimized</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>Save Settings
                            </button>
                            <a href="{{ route('owner.service-display.preview') }}" class="btn btn-info ml-2">
                                <i class="fas fa-eye mr-2"></i>Preview Changes
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Service Management -->
            <div class="col-md-4">
                <!-- Individual Service Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list mr-2"></i>Individual Service Settings
                        </h3>
                    </div>
                    <div class="card-body">
                        @if($services->count() > 0)
                            <div class="service-list" style="max-height: 400px; overflow-y: auto;">
                                @foreach($services as $service)
                                    <div class="service-item border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ $service->name }}</h6>
                                                <small class="text-muted">
                                                    {{ $service->category ? $service->category->name : 'No Category' }}
                                                </small>
                                            </div>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary btn-sm"
                                                        onclick="editService({{ $service->id }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <span class="badge badge-{{ $service->is_public ? 'success' : 'secondary' }}">
                                                {{ $service->is_public ? 'Public' : 'Hidden' }}
                                            </span>
                                            @if($service->featured_on_landing)
                                                <span class="badge badge-warning">Featured</span>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No Services Found</h6>
                                <p class="text-muted">Create services to configure their display settings.</p>
                                <a href="{{ route('owner.services.create') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-2"></i>Create Service
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Bulk Actions -->
                @if($services->count() > 0)
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tasks mr-2"></i>Bulk Actions
                            </h3>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('owner.service-display.bulk-visibility') }}" method="POST" id="bulk-action-form">
                                @csrf
                                <div class="form-group">
                                    <label>Select Services:</label>
                                    <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                        @foreach($services as $service)
                                            <div class="form-check">
                                                <input type="checkbox" name="service_ids[]" value="{{ $service->id }}"
                                                       class="form-check-input service-checkbox" id="service_{{ $service->id }}">
                                                <label class="form-check-label" for="service_{{ $service->id }}">
                                                    {{ $service->name }}
                                                </label>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="bulk_action">Action:</label>
                                    <select name="action" id="bulk_action" class="form-control">
                                        <option value="show">Make Visible</option>
                                        <option value="hide">Hide from Landing</option>
                                        <option value="feature">Feature on Landing</option>
                                        <option value="unfeature">Remove from Featured</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-check mr-2"></i>Apply Action
                                </button>
                            </form>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Service Edit Modal -->
    <div class="modal fade" id="serviceEditModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Service Display Settings</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Content will be loaded via AJAX -->
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
.service-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.service-list {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.75rem;
}

.form-check {
    margin-bottom: 0.5rem;
}

.card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}
</style>
@stop

@section('js')
<script>
function editService(serviceId) {
    // Load service edit form via AJAX
    $.get(`/owner/service-display/service/${serviceId}/edit`, function(data) {
        $('#serviceEditModal .modal-body').html(data);
        $('#serviceEditModal').modal('show');
    }).fail(function() {
        alert('Error loading service settings');
    });
}

// Select all checkboxes
$('#select-all-services').change(function() {
    $('.service-checkbox').prop('checked', this.checked);
});

// Form validation
$('#bulk-action-form').submit(function(e) {
    if ($('.service-checkbox:checked').length === 0) {
        e.preventDefault();
        alert('Please select at least one service');
        return false;
    }
});

// Layout type change handler
$('#layout_type').change(function() {
    const layoutType = $(this).val();
    const gridColumnsGroup = $('#grid_columns').closest('.form-group');

    if (layoutType === 'list' || layoutType === 'carousel') {
        gridColumnsGroup.hide();
    } else {
        gridColumnsGroup.show();
    }
});

// Initialize layout visibility
$('#layout_type').trigger('change');
</script>
@stop
