{{-- Service Card Partial for Owner Service Display --}}
@php
    $cardStyle = $settings->service_card_style ?? 'modern';
@endphp

<div class="service-card h-100 {{ $cardStyle }}" data-service-id="{{ $service->id }}">

    {{-- Service Image --}}
    @if($settings->show_images && $service->images && $service->images->where('is_primary', true)->first())
        <div class="service-image">
            <img src="{{ $service->images->where('is_primary', true)->first()->image_url }}"
                 alt="{{ $service->name }}" class="card-img-top"
                 loading="{{ $settings->mobile_optimized ? 'lazy' : 'eager' }}">

            @if($settings->show_special_offers && $service->hasSpecialOffer())
                <div class="service-badge">
                    <span class="badge badge-danger">Special Offer</span>
                </div>
            @endif

            @if($service->featured_on_landing)
                <div class="featured-badge">
                    <span class="badge badge-warning">Featured</span>
                </div>
            @endif
        </div>
    @else
        <div class="service-image-placeholder">
            @if($settings->show_service_icons && $service->category && $service->category->icon)
                <i class="{{ $service->category->icon }} fa-3x text-muted"></i>
            @else
                <i class="fas fa-concierge-bell fa-3x text-muted"></i>
            @endif
        </div>
    @endif

    {{-- Card Body --}}
    <div class="card-body d-flex flex-column">

        {{-- Service Category --}}
        @if($settings->show_categories && $service->category)
            <div class="service-category mb-2">
                <span class="badge badge-secondary">{{ $service->category->name }}</span>
            </div>
        @endif

        {{-- Service Name --}}
        <h5 class="service-name card-title">{{ $service->name }}</h5>

        {{-- Service Description --}}
        @if($settings->show_description && $service->description)
            <p class="service-description card-text text-muted flex-grow-1">
                {{ Str::limit($service->description, 100) }}
            </p>
        @endif

        {{-- Service Details --}}
        <div class="service-details mt-auto">
            <div class="row mb-2">
                <div class="col-12">
                    {{-- Duration --}}
                    @if($settings->show_duration && $service->duration_minutes)
                        <small class="text-muted d-block">
                            <i class="fas fa-clock mr-1"></i>
                            Duration: {{ $service->duration_minutes }} minutes
                        </small>
                    @endif

                    {{-- Availability Status --}}
                    @if($settings->show_availability_status)
                        @php
                            $isAvailable = method_exists($service, 'isAvailableToday') ? $service->isAvailableToday() : true;
                        @endphp
                        <small class="availability-indicator d-block {{ $isAvailable ? 'text-success' : 'text-warning' }}">
                            <i class="fas fa-circle mr-1"></i>
                            {{ $isAvailable ? 'Available Today' : 'Limited Availability' }}
                        </small>
                    @endif

                    {{-- Reviews/Rating --}}
                    @if($settings->show_reviews_rating && isset($service->reviews_count) && $service->reviews_count > 0)
                        <div class="rating-stars mt-1">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= ($service->average_rating ?? 0) ? 'text-warning' : 'text-muted' }}"></i>
                            @endfor
                            <small class="text-muted ml-1">({{ $service->reviews_count }})</small>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Price and Actions --}}
            <div class="d-flex justify-content-between align-items-center">
                {{-- Price --}}
                @if($settings->show_pricing && $service->price)
                    <div class="service-price">
                        <span class="h6 text-primary mb-0">${{ number_format($service->price, 2) }}</span>
                    </div>
                @endif

                {{-- Quick Booking Button --}}
                @if($settings->enable_quick_booking)
                    <div class="service-actions">
                        <a href="{{ route('customer.bookings.create') }}"
                           class="btn btn-{{ $settings->booking_button_style }} btn-sm">
                            {{ $settings->booking_button_text }}
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

{{-- Service Card Styles --}}
<style>
/* Base Service Card Styles */
.service-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 1px solid #e3e6f0;
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Service Card Styles */
.service-card.modern {
    border-radius: 16px;
    border: none;
}

.service-card.classic {
    border-radius: 8px;
    border: 2px solid #e3e6f0;
}

.service-card.minimal {
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.service-card.creative {
    border-radius: 20px 4px 20px 4px;
    border: 1px solid #007bff;
}

/* Service Image Styles */
.service-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-image-placeholder {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Badge Styles */
.service-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

.featured-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
}

/* Content Styles */
.service-category .badge {
    font-size: 0.75em;
    font-weight: 500;
}

.service-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
    line-height: 1.3;
}

.service-description {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}

.service-details {
    border-top: 1px solid #f0f0f0;
    padding-top: 10px;
}

.service-price {
    font-weight: 600;
}

.availability-indicator {
    font-size: 0.8rem;
    font-weight: 500;
}

.rating-stars {
    font-size: 0.9rem;
}

.service-actions .btn {
    font-size: 0.9rem;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .service-image,
    .service-image-placeholder {
        height: 150px;
    }

    .service-name {
        font-size: 1rem;
    }

    .service-description {
        font-size: 0.85rem;
    }

    .service-actions .btn {
        font-size: 0.8rem;
        padding: 4px 8px;
    }
}

/* Animation for loading */
.service-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.service-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Special offer animation */
.service-badge .badge-danger {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
</style>
