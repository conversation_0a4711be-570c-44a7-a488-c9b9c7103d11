{{-- Service Grid Partial for Owner Service Display --}}
@php
    $layoutType = $settings->layout_type ?? 'grid';
    $gridColumns = $settings->grid_columns ?? 3;
    $cardStyle = $settings->service_card_style ?? 'modern';
@endphp

@if($services && $services->count() > 0)
    @if($layoutType === 'list')
        {{-- List Layout --}}
        <div class="service-list-container">
            @foreach($services as $service)
                <div class="service-list-item mb-3" data-service-id="{{ $service->id }}">
                    <div class="card service-card-list">
                        <div class="card-body">
                            <div class="row align-items-center">
                                {{-- Service Image --}}
                                @if($settings->show_images)
                                    <div class="col-md-3">
                                        @if($service->images && $service->images->where('is_primary', true)->first())
                                            <div class="service-image-list">
                                                <img src="{{ $service->images->where('is_primary', true)->first()->image_url }}"
                                                     alt="{{ $service->name }}" class="img-fluid rounded">

                                                @if($settings->show_special_offers && $service->hasSpecialOffer())
                                                    <div class="service-badge">
                                                        <span class="badge badge-danger">Special Offer</span>
                                                    </div>
                                                @endif
                                            </div>
                                        @else
                                            <div class="service-image-placeholder-list">
                                                @if($settings->show_service_icons && $service->category && $service->category->icon)
                                                    <i class="{{ $service->category->icon }} fa-2x text-muted"></i>
                                                @else
                                                    <i class="fas fa-concierge-bell fa-2x text-muted"></i>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                @endif

                                {{-- Service Content --}}
                                <div class="col-md-{{ $settings->show_images ? '6' : '9' }}">
                                    {{-- Service Name --}}
                                    <h5 class="service-name mb-2">{{ $service->name }}</h5>

                                    {{-- Category --}}
                                    @if($settings->show_categories && $service->category)
                                        <div class="service-category mb-2">
                                            <span class="badge badge-secondary">{{ $service->category->name }}</span>
                                        </div>
                                    @endif

                                    {{-- Description --}}
                                    @if($settings->show_description && $service->description)
                                        <p class="service-description text-muted mb-2">
                                            {{ Str::limit($service->description, 120) }}
                                        </p>
                                    @endif

                                    {{-- Service Details --}}
                                    <div class="service-meta">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                {{-- Duration --}}
                                                @if($settings->show_duration && $service->duration_minutes)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        Duration: {{ $service->duration_minutes }} minutes
                                                    </small>
                                                @endif

                                                {{-- Availability --}}
                                                @if($settings->show_availability_status)
                                                    @php
                                                        $isAvailable = $service->isAvailableToday();
                                                    @endphp
                                                    <small class="availability-indicator d-block {{ $isAvailable ? 'text-success' : 'text-warning' }}">
                                                        <i class="fas fa-circle mr-1"></i>
                                                        {{ $isAvailable ? 'Available Today' : 'Limited Availability' }}
                                                    </small>
                                                @endif
                                            </div>
                                            <div class="col-sm-6">
                                                {{-- Reviews/Rating --}}
                                                @if($settings->show_reviews_rating && $service->reviews_count > 0)
                                                    <div class="rating-stars mb-1">
                                                        @for($i = 1; $i <= 5; $i++)
                                                            <i class="fas fa-star {{ $i <= $service->average_rating ? 'text-warning' : 'text-muted' }}"></i>
                                                        @endfor
                                                        <small class="text-muted ml-1">({{ $service->reviews_count }})</small>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{-- Service Actions --}}
                                <div class="col-md-3 text-right">
                                    {{-- Price --}}
                                    @if($settings->show_pricing && $service->price)
                                        <div class="service-price mb-2">
                                            <span class="h5 text-primary">${{ number_format($service->price, 2) }}</span>
                                        </div>
                                    @endif

                                    {{-- Quick Booking Button --}}
                                    @if($settings->enable_quick_booking)
                                        <div class="service-actions">
                                            <a href="{{ route('customer.bookings.create') }}"
                                               class="btn btn-{{ $settings->booking_button_style }} btn-sm">
                                                {{ $settings->booking_button_text }}
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

    @elseif($layoutType === 'carousel')
        {{-- Carousel Layout --}}
        <div class="service-carousel-container">
            <div class="service-carousel d-flex">
                @foreach($services as $service)
                    <div class="service-carousel-item flex-shrink-0" data-service-id="{{ $service->id }}">
                        @include('owner.service-display.partials.service-card', ['service' => $service, 'settings' => $settings])
                    </div>
                @endforeach
            </div>
        </div>

    @elseif($layoutType === 'masonry')
        {{-- Masonry Layout --}}
        <div class="service-masonry-container">
            @foreach($services as $service)
                <div class="service-masonry-item" data-service-id="{{ $service->id }}">
                    @include('owner.service-display.partials.service-card', ['service' => $service, 'settings' => $settings])
                </div>
            @endforeach
        </div>

    @else
        {{-- Grid Layout (Default) --}}
        <div class="service-grid-container">
            <div class="row">
                @foreach($services as $service)
                    <div class="col-lg-{{ 12 / $gridColumns }} col-md-6 col-sm-12 mb-4" data-service-id="{{ $service->id }}">
                        @include('owner.service-display.partials.service-card', ['service' => $service, 'settings' => $settings])
                    </div>
                @endforeach
            </div>
        </div>
    @endif

@else
    {{-- No Services Message --}}
    <div class="no-services-message text-center py-5">
        <i class="fas fa-concierge-bell fa-4x text-muted mb-3"></i>
        <h4 class="text-muted">No Services Available</h4>
        <p class="text-muted">There are no services to display at the moment.</p>
    </div>
@endif

{{-- Styles for Service Grid --}}
<style>
/* List Layout Styles */
.service-card-list {
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.service-card-list:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.service-image-list {
    position: relative;
    height: 120px;
    overflow: hidden;
    border-radius: 8px;
}

.service-image-list img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.service-image-placeholder-list {
    height: 120px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Carousel Layout Styles */
.service-carousel {
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 1rem;
    scrollbar-width: thin;
}

.service-carousel::-webkit-scrollbar {
    height: 8px;
}

.service-carousel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.service-carousel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.service-carousel-item {
    min-width: 300px;
    max-width: 300px;
}

/* Masonry Layout Styles */
.service-masonry-container {
    columns: {{ $gridColumns }};
    column-gap: 1.5rem;
}

.service-masonry-item {
    break-inside: avoid;
    margin-bottom: 1.5rem;
}

/* Common Styles */
.service-name {
    font-weight: 600;
    color: #333;
}

.service-description {
    font-size: 0.9rem;
    line-height: 1.4;
}

.service-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 2;
}

.availability-indicator {
    font-size: 0.85rem;
    font-weight: 500;
}

.rating-stars {
    font-size: 0.9rem;
}

.service-price {
    font-weight: 600;
}

.service-actions .btn {
    font-size: 0.9rem;
    padding: 8px 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .service-carousel-item {
        min-width: 250px;
        max-width: 250px;
    }

    .service-masonry-container {
        columns: 1;
    }
}
</style>
