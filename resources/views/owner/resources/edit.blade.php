@extends('owner.layouts.app')

@section('title', 'Edit Resource')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Edit Resource</h1>
            <p class="text-muted">Update resource information</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.resources.show', $resource) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Resource
                </a>
                <a href="{{ route('owner.resources.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Resources
                </a>
            </div>
        </div>
    </div>
@stop

@section('content')
    <form action="{{ route('owner.resources.update', $resource) }}" method="POST" id="resourceForm">
        @csrf
        @method('PUT')

        <div class="row">
            <div class="col-md-8">
                {{-- Basic Information --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle mr-2"></i>
                            Basic Information
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Resource Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name', $resource->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="resource_type_id">Resource Type <span class="text-danger">*</span></label>
                                    <select class="form-control @error('resource_type_id') is-invalid @enderror"
                                            id="resource_type_id" name="resource_type_id" required>
                                        <option value="">Select resource type...</option>
                                        @foreach($resourceTypes as $type)
                                            <option value="{{ $type->id }}"
                                                    {{ old('resource_type_id', $resource->resource_type_id) == $type->id ? 'selected' : '' }}>
                                                {{ $type->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('resource_type_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3"
                                      placeholder="Describe this resource...">{{ old('description', $resource->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="capacity">Capacity <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                           id="capacity" name="capacity" value="{{ old('capacity', $resource->capacity) }}"
                                           min="1" max="1000" required>
                                    <small class="form-text text-muted">Maximum number of people this resource can accommodate</small>
                                    @error('capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="hourly_rate">Hourly Rate</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" class="form-control @error('hourly_rate') is-invalid @enderror"
                                               id="hourly_rate" name="hourly_rate" value="{{ old('hourly_rate', $resource->hourly_rate) }}"
                                               min="0" step="0.01" placeholder="0.00">
                                    </div>
                                    <small class="form-text text-muted">Optional hourly rate for this resource</small>
                                    @error('hourly_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Location & Assignment --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Location & Assignment
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="business_branch_id">Branch Location</label>
                            <select class="form-control @error('business_branch_id') is-invalid @enderror"
                                    id="business_branch_id" name="business_branch_id">
                                <option value="">Available at all branches</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                            {{ old('business_branch_id', $resource->business_branch_id) == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                        @if($branch->address)
                                            - {{ $branch->address }}
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">Select a specific branch or leave empty to make available at all branches</small>
                            @error('business_branch_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label>Associated Services</label>
                            <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                @if($services->count() > 0)
                                    @foreach($services as $service)
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="service_ids[]" value="{{ $service->id }}"
                                                   id="service_{{ $service->id }}"
                                                   {{ in_array($service->id, old('service_ids', $resource->services->pluck('id')->toArray())) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="service_{{ $service->id }}">
                                                {{ $service->name }}
                                                @if($service->category)
                                                    <small class="text-muted">({{ $service->category->name }})</small>
                                                @endif
                                            </label>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted mb-0">No services available. <a href="{{ route('owner.services.create') }}">Create a service first</a>.</p>
                                @endif
                            </div>
                            <small class="form-text text-muted">Select which services can use this resource</small>
                        </div>
                    </div>
                </div>

                {{-- Specifications --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list mr-2"></i>
                            Specifications
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="specifications-container">
                            @if($resource->specifications && count($resource->specifications) > 0)
                                @foreach($resource->specifications as $index => $spec)
                                    <div class="specification-item mb-2">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="specifications[]"
                                                   value="{{ $spec }}" placeholder="e.g., Color: Blue, Size: Large, etc.">
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-danger remove-specification" {{ $loop->first && $loop->count == 1 ? 'disabled' : '' }}>
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="specification-item mb-2">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="specifications[]"
                                               placeholder="e.g., Color: Blue, Size: Large, etc.">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-outline-danger remove-specification" disabled>
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="add-specification">
                            <i class="fas fa-plus mr-1"></i>
                            Add Specification
                        </button>
                        <small class="form-text text-muted">Add any additional specifications or features for this resource</small>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                {{-- Settings --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs mr-2"></i>
                            Settings
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active"
                                       name="is_active" value="1" {{ old('is_active', $resource->is_active) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                            <small class="form-text text-muted">Active resources are available for booking</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="requires_approval"
                                       name="requires_approval" value="1" {{ old('requires_approval', $resource->requires_approval) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="requires_approval">Requires Approval</label>
                            </div>
                            <small class="form-text text-muted">Bookings for this resource need manual approval</small>
                        </div>
                    </div>
                </div>

                {{-- Actions --}}
                <div class="card">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-save mr-2"></i>
                            Update Resource
                        </button>
                        <a href="{{ route('owner.resources.show', $resource) }}" class="btn btn-secondary btn-block">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
@stop

@section('adminlte_js')
    <script>
        $(document).ready(function() {
            // Add specification functionality
            $('#add-specification').on('click', function() {
                const container = $('#specifications-container');
                const newItem = `
                    <div class="specification-item mb-2">
                        <div class="input-group">
                            <input type="text" class="form-control" name="specifications[]"
                                   placeholder="e.g., Color: Blue, Size: Large, etc.">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-outline-danger remove-specification">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.append(newItem);
                updateRemoveButtons();
            });

            // Remove specification functionality
            $(document).on('click', '.remove-specification', function() {
                $(this).closest('.specification-item').remove();
                updateRemoveButtons();
            });

            // Update remove button states
            function updateRemoveButtons() {
                const items = $('.specification-item');
                if (items.length === 1) {
                    items.find('.remove-specification').prop('disabled', true);
                } else {
                    items.find('.remove-specification').prop('disabled', false);
                }
            }

            // Form validation
            $('#resourceForm').on('submit', function(e) {
                let isValid = true;

                // Check required fields
                const requiredFields = ['name', 'resource_type_id', 'capacity'];
                requiredFields.forEach(function(field) {
                    const input = $(`[name="${field}"]`);
                    if (!input.val()) {
                        input.addClass('is-invalid');
                        isValid = false;
                    } else {
                        input.removeClass('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    alert('Please fill in all required fields.');
                }
            });

            // Remove validation errors on input
            $('input, select, textarea').on('input change', function() {
                $(this).removeClass('is-invalid');
            });

            // Initialize remove button states
            updateRemoveButtons();
        });
    </script>
@stop
