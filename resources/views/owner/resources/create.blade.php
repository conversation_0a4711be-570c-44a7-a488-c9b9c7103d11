@extends('owner.layouts.app')

@section('title', 'Create Resource')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Create Resource</h1>
            <p class="text-muted">Add a new resource to your business</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.resources.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Resources
                </a>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Display validation errors --}}
    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show">
            <h5><i class="icon fas fa-ban"></i> Validation Errors!</h5>
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    @endif

    {{-- Display success message --}}
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show">
            <h5><i class="icon fas fa-check"></i> Success!</h5>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    @endif

    <form action="{{ route('owner.resources.store') }}" method="POST" id="resourceForm">
        @csrf

        <div class="row">
            <div class="col-md-8">
                {{-- Basic Information --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle mr-2"></i>
                            Basic Information
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Resource Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="resource_type_id">Resource Type <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <select class="form-control @error('resource_type_id') is-invalid @enderror"
                                                id="resource_type_id" name="resource_type_id" required>
                                            <option value="">Select resource type...</option>
                                            @foreach($resourceTypes as $type)
                                                <option value="{{ $type->id }}"
                                                        data-icon="{{ $type->icon }}"
                                                        data-color="{{ $type->color }}"
                                                        {{ old('resource_type_id') == $type->id ? 'selected' : '' }}>
                                                    {{ $type->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-outline-secondary" data-toggle="modal" data-target="#createResourceTypeModal" title="Create New Resource Type">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    @error('resource_type_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Select the category that best describes this resource</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3"
                                      placeholder="Describe this resource...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="capacity">Capacity <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                           id="capacity" name="capacity" value="{{ old('capacity', 1) }}"
                                           min="1" max="1000" required>
                                    <small class="form-text text-muted">Maximum number of people this resource can accommodate</small>
                                    @error('capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="hourly_rate">Hourly Rate</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" class="form-control @error('hourly_rate') is-invalid @enderror"
                                               id="hourly_rate" name="hourly_rate" value="{{ old('hourly_rate') }}"
                                               min="0" step="0.01" placeholder="0.00">
                                    </div>
                                    <small class="form-text text-muted">Optional hourly rate for this resource</small>
                                    @error('hourly_rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Location & Assignment --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Location & Assignment
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="business_branch_id">Branch Location</label>
                            <select class="form-control @error('business_branch_id') is-invalid @enderror"
                                    id="business_branch_id" name="business_branch_id">
                                <option value="">Available at all branches</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                            {{ old('business_branch_id') == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                        @if($branch->address)
                                            - {{ $branch->address }}
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">Select a specific branch or leave empty to make available at all branches</small>
                            @error('business_branch_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label>Associated Services</label>
                            <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                @if($services->count() > 0)
                                    <div class="mb-2">
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="select-all-services">
                                            <i class="fas fa-check-square mr-1"></i>
                                            Select All
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all-services">
                                            <i class="fas fa-square mr-1"></i>
                                            Deselect All
                                        </button>
                                    </div>
                                    @foreach($services as $service)
                                        <div class="service-item border-bottom pb-2 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input service-checkbox" type="checkbox"
                                                       name="service_ids[]" value="{{ $service->id }}"
                                                       id="service_{{ $service->id }}"
                                                       {{ in_array($service->id, old('service_ids', [])) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="service_{{ $service->id }}">
                                                    <strong>{{ $service->name }}</strong>
                                                    @if($service->category)
                                                        <span class="badge badge-info ml-1">{{ $service->category->name }}</span>
                                                    @endif
                                                    <br>
                                                    <small class="text-muted">
                                                        Duration: {{ $service->duration_minutes }}min |
                                                        Price: ${{ number_format($service->base_price, 2) }}
                                                    </small>
                                                </label>
                                            </div>
                                            <div class="service-requirements ml-4" style="display: none;">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <label class="small">Quantity Required</label>
                                                        <input type="number" class="form-control form-control-sm"
                                                               name="service_requirements[{{ $service->id }}][quantity]"
                                                               value="1" min="1" max="10">
                                                    </div>
                                                    <div class="col-6">
                                                        <label class="small">Setup Time (min)</label>
                                                        <input type="number" class="form-control form-control-sm"
                                                               name="service_requirements[{{ $service->id }}][setup_time]"
                                                               value="0" min="0" max="60">
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-6">
                                                        <div class="custom-control custom-checkbox">
                                                            <input type="checkbox" class="custom-control-input"
                                                                   id="required_{{ $service->id }}"
                                                                   name="service_requirements[{{ $service->id }}][is_required]" value="1">
                                                            <label class="custom-control-label small" for="required_{{ $service->id }}">
                                                                Required Resource
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <label class="small">Cleanup Time (min)</label>
                                                        <input type="number" class="form-control form-control-sm"
                                                               name="service_requirements[{{ $service->id }}][cleanup_time]"
                                                               value="0" min="0" max="60">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted mb-0">No services available. <a href="{{ route('owner.services.create') }}">Create a service first</a>.</p>
                                @endif
                            </div>
                            <small class="form-text text-muted">Select which services can use this resource and configure their requirements</small>
                        </div>
                    </div>
                </div>

                {{-- Specifications --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list mr-2"></i>
                            Specifications
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="specifications-container">
                            <div class="specification-item mb-2">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="specifications[]"
                                           placeholder="e.g., Color: Blue, Size: Large, etc.">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-danger remove-specification" disabled>
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="add-specification">
                            <i class="fas fa-plus mr-1"></i>
                            Add Specification
                        </button>
                        <small class="form-text text-muted">Add any additional specifications or features for this resource</small>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                {{-- Settings --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs mr-2"></i>
                            Settings
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active"
                                       name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                            <small class="form-text text-muted">Active resources are available for booking</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="requires_approval"
                                       name="requires_approval" value="1" {{ old('requires_approval') ? 'checked' : '' }}>
                                <label class="custom-control-label" for="requires_approval">Requires Approval</label>
                            </div>
                            <small class="form-text text-muted">Bookings for this resource need manual approval</small>
                        </div>
                    </div>
                </div>

                {{-- Availability Rules --}}
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clock mr-2"></i>
                            Availability Rules
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>Operating Days</label>
                            <div class="row">
                                @php
                                    $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                                    $dayLabels = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                                @endphp
                                @foreach($days as $index => $day)
                                    <div class="col-6 col-md-12 mb-2">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   id="day_{{ $day }}" name="operating_days[]" value="{{ $day }}"
                                                   {{ in_array($day, old('operating_days', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'])) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="day_{{ $day }}">
                                                {{ $dayLabels[$index] }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <small class="form-text text-muted">Select the days this resource is available (defaults to weekdays if none selected)</small>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <div class="form-group">
                                    <label for="available_from">Available From</label>
                                    <input type="time" class="form-control" id="available_from"
                                           name="available_from" value="{{ old('available_from', '09:00') }}">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label for="available_until">Available Until</label>
                                    <input type="time" class="form-control" id="available_until"
                                           name="available_until" value="{{ old('available_until', '17:00') }}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="advance_booking_days">Advance Booking Limit</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="advance_booking_days"
                                       name="advance_booking_days" value="{{ old('advance_booking_days', 30) }}"
                                       min="1" max="365">
                                <div class="input-group-append">
                                    <span class="input-group-text">days</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">How many days in advance can this resource be booked</small>
                        </div>

                        <div class="form-group">
                            <label for="minimum_notice_hours">Minimum Notice Required</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="minimum_notice_hours"
                                       name="minimum_notice_hours" value="{{ old('minimum_notice_hours', 2) }}"
                                       min="0" max="168">
                                <div class="input-group-append">
                                    <span class="input-group-text">hours</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Minimum hours notice required before booking</small>
                        </div>
                    </div>
                </div>

                {{-- Actions --}}
                <div class="card">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary btn-block" onclick="logFormData()">
                            <i class="fas fa-save mr-2"></i>
                            Create Resource
                        </button>
                        <a href="{{ route('owner.resources.index') }}" class="btn btn-secondary btn-block">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>

    {{-- Create Resource Type Modal --}}
    <div class="modal fade" id="createResourceTypeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Resource Type</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="createResourceTypeForm">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="type_name">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="type_name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="type_description">Description</label>
                            <textarea class="form-control" id="type_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="type_icon">Icon</label>
                                    <select class="form-control" id="type_icon" name="icon">
                                        <option value="fas fa-cube">Default</option>
                                        <option value="fas fa-chair">Chair</option>
                                        <option value="fas fa-couch">Couch</option>
                                        <option value="fas fa-bed">Bed</option>
                                        <option value="fas fa-desk">Desk</option>
                                        <option value="fas fa-table">Table</option>
                                        <option value="fas fa-tools">Tools</option>
                                        <option value="fas fa-wrench">Wrench</option>
                                        <option value="fas fa-hammer">Hammer</option>
                                        <option value="fas fa-door-open">Room</option>
                                        <option value="fas fa-car">Vehicle</option>
                                        <option value="fas fa-laptop">Computer</option>
                                        <option value="fas fa-stethoscope">Medical</option>
                                        <option value="fas fa-cut">Beauty</option>
                                        <option value="fas fa-dumbbell">Fitness</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="type_color">Color</label>
                                    <input type="color" class="form-control" id="type_color" name="color" value="#007bff">
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div id="icon-preview" class="d-inline-block p-3 rounded" style="background-color: #007bff;">
                                <i class="fas fa-cube fa-2x text-white"></i>
                            </div>
                            <p class="mt-2 text-muted">Preview</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Resource Type</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    <script>
        $(document).ready(function() {
            // Add specification functionality
            $('#add-specification').on('click', function() {
                const container = $('#specifications-container');
                const newItem = `
                    <div class="specification-item mb-2">
                        <div class="input-group">
                            <input type="text" class="form-control" name="specifications[]"
                                   placeholder="e.g., Color: Blue, Size: Large, etc.">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-outline-danger remove-specification">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.append(newItem);
                updateRemoveButtons();
            });

            // Remove specification functionality
            $(document).on('click', '.remove-specification', function() {
                $(this).closest('.specification-item').remove();
                updateRemoveButtons();
            });

            // Update remove button states
            function updateRemoveButtons() {
                const items = $('.specification-item');
                if (items.length === 1) {
                    items.find('.remove-specification').prop('disabled', true);
                } else {
                    items.find('.remove-specification').prop('disabled', false);
                }
            }

            // Service selection functionality
            $('#select-all-services').on('click', function() {
                $('.service-checkbox').prop('checked', true).trigger('change');
            });

            $('#deselect-all-services').on('click', function() {
                $('.service-checkbox').prop('checked', false).trigger('change');
            });

            // Show/hide service requirements when checkbox is checked
            $('.service-checkbox').on('change', function() {
                const serviceItem = $(this).closest('.service-item');
                const requirements = serviceItem.find('.service-requirements');

                if ($(this).is(':checked')) {
                    requirements.slideDown();
                } else {
                    requirements.slideUp();
                }
            });

            // Resource type preview functionality
            $('#type_icon, #type_color').on('change', function() {
                updateIconPreview();
            });

            function updateIconPreview() {
                const icon = $('#type_icon').val();
                const color = $('#type_color').val();
                const preview = $('#icon-preview');

                preview.css('background-color', color);
                preview.find('i').attr('class', icon + ' fa-2x text-white');
            }

            // Create resource type form submission
            $('#createResourceTypeForm').on('submit', function(e) {
                e.preventDefault();

                const formData = $(this).serialize();
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();

                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Creating...');

                $.ajax({
                    url: '{{ route("owner.resource-types.store") }}',
                    method: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            // Add new option to resource type select
                            const newOption = `<option value="${response.resource_type.id}"
                                                      data-icon="${response.resource_type.icon}"
                                                      data-color="${response.resource_type.color}" selected>
                                                ${response.resource_type.name}
                                              </option>`;
                            $('#resource_type_id').append(newOption);

                            // Close modal and reset form
                            $('#createResourceTypeModal').modal('hide');
                            $('#createResourceTypeForm')[0].reset();
                            updateIconPreview();

                            // Show success message
                            showAlert('success', 'Resource type created successfully!');
                        } else {
                            showAlert('danger', response.message || 'Failed to create resource type');
                        }
                    },
                    error: function(xhr) {
                        const errors = xhr.responseJSON?.errors;
                        if (errors) {
                            let errorMessage = 'Validation errors:\n';
                            Object.keys(errors).forEach(key => {
                                errorMessage += `- ${errors[key][0]}\n`;
                            });
                            showAlert('danger', errorMessage);
                        } else {
                            showAlert('danger', 'Failed to create resource type. Please try again.');
                        }
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Reset modal when hidden
            $('#createResourceTypeModal').on('hidden.bs.modal', function() {
                $('#createResourceTypeForm')[0].reset();
                updateIconPreview();
            });

            // Enhanced form validation
            $('#resourceForm').on('submit', function(e) {
                let isValid = true;
                let errors = [];

                // Clean up empty specifications before validation
                $('input[name="specifications[]"]').each(function() {
                    if (!$(this).val() || $(this).val().trim() === '') {
                        $(this).remove();
                    }
                });

                // Check required fields
                const requiredFields = ['name', 'resource_type_id', 'capacity'];
                requiredFields.forEach(function(field) {
                    const input = $(`[name="${field}"]`);
                    if (!input.val()) {
                        input.addClass('is-invalid');
                        isValid = false;
                        errors.push(`${field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} is required`);
                    } else {
                        input.removeClass('is-invalid');
                    }
                });

                // Validate capacity
                const capacity = parseInt($('#capacity').val());
                if (capacity && (capacity < 1 || capacity > 1000)) {
                    $('#capacity').addClass('is-invalid');
                    isValid = false;
                    errors.push('Capacity must be between 1 and 1000');
                }

                // Validate hourly rate
                const hourlyRate = parseFloat($('#hourly_rate').val());
                if (hourlyRate && hourlyRate < 0) {
                    $('#hourly_rate').addClass('is-invalid');
                    isValid = false;
                    errors.push('Hourly rate cannot be negative');
                }

                // Validate availability times
                const availableFrom = $('#available_from').val();
                const availableUntil = $('#available_until').val();
                if (availableFrom && availableUntil && availableFrom >= availableUntil) {
                    $('#available_from, #available_until').addClass('is-invalid');
                    isValid = false;
                    errors.push('Available from time must be before available until time');
                }

                // Operating days are optional - will default to weekdays if none selected

                if (!isValid) {
                    e.preventDefault();
                    if (errors.length > 0) {
                        showAlert('danger', 'Please fix the following errors:\n• ' + errors.join('\n• '));
                    }

                    // Scroll to first error
                    const firstError = $('.is-invalid').first();
                    if (firstError.length) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 500);
                    }
                } else {
                    // Show loading state
                    const submitBtn = $(this).find('button[type="submit"]');
                    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Creating Resource...');
                }
            });

            // Remove validation errors on input
            $('input, select, textarea').on('input change', function() {
                $(this).removeClass('is-invalid');
            });

            // Real-time name validation
            $('#name').on('input', function() {
                const name = $(this).val().trim();
                if (name.length > 0 && name.length < 3) {
                    $(this).addClass('is-invalid');
                    showValidationMessage($(this), 'Resource name must be at least 3 characters long');
                } else if (name.length > 100) {
                    $(this).addClass('is-invalid');
                    showValidationMessage($(this), 'Resource name cannot exceed 100 characters');
                } else {
                    $(this).removeClass('is-invalid');
                    hideValidationMessage($(this));
                }
            });

            // Capacity input formatting
            $('#capacity').on('input', function() {
                let value = parseInt($(this).val());
                if (isNaN(value) || value < 1) {
                    $(this).val(1);
                } else if (value > 1000) {
                    $(this).val(1000);
                }
            });

            // Hourly rate formatting
            $('#hourly_rate').on('input', function() {
                let value = parseFloat($(this).val());
                if (value < 0) {
                    $(this).val(0);
                }
            });

            // Auto-generate resource name suggestions
            $('#resource_type_id').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                const typeName = selectedOption.text();
                const nameField = $('#name');

                if (!nameField.val() && typeName && typeName !== 'Select resource type...') {
                    // Generate suggestions
                    const suggestions = [
                        `${typeName} #1`,
                        `Main ${typeName}`,
                        `Premium ${typeName}`,
                        `Standard ${typeName}`
                    ];

                    // Show suggestions as placeholder
                    nameField.attr('placeholder', `e.g., ${suggestions.join(', ')}`);
                }
            });

            // Utility functions
            function showAlert(type, message) {
                const alertHtml = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message.replace(/\n/g, '<br>')}
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                `;

                // Remove existing alerts
                $('.alert').remove();

                // Add new alert at the top of the form
                $('.card-body').first().prepend(alertHtml);

                // Auto-dismiss after 5 seconds
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }

            function showValidationMessage(element, message) {
                hideValidationMessage(element);
                element.after(`<div class="invalid-feedback d-block">${message}</div>`);
            }

            function hideValidationMessage(element) {
                element.siblings('.invalid-feedback').remove();
            }

            // Initialize tooltips
            $('[title]').tooltip();

            // Initialize the form
            updateIconPreview();

            // Set minimum date for advance booking
            const today = new Date();
            today.setDate(today.getDate() + 1);
            $('#advance_booking_days').attr('min', 1);
        });

        // Debug function to log form data
        function logFormData() {
            const formData = new FormData($('#resourceForm')[0]);
            const formObject = {};

            for (let [key, value] of formData.entries()) {
                if (formObject[key]) {
                    if (Array.isArray(formObject[key])) {
                        formObject[key].push(value);
                    } else {
                        formObject[key] = [formObject[key], value];
                    }
                } else {
                    formObject[key] = value;
                }
            }

            console.log('Form Data Being Submitted:', formObject);
            console.log('Operating Days:', formObject['operating_days[]']);
            console.log('Available From:', formObject['available_from']);
            console.log('Available Until:', formObject['available_until']);
        }

    </script>
@stop
