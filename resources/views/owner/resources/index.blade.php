@extends('owner.layouts.app')

@section('title', 'Resources')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Resources</h1>
            <p class="text-muted">Manage your business resources and equipment</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.resource-types.index') }}" class="btn btn-info">
                    <i class="fas fa-tags mr-2"></i>
                    Resource Types
                </a>
                <a href="{{ route('owner.resources.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    Add Resource
                </a>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Quick Stats --}}
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total'] }}</h3>
                    <p>Total Resources</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tools"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['active'] }}</h3>
                    <p>Active Resources</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['today_bookings'] }}</h3>
                    <p>Today's Bookings</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $stats['utilization_rate'] }}%</h3>
                    <p>Utilization Rate</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Filters --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ request('search') }}" placeholder="Resource name or description">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">Type</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">All Types</option>
                                @foreach($resourceTypes as $resourceType)
                                    <option value="{{ $resourceType->id }}"
                                            {{ request('type') == $resourceType->id ? 'selected' : '' }}>
                                        {{ $resourceType->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="branch">Branch</label>
                            <select class="form-control" id="branch" name="branch">
                                <option value="">All Branches</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                            {{ request('branch') == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search mr-1"></i>
                                    Filter
                                </button>
                                @if(request()->hasAny(['search', 'type', 'status', 'branch']))
                                    <a href="{{ route('owner.resources.index') }}" class="btn btn-secondary ml-1">
                                        <i class="fas fa-times mr-1"></i>
                                        Clear
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Resources List --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-tools mr-2"></i>
                Resources List
            </h3>
            <div class="card-tools">
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-secondary" id="bulk-actions-btn" style="display: none;">
                        <i class="fas fa-tasks mr-1"></i>
                        Bulk Actions
                    </button>
                    <button type="button" class="btn btn-sm btn-info" onclick="refreshResourcesList()">
                        <i class="fas fa-sync-alt mr-1"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if($resources->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th width="30">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="select-all">
                                        <label class="custom-control-label" for="select-all"></label>
                                    </div>
                                </th>
                                <th>Resource</th>
                                <th>Type</th>
                                <th>Branch</th>
                                <th>Capacity</th>
                                <th>Status</th>
                                <th>Services</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($resources as $resource)
                                <tr>
                                    <td>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input resource-checkbox"
                                                   id="resource-{{ $resource->id }}" value="{{ $resource->id }}">
                                            <label class="custom-control-label" for="resource-{{ $resource->id }}"></label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="resource-icon text-white mr-3"
                                                 style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: {{ $resource->resourceType->color ?? '#007bff' }};">
                                                <i class="{{ $resource->resourceType->icon ?? 'fas fa-cube' }}"></i>
                                            </div>
                                            <div>
                                                <strong>{{ $resource->name }}</strong>
                                                @if($resource->description)
                                                    <br><small class="text-muted">{{ Str::limit($resource->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge" style="background-color: {{ $resource->resourceType->color ?? '#007bff' }}; color: white;">
                                            {{ $resource->resourceType->name }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($resource->branch)
                                            <strong>{{ $resource->branch->name }}</strong>
                                            @if($resource->branch->address)
                                                <br><small class="text-muted">{{ Str::limit($resource->branch->address, 30) }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">All Branches</span>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $resource->capacity }}</strong>
                                        <br><small class="text-muted">{{ $resource->capacity == 1 ? 'person' : 'people' }}</small>
                                    </td>
                                    <td>
                                        @if($resource->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-secondary">Inactive</span>
                                        @endif
                                        @if($resource->requires_approval)
                                            <br><small class="text-warning"><i class="fas fa-check-circle mr-1"></i>Requires Approval</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($resource->services->count() > 0)
                                            <span class="badge badge-info">{{ $resource->services->count() }} service{{ $resource->services->count() > 1 ? 's' : '' }}</span>
                                            @if($resource->services->count() <= 3)
                                                @foreach($resource->services->take(3) as $service)
                                                    <br><small class="text-muted">{{ $service->name }}</small>
                                                @endforeach
                                            @else
                                                <br><small class="text-muted">{{ $resource->services->take(2)->pluck('name')->join(', ') }} +{{ $resource->services->count() - 2 }} more</small>
                                            @endif
                                        @else
                                            <span class="text-muted">No services assigned</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('owner.resources.show', $resource) }}" class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('owner.resources.edit', $resource) }}" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-secondary dropdown-toggle" data-toggle="dropdown" title="More Actions">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <div class="dropdown-menu">
                                                    <form action="{{ route('owner.resources.duplicate', $resource) }}" method="POST" style="display: inline;">
                                                        @csrf
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="fas fa-copy mr-2"></i>Duplicate
                                                        </button>
                                                    </form>
                                                    <div class="dropdown-divider"></div>
                                                    <form action="{{ route('owner.resources.destroy', $resource) }}" method="POST"
                                                          onsubmit="return confirm('Are you sure you want to delete this resource?')" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="dropdown-item text-danger">
                                                            <i class="fas fa-trash mr-2"></i>Delete
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                {{-- Pagination --}}
                @if($resources->hasPages())
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-sm-12 col-md-5">
                                <div class="dataTables_info">
                                    Showing {{ $resources->firstItem() }} to {{ $resources->lastItem() }} of {{ $resources->total() }} resources
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-7">
                                <div class="float-right">
                                    {{ $resources->appends(request()->query())->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            @else
                {{-- Empty State --}}
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-tools fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">No Resources Found</h4>
                    @if(request()->hasAny(['search', 'type', 'status', 'branch']))
                        <p class="text-muted">No resources match your current filters.</p>
                        <a href="{{ route('owner.resources.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times mr-1"></i>
                            Clear Filters
                        </a>
                    @else
                        <p class="text-muted">You haven't added any resources yet. Start by creating your first resource.</p>
                        <a href="{{ route('owner.resources.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus mr-1"></i>
                            Add Your First Resource
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
@stop

@section('adminlte_js')
    <script>
        $(document).ready(function() {
            // Handle select all checkbox
            $('#select-all').on('change', function() {
                const isChecked = $(this).is(':checked');
                $('.resource-checkbox').prop('checked', isChecked);
                toggleBulkActions();
            });

            // Handle individual checkboxes
            $('.resource-checkbox').on('change', function() {
                const totalCheckboxes = $('.resource-checkbox').length;
                const checkedCheckboxes = $('.resource-checkbox:checked').length;

                $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
                toggleBulkActions();
            });

            // Show/hide bulk actions button
            function toggleBulkActions() {
                const checkedCount = $('.resource-checkbox:checked').length;
                if (checkedCount > 0) {
                    $('#bulk-actions-btn').show().text(`Bulk Actions (${checkedCount})`);
                } else {
                    $('#bulk-actions-btn').hide();
                }
            }

            // Handle bulk actions
            $('#bulk-actions-btn').on('click', function() {
                const selectedIds = $('.resource-checkbox:checked').map(function() {
                    return $(this).val();
                }).get();

                if (selectedIds.length === 0) {
                    alert('Please select at least one resource.');
                    return;
                }

                // Show bulk actions modal or dropdown
                showBulkActionsMenu(selectedIds);
            });
        });

        function refreshResourcesList() {
            location.reload();
        }

        function showBulkActionsMenu(selectedIds) {
            const actions = [
                { label: 'Activate Selected', action: 'activate', icon: 'fas fa-check-circle', class: 'text-success' },
                { label: 'Deactivate Selected', action: 'deactivate', icon: 'fas fa-times-circle', class: 'text-warning' },
                { label: 'Delete Selected', action: 'delete', icon: 'fas fa-trash', class: 'text-danger' }
            ];

            let menuHtml = '<div class="dropdown-menu show" style="position: absolute; top: 100%; left: 0;">';
            actions.forEach(function(action) {
                menuHtml += `<a class="dropdown-item ${action.class}" href="#" onclick="executeBulkAction('${action.action}', [${selectedIds.join(',')}])">
                    <i class="${action.icon} mr-2"></i>${action.label}
                </a>`;
            });
            menuHtml += '</div>';

            // Remove existing dropdown
            $('.bulk-actions-dropdown').remove();

            // Add new dropdown
            $('#bulk-actions-btn').after(`<div class="bulk-actions-dropdown">${menuHtml}</div>`);

            // Hide dropdown when clicking outside
            $(document).one('click', function() {
                $('.bulk-actions-dropdown').remove();
            });
        }

        function executeBulkAction(action, selectedIds) {
            $('.bulk-actions-dropdown').remove();

            let confirmMessage = '';
            let url = '';

            switch(action) {
                case 'activate':
                    confirmMessage = `Are you sure you want to activate ${selectedIds.length} resource(s)?`;
                    url = '/owner/resources/bulk-activate';
                    break;
                case 'deactivate':
                    confirmMessage = `Are you sure you want to deactivate ${selectedIds.length} resource(s)?`;
                    url = '/owner/resources/bulk-deactivate';
                    break;
                case 'delete':
                    confirmMessage = `Are you sure you want to delete ${selectedIds.length} resource(s)? This action cannot be undone.`;
                    url = '/owner/resources/bulk-delete';
                    break;
            }

            if (confirm(confirmMessage)) {
                // Create form and submit
                const form = $('<form>', {
                    method: 'POST',
                    action: url
                });

                form.append($('<input>', {
                    type: 'hidden',
                    name: '_token',
                    value: '{{ csrf_token() }}'
                }));

                selectedIds.forEach(function(id) {
                    form.append($('<input>', {
                        type: 'hidden',
                        name: 'resource_ids[]',
                        value: id
                    }));
                });

                $('body').append(form);
                form.submit();
            }
        }
    </script>
@stop