@extends('owner.layouts.app')

@section('title', 'Resource Details')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>{{ $resource->name }}</h1>
            <p class="text-muted">Resource details and management</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.resources.edit', $resource) }}" class="btn btn-warning">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Resource
                </a>
                <a href="{{ route('owner.resources.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Resources
                </a>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-md-8">
            {{-- Resource Information --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Resource Information
                    </h3>
                    <div class="card-tools">
                        @if($resource->is_active)
                            <span class="badge badge-success">Active</span>
                        @else
                            <span class="badge badge-secondary">Inactive</span>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="resource-icon text-white mr-3"
                                     style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: {{ $resource->resourceType->color ?? '#007bff' }};">
                                    <i class="{{ $resource->resourceType->icon ?? 'fas fa-cube' }} fa-2x"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1">{{ $resource->name }}</h4>
                                    <span class="badge" style="background-color: {{ $resource->resourceType->color ?? '#007bff' }}; color: white;">
                                        {{ $resource->resourceType->name }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-6">
                                    <strong>{{ $resource->capacity }}</strong>
                                    <br><small class="text-muted">Capacity</small>
                                </div>
                                <div class="col-6">
                                    @if($resource->hourly_rate)
                                        <strong>${{ number_format($resource->hourly_rate, 2) }}</strong>
                                        <br><small class="text-muted">Hourly Rate</small>
                                    @else
                                        <span class="text-muted">No rate set</span>
                                        <br><small class="text-muted">Hourly Rate</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($resource->description)
                        <div class="mt-3">
                            <h6>Description</h6>
                            <p class="text-muted">{{ $resource->description }}</p>
                        </div>
                    @endif

                    @if($resource->branch)
                        <div class="mt-3">
                            <h6>Branch Location</h6>
                            <p class="mb-1"><strong>{{ $resource->branch->name }}</strong></p>
                            @if($resource->branch->address)
                                <p class="text-muted mb-0">{{ $resource->branch->address }}, {{ $resource->branch->city }}</p>
                            @endif
                        </div>
                    @endif

                    @if($resource->specifications && count($resource->specifications) > 0)
                        <div class="mt-3">
                            <h6>Specifications</h6>
                            <ul class="list-unstyled">
                                @foreach($resource->specifications as $spec)
                                    <li><i class="fas fa-check text-success mr-2"></i>{{ $spec }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if($resource->requires_approval)
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                This resource requires manual approval for bookings.
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Associated Services --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-concierge-bell mr-2"></i>
                        Associated Services
                    </h3>
                </div>
                <div class="card-body">
                    @if($resource->services->count() > 0)
                        <div class="row">
                            @foreach($resource->services as $service)
                                <div class="col-md-6 mb-3">
                                    <div class="border rounded p-3">
                                        <h6 class="mb-1">{{ $service->name }}</h6>
                                        @if($service->category)
                                            <small class="text-muted">{{ $service->category->name }}</small>
                                        @endif
                                        @if($service->duration_minutes)
                                            <br><small class="text-info">{{ $service->duration_minutes }} minutes</small>
                                        @endif
                                        @if($service->price)
                                            <br><small class="text-success">${{ number_format($service->price, 2) }}</small>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Services Associated</h5>
                            <p class="text-muted">This resource is not currently associated with any services.</p>
                            <a href="{{ route('owner.resources.edit', $resource) }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i>
                                Associate Services
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Upcoming Bookings --}}
            @if($upcomingBookings->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Upcoming Bookings
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Service</th>
                                        <th>Customer</th>
                                        <th>Duration</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($upcomingBookings as $booking)
                                        <tr>
                                            <td>
                                                <strong>{{ $booking->start_datetime->format('M j, Y') }}</strong>
                                                <br><small class="text-muted">{{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}</small>
                                            </td>
                                            <td>{{ $booking->bookingService->service->name }}</td>
                                            <td>
                                                @if($booking->bookingService->booking->customer)
                                                    {{ $booking->bookingService->booking->customer->name }}
                                                @else
                                                    {{ $booking->bookingService->booking->customer_name }}
                                                @endif
                                            </td>
                                            <td>{{ $booking->start_datetime->diffInMinutes($booking->end_datetime) }} min</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            {{-- Availability Blocks --}}
            @if($availabilityBlocks->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-ban mr-2"></i>
                            Availability Blocks
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#addBlockModal">
                                <i class="fas fa-plus mr-1"></i>
                                Add Block
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Reason</th>
                                        <th>Type</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($availabilityBlocks as $block)
                                        <tr>
                                            <td>
                                                <strong>{{ $block->start_datetime->format('M j, Y') }}</strong>
                                                <br><small class="text-muted">{{ $block->start_datetime->format('g:i A') }} - {{ $block->end_datetime->format('g:i A') }}</small>
                                            </td>
                                            <td>{{ $block->title }}</td>
                                            <td>
                                                <span class="badge badge-{{ $block->block_type === 'maintenance' ? 'warning' : 'info' }}">
                                                    {{ ucfirst($block->block_type) }}
                                                </span>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="removeBlock({{ $block->id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div class="col-md-4">
            {{-- Usage Statistics --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Usage Statistics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ $usageStats['today'] }}</h4>
                            <small class="text-muted">Today</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">{{ $usageStats['this_week'] }}</h4>
                            <small class="text-muted">This Week</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-warning">{{ $usageStats['this_month'] }}</h4>
                            <small class="text-muted">This Month</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $usageStats['total_bookings'] }}</h4>
                            <small class="text-muted">Total</small>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('owner.resources.edit', $resource) }}" class="btn btn-warning btn-block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Resource
                        </a>

                        <form action="{{ route('owner.resources.duplicate', $resource) }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-info btn-block">
                                <i class="fas fa-copy mr-2"></i>
                                Duplicate Resource
                            </button>
                        </form>

                        <button type="button" class="btn btn-secondary btn-block" data-toggle="modal" data-target="#availabilityModal">
                            <i class="fas fa-calendar-check mr-2"></i>
                            Check Availability
                        </button>

                        <a href="{{ route('owner.calendar.index', ['resource_id' => $resource->id]) }}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            View Calendar
                        </a>

                        <form action="{{ route('owner.resources.destroy', $resource) }}" method="POST"
                              onsubmit="return confirm('Are you sure you want to delete this resource? This action cannot be undone.')" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-block">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Resource
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Availability Check Modal --}}
    <div class="modal fade" id="availabilityModal" tabindex="-1" role="dialog" aria-labelledby="availabilityModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="availabilityModalLabel">
                        <i class="fas fa-calendar-check mr-2"></i>
                        Check Resource Availability
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="availabilityForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date"
                                           min="{{ date('Y-m-d') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_time">Start Time</label>
                                    <input type="time" class="form-control" id="start_time" name="start_time" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date"
                                           min="{{ date('Y-m-d') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_time">End Time</label>
                                    <input type="time" class="form-control" id="end_time" name="end_time" required>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search mr-2"></i>
                                Check Availability
                            </button>
                        </div>
                    </form>

                    {{-- Results Section --}}
                    <div id="availabilityResults" class="mt-4" style="display: none;">
                        <hr>
                        <div id="availabilityStatus"></div>
                        <div id="conflictsList"></div>
                        <div id="alternativesList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Add Block Modal --}}
    <div class="modal fade" id="addBlockModal" tabindex="-1" role="dialog" aria-labelledby="addBlockModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addBlockModalLabel">
                        <i class="fas fa-ban mr-2"></i>
                        Add Availability Block
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="addBlockForm">
                    @csrf
                    <input type="hidden" name="resource_id" value="{{ $resource->id }}">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="block_start_date">Start Date</label>
                                    <input type="date" class="form-control" id="block_start_date" name="start_date"
                                           min="{{ date('Y-m-d') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="block_start_time">Start Time</label>
                                    <input type="time" class="form-control" id="block_start_time" name="start_time" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="block_end_date">End Date</label>
                                    <input type="date" class="form-control" id="block_end_date" name="end_date"
                                           min="{{ date('Y-m-d') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="block_end_time">End Time</label>
                                    <input type="time" class="form-control" id="block_end_time" name="end_time" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="block_type">Block Type</label>
                            <select class="form-control" id="block_type" name="block_type" required>
                                <option value="">Select type...</option>
                                <option value="maintenance">Maintenance</option>
                                <option value="holiday">Holiday</option>
                                <option value="private_event">Private Event</option>
                                <option value="staff_break">Staff Break</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="block_title">Title</label>
                            <input type="text" class="form-control" id="block_title" name="title"
                                   placeholder="Enter a title for this block..." required>
                        </div>
                        <div class="form-group">
                            <label for="block_description">Description</label>
                            <textarea class="form-control" id="block_description" name="description" rows="3"
                                      placeholder="Describe the reason for this availability block..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Add Block
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    <script>
        $(document).ready(function() {
            // Set default times
            $('#start_time').val('09:00');
            $('#end_time').val('10:00');

            // Auto-update end date when start date changes
            $('#start_date').on('change', function() {
                const startDate = $(this).val();
                $('#end_date').attr('min', startDate);
                if (!$('#end_date').val() || $('#end_date').val() < startDate) {
                    $('#end_date').val(startDate);
                }
            });

            // Handle availability form submission
            $('#availabilityForm').on('submit', function(e) {
                e.preventDefault();
                checkResourceAvailability();
            });

            // Auto-update block end date when start date changes
            $('#block_start_date').on('change', function() {
                const startDate = $(this).val();
                $('#block_end_date').attr('min', startDate);
                if (!$('#block_end_date').val() || $('#block_end_date').val() < startDate) {
                    $('#block_end_date').val(startDate);
                }
            });

            // Handle add block form submission
            $('#addBlockForm').on('submit', function(e) {
                e.preventDefault();
                addAvailabilityBlock();
            });
        });

        function checkResourceAvailability() {
            const startDate = $('#start_date').val();
            const startTime = $('#start_time').val();
            const endDate = $('#end_date').val();
            const endTime = $('#end_time').val();

            if (!startDate || !startTime || !endDate || !endTime) {
                $('#availabilityStatus').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle mr-2"></i>Please fill in all date and time fields.</div>');
                $('#availabilityResults').show();
                return;
            }

            const startDateTime = startDate + ' ' + startTime + ':00';
            const endDateTime = endDate + ' ' + endTime + ':00';

            // Show loading state
            $('#availabilityResults').show();
            $('#availabilityStatus').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Checking availability...</div>');
            $('#conflictsList').empty();
            $('#alternativesList').empty();

            // Make AJAX request
            $.ajax({
                url: '{{ route("owner.resources.check-availability", $resource) }}',
                method: 'POST',
                data: {
                    start_datetime: startDateTime,
                    end_datetime: endDateTime,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    displayAvailabilityResults(response);
                },
                error: function(xhr) {
                    let errorMessage = 'An error occurred while checking availability.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    $('#availabilityStatus').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle mr-2"></i>' + errorMessage + '</div>');
                }
            });
        }

        function displayAvailabilityResults(response) {
            const statusDiv = $('#availabilityStatus');
            const conflictsDiv = $('#conflictsList');
            const alternativesDiv = $('#alternativesList');

            if (response.available) {
                statusDiv.html('<div class="alert alert-success"><i class="fas fa-check-circle mr-2"></i>Resource is available for the selected time period!</div>');
            } else {
                statusDiv.html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle mr-2"></i>Resource is not available for the selected time period.</div>');

                // Show conflicts
                if (response.conflicts && response.conflicts.length > 0) {
                    let conflictsHtml = '<div class="mt-3"><h6><i class="fas fa-times-circle text-danger mr-2"></i>Conflicts:</h6><ul class="list-group">';
                    response.conflicts.forEach(function(conflict) {
                        const startTime = new Date(conflict.start_datetime).toLocaleString();
                        const endTime = new Date(conflict.end_datetime).toLocaleString();

                        if (conflict.type === 'booking') {
                            conflictsHtml += `<li class="list-group-item">
                                <strong>Booking:</strong> ${conflict.customer_name}<br>
                                <small class="text-muted">${startTime} - ${endTime}</small>
                            </li>`;
                        } else if (conflict.type === 'block') {
                            conflictsHtml += `<li class="list-group-item">
                                <strong>Blocked:</strong> ${conflict.reason}<br>
                                <small class="text-muted">${startTime} - ${endTime}</small>
                            </li>`;
                        }
                    });
                    conflictsHtml += '</ul></div>';
                    conflictsDiv.html(conflictsHtml);
                }

                // Show availability blocks
                if (response.blocks && response.blocks.length > 0) {
                    let blocksHtml = '<div class="mt-3"><h6><i class="fas fa-ban text-warning mr-2"></i>Availability Blocks:</h6><ul class="list-group">';
                    response.blocks.forEach(function(block) {
                        const startTime = new Date(block.start_datetime).toLocaleString();
                        const endTime = new Date(block.end_datetime).toLocaleString();
                        blocksHtml += `<li class="list-group-item">
                            <strong>Title:</strong> ${block.title || block.reason}<br>
                            <small class="text-muted">${startTime} - ${endTime}</small>
                        </li>`;
                    });
                    blocksHtml += '</ul></div>';
                    alternativesDiv.html(blocksHtml);
                }
            }
        }

        function addAvailabilityBlock() {
            const startDate = $('#block_start_date').val();
            const startTime = $('#block_start_time').val();
            const endDate = $('#block_end_date').val();
            const endTime = $('#block_end_time').val();
            const blockType = $('#block_type').val();
            const title = $('#block_title').val();
            const description = $('#block_description').val();

            if (!startDate || !startTime || !endDate || !endTime || !blockType || !title) {
                alert('Please fill in all required fields.');
                return;
            }

            const startDateTime = startDate + ' ' + startTime + ':00';
            const endDateTime = endDate + ' ' + endTime + ':00';

            // Make AJAX request
            $.ajax({
                url: '/owner/calendar/block',
                method: 'POST',
                data: {
                    resource_id: {{ $resource->id }},
                    title: title,
                    description: description,
                    start_datetime: startDateTime,
                    end_datetime: endDateTime,
                    block_type: blockType,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        $('#addBlockModal').modal('hide');
                        location.reload(); // Refresh the page to show the new block
                    } else {
                        alert('Error creating availability block: ' + (response.message || 'Unknown error'));
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'An error occurred while creating the availability block.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    alert(errorMessage);
                }
            });
        }

        function removeBlock(blockId) {
            if (confirm('Are you sure you want to remove this availability block?')) {
                // Create form and submit
                const form = $('<form>', {
                    method: 'POST',
                    action: `/owner/calendar/block/${blockId}`
                });

                form.append($('<input>', {
                    type: 'hidden',
                    name: '_token',
                    value: '{{ csrf_token() }}'
                }));

                form.append($('<input>', {
                    type: 'hidden',
                    name: '_method',
                    value: 'DELETE'
                }));

                $('body').append(form);
                form.submit();
            }
        }
    </script>
@stop