@extends('owner.layouts.app')

@section('title', 'Bookings')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Bookings</h1>
            <p class="text-muted">Manage your business bookings</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <div class="btn-group" role="group">
                    <a href="{{ route('owner.check-in.index') }}" class="btn btn-success" data-cross-navigate="check-in">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Check-In Dashboard
                    </a>
                    <a href="{{ route('owner.waiting-lists.index') }}" class="btn btn-warning" data-cross-navigate="waiting-lists">
                        <i class="fas fa-clock mr-2"></i>
                        Waiting Lists
                    </a>
                </div>
                <div class="btn-group ml-2" role="group">
                    <a href="{{ route('owner.calendar.index') }}" class="btn btn-info">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Calendar View
                    </a>
                    <a href="{{ route('owner.bookings.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        New Booking
                    </a>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Quick Stats Row --}}
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['today'] }}</h3>
                    <p>Today's Bookings</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <a href="{{ route('owner.bookings.index', ['from_date' => today()->format('Y-m-d'), 'to_date' => today()->format('Y-m-d')]) }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['completed'] }}</h3>
                    <p>Completed</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <a href="{{ route('owner.bookings.index', ['status' => 'completed']) }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['pending'] }}</h3>
                    <p>Pending</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
                <a href="{{ route('owner.bookings.index', ['status' => 'pending']) }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $stats['cancelled'] }}</h3>
                    <p>Cancelled</p>
                </div>
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <a href="{{ route('owner.bookings.index', ['status' => 'cancelled']) }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    {{-- Filters Card --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('owner.bookings.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="Booking # or customer name" value="{{ $request->search }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="pending" {{ $request->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="confirmed" {{ $request->status === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                <option value="in_progress" {{ $request->status === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                <option value="completed" {{ $request->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="cancelled" {{ $request->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                <option value="no_show" {{ $request->status === 'no_show' ? 'selected' : '' }}>No Show</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="payment_status">Payment</label>
                            <select class="form-control" id="payment_status" name="payment_status">
                                <option value="">All Payment Status</option>
                                <option value="pending" {{ $request->payment_status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="partial" {{ $request->payment_status === 'partial' ? 'selected' : '' }}>Partially Paid</option>
                                <option value="paid" {{ $request->payment_status === 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="refunded" {{ $request->payment_status === 'refunded' ? 'selected' : '' }}>Refunded</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="from_date">From Date</label>
                            <input type="date" class="form-control" id="from_date" name="from_date" value="{{ $request->from_date }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="to_date">To Date</label>
                            <input type="date" class="form-control" id="to_date" name="to_date" value="{{ $request->to_date }}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-12">
                        @if($request->hasAny(['search', 'status', 'payment_status', 'from_date', 'to_date']))
                            <a href="{{ route('owner.bookings.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times mr-1"></i>
                                Clear Filters
                            </a>
                            <span class="text-muted ml-2">
                                Showing filtered results
                                @if($request->search) for "{{ $request->search }}" @endif
                                @if($request->status) with status "{{ ucfirst($request->status) }}" @endif
                                @if($request->payment_status) with payment "{{ ucfirst(str_replace('_', ' ', $request->payment_status)) }}" @endif
                                @if($request->from_date || $request->to_date)
                                    from {{ $request->from_date ? \Carbon\Carbon::parse($request->from_date)->format('M j, Y') : 'beginning' }}
                                    to {{ $request->to_date ? \Carbon\Carbon::parse($request->to_date)->format('M j, Y') : 'now' }}
                                @endif
                            </span>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Bookings List --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-calendar-check mr-2"></i>
                Recent Bookings
            </h3>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Booking #</th>
                            <th>Customer</th>
                            <th>Services</th>
                            <th>Date & Time</th>
                            <th>Duration</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($bookings as $booking)
                            <tr>
                                <td>
                                    <strong>{{ $booking->booking_number }}</strong>
                                    <br><small class="text-muted">{{ $booking->created_at->format('M d, Y') }}</small>
                                </td>
                                <td>
                                    <strong>{{ $booking->customer_name }}</strong>
                                    <br><small class="text-muted">{{ $booking->customer_email }}</small>
                                    @if($booking->customer_phone)
                                        <br><small class="text-muted">{{ $booking->customer_phone }}</small>
                                    @endif
                                </td>
                                <td>
                                    @foreach($booking->bookingServices as $bookingService)
                                        <span class="badge badge-info">{{ $bookingService->service->name }}</span>
                                    @endforeach
                                </td>
                                <td>
                                    <strong>{{ $booking->start_datetime->format('M d, Y') }}</strong>
                                    <br>{{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}
                                    @if($booking->start_datetime->isToday())
                                        <br><span class="badge badge-primary">Today</span>
                                    @elseif($booking->start_datetime->isTomorrow())
                                        <br><span class="badge badge-info">Tomorrow</span>
                                    @elseif($booking->start_datetime->isPast())
                                        <br><span class="badge badge-secondary">Past</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge-secondary">{{ $booking->formatted_duration }}</span>
                                    <br><small class="text-muted">{{ $booking->participant_count }} {{ Str::plural('person', $booking->participant_count) }}</small>
                                </td>
                                <td>
                                    <strong>${{ number_format($booking->total_amount, 2) }}</strong>
                                    @if($booking->paid_amount > 0)
                                        <br><small class="text-success">Paid: ${{ number_format($booking->paid_amount, 2) }}</small>
                                    @endif
                                    @if($booking->remaining_amount > 0)
                                        <br><small class="text-danger">Due: ${{ number_format($booking->remaining_amount, 2) }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge" style="background-color: {{ $booking->status_color }}; color: white;">
                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                    </span>
                                    <br><span class="badge" style="background-color: {{ $booking->payment_status_color }}; color: white;">
                                        {{ ucfirst(str_replace('_', ' ', $booking->payment_status)) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group-vertical" role="group">
                                        <a href="{{ route('owner.bookings.show', $booking) }}" class="btn btn-sm btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($booking->can_be_checked_in)
                                            <form method="POST" action="{{ route('owner.bookings.check-in', $booking) }}" style="display: inline;">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-success" title="Check In">
                                                    <i class="fas fa-sign-in-alt"></i>
                                                </button>
                                            </form>
                                        @endif
                                        @if($booking->can_be_checked_out)
                                            <form method="POST" action="{{ route('owner.bookings.check-out', $booking) }}" style="display: inline;">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-primary" title="Check Out">
                                                    <i class="fas fa-sign-out-alt"></i>
                                                </button>
                                            </form>
                                        @endif
                                        @if($booking->can_be_edited)
                                            <a href="{{ route('owner.bookings.edit', $booking) }}" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        @endif
                                        @if($booking->can_be_cancelled)
                                            <button type="button" class="btn btn-sm btn-danger" title="Cancel"
                                                    onclick="cancelBooking({{ $booking->id }})">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                                    <h4>No bookings found</h4>
                                    <p class="text-muted">No bookings found for the selected criteria.</p>
                                    <a href="{{ route('owner.bookings.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus mr-2"></i>
                                        Create Booking
                                    </a>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            @if($bookings->hasPages())
                <div class="card-footer">
                    {{ $bookings->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
function cancelBooking(bookingId) {
    Swal.fire({
        title: 'Cancel Booking',
        text: 'Please provide a reason for cancellation:',
        input: 'textarea',
        inputPlaceholder: 'Cancellation reason...',
        showCancelButton: true,
        confirmButtonText: 'Cancel Booking',
        confirmButtonColor: '#dc3545',
        cancelButtonText: 'Keep Booking',
        inputValidator: (value) => {
            if (!value) {
                return 'You need to provide a cancellation reason!'
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/owner/bookings/${bookingId}/cancel`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';

            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'cancellation_reason';
            reasonInput.value = result.value;

            form.appendChild(csrfToken);
            form.appendChild(reasonInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[action="{{ route('owner.bookings.index') }}"]');
    const autoSubmitFields = ['status', 'payment_status', 'from_date', 'to_date'];

    autoSubmitFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.addEventListener('change', function() {
                form.submit();
            });
        }
    });

    // Integration features
    loadQuickActions();
    setupBookingStatusHandlers();
});

function loadQuickActions() {
    $.get('/owner/integration/quick-actions/bookings')
        .done(function(response) {
            if (response.success && response.actions.length > 0) {
                displayQuickActions(response.actions);
            }
        })
        .fail(function(xhr) {
            console.error('Failed to load quick actions:', xhr);
        });
}

function displayQuickActions(actions) {
    const container = $('#integration-notifications');

    actions.forEach(action => {
        const alert = $(`
            <div class="alert alert-${action.color} alert-dismissible fade show" role="alert">
                <i class="${action.icon} mr-2"></i>
                <strong>${action.title}:</strong> ${action.description}
                ${action.url ? `<a href="${action.url}" class="btn btn-sm btn-outline-${action.color} ml-2">View</a>` : ''}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `);
        container.append(alert);
    });

    if (actions.length > 0) {
        container.show();
    }
}

function setupBookingStatusHandlers() {
    // Listen for booking status changes
    $(document).on('change', '.booking-status-select', function() {
        const bookingId = $(this).data('booking-id');
        const oldStatus = $(this).data('old-status');
        const newStatus = $(this).val();

        if (oldStatus !== newStatus) {
            updateBookingStatus(bookingId, oldStatus, newStatus);
        }
    });
}

function updateBookingStatus(bookingId, oldStatus, newStatus) {
    $.post('/owner/bookings/' + bookingId + '/status', {
        status: newStatus,
        _token: '{{ csrf_token() }}'
    })
    .done(function(response) {
        if (response.success) {
            // Trigger integration event
            if (window.ownerIntegration) {
                window.ownerIntegration.triggerBookingStatusChange({
                    id: bookingId,
                    booking_number: 'BK-' + bookingId
                }, oldStatus, newStatus, response.integration_actions);
            }

            // Update UI
            location.reload();
        } else {
            alert('Failed to update booking status: ' + response.message);
        }
    })
    .fail(function(xhr) {
        const response = xhr.responseJSON;
        alert('Error: ' + (response.message || 'Failed to update booking status'));
    });
}

function refreshBookingsTable() {
    // Reload the page to refresh bookings data
    location.reload();
}
</script>
@stop
