@extends('adminlte::page')

@section('title', 'Create New Booking')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <h1>Create New Booking</h1>
        <a href="{{ route('owner.bookings.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Bookings
        </a>
    </div>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-plus mr-2"></i>
                New Booking Details
            </h3>
        </div>
        <form method="POST" action="{{ route('owner.bookings.store') }}">
            @csrf
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <div class="row">
                    {{-- Customer Information --}}
                    <div class="col-md-6">
                        <h5 class="mb-3">Customer Information</h5>

                        <div class="form-group">
                            <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('customer_name') is-invalid @enderror"
                                   id="customer_name" name="customer_name" value="{{ old('customer_name') }}" required>
                            @error('customer_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="customer_email">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('customer_email') is-invalid @enderror"
                                   id="customer_email" name="customer_email" value="{{ old('customer_email') }}" required>
                            @error('customer_email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="customer_phone">Phone Number</label>
                            <input type="tel" class="form-control @error('customer_phone') is-invalid @enderror"
                                   id="customer_phone" name="customer_phone" value="{{ old('customer_phone') }}">
                            @error('customer_phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Existing Customer Selection --}}
                        @if($customers->count() > 0)
                            <div class="form-group">
                                <label for="existing_customer">Or Select Existing Customer</label>
                                <select class="form-control" id="existing_customer">
                                    <option value="">Select existing customer...</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}"
                                                data-name="{{ $customer->name }}"
                                                data-email="{{ $customer->email }}"
                                                data-phone="{{ $customer->phone }}">
                                            {{ $customer->name }} ({{ $customer->email }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif
                    </div>

                    {{-- Booking Information --}}
                    <div class="col-md-6">
                        <h5 class="mb-3">Booking Information</h5>

                        <div class="form-group">
                            <label for="start_datetime">Date & Time <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control @error('start_datetime') is-invalid @enderror"
                                   id="start_datetime" name="start_datetime" value="{{ old('start_datetime') }}" required>
                            @error('start_datetime')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="services">Services <span class="text-danger">*</span></label>
                            <select class="form-control @error('services') is-invalid @enderror"
                                    id="services" name="services[]" multiple required>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}"
                                            data-price="{{ $service->base_price }}"
                                            data-duration="{{ $service->duration_minutes }}"
                                            {{ in_array($service->id, old('services', [])) ? 'selected' : '' }}>
                                        {{ $service->name }} - ${{ number_format($service->base_price, 2) }} ({{ $service->duration_minutes }}min)
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple services</small>
                            @error('services')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="participant_count">Number of Participants <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('participant_count') is-invalid @enderror"
                                   id="participant_count" name="participant_count" value="{{ old('participant_count', 1) }}"
                                   min="1" required>
                            @error('participant_count')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="payment_status">Payment Status <span class="text-danger">*</span></label>
                            <select class="form-control @error('payment_status') is-invalid @enderror"
                                    id="payment_status" name="payment_status" required>
                                <option value="pending" {{ old('payment_status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="paid" {{ old('payment_status') === 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="partial" {{ old('payment_status') === 'partial' ? 'selected' : '' }}>Partially Paid</option>
                            </select>
                            @error('payment_status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror"
                                      id="notes" name="notes" rows="3" placeholder="Any special notes or requirements...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                {{-- Booking Summary --}}
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h5 class="mb-0">Booking Summary</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Selected Services:</strong>
                                        <div id="selected-services">
                                            <span class="text-muted">No services selected</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Total Duration:</strong>
                                        <div id="total-duration">
                                            <span class="text-muted">0 minutes</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>End Time:</strong>
                                        <div id="end-time">
                                            <span class="text-muted">Select date & time</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Total Amount:</strong>
                                        <div id="total-amount">
                                            <span class="text-muted">$0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Resource Availability Check --}}
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card" id="availability-card" style="display: none;">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-tools mr-2"></i>
                                    Resource Availability
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="availability-status">
                                    <div class="text-center">
                                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                        <p class="mt-2 text-muted">Checking resource availability...</p>
                                    </div>
                                </div>
                                <div id="resource-details" style="display: none;">
                                    <h6>Required Resources:</h6>
                                    <div id="resource-list"></div>
                                </div>
                                <div id="availability-actions" style="display: none;">
                                    <button type="button" class="btn btn-info btn-sm" onclick="findAlternatives()">
                                        <i class="fas fa-search mr-1"></i>
                                        Find Alternatives
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="findNextSlot()">
                                        <i class="fas fa-clock mr-1"></i>
                                        Find Next Available
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>
                    Create Booking
                </button>
                <a href="{{ route('owner.bookings.index') }}" class="btn btn-secondary ml-2">
                    <i class="fas fa-times mr-2"></i>
                    Cancel
                </a>
            </div>
        </form>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
document.addEventListener('DOMContentLoaded', function() {
    const servicesSelect = document.getElementById('services');
    const startDatetimeInput = document.getElementById('start_datetime');
    const existingCustomerSelect = document.getElementById('existing_customer');

    // Handle existing customer selection
    if (existingCustomerSelect) {
        existingCustomerSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                document.getElementById('customer_name').value = selectedOption.dataset.name || '';
                document.getElementById('customer_email').value = selectedOption.dataset.email || '';
                document.getElementById('customer_phone').value = selectedOption.dataset.phone || '';
            }
        });
    }

    // Update booking summary when services change
    servicesSelect.addEventListener('change', function() {
        updateBookingSummary();
        checkResourceAvailability();
    });
    startDatetimeInput.addEventListener('change', function() {
        updateBookingSummary();
        checkResourceAvailability();
    });

    function updateBookingSummary() {
        const selectedOptions = Array.from(servicesSelect.selectedOptions);
        const startDatetime = startDatetimeInput.value;

        // Update selected services
        const servicesDiv = document.getElementById('selected-services');
        if (selectedOptions.length > 0) {
            servicesDiv.innerHTML = selectedOptions.map(option =>
                `<span class="badge badge-info mr-1">${option.text.split(' - ')[0]}</span>`
            ).join('');
        } else {
            servicesDiv.innerHTML = '<span class="text-muted">No services selected</span>';
        }

        // Calculate totals
        let totalDuration = 0;
        let totalAmount = 0;

        selectedOptions.forEach(option => {
            totalDuration += parseInt(option.dataset.duration) || 0;
            totalAmount += parseFloat(option.dataset.price) || 0;
        });

        // Update duration
        const durationDiv = document.getElementById('total-duration');
        if (totalDuration > 0) {
            const hours = Math.floor(totalDuration / 60);
            const minutes = totalDuration % 60;
            let durationText = '';
            if (hours > 0) durationText += `${hours}h `;
            if (minutes > 0) durationText += `${minutes}m`;
            durationDiv.innerHTML = durationText || '0 minutes';
        } else {
            durationDiv.innerHTML = '<span class="text-muted">0 minutes</span>';
        }

        // Update end time
        const endTimeDiv = document.getElementById('end-time');
        if (startDatetime && totalDuration > 0) {
            const startDate = new Date(startDatetime);
            const endDate = new Date(startDate.getTime() + totalDuration * 60000);
            endTimeDiv.innerHTML = endDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        } else {
            endTimeDiv.innerHTML = '<span class="text-muted">Select date & time</span>';
        }

        // Update total amount
        const amountDiv = document.getElementById('total-amount');
        if (totalAmount > 0) {
            amountDiv.innerHTML = `$${totalAmount.toFixed(2)}`;
        } else {
            amountDiv.innerHTML = '<span class="text-muted">$0.00</span>';
        }
    }

    // Check resource availability
    function checkResourceAvailability() {
        const selectedOptions = Array.from(servicesSelect.selectedOptions);
        const startDatetime = startDatetimeInput.value;

        if (selectedOptions.length === 0 || !startDatetime) {
            document.getElementById('availability-card').style.display = 'none';
            return;
        }

        // Calculate total duration
        let totalDuration = 0;
        selectedOptions.forEach(option => {
            totalDuration += parseInt(option.dataset.duration) || 0;
        });

        if (totalDuration === 0) {
            document.getElementById('availability-card').style.display = 'none';
            return;
        }

        // Show availability card and loading state
        document.getElementById('availability-card').style.display = 'block';
        document.getElementById('availability-status').innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Checking resource availability...</p>
            </div>
        `;
        document.getElementById('resource-details').style.display = 'none';
        document.getElementById('availability-actions').style.display = 'none';

        // Prepare data for API call
        const serviceIds = selectedOptions.map(option => option.value);
        const formData = new FormData();
        formData.append('_token', '{{ csrf_token() }}');
        serviceIds.forEach(id => formData.append('service_ids[]', id));
        formData.append('start_datetime', startDatetime);
        formData.append('duration_minutes', totalDuration);

        // Make API call
        fetch('{{ route("owner.resources.availability.check") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            displayAvailabilityResults(data);
        })
        .catch(error => {
            console.error('Error checking availability:', error);
            document.getElementById('availability-status').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Error checking resource availability. Please try again.
                </div>
            `;
        });
    }

    function displayAvailabilityResults(data) {
        const statusDiv = document.getElementById('availability-status');
        const detailsDiv = document.getElementById('resource-details');
        const actionsDiv = document.getElementById('availability-actions');

        if (data.available) {
            statusDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle mr-2"></i>
                    All required resources are available for the selected time slot.
                </div>
            `;
        } else {
            statusDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Some resources are not available for the selected time slot.
                </div>
            `;
            actionsDiv.style.display = 'block';
        }

        // Show resource details
        if (data.resource_details && data.resource_details.length > 0) {
            let resourceHtml = '<div class="row">';
            data.resource_details.forEach(resource => {
                const statusClass = resource.is_available ? 'success' : 'danger';
                const statusIcon = resource.is_available ? 'check-circle' : 'times-circle';

                resourceHtml += `
                    <div class="col-md-6 mb-2">
                        <div class="border rounded p-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${resource.resource_name}</strong>
                                    <br><small class="text-muted">${resource.service_name}</small>
                                </div>
                                <div class="text-${statusClass}">
                                    <i class="fas fa-${statusIcon}"></i>
                                </div>
                            </div>
                            <div class="mt-1">
                                <small>
                                    Required: ${resource.required_quantity} |
                                    Available: ${resource.available_quantity}/${resource.total_capacity}
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });
            resourceHtml += '</div>';

            document.getElementById('resource-list').innerHTML = resourceHtml;
            detailsDiv.style.display = 'block';
        }
    }

    // Find alternatives function
    window.findAlternatives = function() {
        const selectedOptions = Array.from(servicesSelect.selectedOptions);
        if (selectedOptions.length === 0) return;

        // For simplicity, just show the first service's alternatives
        const firstServiceId = selectedOptions[0].value;
        const startDatetime = startDatetimeInput.value;
        let totalDuration = 0;
        selectedOptions.forEach(option => {
            totalDuration += parseInt(option.dataset.duration) || 0;
        });

        const formData = new FormData();
        formData.append('_token', '{{ csrf_token() }}');
        formData.append('service_id', firstServiceId);
        formData.append('start_datetime', startDatetime);
        formData.append('duration_minutes', totalDuration);

        fetch('{{ route("owner.resources.availability.alternatives") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.alternatives.length > 0) {
                let alternativesHtml = '<h6>Alternative Resources:</h6><div class="row">';
                data.alternatives.forEach(alt => {
                    alternativesHtml += `
                        <div class="col-md-4 mb-2">
                            <div class="card card-outline card-info">
                                <div class="card-body p-2">
                                    <strong>${alt.name}</strong>
                                    <br><small>${alt.type} - ${alt.branch}</small>
                                    <br><small>Capacity: ${alt.capacity} | Available: ${alt.available_quantity}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                alternativesHtml += '</div>';

                document.getElementById('resource-list').innerHTML = alternativesHtml;
            } else {
                alert('No alternative resources found for the selected time slot.');
            }
        })
        .catch(error => {
            console.error('Error finding alternatives:', error);
            alert('Error finding alternative resources. Please try again.');
        });
    };

    // Find next available slot function
    window.findNextSlot = function() {
        const selectedOptions = Array.from(servicesSelect.selectedOptions);
        if (selectedOptions.length === 0) return;

        const firstServiceId = selectedOptions[0].value;
        const startDatetime = startDatetimeInput.value;
        let totalDuration = 0;
        selectedOptions.forEach(option => {
            totalDuration += parseInt(option.dataset.duration) || 0;
        });

        const formData = new FormData();
        formData.append('_token', '{{ csrf_token() }}');
        formData.append('service_id', firstServiceId);
        formData.append('from_datetime', startDatetime);
        formData.append('duration_minutes', totalDuration);

        fetch('{{ route("owner.resources.availability.next-slot") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.available) {
                if (confirm(`Next available slot is at ${data.next_slot_formatted}. Would you like to use this time?`)) {
                    // Convert to local datetime format for input
                    const nextSlotDate = new Date(data.next_slot);
                    const localDateTime = new Date(nextSlotDate.getTime() - nextSlotDate.getTimezoneOffset() * 60000)
                        .toISOString().slice(0, 16);
                    startDatetimeInput.value = localDateTime;
                    updateBookingSummary();
                    checkResourceAvailability();
                }
            } else {
                alert('No available slots found within the next 30 days.');
            }
        })
        .catch(error => {
            console.error('Error finding next slot:', error);
            alert('Error finding next available slot. Please try again.');
        });
    };

    // Initialize summary
    updateBookingSummary();
});
</script>
@stop
