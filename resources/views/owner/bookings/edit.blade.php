@extends('adminlte::page')

@section('title', 'Edit Booking')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <h1>Edit Booking - {{ $booking->booking_number }}</h1>
        <div>
            <a href="{{ route('owner.bookings.show', $booking) }}" class="btn btn-info">
                <i class="fas fa-eye mr-2"></i>
                View Booking
            </a>
            <a href="{{ route('owner.bookings.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Bookings
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-edit mr-2"></i>
                Edit Booking Details
            </h3>
            <div class="card-tools">
                <span class="badge" style="background-color: {{ $booking->status_color }}; color: white;">
                    Current Status: {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                </span>
            </div>
        </div>
        <form method="POST" action="{{ route('owner.bookings.update', $booking) }}">
            @csrf
            @method('PUT')
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <div class="row">
                    {{-- Customer Information --}}
                    <div class="col-md-6">
                        <h5 class="mb-3">Customer Information</h5>

                        <div class="form-group">
                            <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('customer_name') is-invalid @enderror"
                                   id="customer_name" name="customer_name" value="{{ old('customer_name', $booking->customer_name) }}" required>
                            @error('customer_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="customer_email">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('customer_email') is-invalid @enderror"
                                   id="customer_email" name="customer_email" value="{{ old('customer_email', $booking->customer_email) }}" required>
                            @error('customer_email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="customer_phone">Phone Number</label>
                            <input type="tel" class="form-control @error('customer_phone') is-invalid @enderror"
                                   id="customer_phone" name="customer_phone" value="{{ old('customer_phone', $booking->customer_phone) }}">
                            @error('customer_phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        {{-- Existing Customer Selection --}}
                        @if($customers->count() > 0)
                            <div class="form-group">
                                <label for="existing_customer">Or Select Existing Customer</label>
                                <select class="form-control" id="existing_customer">
                                    <option value="">Select existing customer...</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}"
                                                data-name="{{ $customer->name }}"
                                                data-email="{{ $customer->email }}"
                                                data-phone="{{ $customer->phone }}"
                                                {{ $booking->customer_id == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }} ({{ $customer->email }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif
                    </div>

                    {{-- Booking Information --}}
                    <div class="col-md-6">
                        <h5 class="mb-3">Booking Information</h5>

                        <div class="form-group">
                            <label for="start_datetime">Date & Time <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control @error('start_datetime') is-invalid @enderror"
                                   id="start_datetime" name="start_datetime"
                                   value="{{ old('start_datetime', $booking->start_datetime->format('Y-m-d\TH:i')) }}" required>
                            @error('start_datetime')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="services">Services <span class="text-danger">*</span></label>
                            <select class="form-control @error('services') is-invalid @enderror"
                                    id="services" name="services[]" multiple required>
                                @foreach($services as $service)
                                    @php
                                        $isSelected = in_array($service->id, old('services', $booking->bookingServices->pluck('service_id')->toArray()));
                                    @endphp
                                    <option value="{{ $service->id }}"
                                            data-price="{{ $service->base_price }}"
                                            data-duration="{{ $service->duration_minutes }}"
                                            {{ $isSelected ? 'selected' : '' }}>
                                        {{ $service->name }} - ${{ number_format($service->base_price, 2) }} ({{ $service->duration_minutes }}min)
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple services</small>
                            @error('services')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="participant_count">Number of Participants <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('participant_count') is-invalid @enderror"
                                   id="participant_count" name="participant_count"
                                   value="{{ old('participant_count', $booking->participant_count) }}" min="1" required>
                            @error('participant_count')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="status">Booking Status <span class="text-danger">*</span></label>
                            <select class="form-control @error('status') is-invalid @enderror"
                                    id="status" name="status" required>
                                <option value="pending" {{ old('status', $booking->status) === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="confirmed" {{ old('status', $booking->status) === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                <option value="in_progress" {{ old('status', $booking->status) === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                <option value="completed" {{ old('status', $booking->status) === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="cancelled" {{ old('status', $booking->status) === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                <option value="no_show" {{ old('status', $booking->status) === 'no_show' ? 'selected' : '' }}>No Show</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="payment_status">Payment Status <span class="text-danger">*</span></label>
                            <select class="form-control @error('payment_status') is-invalid @enderror"
                                    id="payment_status" name="payment_status" required>
                                <option value="pending" {{ old('payment_status', $booking->payment_status) === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="paid" {{ old('payment_status', $booking->payment_status) === 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="partial" {{ old('payment_status', $booking->payment_status) === 'partial' ? 'selected' : '' }}>Partially Paid</option>
                                <option value="refunded" {{ old('payment_status', $booking->payment_status) === 'refunded' ? 'selected' : '' }}>Refunded</option>
                            </select>
                            @error('payment_status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror"
                                      id="notes" name="notes" rows="3"
                                      placeholder="Any special notes or requirements...">{{ old('notes', $booking->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                {{-- Booking Summary --}}
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h5 class="mb-0">Updated Booking Summary</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Selected Services:</strong>
                                        <div id="selected-services">
                                            <span class="text-muted">Loading...</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Total Duration:</strong>
                                        <div id="total-duration">
                                            <span class="text-muted">Loading...</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>End Time:</strong>
                                        <div id="end-time">
                                            <span class="text-muted">Loading...</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Total Amount:</strong>
                                        <div id="total-amount">
                                            <span class="text-muted">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Current Booking Info --}}
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle mr-2"></i>Current Booking Information</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Current Services:</strong><br>
                                    @foreach($booking->bookingServices as $bs)
                                        <span class="badge badge-secondary">{{ $bs->service->name }}</span>
                                    @endforeach
                                </div>
                                <div class="col-md-3">
                                    <strong>Current Duration:</strong><br>
                                    {{ $booking->formatted_duration }}
                                </div>
                                <div class="col-md-3">
                                    <strong>Current Time:</strong><br>
                                    {{ $booking->start_datetime->format('M j, Y g:i A') }}
                                </div>
                                <div class="col-md-3">
                                    <strong>Current Amount:</strong><br>
                                    ${{ number_format($booking->total_amount, 2) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>
                    Update Booking
                </button>
                <a href="{{ route('owner.bookings.show', $booking) }}" class="btn btn-secondary ml-2">
                    <i class="fas fa-times mr-2"></i>
                    Cancel
                </a>
                @if($booking->status !== 'completed' && $booking->status !== 'cancelled')
                    <button type="button" class="btn btn-danger ml-2" onclick="deleteBooking()">
                        <i class="fas fa-trash mr-2"></i>
                        Delete Booking
                    </button>
                @endif
            </div>
        </form>
    </div>
@stop

@section('adminlte_js')
    @parent
<script>
document.addEventListener('DOMContentLoaded', function() {
    const servicesSelect = document.getElementById('services');
    const startDatetimeInput = document.getElementById('start_datetime');
    const existingCustomerSelect = document.getElementById('existing_customer');

    // Handle existing customer selection
    if (existingCustomerSelect) {
        existingCustomerSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                document.getElementById('customer_name').value = selectedOption.dataset.name || '';
                document.getElementById('customer_email').value = selectedOption.dataset.email || '';
                document.getElementById('customer_phone').value = selectedOption.dataset.phone || '';
            }
        });
    }

    // Update booking summary when services change
    servicesSelect.addEventListener('change', updateBookingSummary);
    startDatetimeInput.addEventListener('change', updateBookingSummary);

    function updateBookingSummary() {
        const selectedOptions = Array.from(servicesSelect.selectedOptions);
        const startDatetime = startDatetimeInput.value;

        // Update selected services
        const servicesDiv = document.getElementById('selected-services');
        if (selectedOptions.length > 0) {
            servicesDiv.innerHTML = selectedOptions.map(option =>
                `<span class="badge badge-info mr-1">${option.text.split(' - ')[0]}</span>`
            ).join('');
        } else {
            servicesDiv.innerHTML = '<span class="text-muted">No services selected</span>';
        }

        // Calculate totals
        let totalDuration = 0;
        let totalAmount = 0;

        selectedOptions.forEach(option => {
            totalDuration += parseInt(option.dataset.duration) || 0;
            totalAmount += parseFloat(option.dataset.price) || 0;
        });

        // Update duration
        const durationDiv = document.getElementById('total-duration');
        if (totalDuration > 0) {
            const hours = Math.floor(totalDuration / 60);
            const minutes = totalDuration % 60;
            let durationText = '';
            if (hours > 0) durationText += `${hours}h `;
            if (minutes > 0) durationText += `${minutes}m`;
            durationDiv.innerHTML = durationText || '0 minutes';
        } else {
            durationDiv.innerHTML = '<span class="text-muted">0 minutes</span>';
        }

        // Update end time
        const endTimeDiv = document.getElementById('end-time');
        if (startDatetime && totalDuration > 0) {
            const startDate = new Date(startDatetime);
            const endDate = new Date(startDate.getTime() + totalDuration * 60000);
            endTimeDiv.innerHTML = endDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        } else {
            endTimeDiv.innerHTML = '<span class="text-muted">Select date & time</span>';
        }

        // Update total amount
        const amountDiv = document.getElementById('total-amount');
        if (totalAmount > 0) {
            amountDiv.innerHTML = `$${totalAmount.toFixed(2)}`;
        } else {
            amountDiv.innerHTML = '<span class="text-muted">$0.00</span>';
        }
    }

    // Initialize summary
    updateBookingSummary();
});

function deleteBooking() {
    Swal.fire({
        title: 'Delete Booking',
        text: 'Are you sure you want to delete this booking? This action cannot be undone.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, Delete',
        confirmButtonColor: '#dc3545',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("owner.bookings.destroy", $booking) }}';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';

            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';

            form.appendChild(csrfToken);
            form.appendChild(methodInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
@stop
