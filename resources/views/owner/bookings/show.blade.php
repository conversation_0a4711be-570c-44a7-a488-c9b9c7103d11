@extends('adminlte::page')

@section('title', 'Booking Details')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <h1>Booking Details - {{ $booking->booking_number }}</h1>
        <div>
            @if($booking->can_be_edited)
                <a href="{{ route('owner.bookings.edit', $booking) }}" class="btn btn-warning">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Booking
                </a>
            @endif
            <a href="{{ route('owner.bookings.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Bookings
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        {{-- Main Booking Information --}}
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-check mr-2"></i>
                        Booking Information
                    </h3>
                    <div class="card-tools">
                        <span class="badge" style="background-color: {{ $booking->status_color }}; color: white; font-size: 14px;">
                            {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Customer Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $booking->customer_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>
                                        <a href="mailto:{{ $booking->customer_email }}">{{ $booking->customer_email }}</a>
                                    </td>
                                </tr>
                                @if($booking->customer_phone)
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>
                                            <a href="tel:{{ $booking->customer_phone }}">{{ $booking->customer_phone }}</a>
                                        </td>
                                    </tr>
                                @endif
                                <tr>
                                    <td><strong>Participants:</strong></td>
                                    <td>{{ $booking->participant_count }} {{ Str::plural('person', $booking->participant_count) }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Appointment Details</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Date:</strong></td>
                                    <td>{{ $booking->start_datetime->format('l, F j, Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Time:</strong></td>
                                    <td>{{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Duration:</strong></td>
                                    <td>{{ $booking->formatted_duration }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $booking->created_at->format('M j, Y g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    {{-- Services --}}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Services</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Service</th>
                                            <th>Duration</th>
                                            <th>Price</th>
                                            <th>Time Slot</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($booking->bookingServices as $bookingService)
                                            <tr>
                                                <td>
                                                    <strong>{{ $bookingService->service->name }}</strong>
                                                    @if($bookingService->service->description)
                                                        <br><small class="text-muted">{{ $bookingService->service->description }}</small>
                                                    @endif
                                                </td>
                                                <td>{{ $bookingService->duration_minutes }} minutes</td>
                                                <td>${{ number_format($bookingService->total_price, 2) }}</td>
                                                <td>
                                                    {{ $bookingService->start_datetime->format('g:i A') }} -
                                                    {{ $bookingService->end_datetime->format('g:i A') }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    {{-- Notes --}}
                    @if($booking->notes)
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>Notes</h5>
                                <div class="alert alert-info">
                                    <i class="fas fa-sticky-note mr-2"></i>
                                    {{ $booking->notes }}
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        {{-- Sidebar --}}
        <div class="col-md-4">
            {{-- Quick Actions --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($booking->can_be_checked_in)
                            <form method="POST" action="{{ route('owner.bookings.check-in', $booking) }}" class="mb-2">
                                @csrf
                                <button type="submit" class="btn btn-success btn-block">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Check In Customer
                                </button>
                            </form>
                        @endif

                        @if($booking->can_be_checked_out)
                            <form method="POST" action="{{ route('owner.bookings.check-out', $booking) }}" class="mb-2">
                                @csrf
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-sign-out-alt mr-2"></i>
                                    Check Out Customer
                                </button>
                            </form>
                        @endif

                        @if($booking->status === 'confirmed' || $booking->status === 'pending')
                            <form method="POST" action="{{ route('owner.bookings.no-show', $booking) }}" class="mb-2">
                                @csrf
                                <button type="submit" class="btn btn-warning btn-block"
                                        onclick="return confirm('Mark this booking as no-show?')">
                                    <i class="fas fa-user-times mr-2"></i>
                                    Mark No-Show
                                </button>
                            </form>
                        @endif

                        @if($booking->can_be_cancelled)
                            <button type="button" class="btn btn-danger btn-block" onclick="cancelBooking()">
                                <i class="fas fa-times mr-2"></i>
                                Cancel Booking
                            </button>
                        @endif

                        <a href="{{ route('owner.calendar.index', ['highlight' => $booking->id]) }}" class="btn btn-info btn-block">
                            <i class="fas fa-calendar mr-2"></i>
                            View in Calendar
                        </a>
                    </div>
                </div>
            </div>

            {{-- Payment Information --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-credit-card mr-2"></i>
                        Payment Information
                    </h3>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Subtotal:</strong></td>
                            <td class="text-right">${{ number_format($booking->subtotal, 2) }}</td>
                        </tr>
                        <tr>
                            <td><strong>Total Amount:</strong></td>
                            <td class="text-right"><strong>${{ number_format($booking->total_amount, 2) }}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Paid Amount:</strong></td>
                            <td class="text-right text-success">${{ number_format($booking->paid_amount, 2) }}</td>
                        </tr>
                        @if($booking->remaining_amount > 0)
                            <tr>
                                <td><strong>Remaining:</strong></td>
                                <td class="text-right text-danger">${{ number_format($booking->remaining_amount, 2) }}</td>
                            </tr>
                        @endif
                        <tr>
                            <td><strong>Payment Status:</strong></td>
                            <td class="text-right">
                                <span class="badge" style="background-color: {{ $booking->payment_status_color }}; color: white;">
                                    {{ ucfirst(str_replace('_', ' ', $booking->payment_status)) }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            {{-- Booking Timeline --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history mr-2"></i>
                        Booking Timeline
                    </h3>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="time-label">
                            <span class="bg-primary">{{ $booking->created_at->format('M j, Y') }}</span>
                        </div>
                        <div>
                            <i class="fas fa-plus bg-success"></i>
                            <div class="timeline-item">
                                <span class="time">
                                    <i class="fas fa-clock"></i> {{ $booking->created_at->format('g:i A') }}
                                </span>
                                <h3 class="timeline-header">Booking Created</h3>
                                <div class="timeline-body">
                                    Booking was created for {{ $booking->customer_name }}
                                </div>
                            </div>
                        </div>

                        @if($booking->checked_in_at)
                            <div>
                                <i class="fas fa-sign-in-alt bg-info"></i>
                                <div class="timeline-item">
                                    <span class="time">
                                        <i class="fas fa-clock"></i> {{ $booking->checked_in_at->format('g:i A') }}
                                    </span>
                                    <h3 class="timeline-header">Customer Checked In</h3>
                                </div>
                            </div>
                        @endif

                        @if($booking->checked_out_at)
                            <div>
                                <i class="fas fa-sign-out-alt bg-primary"></i>
                                <div class="timeline-item">
                                    <span class="time">
                                        <i class="fas fa-clock"></i> {{ $booking->checked_out_at->format('g:i A') }}
                                    </span>
                                    <h3 class="timeline-header">Customer Checked Out</h3>
                                </div>
                            </div>
                        @endif

                        @if($booking->cancelled_at)
                            <div>
                                <i class="fas fa-times bg-danger"></i>
                                <div class="timeline-item">
                                    <span class="time">
                                        <i class="fas fa-clock"></i> {{ $booking->cancelled_at->format('g:i A') }}
                                    </span>
                                    <h3 class="timeline-header">Booking Cancelled</h3>
                                    @if($booking->cancellation_reason)
                                        <div class="timeline-body">
                                            Reason: {{ $booking->cancellation_reason }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <div>
                            <i class="fas fa-clock bg-gray"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
function cancelBooking() {
    Swal.fire({
        title: 'Cancel Booking',
        text: 'Please provide a reason for cancellation:',
        input: 'textarea',
        inputPlaceholder: 'Cancellation reason...',
        showCancelButton: true,
        confirmButtonText: 'Cancel Booking',
        confirmButtonColor: '#dc3545',
        cancelButtonText: 'Keep Booking',
        inputValidator: (value) => {
            if (!value) {
                return 'You need to provide a cancellation reason!'
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("owner.bookings.cancel", $booking) }}';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';

            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'cancellation_reason';
            reasonInput.value = result.value;

            form.appendChild(csrfToken);
            form.appendChild(reasonInput);
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
@stop
