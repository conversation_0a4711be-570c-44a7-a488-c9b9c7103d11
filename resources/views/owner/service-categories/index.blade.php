@extends('owner.layouts.app')

@section('title', 'Service Categories')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Service Categories</h1>
            <p class="text-muted">Organize your services into categories</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.services.index') }}" class="btn btn-info">
                    <i class="fas fa-concierge-bell mr-2"></i>
                    All Services
                </a>
                <button class="btn btn-primary" data-toggle="modal" data-target="#addCategoryModal">
                    <i class="fas fa-plus mr-2"></i>
                    Add Category
                </button>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Success/Error Messages --}}
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle mr-2"></i>
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    {{-- Quick Stats --}}
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_categories'] }}</h3>
                    <p>Total Categories</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tags"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['active_categories'] }}</h3>
                    <p>Active Categories</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['categories_with_services'] }}</h3>
                    <p>With Services</p>
                </div>
                <div class="icon">
                    <i class="fas fa-concierge-bell"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3>{{ $stats['empty_categories'] }}</h3>
                    <p>Empty Categories</p>
                </div>
                <div class="icon">
                    <i class="fas fa-folder-open"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Categories Grid --}}
    <div class="row">
        @forelse($categories as $category)
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header text-white" style="background-color: {{ $category->color ?? '#007bff' }};">
                        <h3 class="card-title">
                            <i class="{{ $category->icon ?? 'fas fa-tag' }} mr-2"></i>
                            {{ $category->name }}
                        </h3>
                        <div class="card-tools">
                            <button class="btn btn-sm btn-tool text-white"
                                    onclick="editCategory({{ $category->id }})"
                                    title="Edit Category">
                                <i class="fas fa-edit"></i>
                            </button>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-tool text-white dropdown-toggle"
                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <form action="{{ route('owner.service-categories.toggle-status', $category) }}" method="POST" style="display: inline;">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas {{ $category->is_active ? 'fa-pause' : 'fa-play' }} mr-2"></i>
                                            {{ $category->is_active ? 'Deactivate' : 'Activate' }}
                                        </button>
                                    </form>
                                    <form action="{{ route('owner.service-categories.duplicate', $category) }}" method="POST" style="display: inline;">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-copy mr-2"></i>
                                            Duplicate
                                        </button>
                                    </form>
                                    <div class="dropdown-divider"></div>
                                    @if($category->services_count == 0)
                                        <form action="{{ route('owner.service-categories.destroy', $category) }}" method="POST"
                                              style="display: inline;"
                                              onsubmit="return confirm('Are you sure you want to delete this category?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="fas fa-trash mr-2"></i>
                                                Delete
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if($category->description)
                            <p class="text-muted">{{ $category->description }}</p>
                        @else
                            <p class="text-muted">No description provided</p>
                        @endif

                        <div class="row">
                            <div class="col-6">
                                <strong>{{ $category->active_services_count }} Services</strong>
                                <br><small class="text-muted">Active services</small>
                            </div>
                            <div class="col-6">
                                @php
                                    $avgPrice = $category->services()->avg('base_price') ?? 0;
                                @endphp
                                <strong>${{ number_format($avgPrice, 0) }} avg</strong>
                                <br><small class="text-muted">Average price</small>
                            </div>
                        </div>

                        @if($category->services_count > 0)
                            <hr>
                            <div class="services-list">
                                @foreach($category->services()->active()->limit(3)->get() as $service)
                                    <span class="badge mr-1 mb-1" style="background-color: {{ $category->color ?? '#007bff' }}; color: white;">
                                        {{ $service->name }}
                                    </span>
                                @endforeach
                                @if($category->services_count > 3)
                                    <span class="badge badge-secondary mr-1 mb-1">+{{ $category->services_count - 3 }} more</span>
                                @endif
                            </div>
                        @endif
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">{{ $category->services_count }} total services</small>
                            </div>
                            <div class="col-6 text-right">
                                @if($category->is_active)
                                    <span class="badge badge-success">Active</span>
                                @else
                                    <span class="badge badge-secondary">Inactive</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No Service Categories</h4>
                    <p class="text-muted">You haven't created any service categories yet.</p>
                    <button class="btn btn-primary" data-toggle="modal" data-target="#addCategoryModal">
                        <i class="fas fa-plus mr-2"></i>
                        Create Your First Category
                    </button>
                </div>
            </div>
        @endforelse
    </div>

    {{-- Pagination --}}
    @if($categories->hasPages())
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $categories->links() }}
                </div>
            </div>
        </div>
    @endif

    {{-- Add Category Modal --}}
    <div class="modal fade" id="addCategoryModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Category</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form action="{{ route('owner.service-categories.store') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="modal_name">Category Name *</label>
                            <input type="text" class="form-control" id="modal_name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="modal_description">Description</label>
                            <textarea class="form-control" id="modal_description" name="description" rows="3"
                                      placeholder="Describe what services this category includes"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="modal_color">Category Color</label>
                                    <select class="form-control" id="modal_color" name="color">
                                        <option value="#007bff">Blue</option>
                                        <option value="#28a745">Green</option>
                                        <option value="#ffc107">Yellow</option>
                                        <option value="#17a2b8">Cyan</option>
                                        <option value="#dc3545">Red</option>
                                        <option value="#6c757d">Gray</option>
                                        <option value="#6f42c1">Purple</option>
                                        <option value="#fd7e14">Orange</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="modal_icon">Icon</label>
                                    <select class="form-control" id="modal_icon" name="icon">
                                        <option value="fas fa-cut">Scissors (Hair)</option>
                                        <option value="fas fa-spa">Spa</option>
                                        <option value="fas fa-magic">Magic (Beauty)</option>
                                        <option value="fas fa-leaf">Leaf (Wellness)</option>
                                        <option value="fas fa-heart">Heart</option>
                                        <option value="fas fa-star">Star</option>
                                        <option value="fas fa-concierge-bell">Service Bell</option>
                                        <option value="fas fa-palette">Palette</option>
                                        <option value="fas fa-hands">Hands</option>
                                        <option value="fas fa-user-md">Medical</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="modal_is_active" name="is_active" value="1" checked>
                                <label class="form-check-label" for="modal_is_active">
                                    Category is Active
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Category</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Edit Category Modal --}}
    <div class="modal fade" id="editCategoryModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Category</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="editCategoryForm" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="edit_modal_name">Category Name *</label>
                            <input type="text" class="form-control" id="edit_modal_name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="edit_modal_description">Description</label>
                            <textarea class="form-control" id="edit_modal_description" name="description" rows="3"
                                      placeholder="Describe what services this category includes"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_modal_color">Category Color</label>
                                    <select class="form-control" id="edit_modal_color" name="color">
                                        <option value="#007bff">Blue</option>
                                        <option value="#28a745">Green</option>
                                        <option value="#ffc107">Yellow</option>
                                        <option value="#17a2b8">Cyan</option>
                                        <option value="#dc3545">Red</option>
                                        <option value="#6c757d">Gray</option>
                                        <option value="#6f42c1">Purple</option>
                                        <option value="#fd7e14">Orange</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_modal_icon">Icon</label>
                                    <select class="form-control" id="edit_modal_icon" name="icon">
                                        <option value="fas fa-cut">Scissors (Hair)</option>
                                        <option value="fas fa-spa">Spa</option>
                                        <option value="fas fa-magic">Magic (Beauty)</option>
                                        <option value="fas fa-leaf">Leaf (Wellness)</option>
                                        <option value="fas fa-heart">Heart</option>
                                        <option value="fas fa-star">Star</option>
                                        <option value="fas fa-concierge-bell">Service Bell</option>
                                        <option value="fas fa-palette">Palette</option>
                                        <option value="fas fa-hands">Hands</option>
                                        <option value="fas fa-user-md">Medical</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_modal_is_active" name="is_active" value="1">
                                <label class="form-check-label" for="edit_modal_is_active">
                                    Category is Active
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Category</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    <script>
        // Category data for editing
        const categories = @json($categories->items());

        function editCategory(id) {
            const category = categories.find(c => c.id === id);
            if (!category) {
                alert('Category not found');
                return;
            }

            // Populate the edit form
            $('#edit_modal_name').val(category.name);
            $('#edit_modal_description').val(category.description || '');
            $('#edit_modal_color').val(category.color || '#007bff');
            $('#edit_modal_icon').val(category.icon || 'fas fa-tag');
            $('#edit_modal_is_active').prop('checked', category.is_active);

            // Set form action
            const updateUrl = "{{ route('owner.service-categories.update', ':id') }}".replace(':id', id);
            $('#editCategoryForm').attr('action', updateUrl);

            // Show modal
            $('#editCategoryModal').modal('show');
        }

        // Reset forms when modals are closed
        $('#addCategoryModal').on('hidden.bs.modal', function() {
            $(this).find('form')[0].reset();
        });

        $('#editCategoryModal').on('hidden.bs.modal', function() {
            $(this).find('form')[0].reset();
        });
    </script>
@stop
