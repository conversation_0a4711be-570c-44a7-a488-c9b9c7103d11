<div class="block-details">
    <div class="row">
        <div class="col-md-6">
            <h5>
                <i class="fas fa-ban mr-2"></i>
                Block Details
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Title:</strong></td>
                    <td>{{ $block->title }}</td>
                </tr>
                <tr>
                    <td><strong>Type:</strong></td>
                    <td>
                        <span class="badge badge-{{ $block->type_color }}">
                            {{ ucfirst(str_replace('_', ' ', $block->block_type)) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Start:</strong></td>
                    <td>{{ $block->start_datetime->format('M d, Y g:i A') }}</td>
                </tr>
                <tr>
                    <td><strong>End:</strong></td>
                    <td>{{ $block->end_datetime->format('M d, Y g:i A') }}</td>
                </tr>
                <tr>
                    <td><strong>Duration:</strong></td>
                    <td>{{ $block->formatted_duration }}</td>
                </tr>
                <tr>
                    <td><strong>Created:</strong></td>
                    <td>{{ $block->created_at->format('M d, Y g:i A') }}</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h5>
                <i class="fas fa-cog mr-2"></i>
                Configuration
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Business:</strong></td>
                    <td>{{ $block->business->name }}</td>
                </tr>
                @if($block->branch)
                <tr>
                    <td><strong>Branch:</strong></td>
                    <td>{{ $block->branch->name }}</td>
                </tr>
                @endif
                @if($block->resource)
                <tr>
                    <td><strong>Resource:</strong></td>
                    <td>{{ $block->resource->name }}</td>
                </tr>
                @else
                <tr>
                    <td><strong>Scope:</strong></td>
                    <td>
                        @if($block->affects_all_resources)
                            <span class="badge badge-warning">
                                <i class="fas fa-globe mr-1"></i>
                                All Resources
                            </span>
                        @else
                            <span class="badge badge-info">
                                <i class="fas fa-layer-group mr-1"></i>
                                Specific Services
                            </span>
                        @endif
                    </td>
                </tr>
                @endif
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        @if($block->start_datetime->isPast())
                            @if($block->end_datetime->isPast())
                                <span class="badge badge-secondary">
                                    <i class="fas fa-check mr-1"></i>
                                    Completed
                                </span>
                            @else
                                <span class="badge badge-primary">
                                    <i class="fas fa-play mr-1"></i>
                                    Active
                                </span>
                            @endif
                        @else
                            <span class="badge badge-info">
                                <i class="fas fa-clock mr-1"></i>
                                Scheduled
                            </span>
                        @endif
                    </td>
                </tr>
            </table>
        </div>
    </div>

    @if($block->description)
    <div class="row mt-3">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-align-left mr-2"></i>
                Description
            </h5>
            <div class="alert alert-light">
                {{ $block->description }}
            </div>
        </div>
    </div>
    @endif

    {{-- Impact Analysis --}}
    <div class="row mt-3">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Impact Analysis
            </h5>
            <div class="alert alert-warning">
                @php
                    $conflictingBookings = $block->business->bookings()
                        ->where(function($query) use ($block) {
                            $query->whereBetween('start_datetime', [$block->start_datetime, $block->end_datetime])
                                  ->orWhereBetween('end_datetime', [$block->start_datetime, $block->end_datetime])
                                  ->orWhere(function($q) use ($block) {
                                      $q->where('start_datetime', '<=', $block->start_datetime)
                                        ->where('end_datetime', '>=', $block->end_datetime);
                                  });
                        })
                        ->whereIn('status', ['pending', 'confirmed'])
                        ->count();
                @endphp
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-warning mb-1">{{ $conflictingBookings }}</h4>
                            <small>Conflicting Bookings</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-info mb-1">
                                {{ $block->affects_all_resources ? 'All' : 'Specific' }}
                            </h4>
                            <small>Resources Affected</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-primary mb-1">
                                {{ $block->start_datetime->diffInHours($block->end_datetime) }}h
                            </h4>
                            <small>Duration</small>
                        </div>
                    </div>
                </div>

                @if($conflictingBookings > 0)
                <div class="mt-3">
                    <strong>⚠️ Warning:</strong> This block conflicts with {{ $conflictingBookings }} existing booking(s). 
                    Consider rescheduling conflicting appointments before this block becomes active.
                </div>
                @endif
            </div>
        </div>
    </div>

    {{-- Block Type Information --}}
    <div class="row">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-info-circle mr-2"></i>
                Block Type Information
            </h5>
            <div class="alert alert-light">
                @switch($block->block_type)
                    @case('maintenance')
                        <div class="d-flex align-items-center">
                            <i class="fas fa-tools fa-2x text-warning mr-3"></i>
                            <div>
                                <strong>Maintenance Block</strong><br>
                                <small class="text-muted">
                                    This time is reserved for equipment maintenance, cleaning, or facility upkeep. 
                                    No bookings can be scheduled during this period.
                                </small>
                            </div>
                        </div>
                        @break
                    @case('holiday')
                        <div class="d-flex align-items-center">
                            <i class="fas fa-calendar-times fa-2x text-success mr-3"></i>
                            <div>
                                <strong>Holiday Block</strong><br>
                                <small class="text-muted">
                                    Business is closed for a holiday or special occasion. 
                                    All services are unavailable during this time.
                                </small>
                            </div>
                        </div>
                        @break
                    @case('private_event')
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-friends fa-2x text-purple mr-3"></i>
                            <div>
                                <strong>Private Event</strong><br>
                                <small class="text-muted">
                                    Facility is reserved for a private event or special booking. 
                                    Regular services are not available.
                                </small>
                            </div>
                        </div>
                        @break
                    @case('staff_break')
                        <div class="d-flex align-items-center">
                            <i class="fas fa-coffee fa-2x text-info mr-3"></i>
                            <div>
                                <strong>Staff Break</strong><br>
                                <small class="text-muted">
                                    Scheduled break time for staff. Services may be limited or unavailable.
                                </small>
                            </div>
                        </div>
                        @break
                    @default
                        <div class="d-flex align-items-center">
                            <i class="fas fa-ban fa-2x text-secondary mr-3"></i>
                            <div>
                                <strong>Other Block</strong><br>
                                <small class="text-muted">
                                    Custom time block for specific business needs.
                                </small>
                            </div>
                        </div>
                @endswitch
            </div>
        </div>
    </div>

    {{-- Action Buttons --}}
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="btn-group" role="group">
                @if(!$block->start_datetime->isPast())
                    <button type="button" class="btn btn-warning" onclick="editBlock({{ $block->id }})">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Block
                    </button>
                @endif
                
                @if($conflictingBookings > 0)
                    <button type="button" class="btn btn-info" onclick="viewConflictingBookings({{ $block->id }})">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        View Conflicts ({{ $conflictingBookings }})
                    </button>
                @endif
                
                <button type="button" class="btn btn-secondary" onclick="duplicateBlock({{ $block->id }})">
                    <i class="fas fa-copy mr-2"></i>
                    Duplicate
                </button>
                
                @if(!$block->start_datetime->isPast())
                    <button type="button" class="btn btn-danger" onclick="deleteBlock({{ $block->id }})">
                        <i class="fas fa-trash mr-2"></i>
                        Delete
                    </button>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
function editBlock(blockId) {
    // TODO: Implement edit block functionality
    alert('Edit block feature coming soon');
}

function viewConflictingBookings(blockId) {
    // TODO: Implement view conflicting bookings
    alert('View conflicting bookings feature coming soon');
}

function duplicateBlock(blockId) {
    // TODO: Implement duplicate block
    alert('Duplicate block feature coming soon');
}

function deleteBlock(blockId) {
    if (confirm('Are you sure you want to delete this block? This action cannot be undone.')) {
        fetch(`/owner/calendar/block/${blockId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#blockDetailsModal').modal('hide');
                calendar.refetchEvents();
                showToast('success', 'Block deleted successfully');
            } else {
                showToast('error', data.message || 'Error deleting block');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Error deleting block');
        });
    }
}
</script>
