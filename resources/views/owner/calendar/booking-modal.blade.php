<div class="booking-details">
    <div class="row">
        <div class="col-md-6">
            <h5>
                <i class="fas fa-calendar-check mr-2"></i>
                Booking Details
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Booking #:</strong></td>
                    <td>{{ $booking->booking_number }}</td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        <span class="badge badge-{{ $booking->status_color }}">
                            {{ ucfirst($booking->status) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Date & Time:</strong></td>
                    <td>
                        {{ $booking->start_datetime->format('M d, Y') }}<br>
                        {{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}
                    </td>
                </tr>
                <tr>
                    <td><strong>Duration:</strong></td>
                    <td>{{ $booking->formatted_duration }}</td>
                </tr>
                <tr>
                    <td><strong>Participants:</strong></td>
                    <td>{{ $booking->participant_count }}</td>
                </tr>
                @if($booking->is_recurring)
                <tr>
                    <td><strong>Recurring:</strong></td>
                    <td>
                        <span class="badge badge-info">
                            <i class="fas fa-redo mr-1"></i>
                            Recurring Booking
                        </span>
                    </td>
                </tr>
                @endif
            </table>
        </div>
        <div class="col-md-6">
            <h5>
                <i class="fas fa-user mr-2"></i>
                Customer Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td>{{ $booking->customer_name }}</td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td>
                        <a href="mailto:{{ $booking->customer_email }}">
                            {{ $booking->customer_email }}
                        </a>
                    </td>
                </tr>
                <tr>
                    <td><strong>Phone:</strong></td>
                    <td>
                        @if($booking->customer_phone)
                            <a href="tel:{{ $booking->customer_phone }}">
                                {{ $booking->customer_phone }}
                            </a>
                        @else
                            <span class="text-muted">Not provided</span>
                        @endif
                    </td>
                </tr>
                @if($booking->customer)
                <tr>
                    <td><strong>Customer Since:</strong></td>
                    <td>{{ $booking->customer->created_at->format('M Y') }}</td>
                </tr>
                <tr>
                    <td><strong>Total Bookings:</strong></td>
                    <td>{{ $booking->customer->bookings()->count() }}</td>
                </tr>
                @endif
            </table>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-concierge-bell mr-2"></i>
                Service Details
            </h5>
            @if($booking->bookingServices->count() > 0)
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>Service</th>
                                <th class="text-center">Quantity</th>
                                <th class="text-center">Duration</th>
                                <th class="text-right">Unit Price</th>
                                <th class="text-right">Total Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($booking->bookingServices as $bookingService)
                                <tr>
                                    <td>
                                        @if($bookingService->service)
                                            <strong>{{ $bookingService->service->name }}</strong>
                                            @if($bookingService->service->short_description)
                                                <br><small class="text-muted">{{ $bookingService->service->short_description }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">Service not found</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <span class="badge badge-secondary">{{ $bookingService->quantity ?? 1 }}</span>
                                    </td>
                                    <td class="text-center">
                                        @php
                                            $hours = floor($bookingService->duration_minutes / 60);
                                            $minutes = $bookingService->duration_minutes % 60;
                                            $duration = '';
                                            if ($hours > 0) {
                                                $duration .= $hours . 'h';
                                            }
                                            if ($minutes > 0) {
                                                $duration .= ($hours > 0 ? ' ' : '') . $minutes . 'm';
                                            }
                                        @endphp
                                        {{ $duration ?: $bookingService->duration_minutes . 'm' }}
                                    </td>
                                    <td class="text-right">
                                        ${{ number_format($bookingService->unit_price, 2) }}
                                    </td>
                                    <td class="text-right">
                                        <strong>${{ number_format($bookingService->total_price, 2) }}</strong>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr class="table-active">
                                <td colspan="4" class="text-right"><strong>Subtotal:</strong></td>
                                <td class="text-right">
                                    <strong>${{ number_format($booking->bookingServices->sum('total_price'), 2) }}</strong>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            @else
                <div class="alert alert-light">
                    <i class="fas fa-info-circle mr-2"></i>
                    No services assigned to this booking.
                </div>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <h5>
                <i class="fas fa-building mr-2"></i>
                Business Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Business:</strong></td>
                    <td>{{ $booking->business->name }}</td>
                </tr>
                @if($booking->branch)
                <tr>
                    <td><strong>Branch:</strong></td>
                    <td>{{ $booking->branch->name }}</td>
                </tr>
                @endif
            </table>
        </div>
        <div class="col-md-6">
            <h5>
                <i class="fas fa-dollar-sign mr-2"></i>
                Payment Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Subtotal:</strong></td>
                    <td>${{ number_format($booking->subtotal ?? 0, 2) }}</td>
                </tr>
                @if(($booking->tax_amount ?? 0) > 0)
                <tr>
                    <td><strong>Tax:</strong></td>
                    <td>${{ number_format($booking->tax_amount, 2) }}</td>
                </tr>
                @endif
                @if(($booking->discount_amount ?? 0) > 0)
                <tr>
                    <td><strong>Discount:</strong></td>
                    <td>-${{ number_format($booking->discount_amount, 2) }}</td>
                </tr>
                @endif
                <tr>
                    <td><strong>Total Amount:</strong></td>
                    <td><strong>${{ number_format($booking->total_amount ?? 0, 2) }}</strong></td>
                </tr>
                <tr>
                    <td><strong>Paid Amount:</strong></td>
                    <td>${{ number_format($booking->paid_amount ?? 0, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Remaining:</strong></td>
                    <td>
                        @php $remaining = ($booking->total_amount ?? 0) - ($booking->paid_amount ?? 0); @endphp
                        <span class="@if($remaining > 0) text-warning @else text-success @endif">
                            ${{ number_format($remaining, 2) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Payment Status:</strong></td>
                    <td>
                        <span class="badge badge-{{ $booking->payment_status_color }}">
                            {{ ucfirst($booking->payment_status) }}
                        </span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    @if($booking->notes)
    <div class="row mt-3">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-sticky-note mr-2"></i>
                Notes
            </h5>
            <div class="alert alert-light">
                {{ $booking->notes }}
            </div>
        </div>
    </div>
    @endif

    @if($booking->internal_notes)
    <div class="row">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-eye-slash mr-2"></i>
                Internal Notes
            </h5>
            <div class="alert alert-warning">
                <small><i class="fas fa-lock mr-1"></i> Internal only</small><br>
                {{ $booking->internal_notes }}
            </div>
        </div>
    </div>
    @endif

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="btn-group" role="group">
                <a href="{{ route('owner.bookings.show', $booking) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Full Details
                </a>
                @if($booking->can_be_checked_in)
                    <form action="{{ route('owner.bookings.check-in', $booking) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Check In
                        </button>
                    </form>
                @endif
                @if($booking->can_be_checked_out)
                    <form action="{{ route('owner.bookings.check-out', $booking) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Check Out
                        </button>
                    </form>
                @endif
                @if($booking->can_be_edited)
                    <a href="{{ route('owner.bookings.edit', $booking) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit
                    </a>
                @endif
                @if($booking->can_be_cancelled)
                    <form action="{{ route('owner.bookings.cancel', $booking) }}" method="POST" class="d-inline"
                          onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    @if($booking->is_recurring && $booking->recurring_group_id)
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="alert alert-info">
                <h6>
                    <i class="fas fa-redo mr-2"></i>
                    Recurring Booking Series
                </h6>
                <p class="mb-2">This booking is part of a recurring series.</p>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-info" onclick="viewRecurringSeries('{{ $booking->recurring_group_id }}')">
                        <i class="fas fa-list mr-1"></i>
                        View All in Series
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="editRecurringSeries('{{ $booking->recurring_group_id }}')">
                        <i class="fas fa-edit mr-1"></i>
                        Edit Series
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="cancelRecurringSeries('{{ $booking->recurring_group_id }}')">
                        <i class="fas fa-times mr-1"></i>
                        Cancel Series
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
function viewRecurringSeries(groupId) {
    // TODO: Implement view recurring series
    alert('View recurring series feature coming soon');
}

function editRecurringSeries(groupId) {
    // TODO: Implement edit recurring series
    alert('Edit recurring series feature coming soon');
}

function cancelRecurringSeries(groupId) {
    if (confirm('Are you sure you want to cancel the entire recurring series?')) {
        // TODO: Implement cancel recurring series
        alert('Cancel recurring series feature coming soon');
    }
}
</script>
