@extends('adminlte::page')

@section('title', 'Debug Booking Creation')

@section('content_header')
    <h1>Debug Booking Creation</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Test Booking Creation</h3>
        </div>
        <div class="card-body">
            <form id="debugBookingForm">
                @csrf
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="service_id">Service *</label>
                            <select class="form-control" id="service_id" name="service_id" required>
                                <option value="">Select Service</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}" data-duration="{{ $service->total_duration }}" data-price="{{ $service->base_price }}">
                                        {{ $service->name }} ({{ $service->formatted_duration }} - ${{ $service->formatted_price }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="start_datetime">Date & Time *</label>
                            <input type="datetime-local" class="form-control" id="start_datetime" name="start_datetime" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_name">Customer Name *</label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" value="Test Customer" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_email">Customer Email *</label>
                            <input type="email" class="form-control" id="customer_email" name="customer_email" value="<EMAIL>" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_phone">Customer Phone</label>
                            <input type="tel" class="form-control" id="customer_phone" name="customer_phone" value="************">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="participant_count">Participants *</label>
                            <input type="number" class="form-control" id="participant_count" name="participant_count" value="1" min="1" required>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3">Test booking from debug page</textarea>
                </div>
                <button type="submit" class="btn btn-primary">Create Test Booking</button>
            </form>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h3 class="card-title">Debug Information</h3>
        </div>
        <div class="card-body">
            <div id="debugInfo">
                <p>Submit the form to see debug information...</p>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
<script>
$(document).ready(function() {
    // Set default datetime to tomorrow at 10 AM
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(10, 0, 0, 0);
    const formattedDate = tomorrow.toISOString().slice(0, 16);
    $('#start_datetime').val(formattedDate);

    $('#debugBookingForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // Show the data being sent
        $('#debugInfo').html(`
            <h5>Request Data:</h5>
            <pre>${JSON.stringify(data, null, 2)}</pre>
            <p>Sending request...</p>
        `);

        fetch('{{ route('owner.calendar.create-booking') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                return response.text().then(text => {
                    throw new Error(`Non-JSON response: ${text}`);
                });
            }
        })
        .then(data => {
            $('#debugInfo').append(`
                <h5>Response:</h5>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `);
            
            if (data.success) {
                alert('Booking created successfully!');
            } else {
                alert('Booking creation failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            $('#debugInfo').append(`
                <h5>Error:</h5>
                <pre>${error.message}</pre>
            `);
            alert('Error: ' + error.message);
        });
    });
});
</script>
@stop
