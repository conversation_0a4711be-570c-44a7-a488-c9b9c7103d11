@extends('adminlte::page')

@section('title', 'Debug Booking Creation')

@section('content_header')
    <h1>Debug Booking Creation</h1>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Test Booking Creation</h3>
        </div>
        <div class="card-body">
            <form id="debugBookingForm">
                @csrf
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="service_id">Service *</label>
                            <select class="form-control" id="service_id" name="service_id" required>
                                <option value="">Select Service</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}" data-duration="{{ $service->total_duration }}" data-price="{{ $service->base_price }}">
                                        {{ $service->name }} ({{ $service->formatted_duration }} - ${{ $service->formatted_price }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="start_datetime">Date & Time *</label>
                            <input type="datetime-local" class="form-control" id="start_datetime" name="start_datetime" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_name">Customer Name *</label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" value="Test Customer" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_email">Customer Email *</label>
                            <input type="email" class="form-control" id="customer_email" name="customer_email" value="<EMAIL>" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_phone">Customer Phone</label>
                            <input type="tel" class="form-control" id="customer_phone" name="customer_phone" value="************">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="participant_count">Participants *</label>
                            <input type="number" class="form-control" id="participant_count" name="participant_count" value="1" min="1" required>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3">Test booking from debug page</textarea>
                </div>
                <button type="submit" class="btn btn-primary">Create Test Booking</button>
            </form>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h3 class="card-title">System Information</h3>
        </div>
        <div class="card-body">
            <h5>Available Services:</h5>
            @if($services->count() > 0)
                <ul>
                    @foreach($services as $service)
                        <li>
                            <strong>{{ $service->name }}</strong> (ID: {{ $service->id }})
                            - Duration: {{ $service->total_duration }} min
                            - Price: ${{ $service->base_price }}
                        </li>
                    @endforeach
                </ul>
            @else
                <div class="alert alert-warning">
                    <strong>No services found!</strong> You need to create services before you can create bookings.
                    <a href="{{ route('owner.services.index') }}" class="btn btn-primary btn-sm ml-2">Create Services</a>
                </div>
            @endif

            <h5>Current User:</h5>
            <ul>
                <li>User ID: {{ auth()->id() }}</li>
                <li>Email: {{ auth()->user()->email }}</li>
                <li>Role: {{ auth()->user()->role ?? 'Not set' }}</li>
            </ul>

            <h5>Current Time:</h5>
            <ul>
                <li>Server Time: {{ now() }}</li>
                <li>Timezone: {{ config('app.timezone') }}</li>
            </ul>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h3 class="card-title">Debug Information</h3>
        </div>
        <div class="card-body">
            <div id="debugInfo">
                <p>Submit the form to see debug information...</p>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
<script>
$(document).ready(function() {
    // Set default datetime to tomorrow at 10 AM
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(10, 0, 0, 0);
    const formattedDate = tomorrow.toISOString().slice(0, 16);
    $('#start_datetime').val(formattedDate);

    $('#debugBookingForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // Show the data being sent
        $('#debugInfo').html(`
            <h5>Request Data:</h5>
            <pre>${JSON.stringify(data, null, 2)}</pre>
            <h5>Data Validation:</h5>
            <ul>
                <li>Service ID: ${data.service_id} (type: ${typeof data.service_id})</li>
                <li>Start DateTime: ${data.start_datetime} (type: ${typeof data.start_datetime})</li>
                <li>Customer Name: ${data.customer_name} (length: ${data.customer_name ? data.customer_name.length : 0})</li>
                <li>Customer Email: ${data.customer_email} (valid: ${/\S+@\S+\.\S+/.test(data.customer_email)})</li>
                <li>Participant Count: ${data.participant_count} (type: ${typeof data.participant_count})</li>
            </ul>
            <p>Sending request...</p>
        `);

        fetch('{{ route('owner.calendar.create-booking') }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            $('#debugInfo').append(`
                <h5>Response Status:</h5>
                <p>Status: ${response.status} ${response.statusText}</p>
                <p>Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}</p>
            `);

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json().then(data => ({ response, data }));
            } else {
                return response.text().then(text => ({ response, data: { error: `Non-JSON response: ${text}` } }));
            }
        })
        .then(({ response, data }) => {
            $('#debugInfo').append(`
                <h5>Response Data:</h5>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `);

            if (response.ok && data.success) {
                $('#debugInfo').append(`<div class="alert alert-success">Booking created successfully!</div>`);
            } else {
                $('#debugInfo').append(`<div class="alert alert-danger">Booking creation failed: ${data.message || 'Unknown error'}</div>`);

                // Show validation errors if available
                if (data.errors) {
                    $('#debugInfo').append(`
                        <h5>Validation Errors:</h5>
                        <pre>${JSON.stringify(data.errors, null, 2)}</pre>
                    `);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            $('#debugInfo').append(`
                <h5>Network/Parse Error:</h5>
                <pre>${error.message}</pre>
                <div class="alert alert-danger">Error: ${error.message}</div>
            `);
        });
    });
});
</script>
@stop
