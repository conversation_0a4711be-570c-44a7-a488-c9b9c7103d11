@extends('adminlte::page')

@section('title', 'Calendar Diagnostic')

@section('content_header')
    <h1>Calendar Booking Diagnostic</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Business Information</h3>
                </div>
                <div class="card-body">
                    @if($business)
                        <table class="table table-sm">
                            <tr><td><strong>Business ID:</strong></td><td>{{ $business->id }}</td></tr>
                            <tr><td><strong>Name:</strong></td><td>{{ $business->name }}</td></tr>
                            <tr><td><strong>Status:</strong></td><td>
                                <span class="badge badge-{{ $business->is_active ? 'success' : 'danger' }}">
                                    {{ $business->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td></tr>
                            <tr><td><strong>Owner ID:</strong></td><td>{{ $business->owner_id }}</td></tr>
                            <tr><td><strong>Created:</strong></td><td>{{ $business->created_at }}</td></tr>
                        </table>
                    @else
                        <div class="alert alert-danger">
                            <strong>No Business Found!</strong> You need to create a business first.
                        </div>
                    @endif
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">User Information</h3>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr><td><strong>User ID:</strong></td><td>{{ auth()->id() }}</td></tr>
                        <tr><td><strong>Email:</strong></td><td>{{ auth()->user()->email }}</td></tr>
                        <tr><td><strong>Role:</strong></td><td>{{ auth()->user()->role ?? 'Not set' }}</td></tr>
                        <tr><td><strong>Name:</strong></td><td>{{ auth()->user()->name }}</td></tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Services ({{ $services->count() }})</h3>
                </div>
                <div class="card-body">
                    @if($services->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Duration</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($services as $service)
                                        <tr>
                                            <td>{{ $service->id }}</td>
                                            <td>{{ $service->name }}</td>
                                            <td>{{ $service->total_duration }} min</td>
                                            <td>${{ $service->base_price }}</td>
                                            <td>
                                                <span class="badge badge-{{ $service->is_active ? 'success' : 'danger' }}">
                                                    {{ $service->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <strong>No Services Found!</strong> You need to create services before you can create bookings.
                            <br><br>
                            <a href="{{ route('owner.services.index') }}" class="btn btn-primary">Create Services</a>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Bookings ({{ $recentBookings->count() }})</h3>
                </div>
                <div class="card-body">
                    @if($recentBookings->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Customer</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentBookings as $booking)
                                        <tr>
                                            <td>{{ $booking->id }}</td>
                                            <td>{{ $booking->customer_name }}</td>
                                            <td>{{ $booking->start_datetime->format('M j, Y H:i') }}</td>
                                            <td>
                                                <span class="badge badge-info">{{ $booking->status }}</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No bookings found yet.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">System Status</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-building"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Business</span>
                            <span class="info-box-number">{{ $business ? 'Configured' : 'Missing' }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-cogs"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Services</span>
                            <span class="info-box-number">{{ $services->count() }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning"><i class="fas fa-calendar"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Bookings</span>
                            <span class="info-box-number">{{ $recentBookings->count() }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-danger"><i class="fas fa-user"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">User Role</span>
                            <span class="info-box-number">{{ auth()->user()->role ?? 'None' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Quick Actions</h3>
        </div>
        <div class="card-body">
            <div class="btn-group" role="group">
                <a href="{{ route('owner.calendar.index') }}" class="btn btn-primary">
                    <i class="fas fa-calendar mr-1"></i> Calendar
                </a>
                <a href="{{ route('owner.calendar.debug-booking') }}" class="btn btn-warning">
                    <i class="fas fa-bug mr-1"></i> Debug Booking
                </a>
                @if($services->count() == 0)
                    <a href="{{ route('owner.services.index') }}" class="btn btn-success">
                        <i class="fas fa-plus mr-1"></i> Create Services
                    </a>
                @endif
                @if(!$business)
                    <a href="{{ route('owner.business.create') }}" class="btn btn-info">
                        <i class="fas fa-building mr-1"></i> Setup Business
                    </a>
                @endif
            </div>
        </div>
    </div>
@stop
