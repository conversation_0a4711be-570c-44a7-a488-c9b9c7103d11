@extends('owner.layouts.app')

@section('title', 'Waiting Lists')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Waiting Lists</h1>
            <p class="text-muted">Manage customer waiting lists and notifications</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addToWaitingListModal">
                        <i class="fas fa-plus mr-2"></i>
                        Add to Waiting List
                    </button>
                    <a href="{{ route('owner.bookings.index') }}" class="btn btn-secondary">
                        <i class="fas fa-calendar-check mr-2"></i>
                        All Bookings
                    </a>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Quick Stats --}}
    <div class="row mb-3" id="stats-row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="stat-active">{{ $stats['active'] }}</h3>
                    <p>Active Waiting</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="stat-converted">{{ $stats['converted_today'] }}</h3>
                    <p>Converted Today</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="stat-urgent">{{ $stats['urgent'] }}</h3>
                    <p>Urgent (24h)</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="stat-expired">{{ $stats['expired'] }}</h3>
                    <p>Expired</p>
                </div>
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Filters --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" id="filter-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="Customer name, email, or phone" value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="notified" {{ request('status') == 'notified' ? 'selected' : '' }}>Notified</option>
                                <option value="booked" {{ request('status') == 'booked' ? 'selected' : '' }}>Converted</option>
                                <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>Expired</option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="service_id">Service</label>
                            <select class="form-control" id="service_id" name="service_id">
                                <option value="">All Services</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}" {{ request('service_id') == $service->id ? 'selected' : '' }}>
                                        {{ $service->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="priority">Priority</label>
                            <select class="form-control" id="priority" name="priority">
                                <option value="">All Priorities</option>
                                <option value="high" {{ request('priority') == 'high' ? 'selected' : '' }}>High (7-10)</option>
                                <option value="medium" {{ request('priority') == 'medium' ? 'selected' : '' }}>Medium (3-6)</option>
                                <option value="low" {{ request('priority') == 'low' ? 'selected' : '' }}>Low (0-2)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_added">Date Added</label>
                            <input type="date" class="form-control" id="date_added" name="date_added" value="{{ request('date_added') }}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{{ route('owner.waiting-lists.index') }}" class="btn btn-secondary btn-block btn-sm mt-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Waiting List --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list mr-2"></i>
                Waiting List Entries
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-sm btn-primary" id="auto-match-btn">
                    <i class="fas fa-magic mr-1"></i> Auto-Match
                </button>
                <button type="button" class="btn btn-sm btn-secondary" id="refresh-data">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if($waitingLists->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped" id="waiting-list-table">
                        <thead>
                            <tr>
                                <th width="5%"><i class="fas fa-grip-vertical text-muted" title="Drag to reorder"></i></th>
                                <th>Customer</th>
                                <th>Service Requested</th>
                                <th>Preferred Date/Time</th>
                                <th>Priority</th>
                                <th>Added</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-waiting-list">
                            @foreach($waitingLists as $waitingList)
                                <tr id="waiting-list-{{ $waitingList->id }}" data-id="{{ $waitingList->id }}" class="sortable-row">
                                    <td class="handle text-center">
                                        <i class="fas fa-grip-vertical text-muted"></i>
                                    </td>
                                    <td>
                                        <strong>{{ $waitingList->customer_name }}</strong>
                                        <br><small class="text-muted">{{ $waitingList->customer_email }}</small>
                                        @if($waitingList->customer_phone)
                                            <br><small class="text-muted">{{ $waitingList->customer_phone }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ $waitingList->service->name }}</span>
                                        <br><small class="text-muted">{{ $waitingList->service->duration_minutes }} min duration</small>
                                        @if($waitingList->participant_count > 1)
                                            <br><small class="text-muted">{{ $waitingList->participant_count }} participants</small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $waitingList->preferred_date->format('M j, Y') }}</strong>
                                        @if($waitingList->preferred_time_start && $waitingList->preferred_time_end)
                                            <br><small class="text-muted">{{ $waitingList->preferred_time_start->format('g:i A') }} - {{ $waitingList->preferred_time_end->format('g:i A') }}</small>
                                        @elseif($waitingList->preferred_time_start)
                                            <br><small class="text-muted">After {{ $waitingList->preferred_time_start->format('g:i A') }}</small>
                                        @endif
                                        @if($waitingList->preferred_days_of_week)
                                            <br><small class="text-muted">{{ implode(', ', array_slice($waitingList->preferred_days_names, 0, 3)) }}{{ count($waitingList->preferred_days_names) > 3 ? '...' : '' }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($waitingList->priority >= 7)
                                            <span class="badge badge-danger">High</span>
                                            <br><small class="text-muted">Priority {{ $waitingList->priority }}</small>
                                        @elseif($waitingList->priority >= 3)
                                            <span class="badge badge-warning">Medium</span>
                                            <br><small class="text-muted">Priority {{ $waitingList->priority }}</small>
                                        @else
                                            <span class="badge badge-secondary">Low</span>
                                            <br><small class="text-muted">Priority {{ $waitingList->priority }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $waitingList->created_at->diffForHumans() }}</strong>
                                        <br><small class="text-muted">{{ $waitingList->created_at->format('M j, Y') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $waitingList->status_color }}">{{ ucfirst($waitingList->status) }}</span>
                                        @if($waitingList->notified_at)
                                            <br><small class="text-muted">Last: {{ $waitingList->notified_at->diffForHumans() }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical" role="group">
                                            <button class="btn btn-sm btn-success convert-btn" data-id="{{ $waitingList->id }}" title="Convert to Booking">
                                                <i class="fas fa-calendar-plus"></i>
                                            </button>
                                            <button class="btn btn-sm btn-info notify-btn" data-id="{{ $waitingList->id }}" title="Notify Customer">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning edit-btn" data-id="{{ $waitingList->id }}" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-btn" data-id="{{ $waitingList->id }}" title="Remove">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-list-ul fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No waiting list entries found</h5>
                    <p class="text-muted">
                        @if(request()->hasAny(['search', 'service_id', 'status', 'priority', 'date_added']))
                            No entries match your current filters.
                        @else
                            No customers are currently on the waiting list.
                        @endif
                    </p>
                    <button class="btn btn-primary" data-toggle="modal" data-target="#addToWaitingListModal">
                        <i class="fas fa-plus mr-2"></i>
                        Add First Customer
                    </button>
                </div>
            @endif
        </div>
    </div>

    {{-- Add to Waiting List Modal --}}
    <div class="modal fade" id="addToWaitingListModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Add Customer to Waiting List</h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addToWaitingListForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_name">Customer Name *</label>
                                    <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_email">Email *</label>
                                    <input type="email" class="form-control" id="customer_email" name="customer_email" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_phone">Phone</label>
                                    <input type="tel" class="form-control" id="customer_phone" name="customer_phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="service_id">Service *</label>
                                    <select class="form-control" id="service_id" name="service_id" required>
                                        <option value="">Select Service</option>
                                        @foreach($services as $service)
                                            <option value="{{ $service->id }}">{{ $service->name }} ({{ $service->duration_minutes }} min)</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="preferred_date">Preferred Date *</label>
                                    <input type="date" class="form-control" id="preferred_date" name="preferred_date" required min="{{ date('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="preferred_time_start">Preferred Time (Start)</label>
                                    <input type="time" class="form-control" id="preferred_time_start" name="preferred_time_start">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="preferred_time_end">Preferred Time (End)</label>
                                    <input type="time" class="form-control" id="preferred_time_end" name="preferred_time_end">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="participant_count">Number of Participants *</label>
                                    <input type="number" class="form-control" id="participant_count" name="participant_count" value="1" min="1" max="10" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">Priority *</label>
                                    <select class="form-control" id="priority" name="priority" required>
                                        <option value="1">Low (1)</option>
                                        <option value="2">Low (2)</option>
                                        <option value="3">Medium (3)</option>
                                        <option value="4">Medium (4)</option>
                                        <option value="5" selected>Medium (5)</option>
                                        <option value="6">Medium (6)</option>
                                        <option value="7">High (7)</option>
                                        <option value="8">High (8)</option>
                                        <option value="9">High (9)</option>
                                        <option value="10">Urgent (10)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="preferred_days_of_week">Preferred Days of Week (Optional)</label>
                            <div class="row">
                                @foreach(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'] as $index => $day)
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="preferred_days_of_week[]" value="{{ $index }}" id="day_{{ $index }}">
                                            <label class="form-check-label" for="day_{{ $index }}">
                                                {{ $day }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any special requirements or notes..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveToWaitingList">Add to Waiting List</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Convert to Booking Modal --}}
    <div class="modal fade" id="convertToBookingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Convert to Booking</h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="available-slots-container">
                        <p>Finding available slots...</p>
                    </div>
                    <form id="convertToBookingForm" style="display: none;">
                        <div class="form-group">
                            <label for="booking_datetime">Selected Date & Time:</label>
                            <input type="datetime-local" class="form-control" id="booking_datetime" name="booking_datetime" required>
                        </div>
                        <div class="form-group">
                            <label for="booking_notes">Notes (Optional):</label>
                            <textarea class="form-control" id="booking_notes" name="notes" rows="3" placeholder="Any additional notes for the booking..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" id="confirmConvertToBooking" style="display: none;">Create Booking</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Notify Customer Modal --}}
    <div class="modal fade" id="notifyCustomerModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Notify Customer</h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="notifyCustomerForm">
                        <div class="form-group">
                            <label for="notification_type">Notification Type:</label>
                            <select class="form-control" id="notification_type" name="notification_type" required>
                                <option value="available_slot">Available Slot</option>
                                <option value="reminder">Reminder</option>
                                <option value="custom">Custom Message</option>
                            </select>
                        </div>
                        <div class="form-group" id="available_datetime_group" style="display: none;">
                            <label for="available_datetime">Available Date & Time:</label>
                            <input type="datetime-local" class="form-control" id="available_datetime" name="available_datetime">
                        </div>
                        <div class="form-group">
                            <label for="send_method">Send Method:</label>
                            <select class="form-control" id="send_method" name="send_method" required>
                                <option value="email">Email</option>
                                <option value="sms">SMS</option>
                                <option value="both">Both Email & SMS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject (Email):</label>
                            <input type="text" class="form-control" id="subject" name="subject" placeholder="Email subject line">
                        </div>
                        <div class="form-group">
                            <label for="message">Message:</label>
                            <textarea class="form-control" id="message" name="message" rows="4" required placeholder="Your message to the customer..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmNotifyCustomer">Send Notification</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Auto-Match Results Modal --}}
    <div class="modal fade" id="autoMatchModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Auto-Match Results</h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="auto-match-results">
                        <p>Searching for matches...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script>
        let currentWaitingListId = null;

        $(document).ready(function() {
            // Initialize sortable for priority reordering
            if (document.getElementById('sortable-waiting-list')) {
                new Sortable(document.getElementById('sortable-waiting-list'), {
                    handle: '.handle',
                    animation: 150,
                    onEnd: function(evt) {
                        updatePriorityOrder();
                    }
                });
            }

            // Filter form auto-submit
            $('#service_id, #status, #priority').on('change', function() {
                $('#filter-form').submit();
            });

            // Search with debounce
            let searchTimeout;
            $('#search').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    $('#filter-form').submit();
                }, 500);
            });

            // Add to waiting list
            $('#saveToWaitingList').on('click', function() {
                addToWaitingList();
            });

            // Convert to booking
            $(document).on('click', '.convert-btn', function() {
                currentWaitingListId = $(this).data('id');
                findAvailableSlots(currentWaitingListId);
                $('#convertToBookingModal').modal('show');
            });

            // Notify customer
            $(document).on('click', '.notify-btn', function() {
                currentWaitingListId = $(this).data('id');
                $('#notifyCustomerModal').modal('show');
            });

            // Delete waiting list entry
            $(document).on('click', '.delete-btn', function() {
                const waitingListId = $(this).data('id');
                if (confirm('Are you sure you want to remove this customer from the waiting list?')) {
                    deleteWaitingListEntry(waitingListId);
                }
            });

            // Auto-match
            $('#auto-match-btn').on('click', function() {
                autoMatch();
            });

            // Refresh data
            $('#refresh-data').on('click', function() {
                refreshStats();
                location.reload();
            });

            // Notification type change
            $('#notification_type').on('change', function() {
                if ($(this).val() === 'available_slot') {
                    $('#available_datetime_group').show();
                } else {
                    $('#available_datetime_group').hide();
                }
            });

            // Confirm convert to booking
            $('#confirmConvertToBooking').on('click', function() {
                convertToBooking();
            });

            // Confirm notify customer
            $('#confirmNotifyCustomer').on('click', function() {
                notifyCustomer();
            });

            // Auto-refresh stats every 3 minutes
            setInterval(refreshStats, 180000);
        });

        function addToWaitingList() {
            const formData = new FormData(document.getElementById('addToWaitingListForm'));

            // Get preferred days of week
            const preferredDays = [];
            $('input[name="preferred_days_of_week[]"]:checked').each(function() {
                preferredDays.push(parseInt($(this).val()));
            });
            formData.set('preferred_days_of_week', JSON.stringify(preferredDays));

            $.ajax({
                url: '{{ route('owner.waiting-lists.store') }}',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        $('#addToWaitingListModal').modal('hide');
                        $('#addToWaitingListForm')[0].reset();
                        location.reload();
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    if (response.errors) {
                        let errorMessage = 'Please fix the following errors:\n';
                        Object.values(response.errors).forEach(function(errors) {
                            errors.forEach(function(error) {
                                errorMessage += '- ' + error + '\n';
                            });
                        });
                        showAlert('error', errorMessage);
                    } else {
                        showAlert('error', response.message || 'An error occurred');
                    }
                }
            });
        }

        function showAlert(type, message) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;

            // Remove existing alerts
            $('.alert').remove();

            // Add new alert at the top of content
            $('.content-wrapper .content').prepend(alertHtml);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        function findAvailableSlots(waitingListId) {
            $('#available-slots-container').html('<p><i class="fas fa-spinner fa-spin"></i> Finding available slots...</p>');
            $('#convertToBookingForm').hide();
            $('#confirmConvertToBooking').hide();

            $.get(`{{ route('owner.waiting-lists.index') }}/${waitingListId}/find-matches`)
            .done(function(response) {
                if (response.success && response.available_slots.length > 0) {
                    let html = '<h6>Available Slots:</h6><div class="list-group">';
                    response.available_slots.forEach(function(slot) {
                        html += `
                            <button type="button" class="list-group-item list-group-item-action slot-option"
                                    data-datetime="${slot.datetime}">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${slot.formatted_date}</h6>
                                    <small>${slot.formatted_time}</small>
                                </div>
                                <p class="mb-1">${slot.day_name}</p>
                            </button>
                        `;
                    });
                    html += '</div>';

                    if (response.total_found > response.available_slots.length) {
                        html += `<p class="mt-2 text-muted">Showing first ${response.available_slots.length} of ${response.total_found} available slots.</p>`;
                    }

                    $('#available-slots-container').html(html);
                } else {
                    $('#available-slots-container').html('<div class="alert alert-warning">No available slots found for the customer\'s preferences.</div>');
                }
            })
            .fail(function() {
                $('#available-slots-container').html('<div class="alert alert-danger">Failed to find available slots.</div>');
            });
        }

        // Handle slot selection
        $(document).on('click', '.slot-option', function() {
            $('.slot-option').removeClass('active');
            $(this).addClass('active');

            const datetime = $(this).data('datetime');
            $('#booking_datetime').val(datetime.replace(' ', 'T'));
            $('#convertToBookingForm').show();
            $('#confirmConvertToBooking').show();
        });

        function convertToBooking() {
            const formData = new FormData(document.getElementById('convertToBookingForm'));

            $.ajax({
                url: `{{ route('owner.waiting-lists.index') }}/${currentWaitingListId}/convert-to-booking`,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        $('#convertToBookingModal').modal('hide');
                        location.reload();
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    showAlert('error', response.message || 'Failed to create booking');
                }
            });
        }

        function notifyCustomer() {
            const formData = new FormData(document.getElementById('notifyCustomerForm'));

            $.ajax({
                url: `{{ route('owner.waiting-lists.index') }}/${currentWaitingListId}/notify`,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        $('#notifyCustomerModal').modal('hide');
                        $('#notifyCustomerForm')[0].reset();
                        location.reload();
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    showAlert('error', response.message || 'Failed to send notification');
                }
            });
        }

        function deleteWaitingListEntry(waitingListId) {
            $.ajax({
                url: `{{ route('owner.waiting-lists.index') }}/${waitingListId}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        $(`#waiting-list-${waitingListId}`).fadeOut(function() {
                            $(this).remove();
                        });
                        refreshStats();
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    showAlert('error', response.message || 'Failed to remove from waiting list');
                }
            });
        }

        function updatePriorityOrder() {
            const waitingListIds = [];
            $('#sortable-waiting-list tr').each(function() {
                const id = $(this).data('id');
                if (id) {
                    waitingListIds.push(id);
                }
            });

            $.ajax({
                url: '{{ route('owner.waiting-lists.update-priority') }}',
                method: 'POST',
                data: {
                    waiting_list_ids: waitingListIds,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', 'Priority order updated successfully!');
                    }
                },
                error: function() {
                    showAlert('error', 'Failed to update priority order');
                }
            });
        }

        function autoMatch() {
            $('#autoMatchModal').modal('show');
            $('#auto-match-results').html('<p><i class="fas fa-spinner fa-spin"></i> Searching for matches...</p>');

            $.get('{{ route('owner.waiting-lists.auto-match') }}')
            .done(function(response) {
                if (response.success && response.matches.length > 0) {
                    let html = '<h6>Found ' + response.total_matches + ' potential matches:</h6>';
                    html += '<div class="list-group">';

                    response.matches.forEach(function(match) {
                        html += `
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${match.customer_name}</h6>
                                    <small>Priority ${match.priority}</small>
                                </div>
                                <p class="mb-1">${match.service_name}</p>
                                <small>Available: ${match.available_slot.formatted}</small>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-success" onclick="convertMatch(${match.waiting_list_id}, '${match.available_slot.datetime}')">
                                        Convert to Booking
                                    </button>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    $('#auto-match-results').html(html);
                } else {
                    $('#auto-match-results').html('<div class="alert alert-info">No matches found at this time.</div>');
                }
            })
            .fail(function() {
                $('#auto-match-results').html('<div class="alert alert-danger">Failed to search for matches.</div>');
            });
        }

        function convertMatch(waitingListId, datetime) {
            currentWaitingListId = waitingListId;
            $('#booking_datetime').val(datetime.replace(' ', 'T'));
            $('#autoMatchModal').modal('hide');
            convertToBooking();
        }

        function refreshStats() {
            $.get('{{ route('owner.waiting-lists.stats') }}')
            .done(function(stats) {
                $('#stat-active').text(stats.active);
                $('#stat-converted').text(stats.converted_today);
                $('#stat-urgent').text(stats.urgent);
                $('#stat-expired').text(stats.expired);
            });
        }
    </script>
@stop