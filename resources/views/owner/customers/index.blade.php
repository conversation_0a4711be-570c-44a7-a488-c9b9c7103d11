@extends('owner.layouts.app')

@section('title', 'Customers')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Customers</h1>
            <p class="text-muted">Manage your customer database</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-info" onclick="showBirthdayModal()">
                        <i class="fas fa-birthday-cake mr-2"></i>
                        Birthdays
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="showImportModal()">
                        <i class="fas fa-upload mr-2"></i>
                        Import
                    </button>
                    <a href="{{ route('owner.customers.export') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </a>
                    <a href="{{ route('owner.customers.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add Customer
                    </a>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Quick Stats --}}
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ number_format($metrics['total_customers']) }}</h3>
                    <p>Total Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
                <a href="?status=all" class="small-box-footer">
                    View All <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ number_format($metrics['active_customers']) }}</h3>
                    <p>Active Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <a href="?status=active" class="small-box-footer">
                    View Active <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ number_format($metrics['vip_customers']) }}</h3>
                    <p>VIP Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-crown"></i>
                </div>
                <a href="?status=vip" class="small-box-footer">
                    View VIP <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ number_format($metrics['new_customers_this_month']) }}</h3>
                    <p>New This Month</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <a href="#" class="small-box-footer">
                    Recent Customers <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    {{-- Additional Metrics --}}
    <div class="row mb-3">
        <div class="col-lg-4">
            <div class="info-box">
                <span class="info-box-icon bg-primary"><i class="fas fa-dollar-sign"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Total Revenue</span>
                    <span class="info-box-number">${{ number_format($metrics['total_revenue'], 2) }}</span>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="info-box">
                <span class="info-box-icon bg-info"><i class="fas fa-chart-line"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Avg. Order Value</span>
                    <span class="info-box-number">${{ number_format($metrics['average_order_value'], 2) }}</span>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="info-box">
                <span class="info-box-icon bg-warning"><i class="fas fa-exclamation-triangle"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">At Risk Customers</span>
                    <span class="info-box-number">{{ number_format($metrics['at_risk_customers']) }}</span>
                </div>
            </div>
        </div>
    </div>

    {{-- Filters --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Search & Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ $search }}" placeholder="Name, email, or phone">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="all" {{ $status === 'all' ? 'selected' : '' }}>All Status</option>
                                <option value="active" {{ $status === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ $status === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                <option value="vip" {{ $status === 'vip' ? 'selected' : '' }}>VIP</option>
                                <option value="blocked" {{ $status === 'blocked' ? 'selected' : '' }}>Blocked</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="loyalty_tier">Loyalty Tier</label>
                            <select class="form-control" id="loyalty_tier" name="loyalty_tier">
                                <option value="all" {{ $loyaltyTier === 'all' ? 'selected' : '' }}>All Tiers</option>
                                <option value="bronze" {{ $loyaltyTier === 'bronze' ? 'selected' : '' }}>Bronze</option>
                                <option value="silver" {{ $loyaltyTier === 'silver' ? 'selected' : '' }}>Silver</option>
                                <option value="gold" {{ $loyaltyTier === 'gold' ? 'selected' : '' }}>Gold</option>
                                <option value="platinum" {{ $loyaltyTier === 'platinum' ? 'selected' : '' }}>Platinum</option>
                                <option value="diamond" {{ $loyaltyTier === 'diamond' ? 'selected' : '' }}>Diamond</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="tag">Tag</label>
                            <select class="form-control" id="tag" name="tag">
                                <option value="">All Tags</option>
                                @foreach($tags as $tag)
                                    <option value="{{ $tag->id }}" {{ $tagId == $tag->id ? 'selected' : '' }}>
                                        {{ $tag->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <a href="{{ route('owner.customers.index') }}" class="btn btn-secondary btn-block">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-block dropdown-toggle" type="button" data-toggle="dropdown">
                                        <i class="fas fa-sort"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="?{{ http_build_query(array_merge(request()->all(), ['sort_by' => 'customer_since', 'sort_order' => 'desc'])) }}">
                                            Newest First
                                        </a>
                                        <a class="dropdown-item" href="?{{ http_build_query(array_merge(request()->all(), ['sort_by' => 'customer_since', 'sort_order' => 'asc'])) }}">
                                            Oldest First
                                        </a>
                                        <a class="dropdown-item" href="?{{ http_build_query(array_merge(request()->all(), ['sort_by' => 'total_spent', 'sort_order' => 'desc'])) }}">
                                            Highest Spender
                                        </a>
                                        <a class="dropdown-item" href="?{{ http_build_query(array_merge(request()->all(), ['sort_by' => 'last_visit_date', 'sort_order' => 'desc'])) }}">
                                            Recent Visit
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Bulk Actions Bar --}}
    <div class="card" id="bulkActionsCard" style="display: none;">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <span id="selectedCount">0</span> customers selected
                </div>
                <div class="col-md-6">
                    <div class="float-right">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('activate')">
                                <i class="fas fa-check mr-1"></i> Activate
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('deactivate')">
                                <i class="fas fa-pause mr-1"></i> Deactivate
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="showBulkTagModal()">
                                <i class="fas fa-tags mr-1"></i> Tag
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="bulkAction('export')">
                                <i class="fas fa-download mr-1"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                <i class="fas fa-times mr-1"></i> Clear
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Customers List --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-users mr-2"></i>
                Customer List ({{ $customers->total() }} customers)
            </h3>
        </div>
        <div class="card-body p-0">
            @if($customers->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th width="40">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="selectAll">
                                        <label class="custom-control-label" for="selectAll"></label>
                                    </div>
                                </th>
                                <th>Customer</th>
                                <th>Contact</th>
                                <th>Stats</th>
                                <th>Loyalty</th>
                                <th>Last Visit</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($customers as $customerProfile)
                                <tr>
                                    <td>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input customer-checkbox"
                                                   id="customer_{{ $customerProfile->customer_id }}"
                                                   value="{{ $customerProfile->customer_id }}">
                                            <label class="custom-control-label" for="customer_{{ $customerProfile->customer_id }}"></label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white mr-3"
                                                 style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                {{ strtoupper(substr($customerProfile->customer->name, 0, 1)) }}{{ strtoupper(substr(explode(' ', $customerProfile->customer->name)[1] ?? '', 0, 1)) }}
                                            </div>
                                            <div>
                                                <strong>{{ $customerProfile->customer->name }}</strong>
                                                <br><small class="text-muted">Customer since {{ $customerProfile->customer_since->format('M Y') }}</small>
                                                @if($customerProfile->customer->tagAssignments->count() > 0)
                                                    <br>
                                                    @foreach($customerProfile->customer->tagAssignments->take(2) as $assignment)
                                                        <span class="badge badge-sm" style="background-color: {{ $assignment->tag->color }}; color: white;">
                                                            {{ $assignment->tag->name }}
                                                        </span>
                                                    @endforeach
                                                    @if($customerProfile->customer->tagAssignments->count() > 2)
                                                        <span class="badge badge-secondary badge-sm">+{{ $customerProfile->customer->tagAssignments->count() - 2 }}</span>
                                                    @endif
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ $customerProfile->customer->email }}</strong>
                                        @if($customerProfile->customer->phone)
                                            <br><small class="text-muted">{{ $customerProfile->customer->phone }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ number_format($customerProfile->total_visits) }}</strong> visits
                                        <br><small class="text-muted">${{ number_format($customerProfile->total_spent, 2) }} spent</small>
                                    </td>
                                    <td>
                                        <span class="badge {{ $customerProfile->loyalty_tier_badge_class }}">
                                            {{ ucfirst($customerProfile->loyalty_tier) }}
                                        </span>
                                        <br><small class="text-muted">{{ number_format($customerProfile->loyalty_points_balance) }} pts</small>
                                    </td>
                                    <td>
                                        @if($customerProfile->last_visit_date)
                                            <strong>{{ $customerProfile->last_visit_date->format('M j, Y') }}</strong>
                                            <br><small class="text-muted">{{ $customerProfile->last_visit_date->diffForHumans() }}</small>
                                        @else
                                            <span class="text-muted">Never visited</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $customerProfile->status_badge_class }}">
                                            {{ ucfirst($customerProfile->status) }}
                                        </span>
                                        @if($customerProfile->isAtRisk())
                                            <br><span class="badge badge-warning badge-sm">At Risk</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('owner.customers.show', $customerProfile->customer_id) }}"
                                               class="btn btn-sm btn-info" title="View Profile">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-secondary dropdown-toggle"
                                                        data-toggle="dropdown" title="Quick Actions">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="{{ route('owner.bookings.create', ['customer_id' => $customerProfile->customer_id]) }}">
                                                        <i class="fas fa-calendar-plus mr-2"></i>New Booking
                                                    </a>
                                                    <a class="dropdown-item" href="{{ route('owner.customers.edit', $customerProfile->customer_id) }}">
                                                        <i class="fas fa-edit mr-2"></i>Edit Customer
                                                    </a>
                                                    <div class="dropdown-divider"></div>
                                                    <a class="dropdown-item" href="#" onclick="quickSendMessage({{ $customerProfile->customer_id }}, '{{ $customerProfile->customer->name }}')">
                                                        <i class="fas fa-envelope mr-2"></i>Send Message
                                                    </a>
                                                    <a class="dropdown-item" href="#" onclick="quickAwardPoints({{ $customerProfile->customer_id }}, '{{ $customerProfile->customer->name }}')">
                                                        <i class="fas fa-gift mr-2"></i>Award Points
                                                    </a>
                                                    @if($customerProfile->status === 'active')
                                                        <a class="dropdown-item" href="#" onclick="quickChangeStatus({{ $customerProfile->customer_id }}, 'vip')">
                                                            <i class="fas fa-crown mr-2"></i>Make VIP
                                                        </a>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No customers found</h4>
                    <p class="text-muted">Try adjusting your search criteria or add your first customer.</p>
                    <a href="{{ route('owner.customers.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add First Customer
                    </a>
                </div>
            @endif
        </div>
        @if($customers->hasPages())
            <div class="card-footer">
                <div class="row">
                    <div class="col-sm-12 col-md-5">
                        <div class="dataTables_info">
                            Showing {{ $customers->firstItem() }} to {{ $customers->lastItem() }} of {{ $customers->total() }} customers
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-7">
                        <div class="float-right">
                            {{ $customers->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    {{-- Quick Action Modals --}}
    <div class="modal fade" id="quickMessageModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Send Quick Message</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="quickMessageForm">
                    @csrf
                    <input type="hidden" id="quick_customer_id" name="customer_id">
                    <div class="modal-body">
                        <p>Send a message to: <strong id="quick_customer_name"></strong></p>
                        <div class="form-group">
                            <label for="quick_message_type">Type</label>
                            <select class="form-control" id="quick_message_type" name="type" required>
                                <option value="note">Internal Note</option>
                                <option value="email">Email</option>
                                <option value="sms">SMS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="quick_message">Message</label>
                            <textarea class="form-control" id="quick_message" name="message" rows="3" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Send</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade" id="quickPointsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Award Points</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="quickPointsForm">
                    @csrf
                    <input type="hidden" id="quick_points_customer_id" name="customer_id">
                    <div class="modal-body">
                        <p>Award points to: <strong id="quick_points_customer_name"></strong></p>
                        <div class="form-group">
                            <label for="quick_points_amount">Points</label>
                            <input type="number" class="form-control" id="quick_points_amount" name="points" min="1" max="1000" value="50" required>
                        </div>
                        <div class="form-group">
                            <label for="quick_points_description">Description</label>
                            <input type="text" class="form-control" id="quick_points_description" name="description" value="Bonus points" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Award Points</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Import Modal --}}
    <div class="modal fade" id="importModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Import Customers</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="importForm" enctype="multipart/form-data">
                    @csrf
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <strong>CSV Format:</strong> name, email, phone, date_of_birth, gender, notes<br>
                            <small>Date format: YYYY-MM-DD, Gender: male/female/other/prefer_not_to_say</small>
                        </div>
                        <div class="form-group">
                            <label for="csv_file">CSV File</label>
                            <input type="file" class="form-control-file" id="csv_file" name="csv_file" accept=".csv,.txt" required>
                        </div>
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="skip_header" name="skip_header" checked>
                                <label class="custom-control-label" for="skip_header">Skip header row</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Import</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Birthday Modal --}}
    <div class="modal fade" id="birthdayModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Birthday Customers</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <select class="form-control" id="birthday_month">
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ $i == now()->month ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary" onclick="loadBirthdayCustomers()">
                                <i class="fas fa-search mr-2"></i>Load Customers
                            </button>
                        </div>
                    </div>
                    <div id="birthdayCustomersList">
                        <div class="text-center py-4">
                            <i class="fas fa-birthday-cake fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Click "Load Customers" to see birthday customers for the selected month.</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" id="sendBirthdayWishesBtn" onclick="showBirthdayWishesModal()" style="display: none;">
                        <i class="fas fa-gift mr-2"></i>Send Birthday Wishes
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Birthday Wishes Modal --}}
    <div class="modal fade" id="birthdayWishesModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Send Birthday Wishes</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="birthdayWishesForm">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="birthday_message_template">Message Template</label>
                            <textarea class="form-control" id="birthday_message_template" name="message_template" rows="4" required>Happy Birthday {name}! 🎉

Wishing you a wonderful day filled with happiness and joy. Thank you for being a valued customer of {business_name}.

Best wishes!</textarea>
                            <small class="form-text text-muted">Use {name} for customer name and {business_name} for your business name.</small>
                        </div>
                        <div class="form-group">
                            <label for="birthday_send_method">Send Method</label>
                            <select class="form-control" id="birthday_send_method" name="send_method" required>
                                <option value="email">Email Only</option>
                                <option value="sms">SMS Only</option>
                                <option value="both">Both Email & SMS</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Send Wishes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Bulk Tag Modal --}}
    <div class="modal fade" id="bulkTagModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bulk Tag Assignment</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="bulkTagForm">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="bulk_tag_action">Action</label>
                            <select class="form-control" id="bulk_tag_action" name="action" required>
                                <option value="assign_tag">Assign Tag</option>
                                <option value="remove_tag">Remove Tag</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="bulk_tag_id">Tag</label>
                            <select class="form-control" id="bulk_tag_id" name="tag_id" required>
                                @foreach($tags as $tag)
                                    <option value="{{ $tag->id }}">{{ $tag->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <p>This action will be applied to <span id="bulkTagSelectedCount">0</span> selected customers.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Apply</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
// Global variables
let selectedCustomers = [];
let birthdayCustomers = [];

// Quick action functions
function quickSendMessage(customerId, customerName) {
    $('#quick_customer_id').val(customerId);
    $('#quick_customer_name').text(customerName);
    $('#quickMessageModal').modal('show');
}

function quickAwardPoints(customerId, customerName) {
    $('#quick_points_customer_id').val(customerId);
    $('#quick_points_customer_name').text(customerName);
    $('#quickPointsModal').modal('show');
}

function quickChangeStatus(customerId, newStatus) {
    if (confirm(`Are you sure you want to change this customer's status to ${newStatus}?`)) {
        // This would typically make an AJAX call to update the status
        toastr.info('Status change feature will be implemented in the next update.');
    }
}

// Modal functions
function showImportModal() {
    $('#importModal').modal('show');
}

function showBirthdayModal() {
    $('#birthdayModal').modal('show');
}

function showBulkTagModal() {
    if (selectedCustomers.length === 0) {
        toastr.warning('Please select customers first.');
        return;
    }
    $('#bulkTagSelectedCount').text(selectedCustomers.length);
    $('#bulkTagModal').modal('show');
}

function showBirthdayWishesModal() {
    if (birthdayCustomers.length === 0) {
        toastr.warning('No birthday customers selected.');
        return;
    }
    $('#birthdayWishesModal').modal('show');
}

// Bulk operations
function bulkAction(action) {
    if (selectedCustomers.length === 0) {
        toastr.warning('Please select customers first.');
        return;
    }

    if (action === 'export') {
        // Direct export without confirmation
        performBulkAction(action);
        return;
    }

    const actionText = {
        'activate': 'activate',
        'deactivate': 'deactivate'
    };

    if (confirm(`Are you sure you want to ${actionText[action]} ${selectedCustomers.length} customers?`)) {
        performBulkAction(action);
    }
}

function performBulkAction(action) {
    $.ajax({
        url: '{{ route("owner.customers.bulk-action") }}',
        method: 'POST',
        data: {
            action: action,
            customer_ids: selectedCustomers,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                if (action !== 'export') {
                    location.reload();
                }
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Bulk operation failed');
        }
    });
}

// Selection management
function updateSelection() {
    selectedCustomers = [];
    $('.customer-checkbox:checked').each(function() {
        selectedCustomers.push(parseInt($(this).val()));
    });

    $('#selectedCount').text(selectedCustomers.length);

    if (selectedCustomers.length > 0) {
        $('#bulkActionsCard').show();
    } else {
        $('#bulkActionsCard').hide();
    }
}

function clearSelection() {
    $('.customer-checkbox').prop('checked', false);
    $('#selectAll').prop('checked', false);
    updateSelection();
}

// Birthday functions
function loadBirthdayCustomers() {
    const month = $('#birthday_month').val();

    $.ajax({
        url: '{{ route("owner.customers.birthdays") }}',
        method: 'GET',
        data: { month: month },
        success: function(response) {
            if (response.success) {
                birthdayCustomers = response.customers;
                displayBirthdayCustomers(response.customers);

                if (response.customers.length > 0) {
                    $('#sendBirthdayWishesBtn').show();
                } else {
                    $('#sendBirthdayWishesBtn').hide();
                }
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to load birthday customers');
        }
    });
}

function displayBirthdayCustomers(customers) {
    let html = '';

    if (customers.length === 0) {
        html = `
            <div class="text-center py-4">
                <i class="fas fa-birthday-cake fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No birthdays this month</h5>
                <p class="text-muted">No customers have birthdays in the selected month.</p>
            </div>
        `;
    } else {
        html = '<div class="table-responsive"><table class="table table-sm">';
        html += '<thead><tr><th>Customer</th><th>Birthday</th><th>Age</th><th>Days Until</th></tr></thead><tbody>';

        customers.forEach(function(customer) {
            const daysUntil = customer.days_until;
            const daysText = daysUntil === 0 ? 'Today!' :
                           daysUntil > 0 ? `${daysUntil} days` :
                           `${Math.abs(daysUntil)} days ago`;

            const rowClass = daysUntil === 0 ? 'table-warning' : '';

            html += `
                <tr class="${rowClass}">
                    <td>
                        <strong>${customer.name}</strong><br>
                        <small class="text-muted">${customer.email}</small>
                    </td>
                    <td>${customer.birthday}</td>
                    <td>${customer.age} years</td>
                    <td>${daysText}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    $('#birthdayCustomersList').html(html);
}

// Event handlers
$(document).ready(function() {
    // Select all checkbox
    $('#selectAll').on('change', function() {
        $('.customer-checkbox').prop('checked', $(this).is(':checked'));
        updateSelection();
    });

    // Individual checkboxes
    $(document).on('change', '.customer-checkbox', function() {
        updateSelection();

        // Update select all checkbox
        const totalCheckboxes = $('.customer-checkbox').length;
        const checkedCheckboxes = $('.customer-checkbox:checked').length;

        $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes);
    });
});

// Import form submission
$('#importForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    $.ajax({
        url: '{{ route("owner.customers.import") }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#importModal').modal('hide');
                $('#importForm')[0].reset();

                if (response.errors && response.errors.length > 0) {
                    let errorMsg = 'Import completed with some errors:\n';
                    response.errors.slice(0, 5).forEach(error => {
                        errorMsg += '• ' + error + '\n';
                    });
                    if (response.errors.length > 5) {
                        errorMsg += `• ... and ${response.errors.length - 5} more errors`;
                    }
                    toastr.warning(errorMsg);
                }

                location.reload();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Import failed');
        }
    });
});

// Birthday wishes form submission
$('#birthdayWishesForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('customer_ids', JSON.stringify(birthdayCustomers.map(c => c.id)));

    $.ajax({
        url: '{{ route("owner.customers.birthday-wishes") }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#birthdayWishesModal').modal('hide');

                if (response.errors && response.errors.length > 0) {
                    let errorMsg = 'Birthday wishes sent with some errors:\n';
                    response.errors.slice(0, 3).forEach(error => {
                        errorMsg += '• ' + error + '\n';
                    });
                    toastr.warning(errorMsg);
                }
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to send birthday wishes');
        }
    });
});

// Bulk tag form submission
$('#bulkTagForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('customer_ids', JSON.stringify(selectedCustomers));

    $.ajax({
        url: '{{ route("owner.customers.bulk-action") }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#bulkTagModal').modal('hide');
                location.reload();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Bulk tag operation failed');
        }
    });
});

// Quick message form submission
$('#quickMessageForm').on('submit', function(e) {
    e.preventDefault();

    const customerId = $('#quick_customer_id').val();
    const formData = new FormData(this);

    $.ajax({
        url: `/owner/customers/${customerId}/send-communication`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#quickMessageModal').modal('hide');
                $('#quickMessageForm')[0].reset();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to send message');
        }
    });
});

// Quick points form submission
$('#quickPointsForm').on('submit', function(e) {
    e.preventDefault();

    const customerId = $('#quick_points_customer_id').val();
    const formData = new FormData(this);

    $.ajax({
        url: `/owner/customers/${customerId}/award-points`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#quickPointsModal').modal('hide');
                $('#quickPointsForm')[0].reset();
                // Optionally refresh the page to show updated points
                location.reload();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to award points');
        }
    });
});

// Auto-refresh customer stats every 5 minutes
setInterval(function() {
    $.get('{{ route("owner.customers.stats") }}', function(data) {
        // Update dashboard metrics if needed
        console.log('Customer stats updated:', data);
    });
}, 300000); // 5 minutes
</script>
@stop
