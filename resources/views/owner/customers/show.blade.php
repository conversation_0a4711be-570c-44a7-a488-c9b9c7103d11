@extends('owner.layouts.app')

@section('title', 'Customer Profile - ' . $customerProfile->customer->name)

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>{{ $customerProfile->customer->name }}</h1>
            <p class="text-muted">Customer Profile & Management</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <div class="btn-group" role="group">
                    <a href="{{ route('owner.customers.edit', $customerProfile->customer_id) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit
                    </a>
                    <a href="{{ route('owner.bookings.create', ['customer_id' => $customerProfile->customer_id]) }}" class="btn btn-primary">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        New Booking
                    </a>
                    <a href="{{ route('owner.customers.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back
                    </a>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        {{-- Customer Info --}}
        <div class="col-md-4">
            <div class="card card-primary card-outline">
                <div class="card-body box-profile">
                    <div class="text-center">
                        <div class="avatar-circle bg-primary text-white mx-auto mb-3"
                             style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px;">
                            {{ strtoupper(substr($customerProfile->customer->name, 0, 1)) }}{{ strtoupper(substr(explode(' ', $customerProfile->customer->name)[1] ?? '', 0, 1)) }}
                        </div>
                        <h3 class="profile-username">{{ $customerProfile->customer->name }}</h3>
                        <p class="text-muted">
                            <span class="badge {{ $customerProfile->status_badge_class }}">
                                {{ ucfirst($customerProfile->status) }}
                            </span>
                            <span class="badge {{ $customerProfile->loyalty_tier_badge_class }}">
                                {{ ucfirst($customerProfile->loyalty_tier) }}
                            </span>
                        </p>
                    </div>

                    <ul class="list-group list-group-unbordered mb-3">
                        <li class="list-group-item">
                            <b>Email</b> <span class="float-right">{{ $customerProfile->customer->email }}</span>
                        </li>
                        @if($customerProfile->customer->phone)
                        <li class="list-group-item">
                            <b>Phone</b> <span class="float-right">{{ $customerProfile->customer->phone }}</span>
                        </li>
                        @endif
                        <li class="list-group-item">
                            <b>Customer Since</b> <span class="float-right">{{ $customerProfile->customer_since->format('M j, Y') }}</span>
                        </li>
                        <li class="list-group-item">
                            <b>Total Visits</b> <span class="float-right">{{ number_format($customerProfile->total_visits) }}</span>
                        </li>
                        <li class="list-group-item">
                            <b>Total Spent</b> <span class="float-right">${{ number_format($customerProfile->total_spent, 2) }}</span>
                        </li>
                        <li class="list-group-item">
                            <b>Loyalty Points</b> <span class="float-right">{{ number_format($customerProfile->loyalty_points_balance) }}</span>
                        </li>
                        @if($customerProfile->last_visit_date)
                        <li class="list-group-item">
                            <b>Last Visit</b> <span class="float-right">{{ $customerProfile->last_visit_date->format('M j, Y') }}</span>
                        </li>
                        @endif
                    </ul>

                    @if($customerTags->count() > 0)
                        <div class="mb-3">
                            <strong>Tags:</strong><br>
                            @foreach($customerTags as $assignment)
                                <span class="badge mr-1 mt-1" style="background-color: {{ $assignment->tag->color }}; color: white;">
                                    {{ $assignment->tag->name }}
                                </span>
                            @endforeach
                        </div>
                    @endif

                    @if($customerProfile->notes)
                        <div class="mb-3">
                            <strong>Notes:</strong>
                            <p class="text-muted">{{ $customerProfile->notes }}</p>
                        </div>
                    @endif

                    @if($customerProfile->special_requirements)
                        <div class="mb-3">
                            <strong>Special Requirements:</strong>
                            <p class="text-muted">{{ $customerProfile->special_requirements }}</p>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body">
                    <button class="btn btn-info btn-block mb-2" onclick="sendCommunication()">
                        <i class="fas fa-envelope mr-2"></i>
                        Send Message
                    </button>
                    <button class="btn btn-success btn-block mb-2" onclick="awardPoints()">
                        <i class="fas fa-gift mr-2"></i>
                        Award Points
                    </button>
                    <button class="btn btn-warning btn-block mb-2" onclick="assignTag()">
                        <i class="fas fa-tag mr-2"></i>
                        Assign Tag
                    </button>
                </div>
            </div>
        </div>

        {{-- Main Content --}}
        <div class="col-md-8">
            {{-- Analytics Cards --}}
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-calendar-check"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Completed</span>
                            <span class="info-box-number">{{ $analytics['completed_bookings'] }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning"><i class="fas fa-calendar-times"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Cancelled</span>
                            <span class="info-box-number">{{ $analytics['cancelled_bookings'] }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-danger"><i class="fas fa-user-times"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">No Shows</span>
                            <span class="info-box-number">{{ $analytics['no_show_bookings'] }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-percentage"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Success Rate</span>
                            <span class="info-box-number">{{ $analytics['completion_rate'] }}%</span>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Tabs --}}
            <div class="card">
                <div class="card-header p-0 pt-1">
                    <ul class="nav nav-tabs" id="custom-tabs-one-tab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="bookings-tab" data-toggle="pill" href="#bookings" role="tab">
                                <i class="fas fa-calendar-alt mr-2"></i>Bookings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="communications-tab" data-toggle="pill" href="#communications" role="tab">
                                <i class="fas fa-comments mr-2"></i>Communications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="loyalty-tab" data-toggle="pill" href="#loyalty" role="tab">
                                <i class="fas fa-star mr-2"></i>Loyalty Points
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="referrals-tab" data-toggle="pill" href="#referrals" role="tab">
                                <i class="fas fa-user-friends mr-2"></i>Referrals
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="activity-tab" data-toggle="pill" href="#activity" role="tab">
                                <i class="fas fa-history mr-2"></i>Activity
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="custom-tabs-one-tabContent">
                        {{-- Bookings Tab --}}
                        <div class="tab-pane fade show active" id="bookings" role="tabpanel">
                            @if($bookings->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Date & Time</th>
                                                <th>Services</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($bookings as $booking)
                                                <tr>
                                                    <td>
                                                        <strong>{{ $booking->start_datetime->format('M j, Y') }}</strong>
                                                        <br><small class="text-muted">{{ $booking->start_datetime->format('g:i A') }}</small>
                                                    </td>
                                                    <td>
                                                        @foreach($booking->services as $service)
                                                            <span class="badge badge-info">{{ $service->name }}</span>
                                                        @endforeach
                                                    </td>
                                                    <td>${{ number_format($booking->total_amount, 2) }}</td>
                                                    <td>
                                                        <span class="badge badge-{{ $booking->status === 'completed' ? 'success' : ($booking->status === 'cancelled' ? 'danger' : 'warning') }}">
                                                            {{ ucfirst($booking->status) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('owner.bookings.show', $booking->id) }}" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                {{ $bookings->links() }}
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No bookings yet</h5>
                                    <p class="text-muted">This customer hasn't made any bookings.</p>
                                    <a href="{{ route('owner.bookings.create', ['customer_id' => $customerProfile->customer_id]) }}" class="btn btn-primary">
                                        <i class="fas fa-plus mr-2"></i>
                                        Create First Booking
                                    </a>
                                </div>
                            @endif
                        </div>

                        {{-- Communications Tab --}}
                        <div class="tab-pane fade" id="communications" role="tabpanel">
                            @if($communications->count() > 0)
                                @foreach($communications as $communication)
                                    <div class="card mb-2">
                                        <div class="card-body p-3">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <i class="{{ $communication->type_icon }} mr-2"></i>
                                                    <strong>{{ ucfirst($communication->type) }}</strong>
                                                    @if($communication->subject)
                                                        - {{ $communication->subject }}
                                                    @endif
                                                </div>
                                                <small class="text-muted">{{ $communication->created_at->diffForHumans() }}</small>
                                            </div>
                                            <p class="mb-1 mt-2">{{ $communication->message_preview }}</p>
                                            <span class="badge {{ $communication->status_badge_class }}">{{ ucfirst($communication->status) }}</span>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No communications</h5>
                                    <p class="text-muted">No messages have been sent to this customer.</p>
                                </div>
                            @endif
                        </div>

                        {{-- Loyalty Points Tab --}}
                        <div class="tab-pane fade" id="loyalty" role="tabpanel">
                            @if($loyaltyHistory->count() > 0)
                                @foreach($loyaltyHistory as $transaction)
                                    <div class="card mb-2">
                                        <div class="card-body p-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="{{ $transaction->type_icon }} mr-2"></i>
                                                    <strong>{{ $transaction->formatted_points }} points</strong>
                                                    - {{ $transaction->description }}
                                                </div>
                                                <small class="text-muted">{{ $transaction->created_at->format('M j, Y') }}</small>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No loyalty activity</h5>
                                    <p class="text-muted">No loyalty points have been earned or redeemed.</p>
                                </div>
                            @endif
                        </div>

                        {{-- Referrals Tab --}}
                        <div class="tab-pane fade" id="referrals" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Customer Referrals</h6>
                                <button type="button" class="btn btn-primary btn-sm" onclick="showAddReferralModal()">
                                    <i class="fas fa-plus mr-2"></i>Add Referral
                                </button>
                            </div>
                            <div id="referralsList">
                                <div class="text-center py-4">
                                    <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Loading referrals...</p>
                                </div>
                            </div>
                        </div>

                        {{-- Activity Timeline Tab --}}
                        <div class="tab-pane fade" id="activity" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Activity Timeline</h6>
                                <button type="button" class="btn btn-primary btn-sm" onclick="showAddActivityModal()">
                                    <i class="fas fa-plus mr-2"></i>Add Note
                                </button>
                            </div>
                            <div id="activityTimeline">
                                <div class="text-center py-4">
                                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Loading activity timeline...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Add Referral Modal --}}
    <div class="modal fade" id="addReferralModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Customer Referral</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="addReferralForm">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="referred_name">Referred Person Name</label>
                            <input type="text" class="form-control" id="referred_name" name="referred_name" required>
                        </div>
                        <div class="form-group">
                            <label for="referred_email">Email Address</label>
                            <input type="email" class="form-control" id="referred_email" name="referred_email" required>
                        </div>
                        <div class="form-group">
                            <label for="referred_phone">Phone Number</label>
                            <input type="tel" class="form-control" id="referred_phone" name="referred_phone">
                        </div>
                        <div class="form-group">
                            <label for="referral_notes">Notes</label>
                            <textarea class="form-control" id="referral_notes" name="notes" rows="3"
                                      placeholder="Additional information about the referral..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Referral</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Add Activity Modal --}}
    <div class="modal fade" id="addActivityModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Activity Note</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="addActivityForm">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="activity_title">Title</label>
                            <input type="text" class="form-control" id="activity_title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="activity_type">Activity Type</label>
                            <select class="form-control" id="activity_type" name="activity_type" required>
                                <option value="note_added">Note Added</option>
                                <option value="communication_sent">Communication Sent</option>
                                <option value="status_changed">Status Changed</option>
                                <option value="profile_updated">Profile Updated</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="activity_description">Description</label>
                            <textarea class="form-control" id="activity_description" name="description" rows="4"
                                      placeholder="Detailed description of the activity..."></textarea>
                        </div>
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="activity_important" name="is_important" value="1">
                                <label class="custom-control-label" for="activity_important">Mark as Important</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Activity</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

    {{-- Communication Modal --}}
    <div class="modal fade" id="communicationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Send Communication</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="communicationForm">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="comm_type">Communication Type</label>
                            <select class="form-control" id="comm_type" name="type" required>
                                <option value="">Select Type</option>
                                <option value="email">Email</option>
                                <option value="sms">SMS</option>
                                <option value="note">Internal Note</option>
                            </select>
                        </div>
                        <div class="form-group" id="subject_group" style="display: none;">
                            <label for="comm_subject">Subject</label>
                            <input type="text" class="form-control" id="comm_subject" name="subject">
                        </div>
                        <div class="form-group">
                            <label for="comm_message">Message</label>
                            <textarea class="form-control" id="comm_message" name="message" rows="4" required></textarea>
                            <small class="form-text text-muted">Maximum 2000 characters</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Send
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Award Points Modal --}}
    <div class="modal fade" id="pointsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Award Loyalty Points</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="pointsForm">
                    @csrf
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            Current Balance: <strong>{{ number_format($customerProfile->loyalty_points_balance) }} points</strong>
                        </div>
                        <div class="form-group">
                            <label for="points_amount">Points to Award</label>
                            <input type="number" class="form-control" id="points_amount" name="points" min="1" max="10000" required>
                            <small class="form-text text-muted">Enter between 1 and 10,000 points</small>
                        </div>
                        <div class="form-group">
                            <label for="points_description">Description</label>
                            <input type="text" class="form-control" id="points_description" name="description"
                                   placeholder="e.g., Bonus points for loyalty" required>
                        </div>
                        <div class="form-group">
                            <label>Quick Options:</label>
                            <div class="btn-group-toggle" data-toggle="buttons">
                                <label class="btn btn-outline-primary btn-sm">
                                    <input type="radio" name="quick_points" value="50"> 50 pts
                                </label>
                                <label class="btn btn-outline-primary btn-sm">
                                    <input type="radio" name="quick_points" value="100"> 100 pts
                                </label>
                                <label class="btn btn-outline-primary btn-sm">
                                    <input type="radio" name="quick_points" value="250"> 250 pts
                                </label>
                                <label class="btn btn-outline-primary btn-sm">
                                    <input type="radio" name="quick_points" value="500"> 500 pts
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-gift mr-2"></i>
                            Award Points
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Assign Tag Modal --}}
    <div class="modal fade" id="tagModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Manage Customer Tags</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <h6>Current Tags:</h6>
                        <div id="current-tags">
                            @foreach($customerTags as $assignment)
                                <span class="badge mr-1 mb-1" style="background-color: {{ $assignment->tag->color }}; color: white;"
                                      data-tag-id="{{ $assignment->tag->id }}">
                                    {{ $assignment->tag->name }}
                                    <button type="button" class="btn btn-sm p-0 ml-1" onclick="removeTag({{ $assignment->tag->id }})"
                                            style="background: none; border: none; color: white;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </span>
                            @endforeach
                        </div>
                    </div>

                    @if($availableTags->count() > 0)
                        <div>
                            <h6>Available Tags:</h6>
                            <div id="available-tags">
                                @foreach($availableTags as $tag)
                                    <button type="button" class="btn btn-outline-secondary btn-sm mr-1 mb-1"
                                            onclick="assignTag({{ $tag->id }})" data-tag-id="{{ $tag->id }}">
                                        <span class="badge mr-1" style="background-color: {{ $tag->color }}; color: white;">
                                            {{ $tag->name }}
                                        </span>
                                        <i class="fas fa-plus"></i>
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="text-muted">
                            <i class="fas fa-info-circle mr-2"></i>
                            All available tags have been assigned to this customer.
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

@section('adminlte_js')
    @parent
<script>
// Communication Modal Functions
function sendCommunication() {
    $('#communicationModal').modal('show');
}

$('#comm_type').on('change', function() {
    if ($(this).val() === 'email') {
        $('#subject_group').show();
        $('#comm_subject').attr('required', true);
    } else {
        $('#subject_group').hide();
        $('#comm_subject').attr('required', false);
    }
});

$('#communicationForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = $(this).find('button[type="submit"]');

    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Sending...');

    $.ajax({
        url: '{{ route("owner.customers.send-communication", $customerProfile->customer_id) }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#communicationModal').modal('hide');
                $('#communicationForm')[0].reset();
                location.reload(); // Refresh to show new communication
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to send communication');
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('<i class="fas fa-paper-plane mr-2"></i>Send');
        }
    });
});

// Points Award Functions
function awardPoints() {
    $('#pointsModal').modal('show');
}

$('input[name="quick_points"]').on('change', function() {
    $('#points_amount').val($(this).val());
});

$('#pointsForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = $(this).find('button[type="submit"]');

    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Awarding...');

    $.ajax({
        url: '{{ route("owner.customers.award-points", $customerProfile->customer_id) }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#pointsModal').modal('hide');
                $('#pointsForm')[0].reset();
                location.reload(); // Refresh to show updated balance
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to award points');
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('<i class="fas fa-gift mr-2"></i>Award Points');
        }
    });
});

// Tag Management Functions
function assignTag() {
    $('#tagModal').modal('show');
}

function assignTag(tagId) {
    $.ajax({
        url: '{{ route("owner.customers.assign-tag", $customerProfile->customer_id) }}',
        method: 'POST',
        data: {
            tag_id: tagId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                location.reload(); // Refresh to update tag display
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to assign tag');
        }
    });
}

function removeTag(tagId) {
    if (confirm('Are you sure you want to remove this tag?')) {
        $.ajax({
            url: '{{ route("owner.customers.remove-tag", $customerProfile->customer_id) }}',
            method: 'DELETE',
            data: {
                tag_id: tagId,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload(); // Refresh to update tag display
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response?.message || 'Failed to remove tag');
            }
        });
    }
}

// Referrals functions
function showAddReferralModal() {
    $('#addReferralModal').modal('show');
}

function loadReferrals() {
    $.ajax({
        url: '{{ route("owner.customers.referrals", $customerProfile->customer_id) }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayReferrals(response.referrals.data);
            } else {
                $('#referralsList').html('<div class="alert alert-warning">Failed to load referrals</div>');
            }
        },
        error: function() {
            $('#referralsList').html('<div class="alert alert-danger">Error loading referrals</div>');
        }
    });
}

function displayReferrals(referrals) {
    let html = '';

    if (referrals.length === 0) {
        html = `
            <div class="text-center py-4">
                <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No referrals yet</h5>
                <p class="text-muted">This customer hasn't made any referrals.</p>
            </div>
        `;
    } else {
        referrals.forEach(function(referral) {
            html += `
                <div class="card mb-2">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${referral.referred_name}</strong>
                                <br><small class="text-muted">${referral.referred_email}</small>
                                ${referral.referred_phone ? '<br><small class="text-muted">' + referral.referred_phone + '</small>' : ''}
                            </div>
                            <div class="text-right">
                                <span class="badge ${getStatusBadgeClass(referral.status)}">${referral.status}</span>
                                <br><small class="text-muted">${formatDate(referral.referred_at)}</small>
                            </div>
                        </div>
                        ${referral.notes ? '<div class="mt-2"><small class="text-muted">' + referral.notes + '</small></div>' : ''}
                    </div>
                </div>
            `;
        });
    }

    $('#referralsList').html(html);
}

// Activity timeline functions
function showAddActivityModal() {
    $('#addActivityModal').modal('show');
}

function loadActivityTimeline() {
    $.ajax({
        url: '{{ route("owner.customers.activity", $customerProfile->customer_id) }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayActivityTimeline(response.activities.data);
            } else {
                $('#activityTimeline').html('<div class="alert alert-warning">Failed to load activity timeline</div>');
            }
        },
        error: function() {
            $('#activityTimeline').html('<div class="alert alert-danger">Error loading activity timeline</div>');
        }
    });
}

function displayActivityTimeline(activities) {
    let html = '';

    if (activities.length === 0) {
        html = `
            <div class="text-center py-4">
                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No activity yet</h5>
                <p class="text-muted">No activities have been recorded for this customer.</p>
            </div>
        `;
    } else {
        activities.forEach(function(activity) {
            const importantClass = activity.is_important ? 'border-left-warning' : '';
            html += `
                <div class="card mb-2 ${importantClass}">
                    <div class="card-body p-3">
                        <div class="d-flex align-items-start">
                            <div class="mr-3">
                                <i class="${activity.icon}" style="color: ${activity.color}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>${activity.title}</strong>
                                        ${activity.is_important ? '<span class="badge badge-warning badge-sm ml-2">Important</span>' : ''}
                                        ${activity.description ? '<br><small class="text-muted">' + activity.description + '</small>' : ''}
                                    </div>
                                    <small class="text-muted">${formatDate(activity.created_at)}</small>
                                </div>
                                ${activity.creator ? '<div class="mt-1"><small class="text-muted">by ' + activity.creator.name + '</small></div>' : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    $('#activityTimeline').html(html);
}

// Form submissions
$('#addReferralForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    $.ajax({
        url: '{{ route("owner.customers.add-referral", $customerProfile->customer_id) }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#addReferralModal').modal('hide');
                $('#addReferralForm')[0].reset();
                loadReferrals();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to add referral');
        }
    });
});

$('#addActivityForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    $.ajax({
        url: '{{ route("owner.customers.add-activity", $customerProfile->customer_id) }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#addActivityModal').modal('hide');
                $('#addActivityForm')[0].reset();
                loadActivityTimeline();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to add activity');
        }
    });
});

// Utility functions
function getStatusBadgeClass(status) {
    const classes = {
        'pending': 'badge-warning',
        'contacted': 'badge-info',
        'converted': 'badge-success',
        'declined': 'badge-danger'
    };
    return classes[status] || 'badge-secondary';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Load data when tabs are clicked
$('#referrals-tab').on('shown.bs.tab', function() {
    loadReferrals();
});

$('#activity-tab').on('shown.bs.tab', function() {
    loadActivityTimeline();
});
</script>
@stop
