@extends('owner.layouts.app')

@section('title', 'Edit Customer - ' . $customerProfile->customer->name)

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Edit Customer</h1>
            <p class="text-muted">Update customer information</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.customers.show', $customerProfile->customer_id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Profile
                </a>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-edit mr-2"></i>
                        Customer Information
                    </h3>
                </div>
                <form action="{{ route('owner.customers.update', $customerProfile->customer_id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name', $customerProfile->customer->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email', $customerProfile->customer->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                           id="phone" name="phone" value="{{ old('phone', $customerProfile->customer->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="date_of_birth">Date of Birth</label>
                                    <input type="date" class="form-control @error('date_of_birth') is-invalid @enderror"
                                           id="date_of_birth" name="date_of_birth"
                                           value="{{ old('date_of_birth', $customerProfile->customer->date_of_birth?->format('Y-m-d')) }}">
                                    @error('date_of_birth')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="gender">Gender</label>
                                    <select class="form-control @error('gender') is-invalid @enderror" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="male" {{ old('gender', $customerProfile->customer->gender) === 'male' ? 'selected' : '' }}>Male</option>
                                        <option value="female" {{ old('gender', $customerProfile->customer->gender) === 'female' ? 'selected' : '' }}>Female</option>
                                        <option value="other" {{ old('gender', $customerProfile->customer->gender) === 'other' ? 'selected' : '' }}>Other</option>
                                        <option value="prefer_not_to_say" {{ old('gender', $customerProfile->customer->gender) === 'prefer_not_to_say' ? 'selected' : '' }}>Prefer not to say</option>
                                    </select>
                                    @error('gender')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">Customer Status</label>
                                    <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                        <option value="active" {{ old('status', $customerProfile->status) === 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ old('status', $customerProfile->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="vip" {{ old('status', $customerProfile->status) === 'vip' ? 'selected' : '' }}>VIP</option>
                                        <option value="blocked" {{ old('status', $customerProfile->status) === 'blocked' ? 'selected' : '' }}>Blocked</option>
                                        <option value="prospect" {{ old('status', $customerProfile->status) === 'prospect' ? 'selected' : '' }}>Prospect</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="marketing_consent">Marketing Consent</label>
                            <div class="custom-control custom-checkbox">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="marketing_consent" value="0">
                                <input type="checkbox" class="custom-control-input" id="marketing_consent"
                                       name="marketing_consent" value="1"
                                       {{ old('marketing_consent', $customerProfile->marketing_consent) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="marketing_consent">
                                    Allow marketing communications
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror"
                                      id="notes" name="notes" rows="3"
                                      placeholder="Any special notes about the customer">{{ old('notes', $customerProfile->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="special_requirements">Special Requirements</label>
                            <textarea class="form-control @error('special_requirements') is-invalid @enderror"
                                      id="special_requirements" name="special_requirements" rows="2"
                                      placeholder="Allergies, accessibility needs, etc.">{{ old('special_requirements', $customerProfile->special_requirements) }}</textarea>
                            @error('special_requirements')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Update Customer
                        </button>
                        <a href="{{ route('owner.customers.show', $customerProfile->customer_id) }}" class="btn btn-secondary ml-2">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tags mr-2"></i>
                        Customer Tags
                    </h3>
                </div>
                <div class="card-body">
                    @if($tags->count() > 0)
                        <p class="text-muted mb-3">Select tags to categorize this customer:</p>
                        @foreach($tags as $tag)
                            <div class="custom-control custom-checkbox mb-2">
                                <input type="checkbox" class="custom-control-input"
                                       id="tag_{{ $tag->id }}" name="tags[]" value="{{ $tag->id }}"
                                       {{ in_array($tag->id, old('tags', $customerTags)) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="tag_{{ $tag->id }}">
                                    <span class="badge mr-2" style="background-color: {{ $tag->color }}; color: white;">
                                        {{ $tag->name }}
                                    </span>
                                    @if($tag->description)
                                        <small class="text-muted d-block">{{ $tag->description }}</small>
                                    @endif
                                </label>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted">No tags available. You can create tags in the customer management section.</p>
                    @endif
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Customer Stats
                    </h3>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><strong>Customer Since:</strong> {{ $customerProfile->customer_since->format('M j, Y') }}</li>
                        <li><strong>Total Visits:</strong> {{ number_format($customerProfile->total_visits) }}</li>
                        <li><strong>Total Spent:</strong> ${{ number_format($customerProfile->total_spent, 2) }}</li>
                        <li><strong>Avg. Order Value:</strong> ${{ number_format($customerProfile->average_order_value, 2) }}</li>
                        <li><strong>Loyalty Tier:</strong>
                            <span class="badge {{ $customerProfile->loyalty_tier_badge_class }}">
                                {{ ucfirst($customerProfile->loyalty_tier) }}
                            </span>
                        </li>
                        <li><strong>Loyalty Points:</strong> {{ number_format($customerProfile->loyalty_points_balance) }}</li>
                        @if($customerProfile->last_visit_date)
                            <li><strong>Last Visit:</strong> {{ $customerProfile->last_visit_date->format('M j, Y') }}</li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
    $(document).ready(function() {
        // Auto-format phone number
        $('#phone').on('input', function() {
            let value = $(this).val().replace(/\D/g, '');
            if (value.length >= 6) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            } else if (value.length >= 3) {
                value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
            }
            $(this).val(value);
        });

        // Form validation
        $('form').on('submit', function(e) {
            let isValid = true;

            // Check required fields
            $('input[required], select[required]').each(function() {
                if (!$(this).val().trim()) {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                if (typeof toastr !== 'undefined') {
                    toastr.error('Please fill in all required fields.');
                } else {
                    alert('Please fill in all required fields.');
                }
            }
        });
    });
    </script>
@stop
