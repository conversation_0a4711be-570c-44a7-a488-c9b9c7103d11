@extends('owner.layouts.app')

@section('title', 'Service Details')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>{{ $service->name }}</h1>
            <p class="text-muted">Service details and statistics</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.services.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Services
                </a>
                <a href="{{ route('owner.services.edit', $service) }}" class="btn btn-warning">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Service
                </a>
                <form action="{{ route('owner.services.toggle-status', $service) }}" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn {{ $service->is_active ? 'btn-outline-secondary' : 'btn-outline-success' }}">
                        <i class="fas {{ $service->is_active ? 'fa-pause' : 'fa-play' }} mr-2"></i>
                        {{ $service->is_active ? 'Deactivate' : 'Activate' }}
                    </button>
                </form>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Success/Error Messages --}}
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="row">
        {{-- Service Information --}}
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Service Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $service->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>
                                        @if($service->category)
                                            <span class="badge badge-info">{{ $service->category->name }}</span>
                                        @else
                                            <span class="badge badge-secondary">No Category</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Duration:</strong></td>
                                    <td>{{ $service->duration_minutes }} minutes</td>
                                </tr>
                                <tr>
                                    <td><strong>Base Price:</strong></td>
                                    <td>${{ number_format($service->base_price, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Max Participants:</strong></td>
                                    <td>{{ $service->max_participants }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($service->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-secondary">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Online Booking:</strong></td>
                                    <td>
                                        @if($service->online_booking_enabled)
                                            <span class="badge badge-success">Enabled</span>
                                        @else
                                            <span class="badge badge-warning">Disabled</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Requires Approval:</strong></td>
                                    <td>
                                        @if($service->requires_approval)
                                            <span class="badge badge-warning">Yes</span>
                                        @else
                                            <span class="badge badge-success">No</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Deposit Required:</strong></td>
                                    <td>
                                        @if($service->deposit_required)
                                            <span class="badge badge-info">${{ number_format($service->deposit_amount, 2) }}</span>
                                        @else
                                            <span class="badge badge-secondary">No</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Buffer Time:</strong></td>
                                    <td>{{ $service->buffer_time_before }}min before, {{ $service->buffer_time_after }}min after</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($service->description)
                        <hr>
                        <div class="row">
                            <div class="col-md-12">
                                <h5>Description</h5>
                                <p>{{ $service->description }}</p>
                            </div>
                        </div>
                    @endif

                    @if($service->short_description)
                        <div class="row">
                            <div class="col-md-12">
                                <h5>Short Description</h5>
                                <p>{{ $service->short_description }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Booking Settings --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Booking Settings
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Max Advance Booking:</strong></td>
                                    <td>{{ $service->max_advance_booking_days ?? 'No limit' }} days</td>
                                </tr>
                                <tr>
                                    <td><strong>Min Advance Booking:</strong></td>
                                    <td>{{ $service->min_advance_booking_hours ?? 0 }} hours</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Total Duration:</strong></td>
                                    <td>{{ $service->total_duration }} minutes (including buffers)</td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $service->created_at->format('M j, Y g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Statistics and Actions --}}
        <div class="col-md-4">
            {{-- Service Statistics --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Statistics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="text-center">
                                <h3 class="text-primary">{{ $serviceStats['total_bookings'] }}</h3>
                                <small class="text-muted">Total Bookings</small>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="text-center">
                                <h3 class="text-success">${{ number_format($serviceStats['total_revenue'], 2) }}</h3>
                                <small class="text-muted">Total Revenue</small>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="text-center">
                                <h3 class="text-warning">{{ $serviceStats['average_rating'] }}/5</h3>
                                <small class="text-muted">Average Rating</small>
                            </div>
                        </div>
                    </div>

                    @if($serviceStats['last_booking'])
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                Last booking: {{ $serviceStats['last_booking']->booking->booking_date->format('M j, Y') }}
                            </small>
                        </div>
                    @endif
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <form action="{{ route('owner.services.duplicate', $service) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-info btn-block">
                                <i class="fas fa-copy mr-2"></i>
                                Duplicate Service
                            </button>
                        </form>

                        <a href="{{ route('owner.services.edit', $service) }}" class="btn btn-warning btn-block mb-2">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Service
                        </a>

                        <form action="{{ route('owner.services.toggle-status', $service) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn {{ $service->is_active ? 'btn-outline-secondary' : 'btn-outline-success' }} btn-block">
                                <i class="fas {{ $service->is_active ? 'fa-pause' : 'fa-play' }} mr-2"></i>
                                {{ $service->is_active ? 'Deactivate' : 'Activate' }} Service
                            </button>
                        </form>

                        @if($serviceStats['total_bookings'] == 0)
                            <form action="{{ route('owner.services.destroy', $service) }}" method="POST" 
                                  onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-block">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Service
                                </button>
                            </form>
                        @else
                            <div class="alert alert-info">
                                <small>
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Cannot delete service with existing bookings
                                </small>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            {{-- Service Preview --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-eye mr-2"></i>
                        Customer View
                    </h3>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="service-icon {{ $service->category ? 'bg-primary' : 'bg-secondary' }} text-white mx-auto mb-3" 
                             style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="{{ $service->category->icon ?? 'fas fa-concierge-bell' }} fa-2x"></i>
                        </div>
                        <h5>{{ $service->name }}</h5>
                        @if($service->short_description)
                            <p class="text-muted">{{ $service->short_description }}</p>
                        @endif
                        <div class="row">
                            <div class="col-6">
                                <strong>${{ number_format($service->base_price, 2) }}</strong>
                                <br><small class="text-muted">Price</small>
                            </div>
                            <div class="col-6">
                                <strong>{{ $service->duration_minutes }} min</strong>
                                <br><small class="text-muted">Duration</small>
                            </div>
                        </div>
                        @if($service->deposit_required)
                            <div class="mt-2">
                                <small class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    ${{ number_format($service->deposit_amount, 2) }} deposit required
                                </small>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
