@extends('owner.layouts.app')

@section('title', 'Edit Service')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Edit Service</h1>
            <p class="text-muted">Update your service information</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.services.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Services
                </a>
                <a href="{{ route('owner.services.show', $service) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Service
                </a>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Success/Error Messages --}}
    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit mr-2"></i>
                        Service Information
                    </h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('owner.services.update', $service) }}" method="POST">
                        @csrf
                        @method('PUT')

                        {{-- Basic Information --}}
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3">Basic Information</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="name">Service Name *</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name', $service->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="service_category_id">Category</label>
                                    <select class="form-control @error('service_category_id') is-invalid @enderror"
                                            id="service_category_id" name="service_category_id">
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}"
                                                {{ old('service_category_id', $service->service_category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('service_category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="description">Full Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description" name="description" rows="3"
                                              placeholder="Describe your service in detail">{{ old('description', $service->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="short_description">Short Description</label>
                                    <textarea class="form-control @error('short_description') is-invalid @enderror"
                                              id="short_description" name="short_description" rows="2"
                                              placeholder="Brief description for listings and previews">{{ old('short_description', $service->short_description) }}</textarea>
                                    @error('short_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                               {{ old('is_active', $service->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Service is Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="online_booking_enabled" name="online_booking_enabled" value="1"
                                               {{ old('online_booking_enabled', $service->online_booking_enabled) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="online_booking_enabled">
                                            Allow Online Booking
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        {{-- Pricing & Duration --}}
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3">Pricing & Duration</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="base_price">Base Price *</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" class="form-control @error('base_price') is-invalid @enderror"
                                               id="base_price" name="base_price" step="0.01" min="0"
                                               value="{{ old('base_price', $service->base_price) }}" required>
                                        @error('base_price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="duration_minutes">Duration (minutes) *</label>
                                    <input type="number" class="form-control @error('duration_minutes') is-invalid @enderror"
                                           id="duration_minutes" name="duration_minutes" min="1"
                                           value="{{ old('duration_minutes', $service->duration_minutes) }}" required>
                                    @error('duration_minutes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="max_participants">Max Participants *</label>
                                    <input type="number" class="form-control @error('max_participants') is-invalid @enderror"
                                           id="max_participants" name="max_participants" min="1"
                                           value="{{ old('max_participants', $service->max_participants) }}" required>
                                    @error('max_participants')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="buffer_time_before">Buffer Before (minutes)</label>
                                    <input type="number" class="form-control @error('buffer_time_before') is-invalid @enderror"
                                           id="buffer_time_before" name="buffer_time_before" min="0"
                                           value="{{ old('buffer_time_before', $service->buffer_time_before) }}">
                                    <small class="form-text text-muted">Setup time before service</small>
                                    @error('buffer_time_before')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="buffer_time_after">Buffer After (minutes)</label>
                                    <input type="number" class="form-control @error('buffer_time_after') is-invalid @enderror"
                                           id="buffer_time_after" name="buffer_time_after" min="0"
                                           value="{{ old('buffer_time_after', $service->buffer_time_after) }}">
                                    <small class="form-text text-muted">Cleanup time after service</small>
                                    @error('buffer_time_after')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="deposit_required" name="deposit_required" value="1"
                                               {{ old('deposit_required', $service->deposit_required) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="deposit_required">
                                            Deposit Required
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="deposit_amount_row" style="{{ old('deposit_required', $service->deposit_required) ? '' : 'display: none;' }}">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="deposit_amount">Deposit Amount</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" class="form-control @error('deposit_amount') is-invalid @enderror"
                                               id="deposit_amount" name="deposit_amount" step="0.01" min="0"
                                               value="{{ old('deposit_amount', $service->deposit_amount) }}">
                                        @error('deposit_amount')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="requires_approval" name="requires_approval" value="1"
                                               {{ old('requires_approval', $service->requires_approval) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="requires_approval">
                                            Requires Manual Approval
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        {{-- Availability Settings --}}
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="mb-3">Availability Settings</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="max_advance_booking_days">Max Advance Booking (days)</label>
                                    <input type="number" class="form-control @error('max_advance_booking_days') is-invalid @enderror"
                                           id="max_advance_booking_days" name="max_advance_booking_days" min="1"
                                           value="{{ old('max_advance_booking_days', $service->max_advance_booking_days) }}">
                                    <small class="form-text text-muted">How far in advance can customers book</small>
                                    @error('max_advance_booking_days')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="min_advance_booking_hours">Min Advance Booking (hours)</label>
                                    <input type="number" class="form-control @error('min_advance_booking_hours') is-invalid @enderror"
                                           id="min_advance_booking_hours" name="min_advance_booking_hours" min="0"
                                           value="{{ old('min_advance_booking_hours', $service->min_advance_booking_hours) }}">
                                    <small class="form-text text-muted">Minimum notice required for booking</small>
                                    @error('min_advance_booking_hours')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-2"></i>
                                    Update Service
                                </button>
                                <a href="{{ route('owner.services.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            {{-- Service Preview --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-eye mr-2"></i>
                        Service Preview
                    </h3>
                </div>
                <div class="card-body">
                    <div id="service-preview">
                        <div class="text-center">
                            <div class="service-icon bg-primary text-white mx-auto mb-3" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-concierge-bell fa-2x"></i>
                            </div>
                            <h5 id="preview-name">{{ $service->name }}</h5>
                            <p class="text-muted" id="preview-description">{{ $service->short_description ?: 'Service description will appear here' }}</p>
                            <div class="row">
                                <div class="col-6">
                                    <strong id="preview-price">${{ number_format($service->base_price, 2) }}</strong>
                                    <br><small class="text-muted">Price</small>
                                </div>
                                <div class="col-6">
                                    <strong id="preview-duration">{{ $service->duration_minutes }} min</strong>
                                    <br><small class="text-muted">Duration</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Service Statistics --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Service Statistics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">{{ $service->booking_services_count ?? 0 }}</h4>
                                <small class="text-muted">Total Bookings</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success">${{ number_format($service->base_price * ($service->booking_services_count ?? 0), 2) }}</h4>
                                <small class="text-muted">Total Revenue</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    <script>
        $(document).ready(function() {
            // Update preview when form fields change
            $('#name').on('input', function() {
                $('#preview-name').text($(this).val() || 'Service Name');
            });

            $('#short_description').on('input', function() {
                $('#preview-description').text($(this).val() || 'Service description will appear here');
            });

            $('#base_price').on('input', function() {
                var price = parseFloat($(this).val()) || 0;
                $('#preview-price').text('$' + price.toFixed(2));
            });

            $('#duration_minutes').on('input', function() {
                var duration = parseInt($(this).val()) || 0;
                $('#preview-duration').text(duration + ' min');
            });

            // Handle deposit required checkbox
            $('#deposit_required').change(function() {
                if ($(this).is(':checked')) {
                    $('#deposit_amount_row').show();
                } else {
                    $('#deposit_amount_row').hide();
                }
            });

            // Form validation
            $('form').on('submit', function(e) {
                var isValid = true;
                var errors = [];

                // Check required fields
                if (!$('#name').val().trim()) {
                    errors.push('Service name is required');
                    isValid = false;
                }

                if (!$('#base_price').val() || parseFloat($('#base_price').val()) <= 0) {
                    errors.push('Base price must be greater than 0');
                    isValid = false;
                }

                if (!$('#duration_minutes').val() || parseInt($('#duration_minutes').val()) <= 0) {
                    errors.push('Duration must be greater than 0 minutes');
                    isValid = false;
                }

                if (!$('#max_participants').val() || parseInt($('#max_participants').val()) <= 0) {
                    errors.push('Max participants must be at least 1');
                    isValid = false;
                }

                // Check deposit amount if deposit is required
                if ($('#deposit_required').is(':checked')) {
                    var depositAmount = parseFloat($('#deposit_amount').val()) || 0;
                    var basePrice = parseFloat($('#base_price').val()) || 0;

                    if (depositAmount <= 0) {
                        errors.push('Deposit amount is required when deposit is enabled');
                        isValid = false;
                    } else if (depositAmount > basePrice) {
                        errors.push('Deposit amount cannot be greater than base price');
                        isValid = false;
                    }
                }

                if (!isValid) {
                    e.preventDefault();
                    alert('Please fix the following errors:\n\n' + errors.join('\n'));
                }
            });
        });
    </script>
@stop