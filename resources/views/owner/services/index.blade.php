@extends('owner.layouts.app')

@section('title', 'Services')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Services</h1>
            <p class="text-muted">Manage your business services and offerings</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.service-categories.index') }}" class="btn btn-info">
                    <i class="fas fa-tags mr-2"></i>
                    Categories
                </a>
                <a href="{{ route('owner.services.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    Add Service
                </a>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Success/Error Messages --}}
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle mr-2"></i>
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    {{-- Quick Stats --}}
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_services'] }}</h3>
                    <p>Total Services</p>
                </div>
                <div class="icon">
                    <i class="fas fa-concierge-bell"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['active_services'] }}</h3>
                    <p>Active Services</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>${{ number_format($stats['average_price'], 2) }}</h3>
                    <p>Average Price</p>
                </div>
                <div class="icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3>{{ $stats['total_categories'] }}</h3>
                    <p>Categories</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tags"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Filters --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ request('search') }}"
                                   placeholder="Service name or description">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select class="form-control" id="category" name="category">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="price_range">Price Range</label>
                            <select class="form-control" id="price_range" name="price_range">
                                <option value="">All Prices</option>
                                <option value="under_50" {{ request('price_range') == 'under_50' ? 'selected' : '' }}>Under $50</option>
                                <option value="50_100" {{ request('price_range') == '50_100' ? 'selected' : '' }}>$50 - $100</option>
                                <option value="100_200" {{ request('price_range') == '100_200' ? 'selected' : '' }}>$100 - $200</option>
                                <option value="over_200" {{ request('price_range') == 'over_200' ? 'selected' : '' }}>Over $200</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="duration">Duration</label>
                            <select class="form-control" id="duration" name="duration">
                                <option value="">All Durations</option>
                                <option value="under_30" {{ request('duration') == 'under_30' ? 'selected' : '' }}>Under 30 min</option>
                                <option value="30_60" {{ request('duration') == '30_60' ? 'selected' : '' }}>30-60 min</option>
                                <option value="60_120" {{ request('duration') == '60_120' ? 'selected' : '' }}>1-2 hours</option>
                                <option value="over_120" {{ request('duration') == 'over_120' ? 'selected' : '' }}>Over 2 hours</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Services List --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-concierge-bell mr-2"></i>
                Services List
            </h3>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>Category</th>
                            <th>Duration</th>
                            <th>Price</th>
                            <th>Bookings</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($services as $service)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="service-icon {{ $service->category ? 'bg-primary' : 'bg-secondary' }} text-white mr-3"
                                             style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="{{ $service->category->icon ?? 'fas fa-concierge-bell' }}"></i>
                                        </div>
                                        <div>
                                            <strong>{{ $service->name }}</strong>
                                            @if($service->short_description)
                                                <br><small class="text-muted">{{ Str::limit($service->short_description, 50) }}</small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($service->category)
                                        <span class="badge badge-info">{{ $service->category->name }}</span>
                                    @else
                                        <span class="badge badge-secondary">No Category</span>
                                    @endif
                                </td>
                                <td>
                                    <strong>{{ $service->duration_minutes }} min</strong>
                                    <br><small class="text-muted">
                                        @if($service->duration_minutes < 60)
                                            Quick service
                                        @elseif($service->duration_minutes <= 120)
                                            Standard duration
                                        @else
                                            Extended service
                                        @endif
                                    </small>
                                </td>
                                <td>
                                    <strong>${{ number_format($service->base_price, 2) }}</strong>
                                    <br><small class="text-muted">Base price</small>
                                    @if($service->deposit_required)
                                        <br><small class="text-warning">
                                            <i class="fas fa-exclamation-triangle"></i> Deposit required
                                        </small>
                                    @endif
                                </td>
                                <td>
                                    <strong>{{ $service->booking_services_count ?? 0 }}</strong>
                                    <br><small class="text-muted">total bookings</small>
                                </td>
                                <td>
                                    @if($service->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-secondary">Inactive</span>
                                    @endif

                                    @if($service->requires_approval)
                                        <br><span class="badge badge-warning">Requires Approval</span>
                                    @endif

                                    @if(!$service->online_booking_enabled)
                                        <br><span class="badge badge-info">In-person Only</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group-vertical" role="group">
                                        <a href="{{ route('owner.services.show', $service) }}" class="btn btn-sm btn-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('owner.services.edit', $service) }}" class="btn btn-sm btn-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('owner.services.duplicate', $service) }}" method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-secondary" title="Duplicate">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </form>
                                        <form action="{{ route('owner.services.toggle-status', $service) }}" method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-sm {{ $service->is_active ? 'btn-outline-secondary' : 'btn-outline-success' }}"
                                                    title="{{ $service->is_active ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas {{ $service->is_active ? 'fa-pause' : 'fa-play' }}"></i>
                                            </button>
                                        </form>
                                        <form action="{{ route('owner.services.destroy', $service) }}" method="POST"
                                              style="display: inline;"
                                              onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-concierge-bell fa-3x mb-3"></i>
                                        <h5>No services found</h5>
                                        <p>You haven't created any services yet, or no services match your current filters.</p>
                                        <a href="{{ route('owner.services.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus mr-2"></i>
                                            Create Your First Service
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">
                <div class="col-sm-12 col-md-5">
                    <div class="dataTables_info">
                        Showing {{ $services->firstItem() ?? 0 }} to {{ $services->lastItem() ?? 0 }} of {{ $services->total() }} services
                    </div>
                </div>
                <div class="col-sm-12 col-md-7">
                    <div class="float-right">
                        {{ $services->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
