@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Hero Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title" 
                   value="{{ old('title', $content['title'] ?? '') }}" 
                   placeholder="Enter your main headline" required>
            <small class="form-text text-muted">This is the main headline visitors will see first</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Subtitle</label>
            <textarea class="form-control" id="subtitle" name="subtitle" rows="3" 
                      placeholder="Enter a compelling subtitle or description">{{ old('subtitle', $content['subtitle'] ?? '') }}</textarea>
            <small class="form-text text-muted">A brief description that supports your main headline</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="cta_text">Primary Button Text</label>
            <input type="text" class="form-control" id="cta_text" name="cta_text" 
                   value="{{ old('cta_text', $content['cta_text'] ?? '') }}" 
                   placeholder="e.g., Book Now">
            <small class="form-text text-muted">Text for your main call-to-action button</small>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="cta_url">Primary Button Link</label>
            <input type="text" class="form-control" id="cta_url" name="cta_url" 
                   value="{{ old('cta_url', $content['cta_url'] ?? '') }}" 
                   placeholder="e.g., #booking or /booking">
            <small class="form-text text-muted">Where the button should link to</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="secondary_cta_text">Secondary Button Text</label>
            <input type="text" class="form-control" id="secondary_cta_text" name="secondary_cta_text" 
                   value="{{ old('secondary_cta_text', $content['secondary_cta_text'] ?? '') }}" 
                   placeholder="e.g., Learn More">
            <small class="form-text text-muted">Text for your secondary button (optional)</small>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="secondary_cta_url">Secondary Button Link</label>
            <input type="text" class="form-control" id="secondary_cta_url" name="secondary_cta_url" 
                   value="{{ old('secondary_cta_url', $content['secondary_cta_url'] ?? '') }}" 
                   placeholder="e.g., #about or /about">
            <small class="form-text text-muted">Where the secondary button should link to</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="background_image">Background Image URL</label>
            <input type="text" class="form-control" id="background_image" name="background_image" 
                   value="{{ old('background_image', $content['background_image'] ?? '') }}" 
                   placeholder="https://example.com/image.jpg">
            <small class="form-text text-muted">URL to your hero background image</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="background_video">Background Video URL</label>
            <input type="text" class="form-control" id="background_video" name="background_video" 
                   value="{{ old('background_video', $content['background_video'] ?? '') }}" 
                   placeholder="https://example.com/video.mp4">
            <small class="form-text text-muted">URL to your hero background video (optional, will override image)</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="text_alignment">Text Alignment</label>
            <select class="form-control" id="text_alignment" name="text_alignment">
                <option value="left" {{ old('text_alignment', $content['text_alignment'] ?? 'center') === 'left' ? 'selected' : '' }}>Left</option>
                <option value="center" {{ old('text_alignment', $content['text_alignment'] ?? 'center') === 'center' ? 'selected' : '' }}>Center</option>
                <option value="right" {{ old('text_alignment', $content['text_alignment'] ?? 'center') === 'right' ? 'selected' : '' }}>Right</option>
            </select>
            <small class="form-text text-muted">How to align the hero text content</small>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="overlay_opacity">Background Overlay Opacity</label>
            <input type="range" class="form-control-range" id="overlay_opacity" name="overlay_opacity" 
                   min="0" max="1" step="0.1" 
                   value="{{ old('overlay_opacity', $content['overlay_opacity'] ?? 0.5) }}">
            <small class="form-text text-muted">
                Darkness of overlay on background image/video (0 = transparent, 1 = opaque)
                <span id="opacity-value" class="font-weight-bold">{{ old('overlay_opacity', $content['overlay_opacity'] ?? 0.5) }}</span>
            </small>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update opacity value display
    const opacitySlider = document.getElementById('overlay_opacity');
    const opacityValue = document.getElementById('opacity-value');
    
    if (opacitySlider && opacityValue) {
        opacitySlider.addEventListener('input', function() {
            opacityValue.textContent = this.value;
        });
    }
});
</script>
