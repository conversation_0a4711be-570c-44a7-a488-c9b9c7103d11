@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title" 
                   value="{{ old('title', $content['title'] ?? '') }}" 
                   placeholder="e.g., Why Choose Us" required>
            <small class="form-text text-muted">The main heading for your features section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Subtitle</label>
            <input type="text" class="form-control" id="subtitle" name="subtitle" 
                   value="{{ old('subtitle', $content['subtitle'] ?? '') }}" 
                   placeholder="e.g., What sets us apart from the competition">
            <small class="form-text text-muted">Optional subtitle to provide more context</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="layout">Layout Style</label>
            <select class="form-control" id="layout" name="layout">
                <option value="grid" {{ ($content['layout'] ?? 'grid') == 'grid' ? 'selected' : '' }}>Grid Layout</option>
                <option value="list" {{ ($content['layout'] ?? 'grid') == 'list' ? 'selected' : '' }}>List Layout</option>
                <option value="carousel" {{ ($content['layout'] ?? 'grid') == 'carousel' ? 'selected' : '' }}>Carousel</option>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="columns">Columns (Grid Layout)</label>
            <select class="form-control" id="columns" name="columns">
                <option value="2" {{ ($content['columns'] ?? 3) == 2 ? 'selected' : '' }}>2 Columns</option>
                <option value="3" {{ ($content['columns'] ?? 3) == 3 ? 'selected' : '' }}>3 Columns</option>
                <option value="4" {{ ($content['columns'] ?? 3) == 4 ? 'selected' : '' }}>4 Columns</option>
            </select>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <label>Features</label>
        <div id="features-container">
            @if(isset($content['features']) && is_array($content['features']))
                @foreach($content['features'] as $index => $feature)
                    <div class="feature-item border rounded p-3 mb-3" data-index="{{ $index }}">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Feature {{ $index + 1 }}</h6>
                            <button type="button" class="btn btn-sm btn-danger remove-feature">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Icon</label>
                                    <input type="text" class="form-control" name="features[{{ $index }}][icon]" 
                                           value="{{ $feature['icon'] ?? 'fas fa-star' }}" 
                                           placeholder="e.g., fas fa-star">
                                    <small class="form-text text-muted">FontAwesome icon class</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Title</label>
                                    <input type="text" class="form-control" name="features[{{ $index }}][title]" 
                                           value="{{ $feature['title'] ?? '' }}" 
                                           placeholder="Feature title" required>
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control" name="features[{{ $index }}][description]" 
                                              rows="2" placeholder="Feature description">{{ $feature['description'] ?? '' }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
        
        <button type="button" class="btn btn-outline-primary" id="add-feature">
            <i class="fas fa-plus mr-1"></i>
            Add Feature
        </button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let featureIndex = {{ count($content['features'] ?? []) }};
    
    document.getElementById('add-feature').addEventListener('click', function() {
        const container = document.getElementById('features-container');
        const featureHtml = `
            <div class="feature-item border rounded p-3 mb-3" data-index="${featureIndex}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">Feature ${featureIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-feature">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Icon</label>
                            <input type="text" class="form-control" name="features[${featureIndex}][icon]" 
                                   value="fas fa-star" placeholder="e.g., fas fa-star">
                            <small class="form-text text-muted">FontAwesome icon class</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Title</label>
                            <input type="text" class="form-control" name="features[${featureIndex}][title]" 
                                   placeholder="Feature title" required>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="form-group">
                            <label>Description</label>
                            <textarea class="form-control" name="features[${featureIndex}][description]" 
                                      rows="2" placeholder="Feature description"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', featureHtml);
        featureIndex++;
    });
    
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-feature')) {
            e.target.closest('.feature-item').remove();
        }
    });
});
</script>
