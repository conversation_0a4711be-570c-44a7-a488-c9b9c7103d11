@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title"
                   value="{{ old('title', $content['title'] ?? '') }}"
                   placeholder="e.g., Ready to Get Started?" required>
            <small class="form-text text-muted">The main heading for your call-to-action section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Subtitle</label>
            <textarea class="form-control" id="subtitle" name="subtitle" rows="2"
                      placeholder="e.g., Book your appointment today and experience our exceptional service">{{ old('subtitle', $content['subtitle'] ?? '') }}</textarea>
            <small class="form-text text-muted">Supporting text to encourage action</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="button_text">Primary Button Text <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="button_text" name="button_text"
                   value="{{ old('button_text', $content['button_text'] ?? '') }}"
                   placeholder="e.g., Book Now" required>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="button_url">Primary Button URL <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="button_url" name="button_url"
                   value="{{ old('button_url', $content['button_url'] ?? '') }}"
                   placeholder="e.g., #booking or full URL" required>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="secondary_button_text">Secondary Button Text (optional)</label>
            <input type="text" class="form-control" id="secondary_button_text" name="secondary_button_text"
                   value="{{ old('secondary_button_text', $content['secondary_button_text'] ?? '') }}"
                   placeholder="e.g., Call Us">
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="secondary_button_url">Secondary Button URL</label>
            <input type="text" class="form-control" id="secondary_button_url" name="secondary_button_url"
                   value="{{ old('secondary_button_url', $content['secondary_button_url'] ?? '') }}"
                   placeholder="e.g., tel:+1234567890">
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Visual Design</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="background_color">Background Color</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color" id="background_color" name="background_color"
                                           value="{{ old('background_color', $content['background_color'] ?? '#007bff') }}">
                                    <input type="text" class="form-control" id="background_color_text" name="background_color_text"
                                           value="{{ old('background_color', $content['background_color'] ?? '#007bff') }}"
                                           placeholder="#007bff">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="text_color">Text Color</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color" id="text_color" name="text_color"
                                           value="{{ old('text_color', $content['text_color'] ?? '#ffffff') }}">
                                    <input type="text" class="form-control" id="text_color_text" name="text_color_text"
                                           value="{{ old('text_color', $content['text_color'] ?? '#ffffff') }}"
                                           placeholder="#ffffff">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="button_style">Button Style</label>
                                <select class="form-control" id="button_style" name="button_style">
                                    <option value="solid" {{ ($content['button_style'] ?? 'solid') == 'solid' ? 'selected' : '' }}>Solid</option>
                                    <option value="outline" {{ ($content['button_style'] ?? 'solid') == 'outline' ? 'selected' : '' }}>Outline</option>
                                    <option value="ghost" {{ ($content['button_style'] ?? 'solid') == 'ghost' ? 'selected' : '' }}>Ghost</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="button_size">Button Size</label>
                                <select class="form-control" id="button_size" name="button_size">
                                    <option value="small" {{ ($content['button_size'] ?? 'large') == 'small' ? 'selected' : '' }}>Small</option>
                                    <option value="medium" {{ ($content['button_size'] ?? 'large') == 'medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="large" {{ ($content['button_size'] ?? 'large') == 'large' ? 'selected' : '' }}>Large</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="background_image">Background Image (optional)</label>
            <div class="input-group">
                <input type="text" class="form-control" id="background_image" name="background_image"
                       value="{{ old('background_image', $content['background_image'] ?? '') }}"
                       placeholder="Enter image URL or upload">
                <div class="input-group-append">
                    <button type="button" class="btn btn-outline-secondary" id="upload-bg-image">
                        <i class="fas fa-upload"></i> Upload
                    </button>
                    @if(!empty($content['background_image']))
                        <button type="button" class="btn btn-outline-danger" id="remove-bg-image">
                            <i class="fas fa-trash"></i>
                        </button>
                    @endif
                </div>
            </div>
            <small class="form-text text-muted">Optional background image for the CTA section. Supported formats: JPEG, PNG, GIF, WebP (max 5MB)</small>

            <!-- Image Preview -->
            <div id="image-preview-container" class="mt-3" style="{{ empty($content['background_image']) ? 'display: none;' : '' }}">
                <div class="card">
                    <div class="card-body p-2">
                        <div class="d-flex align-items-center">
                            <img id="image-preview" src="{{ $content['background_image'] ?? '' }}"
                                 alt="Background Image Preview" class="img-thumbnail me-3"
                                 style="max-width: 100px; max-height: 60px; object-fit: cover;">
                            <div class="flex-grow-1">
                                <small class="text-muted">Current background image</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" id="remove-preview">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden file input for upload -->
<input type="file" id="background-image-file" accept="image/*" style="display: none;">

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Layout Options</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="text_alignment">Text Alignment</label>
                                <select class="form-control" id="text_alignment" name="text_alignment">
                                    <option value="left" {{ ($content['text_alignment'] ?? 'center') == 'left' ? 'selected' : '' }}>Left</option>
                                    <option value="center" {{ ($content['text_alignment'] ?? 'center') == 'center' ? 'selected' : '' }}>Center</option>
                                    <option value="right" {{ ($content['text_alignment'] ?? 'center') == 'right' ? 'selected' : '' }}>Right</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="section_padding">Section Padding</label>
                                <select class="form-control" id="section_padding" name="section_padding">
                                    <option value="small" {{ ($content['section_padding'] ?? 'large') == 'small' ? 'selected' : '' }}>Small</option>
                                    <option value="medium" {{ ($content['section_padding'] ?? 'large') == 'medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="large" {{ ($content['section_padding'] ?? 'large') == 'large' ? 'selected' : '' }}>Large</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="border_radius">Border Radius</label>
                                <select class="form-control" id="border_radius" name="border_radius">
                                    <option value="none" {{ ($content['border_radius'] ?? 'medium') == 'none' ? 'selected' : '' }}>None</option>
                                    <option value="small" {{ ($content['border_radius'] ?? 'medium') == 'small' ? 'selected' : '' }}>Small</option>
                                    <option value="medium" {{ ($content['border_radius'] ?? 'medium') == 'medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="large" {{ ($content['border_radius'] ?? 'medium') == 'large' ? 'selected' : '' }}>Large</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="enable_animation" value="0">
                                <input class="form-check-input" type="checkbox" id="enable_animation" name="enable_animation" value="1"
                                       {{ ($content['enable_animation'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="enable_animation">
                                    Enable hover animations
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="full_width" value="0">
                                <input class="form-check-input" type="checkbox" id="full_width" name="full_width" value="1"
                                       {{ ($content['full_width'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="full_width">
                                    Full width section
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sync color picker with text input
    const backgroundColorPicker = document.getElementById('background_color');
    const backgroundColorText = document.getElementById('background_color_text');
    const textColorPicker = document.getElementById('text_color');
    const textColorText = document.getElementById('text_color_text');

    backgroundColorPicker.addEventListener('change', function() {
        backgroundColorText.value = this.value;
    });

    backgroundColorText.addEventListener('change', function() {
        if (/^#[0-9A-F]{6}$/i.test(this.value)) {
            backgroundColorPicker.value = this.value;
        }
    });

    textColorPicker.addEventListener('change', function() {
        textColorText.value = this.value;
    });

    textColorText.addEventListener('change', function() {
        if (/^#[0-9A-F]{6}$/i.test(this.value)) {
            textColorPicker.value = this.value;
        }
    });

    // Background image upload functionality
    const uploadBtn = document.getElementById('upload-bg-image');
    const fileInput = document.getElementById('background-image-file');
    const backgroundImageInput = document.getElementById('background_image');
    const previewContainer = document.getElementById('image-preview-container');
    const previewImage = document.getElementById('image-preview');
    const removeBtn = document.getElementById('remove-preview');
    const removeBgBtn = document.getElementById('remove-bg-image');

    // Get section ID from the current URL
    const pathParts = window.location.pathname.split('/');
    const sectionId = pathParts[pathParts.indexOf('sections') + 1];

    // Upload button click
    uploadBtn.addEventListener('click', function() {
        fileInput.click();
    });

    // File selection
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            toastr.error('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
            return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            toastr.error('File size must be less than 5MB');
            return;
        }

        // Show loading state
        uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
        uploadBtn.disabled = true;

        // Create FormData
        const formData = new FormData();
        formData.append('image', file);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        // Upload the file
        fetch(`/owner/landing-page/sections/${sectionId}/upload-image`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the input field
                backgroundImageInput.value = data.image_url;

                // Update preview
                previewImage.src = data.image_url;
                previewContainer.style.display = 'block';

                // Show success message
                toastr.success(data.message);

                // Add remove button if not present
                if (!document.getElementById('remove-bg-image')) {
                    const removeButton = document.createElement('button');
                    removeButton.type = 'button';
                    removeButton.className = 'btn btn-outline-danger';
                    removeButton.id = 'remove-bg-image';
                    removeButton.innerHTML = '<i class="fas fa-trash"></i>';
                    uploadBtn.parentNode.appendChild(removeButton);

                    // Add event listener to new remove button
                    removeButton.addEventListener('click', removeBackgroundImage);
                }
            } else {
                toastr.error(data.message || 'Upload failed');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            toastr.error('Upload failed. Please try again.');
        })
        .finally(() => {
            // Reset upload button
            uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload';
            uploadBtn.disabled = false;
            fileInput.value = '';
        });
    });

    // Remove image functionality
    function removeBackgroundImage() {
        backgroundImageInput.value = '';
        previewContainer.style.display = 'none';

        // Remove the remove button
        const removeBgButton = document.getElementById('remove-bg-image');
        if (removeBgButton) {
            removeBgButton.remove();
        }

        toastr.info('Background image removed. Remember to save your changes.');
    }

    // Remove preview button
    if (removeBtn) {
        removeBtn.addEventListener('click', removeBackgroundImage);
    }

    // Remove background image button (if exists)
    if (removeBgBtn) {
        removeBgBtn.addEventListener('click', removeBackgroundImage);
    }
});
</script>
