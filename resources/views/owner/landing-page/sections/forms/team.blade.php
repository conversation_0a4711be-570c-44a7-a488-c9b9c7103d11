@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title"
                   value="{{ old('title', $content['title'] ?? '') }}"
                   placeholder="e.g., Meet Our Team" required>
            <small class="form-text text-muted">The main heading for your team section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Subtitle</label>
            <input type="text" class="form-control" id="subtitle" name="subtitle"
                   value="{{ old('subtitle', $content['subtitle'] ?? '') }}"
                   placeholder="e.g., Get to know the professionals who will serve you">
            <small class="form-text text-muted">Optional subtitle to provide more context</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="layout">Layout Style</label>
            <select class="form-control" id="layout" name="layout">
                <option value="grid" {{ ($content['layout'] ?? 'grid') == 'grid' ? 'selected' : '' }}>Grid Layout</option>
                <option value="list" {{ ($content['layout'] ?? 'grid') == 'list' ? 'selected' : '' }}>List Layout</option>
                <option value="carousel" {{ ($content['layout'] ?? 'grid') == 'carousel' ? 'selected' : '' }}>Carousel</option>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="columns">Columns (Grid Layout)</label>
            <select class="form-control" id="columns" name="columns">
                <option value="2" {{ ($content['columns'] ?? 3) == 2 ? 'selected' : '' }}>2 Columns</option>
                <option value="3" {{ ($content['columns'] ?? 3) == 3 ? 'selected' : '' }}>3 Columns</option>
                <option value="4" {{ ($content['columns'] ?? 3) == 4 ? 'selected' : '' }}>4 Columns</option>
            </select>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Display Options</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_bio" value="0">
                                <input class="form-check-input" type="checkbox" id="show_bio" name="show_bio" value="1"
                                       {{ ($content['show_bio'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_bio">
                                    Show Biography
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_experience" value="0">
                                <input class="form-check-input" type="checkbox" id="show_experience" name="show_experience" value="1"
                                       {{ ($content['show_experience'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_experience">
                                    Show Years of Experience
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_specializations" value="0">
                                <input class="form-check-input" type="checkbox" id="show_specializations" name="show_specializations" value="1"
                                       {{ ($content['show_specializations'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_specializations">
                                    Show Specializations
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_contact" value="0">
                                <input class="form-check-input" type="checkbox" id="show_contact" name="show_contact" value="1"
                                       {{ ($content['show_contact'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_contact">
                                    Show Contact Information
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_social_links" value="0">
                                <input class="form-check-input" type="checkbox" id="show_social_links" name="show_social_links" value="1"
                                       {{ ($content['show_social_links'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_social_links">
                                    Show Social Media Links
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_booking_button" value="0">
                                <input class="form-check-input" type="checkbox" id="show_booking_button" name="show_booking_button" value="1"
                                       {{ ($content['show_booking_button'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_booking_button">
                                    Show "Book with [Name]" Button
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="team_filter">Team Member Filter</label>
            <select class="form-control" id="team_filter" name="team_filter">
                <option value="all" {{ ($content['team_filter'] ?? 'active_only') == 'all' ? 'selected' : '' }}>Show All Team Members</option>
                <option value="active_only" {{ ($content['team_filter'] ?? 'active_only') == 'active_only' ? 'selected' : '' }}>Active Members Only</option>
                <option value="landing_visible" {{ ($content['team_filter'] ?? 'active_only') == 'landing_visible' ? 'selected' : '' }}>Landing Page Visible Only</option>
                <option value="accepts_bookings" {{ ($content['team_filter'] ?? 'active_only') == 'accepts_bookings' ? 'selected' : '' }}>Accepts Bookings Only</option>
            </select>
            <small class="form-text text-muted">Choose which team members to display</small>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="sort_order">Sort Order</label>
            <select class="form-control" id="sort_order" name="sort_order">
                <option value="sort_order" {{ ($content['sort_order'] ?? 'sort_order') == 'sort_order' ? 'selected' : '' }}>Custom Order</option>
                <option value="name" {{ ($content['sort_order'] ?? 'sort_order') == 'name' ? 'selected' : '' }}>Alphabetical by Name</option>
                <option value="experience" {{ ($content['sort_order'] ?? 'sort_order') == 'experience' ? 'selected' : '' }}>Years of Experience</option>
                <option value="position" {{ ($content['sort_order'] ?? 'sort_order') == 'position' ? 'selected' : '' }}>By Position</option>
            </select>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle mr-2"></i>
    <strong>Note:</strong> Team members are managed in the <a href="{{ route('owner.staff.index') }}" target="_blank">Staff Management</a> section.
    Make sure to add team members there and mark them as "Show on Landing Page" to display them here.
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Advanced Settings</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max_members">Maximum Members to Show</label>
                                <input type="number" class="form-control" id="max_members" name="max_members"
                                       value="{{ old('max_members', $content['max_members'] ?? 0) }}"
                                       min="0" max="20">
                                <small class="form-text text-muted">0 = Show all members</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="image_style">Image Style</label>
                                <select class="form-control" id="image_style" name="image_style">
                                    <option value="square" {{ ($content['image_style'] ?? 'circle') == 'square' ? 'selected' : '' }}>Square</option>
                                    <option value="circle" {{ ($content['image_style'] ?? 'circle') == 'circle' ? 'selected' : '' }}>Circle</option>
                                    <option value="rounded" {{ ($content['image_style'] ?? 'circle') == 'rounded' ? 'selected' : '' }}>Rounded Corners</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="enable_modal" value="0">
                                <input class="form-check-input" type="checkbox" id="enable_modal" name="enable_modal" value="1"
                                       {{ ($content['enable_modal'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="enable_modal">
                                    Enable detailed view modal when clicking on team member
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
