@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title"
                   value="{{ old('title', $content['title'] ?? '') }}"
                   placeholder="e.g., Our Pricing" required>
            <small class="form-text text-muted">The main heading for your pricing section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Subtitle</label>
            <input type="text" class="form-control" id="subtitle" name="subtitle"
                   value="{{ old('subtitle', $content['subtitle'] ?? '') }}"
                   placeholder="e.g., Choose the package that works best for you">
            <small class="form-text text-muted">Optional subtitle to provide more context</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="layout">Layout Style</label>
            <select class="form-control" id="layout" name="layout">
                <option value="cards" {{ ($content['layout'] ?? 'cards') == 'cards' ? 'selected' : '' }}>Pricing Cards</option>
                <option value="table" {{ ($content['layout'] ?? 'cards') == 'table' ? 'selected' : '' }}>Comparison Table</option>
                <option value="list" {{ ($content['layout'] ?? 'cards') == 'list' ? 'selected' : '' }}>Simple List</option>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="columns">Columns (Cards Layout)</label>
            <select class="form-control" id="columns" name="columns">
                <option value="2" {{ ($content['columns'] ?? 3) == 2 ? 'selected' : '' }}>2 Columns</option>
                <option value="3" {{ ($content['columns'] ?? 3) == 3 ? 'selected' : '' }}>3 Columns</option>
                <option value="4" {{ ($content['columns'] ?? 3) == 4 ? 'selected' : '' }}>4 Columns</option>
            </select>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Display Options</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_features" value="0">
                                <input class="form-check-input" type="checkbox" id="show_features" name="show_features" value="1"
                                       {{ ($content['show_features'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_features">
                                    Show Package Features
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_popular_badge" value="0">
                                <input class="form-check-input" type="checkbox" id="show_popular_badge" name="show_popular_badge" value="1"
                                       {{ ($content['show_popular_badge'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_popular_badge">
                                    Show "Most Popular" Badge
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_booking_button" value="0">
                                <input class="form-check-input" type="checkbox" id="show_booking_button" name="show_booking_button" value="1"
                                       {{ ($content['show_booking_button'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_booking_button">
                                    Show Booking Buttons
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle mr-2"></i>
    <strong>Note:</strong> You can also display your individual service prices by enabling the pricing display in the Services section.
    This pricing section is for package deals or bundled services.
</div>

<div class="row">
    <div class="col-md-12">
        <label>Pricing Packages</label>
        <div id="packages-container">
            @if(isset($content['packages']) && is_array($content['packages']))
                @foreach($content['packages'] as $index => $package)
                    <div class="package-item border rounded p-3 mb-3" data-index="{{ $index }}">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Package {{ $index + 1 }}</h6>
                            <button type="button" class="btn btn-sm btn-danger remove-package">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Package Name</label>
                                    <input type="text" class="form-control" name="packages[{{ $index }}][name]"
                                           value="{{ $package['name'] ?? '' }}"
                                           placeholder="e.g., Basic Package" required>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Price</label>
                                    <input type="text" class="form-control" name="packages[{{ $index }}][price]"
                                           value="{{ $package['price'] ?? '' }}"
                                           placeholder="e.g., $99">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Period</label>
                                    <input type="text" class="form-control" name="packages[{{ $index }}][period]"
                                           value="{{ $package['period'] ?? '' }}"
                                           placeholder="e.g., per month">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Description</label>
                                    <input type="text" class="form-control" name="packages[{{ $index }}][description]"
                                           value="{{ $package['description'] ?? '' }}"
                                           placeholder="Brief description">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <!-- Hidden input to ensure a value is always sent -->
                                        <input type="hidden" name="packages[{{ $index }}][popular]" value="0">
                                        <input class="form-check-input" type="checkbox"
                                               name="packages[{{ $index }}][popular]" value="1"
                                               {{ ($package['popular'] ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label">
                                            Most Popular
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <label>Features (one per line)</label>
                                <textarea class="form-control" name="packages[{{ $index }}][features]"
                                          rows="4" placeholder="Feature 1&#10;Feature 2&#10;Feature 3">{{ is_array($package['features'] ?? []) ? implode("\n", $package['features']) : ($package['features'] ?? '') }}</textarea>
                                <small class="form-text text-muted">Enter each feature on a new line</small>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Button Text</label>
                                    <input type="text" class="form-control" name="packages[{{ $index }}][button_text]"
                                           value="{{ $package['button_text'] ?? 'Choose Plan' }}"
                                           placeholder="e.g., Choose Plan">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Button URL</label>
                                    <input type="text" class="form-control" name="packages[{{ $index }}][button_url]"
                                           value="{{ $package['button_url'] ?? '#booking' }}"
                                           placeholder="e.g., #booking or full URL">
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>

        <button type="button" class="btn btn-outline-primary" id="add-package">
            <i class="fas fa-plus mr-1"></i>
            Add Package
        </button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let packageIndex = {{ count($content['packages'] ?? []) }};

    document.getElementById('add-package').addEventListener('click', function() {
        const container = document.getElementById('packages-container');
        const packageHtml = `
            <div class="package-item border rounded p-3 mb-3" data-index="${packageIndex}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">Package ${packageIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-package">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Package Name</label>
                            <input type="text" class="form-control" name="packages[${packageIndex}][name]"
                                   placeholder="e.g., Basic Package" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Price</label>
                            <input type="text" class="form-control" name="packages[${packageIndex}][price]"
                                   placeholder="e.g., $99">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Period</label>
                            <input type="text" class="form-control" name="packages[${packageIndex}][period]"
                                   placeholder="e.g., per month">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Description</label>
                            <input type="text" class="form-control" name="packages[${packageIndex}][description]"
                                   placeholder="Brief description">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <div class="form-check mt-4">
                                <input type="hidden" name="packages[${packageIndex}][popular]" value="0">
                                <input class="form-check-input" type="checkbox"
                                       name="packages[${packageIndex}][popular]" value="1">
                                <label class="form-check-label">
                                    Most Popular
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <label>Features (one per line)</label>
                        <textarea class="form-control" name="packages[${packageIndex}][features]"
                                  rows="4" placeholder="Feature 1&#10;Feature 2&#10;Feature 3"></textarea>
                        <small class="form-text text-muted">Enter each feature on a new line</small>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Button Text</label>
                            <input type="text" class="form-control" name="packages[${packageIndex}][button_text]"
                                   value="Choose Plan" placeholder="e.g., Choose Plan">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Button URL</label>
                            <input type="text" class="form-control" name="packages[${packageIndex}][button_url]"
                                   value="#booking" placeholder="e.g., #booking or full URL">
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', packageHtml);
        packageIndex++;
    });

    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-package')) {
            e.target.closest('.package-item').remove();
        }
    });
});
</script>
