@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title" 
                   value="{{ old('title', $content['title'] ?? '') }}" 
                   placeholder="e.g., About Our Business" required>
            <small class="form-text text-muted">The main heading for your about section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="content">About Content <span class="text-danger">*</span></label>
            <textarea class="form-control" id="content" name="content" rows="6" 
                      placeholder="Tell your story, describe your business, values, and what makes you special..." required>{{ old('content', $content['content'] ?? '') }}</textarea>
            <small class="form-text text-muted">Describe your business, mission, values, and what makes you unique</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="image">About Image URL</label>
            <input type="text" class="form-control" id="image" name="image" 
                   value="{{ old('image', $content['image'] ?? '') }}" 
                   placeholder="https://example.com/about-image.jpg">
            <small class="form-text text-muted">URL to an image that represents your business</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="layout">Layout Style</label>
            <select class="form-control" id="layout" name="layout">
                <option value="image-left" {{ old('layout', $content['layout'] ?? 'image-left') === 'image-left' ? 'selected' : '' }}>Image on Left</option>
                <option value="image-right" {{ old('layout', $content['layout'] ?? 'image-left') === 'image-right' ? 'selected' : '' }}>Image on Right</option>
                <option value="image-top" {{ old('layout', $content['layout'] ?? 'image-left') === 'image-top' ? 'selected' : '' }}>Image on Top</option>
                <option value="no-image" {{ old('layout', $content['layout'] ?? 'image-left') === 'no-image' ? 'selected' : '' }}>Text Only</option>
            </select>
            <small class="form-text text-muted">How to arrange the content and image</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Key Features/Highlights</label>
            <div id="features-container">
                @php
                    $features = old('features', $content['features'] ?? []);
                    if (empty($features)) {
                        $features = [''];
                    }
                @endphp
                
                @foreach($features as $index => $feature)
                    <div class="feature-item mb-2">
                        <div class="input-group">
                            <input type="text" class="form-control" name="features[]" 
                                   value="{{ $feature }}" 
                                   placeholder="e.g., 10+ years of experience">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-outline-danger remove-feature" 
                                        {{ $index === 0 ? 'style=display:none;' : '' }}>
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            <button type="button" class="btn btn-outline-primary btn-sm" id="add-feature">
                <i class="fas fa-plus mr-1"></i>
                Add Feature
            </button>
            <small class="form-text text-muted">Highlight key features or benefits of your business</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Business Statistics</label>
            <div id="stats-container">
                @php
                    $stats = old('stats', $content['stats'] ?? []);
                    if (empty($stats)) {
                        $stats = [['label' => '', 'value' => '']];
                    }
                @endphp
                
                @foreach($stats as $index => $stat)
                    <div class="stat-item mb-2">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" class="form-control" name="stats[{{ $index }}][value]" 
                                       value="{{ $stat['value'] ?? '' }}" 
                                       placeholder="e.g., 500+">
                            </div>
                            <div class="col-md-5">
                                <input type="text" class="form-control" name="stats[{{ $index }}][label]" 
                                       value="{{ $stat['label'] ?? '' }}" 
                                       placeholder="e.g., Happy Customers">
                            </div>
                            <div class="col-md-1">
                                <button type="button" class="btn btn-outline-danger remove-stat" 
                                        {{ $index === 0 ? 'style=display:none;' : '' }}>
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            <button type="button" class="btn btn-outline-primary btn-sm" id="add-stat">
                <i class="fas fa-plus mr-1"></i>
                Add Statistic
            </button>
            <small class="form-text text-muted">Add impressive numbers about your business (customers served, years in business, etc.)</small>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add feature functionality
    document.getElementById('add-feature').addEventListener('click', function() {
        const container = document.getElementById('features-container');
        const newFeature = document.createElement('div');
        newFeature.className = 'feature-item mb-2';
        newFeature.innerHTML = `
            <div class="input-group">
                <input type="text" class="form-control" name="features[]" placeholder="e.g., Professional staff">
                <div class="input-group-append">
                    <button type="button" class="btn btn-outline-danger remove-feature">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newFeature);
        updateFeatureButtons();
    });

    // Remove feature functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-feature')) {
            e.target.closest('.feature-item').remove();
            updateFeatureButtons();
        }
    });

    // Add stat functionality
    document.getElementById('add-stat').addEventListener('click', function() {
        const container = document.getElementById('stats-container');
        const index = container.children.length;
        const newStat = document.createElement('div');
        newStat.className = 'stat-item mb-2';
        newStat.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control" name="stats[${index}][value]" placeholder="e.g., 100%">
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control" name="stats[${index}][label]" placeholder="e.g., Satisfaction Rate">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-outline-danger remove-stat">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newStat);
        updateStatButtons();
    });

    // Remove stat functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-stat')) {
            e.target.closest('.stat-item').remove();
            updateStatButtons();
        }
    });

    function updateFeatureButtons() {
        const features = document.querySelectorAll('.feature-item');
        features.forEach((feature, index) => {
            const removeBtn = feature.querySelector('.remove-feature');
            if (features.length === 1) {
                removeBtn.style.display = 'none';
            } else {
                removeBtn.style.display = 'block';
            }
        });
    }

    function updateStatButtons() {
        const stats = document.querySelectorAll('.stat-item');
        stats.forEach((stat, index) => {
            const removeBtn = stat.querySelector('.remove-stat');
            if (stats.length === 1) {
                removeBtn.style.display = 'none';
            } else {
                removeBtn.style.display = 'block';
            }
        });
    }
});
</script>
