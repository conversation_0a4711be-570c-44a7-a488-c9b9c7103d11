@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title"
                   value="{{ old('title', $content['title'] ?? '') }}"
                   placeholder="e.g., Our Services" required>
            <small class="form-text text-muted">The main heading for your services section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Section Subtitle</label>
            <textarea class="form-control" id="subtitle" name="subtitle" rows="2"
                      placeholder="Brief description of your services...">{{ old('subtitle', $content['subtitle'] ?? '') }}</textarea>
            <small class="form-text text-muted">A brief description that introduces your services</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="layout">Display Layout</label>
            <select class="form-control" id="layout" name="layout">
                <option value="grid" {{ old('layout', $content['layout'] ?? 'grid') === 'grid' ? 'selected' : '' }}>Grid Layout</option>
                <option value="list" {{ old('layout', $content['layout'] ?? 'grid') === 'list' ? 'selected' : '' }}>List Layout</option>
                <option value="carousel" {{ old('layout', $content['layout'] ?? 'grid') === 'carousel' ? 'selected' : '' }}>Carousel/Slider</option>
            </select>
            <small class="form-text text-muted">How to display your services</small>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="columns">Columns (Grid Layout)</label>
            <select class="form-control" id="columns" name="columns">
                <option value="1" {{ old('columns', $content['columns'] ?? 3) == 1 ? 'selected' : '' }}>1 Column</option>
                <option value="2" {{ old('columns', $content['columns'] ?? 3) == 2 ? 'selected' : '' }}>2 Columns</option>
                <option value="3" {{ old('columns', $content['columns'] ?? 3) == 3 ? 'selected' : '' }}>3 Columns</option>
                <option value="4" {{ old('columns', $content['columns'] ?? 3) == 4 ? 'selected' : '' }}>4 Columns</option>
            </select>
            <small class="form-text text-muted">Number of columns for grid layout</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Display Options</label>
            <div class="row">
                <div class="col-md-6">
                    <div class="custom-control custom-checkbox mb-2">
                        <input type="hidden" name="show_prices" value="0">
                        <input type="checkbox" class="custom-control-input" id="show_prices" name="show_prices" value="1"
                               {{ old('show_prices', $content['show_prices'] ?? true) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="show_prices">
                            Show Service Prices
                        </label>
                    </div>

                    <div class="custom-control custom-checkbox mb-2">
                        <input type="hidden" name="show_duration" value="0">
                        <input type="checkbox" class="custom-control-input" id="show_duration" name="show_duration" value="1"
                               {{ old('show_duration', $content['show_duration'] ?? true) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="show_duration">
                            Show Service Duration
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="custom-control custom-checkbox mb-2">
                        <input type="hidden" name="show_description" value="0">
                        <input type="checkbox" class="custom-control-input" id="show_description" name="show_description" value="1"
                               {{ old('show_description', $content['show_description'] ?? true) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="show_description">
                            Show Service Descriptions
                        </label>
                    </div>

                    <div class="custom-control custom-checkbox mb-2">
                        <input type="hidden" name="show_booking_button" value="0">
                        <input type="checkbox" class="custom-control-input" id="show_booking_button" name="show_booking_button" value="1"
                               {{ old('show_booking_button', $content['show_booking_button'] ?? true) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="show_booking_button">
                            Show Booking Buttons
                        </label>
                    </div>
                </div>
            </div>
            <small class="form-text text-muted">Choose what information to display for each service</small>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle mr-2"></i>
    <strong>Note:</strong> The actual services displayed will be pulled from your Services section.
    This configuration controls how they are presented on your landing page.
    <br><br>
    <a href="{{ route('owner.services.index') }}" class="btn btn-sm btn-outline-primary mt-2">
        <i class="fas fa-cog mr-1"></i>
        Manage Services
    </a>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide columns option based on layout
    const layoutSelect = document.getElementById('layout');
    const columnsGroup = document.getElementById('columns').closest('.form-group');

    function toggleColumnsVisibility() {
        if (layoutSelect.value === 'grid') {
            columnsGroup.style.display = 'block';
        } else {
            columnsGroup.style.display = 'none';
        }
    }

    layoutSelect.addEventListener('change', toggleColumnsVisibility);
    toggleColumnsVisibility(); // Initial check
});
</script>
