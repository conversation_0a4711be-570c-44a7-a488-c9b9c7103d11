@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title"
                   value="{{ old('title', $content['title'] ?? '') }}"
                   placeholder="e.g., Get in Touch" required>
            <small class="form-text text-muted">The main heading for your contact section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Section Subtitle</label>
            <textarea class="form-control" id="subtitle" name="subtitle" rows="2"
                      placeholder="We'd love to hear from you...">{{ old('subtitle', $content['subtitle'] ?? '') }}</textarea>
            <small class="form-text text-muted">A brief message encouraging visitors to contact you</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Contact Elements</label>
            <div class="row">
                <div class="col-md-6">
                    <div class="custom-control custom-checkbox mb-2">
                        <input type="hidden" name="show_contact_form" value="0">
                        <input type="checkbox" class="custom-control-input" id="show_contact_form" name="show_contact_form" value="1"
                               {{ old('show_contact_form', $content['show_contact_form'] ?? true) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="show_contact_form">
                            <strong>Show Contact Form</strong>
                            <small class="text-muted d-block">Allow visitors to send messages directly</small>
                        </label>
                    </div>

                    <div class="custom-control custom-checkbox mb-2">
                        <input type="hidden" name="show_map" value="0">
                        <input type="checkbox" class="custom-control-input" id="show_map" name="show_map" value="1"
                               {{ old('show_map', $content['show_map'] ?? true) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="show_map">
                            <strong>Show Location Map</strong>
                            <small class="text-muted d-block">Display your business location on a map</small>
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="custom-control custom-checkbox mb-2">
                        <input type="hidden" name="show_hours" value="0">
                        <input type="checkbox" class="custom-control-input" id="show_hours" name="show_hours" value="1"
                               {{ old('show_hours', $content['show_hours'] ?? true) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="show_hours">
                            <strong>Show Business Hours</strong>
                            <small class="text-muted d-block">Display your operating hours</small>
                        </label>
                    </div>

                    <div class="custom-control custom-checkbox mb-2">
                        <input type="hidden" name="show_social_links" value="0">
                        <input type="checkbox" class="custom-control-input" id="show_social_links" name="show_social_links" value="1"
                               {{ old('show_social_links', $content['show_social_links'] ?? true) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="show_social_links">
                            <strong>Show Social Media Links</strong>
                            <small class="text-muted d-block">Link to your social media profiles</small>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row" id="map-settings">
    <div class="col-md-12">
        <div class="form-group">
            <label for="map_zoom">Map Zoom Level</label>
            <input type="range" class="form-control-range" id="map_zoom" name="map_zoom"
                   min="1" max="20" step="1"
                   value="{{ old('map_zoom', $content['map_zoom'] ?? 15) }}">
            <small class="form-text text-muted">
                Map zoom level (1 = world view, 20 = street level)
                <span id="zoom-value" class="font-weight-bold">{{ old('map_zoom', $content['map_zoom'] ?? 15) }}</span>
            </small>
        </div>
    </div>
</div>

<div class="row" id="form-settings">
    <div class="col-md-12">
        <div class="form-group">
            <label>Contact Form Fields</label>
            <div class="row">
                @php
                    $formFields = old('contact_form_fields', $content['contact_form_fields'] ?? ['name', 'email', 'phone', 'message']);
                @endphp

                <div class="col-md-6">
                    <div class="custom-control custom-checkbox mb-2">
                        <input type="checkbox" class="custom-control-input" id="field_name" name="contact_form_fields[]" value="name"
                               {{ in_array('name', $formFields) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="field_name">
                            Name Field
                        </label>
                    </div>

                    <div class="custom-control custom-checkbox mb-2">
                        <input type="checkbox" class="custom-control-input" id="field_email" name="contact_form_fields[]" value="email"
                               {{ in_array('email', $formFields) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="field_email">
                            Email Field
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="custom-control custom-checkbox mb-2">
                        <input type="checkbox" class="custom-control-input" id="field_phone" name="contact_form_fields[]" value="phone"
                               {{ in_array('phone', $formFields) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="field_phone">
                            Phone Field
                        </label>
                    </div>

                    <div class="custom-control custom-checkbox mb-2">
                        <input type="checkbox" class="custom-control-input" id="field_message" name="contact_form_fields[]" value="message"
                               {{ in_array('message', $formFields) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="field_message">
                            Message Field
                        </label>
                    </div>
                </div>
            </div>
            <small class="form-text text-muted">Select which fields to include in your contact form</small>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle mr-2"></i>
    <strong>Note:</strong> Contact information (address, phone, email) will be pulled from your business profile.
    Business hours and social media links can be configured in your business settings.
    <br><br>
    <a href="{{ route('owner.business.general') }}" class="btn btn-sm btn-outline-primary mt-2">
        <i class="fas fa-cog mr-1"></i>
        Update Business Info
    </a>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update zoom value display
    const zoomSlider = document.getElementById('map_zoom');
    const zoomValue = document.getElementById('zoom-value');

    if (zoomSlider && zoomValue) {
        zoomSlider.addEventListener('input', function() {
            zoomValue.textContent = this.value;
        });
    }

    // Show/hide settings based on checkboxes
    function toggleSettings() {
        const showMap = document.getElementById('show_map').checked;
        const showForm = document.getElementById('show_contact_form').checked;

        document.getElementById('map-settings').style.display = showMap ? 'block' : 'none';
        document.getElementById('form-settings').style.display = showForm ? 'block' : 'none';
    }

    document.getElementById('show_map').addEventListener('change', toggleSettings);
    document.getElementById('show_contact_form').addEventListener('change', toggleSettings);

    // Initial toggle
    toggleSettings();
});
</script>
