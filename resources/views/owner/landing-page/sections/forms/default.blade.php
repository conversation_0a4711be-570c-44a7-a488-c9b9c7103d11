@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title" 
                   value="{{ old('title', $content['title'] ?? '') }}" 
                   placeholder="Enter section title" required>
            <small class="form-text text-muted">The main heading for this section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="content">Section Content <span class="text-danger">*</span></label>
            <textarea class="form-control" id="content" name="content" rows="8" 
                      placeholder="Enter your content here..." required>{{ old('content', $content['content'] ?? '') }}</textarea>
            <small class="form-text text-muted">The main content for this section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="layout">Layout Style</label>
            <select class="form-control" id="layout" name="layout">
                <option value="default" {{ old('layout', $content['layout'] ?? 'default') === 'default' ? 'selected' : '' }}>Default</option>
                <option value="centered" {{ old('layout', $content['layout'] ?? 'default') === 'centered' ? 'selected' : '' }}>Centered</option>
                <option value="full-width" {{ old('layout', $content['layout'] ?? 'default') === 'full-width' ? 'selected' : '' }}>Full Width</option>
            </select>
            <small class="form-text text-muted">Choose how this section should be displayed</small>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle mr-2"></i>
    <strong>Custom Section:</strong> This is a {{ ucfirst($section->section_type) }} section. 
    You can customize the content and layout to fit your needs.
</div>
