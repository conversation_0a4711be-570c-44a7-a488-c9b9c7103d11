@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title"
                   value="{{ old('title', $content['title'] ?? '') }}"
                   placeholder="e.g., Our Work" required>
            <small class="form-text text-muted">The main heading for your gallery section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Subtitle</label>
            <input type="text" class="form-control" id="subtitle" name="subtitle"
                   value="{{ old('subtitle', $content['subtitle'] ?? '') }}"
                   placeholder="e.g., Take a look at our facilities and previous work">
            <small class="form-text text-muted">Optional subtitle to provide more context</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="layout">Layout Style</label>
            <select class="form-control" id="layout" name="layout">
                <option value="masonry" {{ ($content['layout'] ?? 'masonry') == 'masonry' ? 'selected' : '' }}>Masonry (Pinterest-style)</option>
                <option value="grid" {{ ($content['layout'] ?? 'masonry') == 'grid' ? 'selected' : '' }}>Grid Layout</option>
                <option value="carousel" {{ ($content['layout'] ?? 'masonry') == 'carousel' ? 'selected' : '' }}>Carousel</option>
                <option value="slider" {{ ($content['layout'] ?? 'masonry') == 'slider' ? 'selected' : '' }}>Full-width Slider</option>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="columns">Columns</label>
            <select class="form-control" id="columns" name="columns">
                <option value="2" {{ ($content['columns'] ?? 4) == 2 ? 'selected' : '' }}>2 Columns</option>
                <option value="3" {{ ($content['columns'] ?? 4) == 3 ? 'selected' : '' }}>3 Columns</option>
                <option value="4" {{ ($content['columns'] ?? 4) == 4 ? 'selected' : '' }}>4 Columns</option>
                <option value="5" {{ ($content['columns'] ?? 4) == 5 ? 'selected' : '' }}>5 Columns</option>
                <option value="6" {{ ($content['columns'] ?? 4) == 6 ? 'selected' : '' }}>6 Columns</option>
            </select>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Display Options</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="lightbox_enabled" value="0">
                                <input class="form-check-input" type="checkbox" id="lightbox_enabled" name="lightbox_enabled" value="1"
                                       {{ ($content['lightbox_enabled'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="lightbox_enabled">
                                    Enable Lightbox (Click to enlarge)
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_captions" value="0">
                                <input class="form-check-input" type="checkbox" id="show_captions" name="show_captions" value="1"
                                       {{ ($content['show_captions'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_captions">
                                    Show Image Captions
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="filter_enabled" value="0">
                                <input class="form-check-input" type="checkbox" id="filter_enabled" name="filter_enabled" value="1"
                                       {{ ($content['filter_enabled'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="filter_enabled">
                                    Enable Category Filters
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="lazy_loading" value="0">
                                <input class="form-check-input" type="checkbox" id="lazy_loading" name="lazy_loading" value="1"
                                       {{ ($content['lazy_loading'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="lazy_loading">
                                    Enable Lazy Loading
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="hover_effects" value="0">
                                <input class="form-check-input" type="checkbox" id="hover_effects" name="hover_effects" value="1"
                                       {{ ($content['hover_effects'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="hover_effects">
                                    Enable Hover Effects
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="show_load_more" value="0">
                                <input class="form-check-input" type="checkbox" id="show_load_more" name="show_load_more" value="1"
                                       {{ ($content['show_load_more'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_load_more">
                                    Show "Load More" Button
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="images_per_page">Images Per Page</label>
            <input type="number" class="form-control" id="images_per_page" name="images_per_page"
                   value="{{ old('images_per_page', $content['images_per_page'] ?? 12) }}"
                   min="6" max="50">
            <small class="form-text text-muted">Number of images to show initially</small>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="image_quality">Image Quality</label>
            <select class="form-control" id="image_quality" name="image_quality">
                <option value="thumbnail" {{ ($content['image_quality'] ?? 'medium') == 'thumbnail' ? 'selected' : '' }}>Thumbnail (Fast loading)</option>
                <option value="medium" {{ ($content['image_quality'] ?? 'medium') == 'medium' ? 'selected' : '' }}>Medium Quality</option>
                <option value="high" {{ ($content['image_quality'] ?? 'medium') == 'high' ? 'selected' : '' }}>High Quality</option>
                <option value="original" {{ ($content['image_quality'] ?? 'medium') == 'original' ? 'selected' : '' }}>Original Size</option>
            </select>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle mr-2"></i>
    <strong>Note:</strong> Gallery images are managed in the <a href="{{ route('owner.gallery.index') }}" target="_blank">Gallery Management</a> section.
    Upload and organize your images there to display them in this section.
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Image Categories (for filtering)</label>
            <div class="card">
                <div class="card-body">
                    <div id="categories-container">
                        @if(isset($content['categories']) && is_array($content['categories']))
                            @foreach($content['categories'] as $index => $category)
                                <div class="category-item d-flex align-items-center mb-2" data-index="{{ $index }}">
                                    <input type="text" class="form-control me-2" name="categories[{{ $index }}][name]"
                                           value="{{ $category['name'] ?? '' }}" placeholder="Category name">
                                    <input type="text" class="form-control me-2" name="categories[{{ $index }}][slug]"
                                           value="{{ $category['slug'] ?? '' }}" placeholder="category-slug">
                                    <button type="button" class="btn btn-sm btn-danger remove-category">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endforeach
                        @endif
                    </div>

                    <button type="button" class="btn btn-outline-primary btn-sm" id="add-category">
                        <i class="fas fa-plus mr-1"></i>
                        Add Category
                    </button>

                    <small class="form-text text-muted mt-2">
                        Categories allow visitors to filter images. Make sure to assign categories to your images in Gallery Management.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let categoryIndex = {{ count($content['categories'] ?? []) }};

    document.getElementById('add-category').addEventListener('click', function() {
        const container = document.getElementById('categories-container');
        const categoryHtml = `
            <div class="category-item d-flex align-items-center mb-2" data-index="${categoryIndex}">
                <input type="text" class="form-control me-2" name="categories[${categoryIndex}][name]"
                       placeholder="Category name">
                <input type="text" class="form-control me-2" name="categories[${categoryIndex}][slug]"
                       placeholder="category-slug">
                <button type="button" class="btn btn-sm btn-danger remove-category">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', categoryHtml);
        categoryIndex++;
    });

    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-category')) {
            e.target.closest('.category-item').remove();
        }
    });

    // Auto-generate slug from name
    document.addEventListener('input', function(e) {
        if (e.target.name && e.target.name.includes('[name]')) {
            const slugInput = e.target.parentElement.querySelector('input[name*="[slug]"]');
            if (slugInput) {
                slugInput.value = e.target.value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
            }
        }
    });
});
</script>
