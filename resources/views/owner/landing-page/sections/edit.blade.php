@extends('owner.layouts.app')

@section('title', 'Edit ' . ucfirst($section->section_name))

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Edit {{ ucfirst($section->section_name) }}</h1>
            <p class="text-muted mb-0">Customize the content and appearance of your {{ strtolower($section->section_name) }} section.</p>
        </div>
        <div>
            <a href="{{ route('owner.landing-page.edit') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Landing Page
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit mr-2"></i>
                        Section Content
                    </h3>
                </div>
                <div class="card-body">
                    <form id="section-form" action="{{ route('owner.landing-page.sections.update', $section->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        @if($section->section_type === 'hero')
                            @include('owner.landing-page.sections.forms.hero')
                        @elseif($section->section_type === 'about')
                            @include('owner.landing-page.sections.forms.about')
                        @elseif($section->section_type === 'services')
                            @include('owner.landing-page.sections.forms.services')
                        @elseif($section->section_type === 'features')
                            @include('owner.landing-page.sections.forms.features')
                        @elseif($section->section_type === 'testimonials')
                            @include('owner.landing-page.sections.forms.testimonials')
                        @elseif($section->section_type === 'team')
                            @include('owner.landing-page.sections.forms.team')
                        @elseif($section->section_type === 'gallery')
                            @include('owner.landing-page.sections.forms.gallery')
                        @elseif($section->section_type === 'pricing')
                            @include('owner.landing-page.sections.forms.pricing')
                        @elseif($section->section_type === 'faq')
                            @include('owner.landing-page.sections.forms.faq')
                        @elseif($section->section_type === 'cta')
                            @include('owner.landing-page.sections.forms.cta')
                        @elseif($section->section_type === 'contact')
                            @include('owner.landing-page.sections.forms.contact')
                        @else
                            @include('owner.landing-page.sections.forms.default')
                        @endif

                        <div class="form-group mt-4">
                            <div class="custom-control custom-switch">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="is_visible" value="0">
                                <input type="checkbox" class="custom-control-input" id="is_visible" name="is_visible" value="1" {{ $section->is_visible ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_visible">
                                    <strong>Section Visible</strong>
                                    <small class="text-muted d-block">Toggle to show or hide this section on your landing page</small>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Save Changes
                            </button>
                            <button type="button" class="btn btn-info ml-2" id="preview-section">
                                <i class="fas fa-eye mr-2"></i>
                                Preview Section
                            </button>
                            <button type="button" class="btn btn-warning ml-2" id="test-connection">
                                <i class="fas fa-network-wired mr-2"></i>
                                Test Connection
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Section Info -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Section Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-item mb-3">
                        <strong>Section Type:</strong>
                        <div class="mt-1">
                            <span class="badge badge-primary">{{ ucfirst($section->section_type) }}</span>
                        </div>
                    </div>

                    <div class="info-item mb-3">
                        <strong>Status:</strong>
                        <div class="mt-1">
                            @if($section->is_visible)
                                <span class="badge badge-success">Visible</span>
                            @else
                                <span class="badge badge-secondary">Hidden</span>
                            @endif
                        </div>
                    </div>

                    <div class="info-item mb-3">
                        <strong>Sort Order:</strong>
                        <div class="mt-1 text-muted">
                            {{ $section->sort_order }}
                        </div>
                    </div>

                    <div class="info-item">
                        <strong>Last Updated:</strong>
                        <div class="mt-1 text-muted">
                            {{ $section->updated_at->format('M d, Y \a\t g:i A') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-question-circle mr-2"></i>
                        Section Help
                    </h3>
                </div>
                <div class="card-body">
                    @if($section->section_type === 'hero')
                        <p class="text-muted">
                            The Hero section is the first thing visitors see. Make it compelling with a strong headline and clear call-to-action.
                        </p>
                    @elseif($section->section_type === 'about')
                        <p class="text-muted">
                            Use the About section to tell your story and build trust with potential customers.
                        </p>
                    @elseif($section->section_type === 'services')
                        <p class="text-muted">
                            Showcase your services with clear descriptions and pricing to help customers make decisions.
                        </p>
                    @elseif($section->section_type === 'contact')
                        <p class="text-muted">
                            Make it easy for customers to reach you with multiple contact options and a contact form.
                        </p>
                    @endif

                    <a href="#" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-book mr-1"></i>
                        View Guide
                    </a>
                </div>
            </div>

            @if(config('app.debug'))
            <!-- Debug Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bug mr-2"></i>
                        Debug Information
                    </h3>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <strong>Section ID:</strong> {{ $section->id }}<br>
                        <strong>Form Action:</strong> {{ route('owner.landing-page.sections.update', $section->id) }}<br>
                        <strong>CSRF Token:</strong> <span id="csrf-token-display">{{ csrf_token() }}</span><br>
                        <strong>Current Content:</strong>
                        <pre class="mt-2" style="font-size: 11px; max-height: 150px; overflow-y: auto;">{{ json_encode($section->content_data, JSON_PRETTY_PRINT) }}</pre>
                    </small>
                </div>
            </div>
            @endif
        </div>
    </div>
@stop

@section('css')
<style>
.info-item {
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.form-group label {
    font-weight: 600;
}

.form-text {
    font-size: 0.875rem;
}
</style>
@endsection

@section('js')
<script>
$(document).ready(function() {
    // Auto-save functionality
    let autoSaveTimeout;
    $('#section-form input, #section-form textarea, #section-form select').on('input change', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(function() {
            toastr.info('Changes detected. Remember to save your work.');
        }, 3000);
    });

    // Preview section functionality
    $('#preview-section').on('click', function() {
        // This would open a preview modal or new window
        toastr.info('Preview functionality coming soon!');
    });

    // Test connection functionality
    $('#test-connection').on('click', function() {
        const btn = $(this);
        const originalText = btn.html();

        btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Testing...').prop('disabled', true);

        $.ajax({
            url: '{{ route("owner.landing-page.sections.content", $section->id) }}',
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Test connection successful:', response);
                toastr.success('Connection test successful! Server is responding.');
            },
            error: function(xhr) {
                console.error('Test connection failed:', xhr);
                toastr.error('Connection test failed. Status: ' + xhr.status);
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Form submission with AJAX
    $('#section-form').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();

        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);

        // Get form data
        const formData = new FormData(form[0]);

        // Add method override for PUT request
        formData.append('_method', 'PUT');

        // Debug: Log form data
        console.log('Form action:', form.attr('action'));
        console.log('Form data:', Object.fromEntries(formData));
        console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));

        // First attempt with AJAX
        $.ajax({
            url: form.attr('action'),
            method: 'POST', // Use POST with _method override
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            timeout: 30000, // 30 second timeout
            success: function(response) {
                console.log('Success response:', response);
                toastr.success(response.message || 'Section updated successfully!');

                // Update section info if needed
                if (response.section) {
                    // Update last updated time
                    const lastUpdated = new Date(response.section.updated_at);
                    $('.info-item:last .text-muted').text(lastUpdated.toLocaleDateString() + ' at ' + lastUpdated.toLocaleTimeString());
                }

                // Update visibility badge if changed
                const isVisibleCheckbox = $('#is_visible').is(':checked');
                const statusBadge = $('.info-item:nth-child(2) .badge');
                if (isVisibleCheckbox) {
                    statusBadge.removeClass('badge-secondary').addClass('badge-success').text('Visible');
                } else {
                    statusBadge.removeClass('badge-success').addClass('badge-secondary').text('Hidden');
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error('AJAX Error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    textStatus: textStatus,
                    errorThrown: errorThrown,
                    responseText: xhr.responseText,
                    response: xhr.responseJSON
                });

                // Handle different error scenarios
                if (textStatus === 'timeout') {
                    toastr.error('Request timed out. Please try again.');
                } else if (xhr.status === 0) {
                    toastr.error('Network error. Please check your connection and try again.');
                } else if (xhr.status === 419) {
                    toastr.error('Session expired. Please refresh the page and try again.');
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else if (xhr.status === 422) {
                    const errors = xhr.responseJSON?.errors;
                    const debugInfo = xhr.responseJSON?.debug_info;

                    if (errors) {
                        let errorMessage = 'Please fix the following errors:\n';
                        Object.keys(errors).forEach(function(key) {
                            errorMessage += '- ' + errors[key][0] + '\n';
                        });
                        toastr.error(errorMessage);

                        // Log debug info if available
                        if (debugInfo) {
                            console.log('Validation debug info:', debugInfo);
                        }
                    } else {
                        toastr.error('Validation error occurred. Please check your input and try again.');
                    }
                } else if (xhr.status === 404) {
                    toastr.error('Section not found. Please refresh the page.');
                } else if (xhr.status === 500) {
                    toastr.error('Server error occurred. Please try again.');

                    // Fallback: Try traditional form submission
                    console.log('Attempting fallback form submission...');
                    setTimeout(() => {
                        if (confirm('AJAX failed. Would you like to try a traditional form submission? This will reload the page.')) {
                            // Create a temporary form for traditional submission
                            const tempForm = $('<form>', {
                                'method': 'POST',
                                'action': form.attr('action')
                            });

                            // Add CSRF token
                            tempForm.append($('<input>', {
                                'type': 'hidden',
                                'name': '_token',
                                'value': $('meta[name="csrf-token"]').attr('content')
                            }));

                            // Add method override
                            tempForm.append($('<input>', {
                                'type': 'hidden',
                                'name': '_method',
                                'value': 'PUT'
                            }));

                            // Copy form data
                            form.find('input, textarea, select').each(function() {
                                const input = $(this);
                                if (input.attr('type') === 'checkbox' || input.attr('type') === 'radio') {
                                    if (input.is(':checked')) {
                                        tempForm.append($('<input>', {
                                            'type': 'hidden',
                                            'name': input.attr('name'),
                                            'value': input.val()
                                        }));
                                    }
                                } else if (input.attr('name') && input.attr('name') !== '_token' && input.attr('name') !== '_method') {
                                    tempForm.append($('<input>', {
                                        'type': 'hidden',
                                        'name': input.attr('name'),
                                        'value': input.val()
                                    }));
                                }
                            });

                            // Append to body and submit
                            $('body').append(tempForm);
                            tempForm.submit();
                        }
                    }, 1000);
                } else {
                    toastr.error('An error occurred while saving the section. Status: ' + xhr.status);
                }
            },
            complete: function() {
                // Restore button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
@endsection
