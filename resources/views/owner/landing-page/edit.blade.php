@extends('owner.layouts.app')

@section('title', 'Edit Landing Page')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Edit Landing Page</h1>
            <p class="text-muted mb-0">Customize your business landing page content and design.</p>
        </div>
        <div>
            <a href="{{ route('owner.landing-page.preview') }}" target="_blank" class="btn btn-info me-2">
                <i class="fas fa-eye mr-1"></i>
                Preview
            </a>
            <a href="{{ route('owner.landing-page.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Overview
            </a>
        </div>
    </div>
@stop

@section('content')
    <form action="{{ route('owner.landing-page.update') }}" method="POST">
        @csrf
        @method('PUT')

        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <!-- Basic Settings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cog mr-2"></i>
                            Basic Settings
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="page_title">Page Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('page_title') is-invalid @enderror"
                                   id="page_title" name="page_title" value="{{ old('page_title', $landingPage->page_title) }}" required>
                            @error('page_title')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="page_description">Page Description</label>
                            <textarea class="form-control @error('page_description') is-invalid @enderror"
                                      id="page_description" name="page_description" rows="3">{{ old('page_description', $landingPage->page_description) }}</textarea>
                            <small class="form-text text-muted">
                                This will appear as the subtitle on your landing page.
                            </small>
                            @error('page_description')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="theme">Theme <span class="text-danger">*</span></label>
                            <select class="form-control @error('theme') is-invalid @enderror"
                                    id="theme" name="theme" required>
                                @foreach($themes as $themeKey => $themeData)
                                    <option value="{{ $themeKey }}" {{ old('theme', $landingPage->theme) == $themeKey ? 'selected' : '' }}>
                                        {{ $themeData['name'] }} - {{ $themeData['description'] }}
                                    </option>
                                @endforeach
                            </select>
                            @error('theme')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Booking Settings -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar-check mr-2"></i>
                            Booking Settings
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="booking_enabled"
                                       name="booking_enabled" value="1" {{ old('booking_enabled', $landingPage->booking_enabled) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="booking_enabled">
                                    Enable Online Booking
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                Allow customers to book appointments directly from your landing page.
                            </small>
                        </div>

                        <div id="booking-options" style="{{ old('booking_enabled', $landingPage->booking_enabled) ? '' : 'display: none;' }}">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="booking_button_text">Booking Button Text</label>
                                        <input type="text" class="form-control @error('booking_button_text') is-invalid @enderror"
                                               id="booking_button_text" name="booking_button_text"
                                               value="{{ old('booking_button_text', $landingPage->booking_button_text ?? 'Book Now') }}">
                                        @error('booking_button_text')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="booking_button_color">Button Color</label>
                                        <input type="color" class="form-control @error('booking_button_color') is-invalid @enderror"
                                               id="booking_button_color" name="booking_button_color"
                                               value="{{ old('booking_button_color', $landingPage->booking_button_color ?? '#007bff') }}">
                                        @error('booking_button_color')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Page Sections -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-puzzle-piece mr-2"></i>
                            Page Sections
                        </h3>
                    </div>
                    <div class="card-body">
                        @if($sections->isNotEmpty())
                            <div class="sections-list">
                                @foreach($sections as $section)
                                    <div class="section-item border rounded p-3 mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">{{ $section->section_name }}</h6>
                                                <small class="text-muted">{{ ucfirst($section->section_type) }} Section</small>
                                            </div>
                                            <div>
                                                @if($section->is_visible)
                                                    <span class="badge badge-success me-2">Visible</span>
                                                @else
                                                    <span class="badge badge-secondary me-2">Hidden</span>
                                                @endif
                                                <a href="{{ route('owner.landing-page.sections.edit', $section->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-puzzle-piece fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No sections configured</h5>
                                <p class="text-muted">Default sections will be created automatically.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Current Status -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle mr-2"></i>
                            Current Status
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="status-item mb-3">
                            <strong>Status:</strong>
                            @if($landingPage->is_published)
                                <span class="badge badge-success">Published</span>
                            @else
                                <span class="badge badge-warning">Draft</span>
                            @endif
                        </div>

                        <div class="status-item mb-3">
                            <strong>URL:</strong>
                            <div class="mt-1">
                                <a href="{{ $landingPage->full_url }}" target="_blank" class="text-decoration-none">
                                    {{ $landingPage->full_url }}
                                </a>
                            </div>
                        </div>

                        <div class="status-item mb-3">
                            <strong>Last Updated:</strong>
                            <div class="mt-1 text-muted">
                                {{ $landingPage->updated_at->format('M d, Y \a\t g:i A') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools mr-2"></i>
                            Actions
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Save Changes
                            </button>

                            <a href="{{ route('owner.landing-page.preview') }}" target="_blank" class="btn btn-info">
                                <i class="fas fa-eye mr-2"></i>
                                Preview Page
                            </a>

                            @if($landingPage->is_published)
                                <a href="{{ $landingPage->full_url }}" target="_blank" class="btn btn-success">
                                    <i class="fas fa-external-link-alt mr-2"></i>
                                    View Live Page
                                </a>
                            @else
                                <form action="{{ route('owner.landing-page.publish') }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-success w-100"
                                            onclick="return confirm('Are you sure you want to publish this landing page?')">
                                        <i class="fas fa-rocket mr-2"></i>
                                        Publish Page
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Help -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-question-circle mr-2"></i>
                            Need Help?
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            Learn how to customize your landing page and attract more customers.
                        </p>
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-book mr-1"></i>
                            View Guide
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
@stop

@section('css')
<style>
.section-item {
    transition: all 0.3s ease;
}

.section-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.status-item {
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}
</style>
@endsection

@section('js')
<script>
$(document).ready(function() {
    // Toggle booking options
    $('#booking_enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('#booking-options').slideDown();
        } else {
            $('#booking-options').slideUp();
        }
    });

    // Auto-save functionality (optional)
    let autoSaveTimeout;
    $('input, textarea, select').on('input change', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(function() {
            // Show auto-save indicator
            toastr.info('Changes detected. Remember to save your work.');
        }, 3000);
    });
});
</script>
@endsection
