@extends('owner.layouts.app')

@section('title', 'Landing Page Management')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Landing Page Management</h1>
            <p class="text-muted mb-0">Manage your business landing page and track its performance.</p>
        </div>
        <div>
            @if($landingPage->is_published)
                <a href="{{ $landingPage->full_url }}" target="_blank" class="btn btn-success me-2">
                    <i class="fas fa-external-link-alt mr-1"></i>
                    View Live Page
                </a>
                <form action="{{ route('owner.landing-page.unpublish') }}" method="POST" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-warning me-2">
                        <i class="fas fa-eye-slash mr-1"></i>
                        Unpublish
                    </button>
                </form>
            @else
                <form action="{{ route('owner.landing-page.publish') }}" method="POST" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-success me-2">
                        <i class="fas fa-rocket mr-1"></i>
                        Publish Page
                    </button>
                </form>
            @endif
            <a href="{{ route('owner.landing-page.edit') }}" class="btn btn-primary">
                <i class="fas fa-edit mr-1"></i>
                Edit Page
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <!-- Landing Page Status -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-globe mr-2"></i>
                        Landing Page Overview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Page Title:</strong></td>
                                    <td>{{ $landingPage->page_title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>URL:</strong></td>
                                    <td>
                                        <a href="{{ $landingPage->full_url }}" target="_blank" class="text-decoration-none">
                                            {{ $landingPage->full_url }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Theme:</strong></td>
                                    <td>{{ ucfirst($landingPage->theme) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($landingPage->is_published)
                                            <span class="badge badge-success">Published</span>
                                        @else
                                            <span class="badge badge-warning">Draft</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Booking Enabled:</strong></td>
                                    <td>
                                        @if($landingPage->booking_enabled)
                                            <span class="badge badge-success">Yes</span>
                                        @else
                                            <span class="badge badge-secondary">No</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td>{{ $landingPage->updated_at->format('M d, Y \a\t g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="landing-page-preview">
                                <h6>Page Preview</h6>
                                <div class="preview-frame">
                                    <div class="browser-mockup">
                                        <div class="browser-header">
                                            <div class="browser-buttons">
                                                <span class="browser-button red"></span>
                                                <span class="browser-button yellow"></span>
                                                <span class="browser-button green"></span>
                                            </div>
                                            <div class="browser-url">{{ $landingPage->full_url }}</div>
                                        </div>
                                        <div class="browser-content">
                                            <iframe src="{{ route('owner.landing-page.preview') }}" 
                                                    width="100%" height="200" frameborder="0">
                                            </iframe>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line mr-2"></i>
                        Performance
                    </h3>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Page Views</span>
                            <span class="badge badge-primary">{{ number_format($analytics['views']) }}</span>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Unique Visitors</span>
                            <span class="badge badge-info">{{ number_format($analytics['unique_visitors']) }}</span>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Bounce Rate</span>
                            <span class="badge badge-warning">{{ $analytics['bounce_rate'] }}%</span>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Conversion Rate</span>
                            <span class="badge badge-success">{{ $analytics['conversion_rate'] }}%</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Bookings from Landing</span>
                            <span class="badge badge-primary">{{ number_format($analytics['bookings_from_landing']) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Page Sections -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-puzzle-piece mr-2"></i>
                        Page Sections
                    </h3>
                </div>
                <div class="card-body">
                    @if($landingPage->sections->isNotEmpty())
                        <div class="sections-list">
                            @foreach($landingPage->sections->sortBy('sort_order') as $section)
                                <div class="section-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded">
                                    <div class="section-info">
                                        <h6 class="mb-1">{{ $section->section_name }}</h6>
                                        <small class="text-muted">{{ ucfirst($section->section_type) }} Section</small>
                                    </div>
                                    <div class="section-actions">
                                        @if($section->is_visible)
                                            <span class="badge badge-success me-2">Visible</span>
                                        @else
                                            <span class="badge badge-secondary me-2">Hidden</span>
                                        @endif
                                        <span class="badge badge-light">Order: {{ $section->sort_order }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-puzzle-piece fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No sections configured</h5>
                            <p class="text-muted">Add sections to customize your landing page.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('owner.landing-page.edit') }}" class="btn btn-primary">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Content
                        </a>
                        <a href="{{ route('owner.landing-page.preview') }}" target="_blank" class="btn btn-info">
                            <i class="fas fa-eye mr-2"></i>
                            Preview Changes
                        </a>
                        <button class="btn btn-secondary" onclick="copyToClipboard('{{ $landingPage->full_url }}')">
                            <i class="fas fa-copy mr-2"></i>
                            Copy URL
                        </button>
                        @if($landingPage->is_published)
                            <a href="{{ $landingPage->full_url }}/sitemap.xml" target="_blank" class="btn btn-outline-secondary">
                                <i class="fas fa-sitemap mr-2"></i>
                                View Sitemap
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
.browser-mockup {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.browser-header {
    background: #f5f5f5;
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
}

.browser-buttons {
    display: flex;
    gap: 4px;
    margin-right: 12px;
}

.browser-button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.browser-button.red { background: #ff5f56; }
.browser-button.yellow { background: #ffbd2e; }
.browser-button.green { background: #27ca3f; }

.browser-url {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: #666;
    flex: 1;
}

.browser-content {
    height: 200px;
    overflow: hidden;
}

.section-item {
    transition: all 0.3s ease;
}

.section-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.stat-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}
</style>
@endsection

@section('js')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        toastr.success('URL copied to clipboard!');
    }, function(err) {
        toastr.error('Failed to copy URL');
    });
}
</script>
@endsection
