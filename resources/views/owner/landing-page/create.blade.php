@extends('owner.layouts.app')

@section('title', 'Create Landing Page')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Create Landing Page</h1>
            <p class="text-muted mb-0">Set up your business landing page to attract more customers online.</p>
        </div>
        <div>
            <a href="{{ route('owner.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-rocket mr-2"></i>
                        Landing Page Setup
                    </h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('owner.landing-page.store') }}" method="POST">
                        @csrf

                        <!-- Basic Information -->
                        <div class="form-group">
                            <label for="page_title">Page Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('page_title') is-invalid @enderror"
                                   id="page_title" name="page_title" value="{{ old('page_title', $business->name) }}" required>
                            <small class="form-text text-muted">
                                This will be displayed in the browser tab and search results.
                            </small>
                            @error('page_title')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="page_description">Page Description</label>
                            <textarea class="form-control @error('page_description') is-invalid @enderror"
                                      id="page_description" name="page_description" rows="3">{{ old('page_description', $business->description) }}</textarea>
                            <small class="form-text text-muted">
                                A brief description of your business that will appear in search results.
                            </small>
                            @error('page_description')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- URL Configuration -->
                        <div class="form-group">
                            <label for="domain_type">Domain Type <span class="text-danger">*</span></label>
                            <select class="form-control @error('domain_type') is-invalid @enderror" id="domain_type" name="domain_type" required>
                                <option value="subdirectory" {{ old('domain_type', 'subdirectory') == 'subdirectory' ? 'selected' : '' }}>
                                    Subdirectory (bookkei.com/your-business)
                                </option>
                                <option value="subdomain" {{ old('domain_type') == 'subdomain' ? 'selected' : '' }}>
                                    Subdomain (your-business.bookkei.com)
                                </option>
                                <option value="custom" {{ old('domain_type') == 'custom' ? 'selected' : '' }}>
                                    Custom Domain (your-domain.com)
                                </option>
                            </select>
                            <small class="form-text text-muted">
                                Choose how your landing page URL will be structured.
                            </small>
                            @error('domain_type')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="custom_slug">Custom URL <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" id="url-prefix">bookkei.com/</span>
                                </div>
                                <input type="text" class="form-control @error('custom_slug') is-invalid @enderror"
                                       id="custom_slug" name="custom_slug" value="{{ old('custom_slug', $suggestedSlug) }}" required>
                            </div>
                            <small class="form-text text-muted">
                                Choose a unique URL for your landing page. Only letters, numbers, and hyphens allowed.
                            </small>
                            @error('custom_slug')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group" id="custom_domain_group" style="display: none;">
                            <label for="custom_domain">Custom Domain</label>
                            <input type="text" class="form-control @error('custom_domain') is-invalid @enderror"
                                   id="custom_domain" name="custom_domain" value="{{ old('custom_domain') }}" placeholder="www.yourdomain.com">
                            <small class="form-text text-muted">
                                Enter your custom domain name (requires DNS configuration).
                            </small>
                            @error('custom_domain')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Theme Selection -->
                        <div class="form-group">
                            <label for="theme">Theme <span class="text-danger">*</span></label>
                            <select class="form-control @error('theme') is-invalid @enderror" id="theme" name="theme" required>
                                @foreach($themes as $themeKey => $themeData)
                                    <option value="{{ $themeKey }}" {{ old('theme', 'default') == $themeKey ? 'selected' : '' }}>
                                        {{ $themeData['name'] }} - {{ $themeData['description'] }}
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">
                                Choose a design theme for your landing page. You can change this later.
                            </small>
                            @error('theme')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Booking Integration -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="booking_enabled" name="booking_enabled" value="1" {{ old('booking_enabled', true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="booking_enabled">Enable Online Booking</label>
                            </div>
                            <small class="form-text text-muted">
                                Allow customers to book appointments directly from your landing page.
                            </small>
                        </div>

                        <div class="form-group" id="booking_button_config" style="{{ old('booking_enabled', true) ? '' : 'display: none;' }}">
                            <label for="booking_button_text">Booking Button Text</label>
                            <input type="text" class="form-control @error('booking_button_text') is-invalid @enderror"
                                   id="booking_button_text" name="booking_button_text" value="{{ old('booking_button_text', 'Book Now') }}">
                            @error('booking_button_text')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- SEO Settings -->
                        <div class="form-group">
                            <label for="meta_title">SEO Title</label>
                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                                   id="meta_title" name="meta_title" value="{{ old('meta_title') }}" maxlength="60">
                            <small class="form-text text-muted">
                                <span id="meta-title-count">0</span>/60 characters. Leave empty to use page title.
                            </small>
                            @error('meta_title')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="meta_description">SEO Description</label>
                            <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                      id="meta_description" name="meta_description" rows="3" maxlength="160">{{ old('meta_description') }}</textarea>
                            <small class="form-text text-muted">
                                <span id="meta-description-count">0</span>/160 characters. Leave empty to use page description.
                            </small>
                            @error('meta_description')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="meta_keywords">Keywords</label>
                            <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror"
                                   id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords') }}">
                            <small class="form-text text-muted">
                                Separate keywords with commas (e.g., salon, haircut, beauty, spa).
                            </small>
                            @error('meta_keywords')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-rocket mr-1"></i>
                                Create Landing Page
                            </button>
                            <a href="{{ route('owner.dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-times mr-1"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // Auto-generate slug from page title
    $('#page_title').on('input', function() {
        const title = $(this).val();
        if (!$('#custom_slug').val() || $('#custom_slug').data('auto-generated')) {
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-|-$/g, '');

            $('#custom_slug').val(slug).data('auto-generated', true);
        }

        if (!$('#meta_title').val()) {
            $('#meta_title').val(title);
            updateCharCount('#meta_title', '#meta-title-count');
        }
    });

    // Manual slug editing
    $('#custom_slug').on('input', function() {
        $(this).data('auto-generated', false);

        // Clean slug
        const cleanSlug = $(this).val().toLowerCase()
            .replace(/[^a-z0-9-]/g, '')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');

        if ($(this).val() !== cleanSlug) {
            $(this).val(cleanSlug);
        }
    });

    // Auto-generate meta description from page description
    $('#page_description').on('input', function() {
        if (!$('#meta_description').val()) {
            $('#meta_description').val($(this).val());
            updateCharCount('#meta_description', '#meta-description-count');
        }
    });

    // Character counters
    function updateCharCount(inputSelector, counterSelector) {
        const length = $(inputSelector).val().length;
        $(counterSelector).text(length);

        const maxLength = $(inputSelector).attr('maxlength');
        if (maxLength && length > maxLength * 0.9) {
            $(counterSelector).addClass('text-warning');
        } else {
            $(counterSelector).removeClass('text-warning');
        }
    }

    $('#meta_title, #meta_description').on('input', function() {
        const counterId = $(this).attr('id') === 'meta_title' ? '#meta-title-count' : '#meta-description-count';
        updateCharCount('#' + $(this).attr('id'), counterId);
    });

    // Domain type configuration
    $('#domain_type').on('change', function() {
        const domainType = $(this).val();
        const urlPrefix = $('#url-prefix');
        const customDomainGroup = $('#custom_domain_group');

        switch(domainType) {
            case 'subdirectory':
                urlPrefix.text('bookkei.com/');
                customDomainGroup.hide();
                break;
            case 'subdomain':
                urlPrefix.text('.bookkei.com');
                customDomainGroup.hide();
                break;
            case 'custom':
                urlPrefix.text('');
                customDomainGroup.show();
                break;
        }
    });

    // Booking configuration toggle
    $('#booking_enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('#booking_button_config').slideDown();
        } else {
            $('#booking_button_config').slideUp();
        }
    });

    // Initialize character counts
    updateCharCount('#meta_title', '#meta-title-count');
    updateCharCount('#meta_description', '#meta-description-count');
});
</script>
@stop
