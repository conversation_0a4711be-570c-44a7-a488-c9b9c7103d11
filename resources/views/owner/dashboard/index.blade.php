@extends('owner.layouts.app')

@section('title', 'Owner Dashboard')

@section('content_header')
    <h1>{{ $business->name }} Dashboard</h1>
    <p class="text-muted">Welcome back! Here's what's happening with your business today.</p>
@stop

@section('content')
    {{-- Quick Stats Row --}}
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $metrics['today_bookings'] }}</h3>
                    <p>Today's Bookings</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <a href="{{ route('owner.bookings.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>${{ number_format($metrics['today_revenue'], 2) }}</h3>
                    <p>Today's Revenue</p>
                </div>
                <div class="icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <a href="{{ route('owner.revenue.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $metrics['pending_bookings'] }}</h3>
                    <p>Pending Bookings</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
                <a href="{{ route('owner.bookings.index') }}?status=pending" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $metrics['upcoming_bookings'] }}</h3>
                    <p>Upcoming Today</p>
                </div>
                <div class="icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <a href="{{ route('owner.schedule.today') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    {{-- Quick Actions Row --}}
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-bolt text-warning mr-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{{ route('owner.bookings.create') }}" class="btn btn-success btn-block">
                                <i class="fas fa-plus-circle mr-2"></i>
                                New Booking
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{{ route('owner.schedule.today') }}" class="btn btn-info btn-block">
                                <i class="fas fa-calendar-day mr-2"></i>
                                Today's Schedule
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{{ route('owner.customers.index') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-users mr-2"></i>
                                Manage Customers
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{{ route('owner.services.index') }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-concierge-bell mr-2"></i>
                                Manage Services
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Charts and Data Row --}}
    <div class="row">
        {{-- Revenue Chart --}}
        <div class="col-lg-8">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-chart-line text-success mr-2"></i>
                        Revenue Trend (Last 7 Days)
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        {{-- Booking Status Distribution --}}
        <div class="col-lg-4">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-chart-pie text-info mr-2"></i>
                        Booking Status
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="chart-container">
                        <canvas id="bookingStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Recent Activity Row --}}
    <div class="row">
        {{-- Recent Bookings --}}
        <div class="col-lg-6">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-history text-primary mr-2"></i>
                        Recent Bookings
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Service</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($metrics['recent_bookings'] as $booking)
                                <tr>
                                    <td>{{ $booking['customer_name'] }}</td>
                                    <td>{{ $booking['service'] }}</td>
                                    <td>{{ $booking['time'] }}</td>
                                    <td>
                                        @if($booking['status'] === 'completed')
                                            <span class="badge badge-success">Completed</span>
                                        @elseif($booking['status'] === 'in_progress')
                                            <span class="badge badge-warning">In Progress</span>
                                        @elseif($booking['status'] === 'upcoming')
                                            <span class="badge badge-info">Upcoming</span>
                                        @else
                                            <span class="badge badge-secondary">{{ ucfirst($booking['status']) }}</span>
                                        @endif
                                    </td>
                                    <td>${{ number_format($booking['amount'], 2) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="{{ route('owner.bookings.index') }}" class="btn btn-sm btn-outline-primary">
                            View All Bookings
                        </a>
                    </div>
                </div>
            </div>
        </div>

        {{-- Upcoming Appointments --}}
        <div class="col-lg-6">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-clock text-warning mr-2"></i>
                        Upcoming Appointments
                    </h5>
                </div>
                <div class="widget-body">
                    @foreach($metrics['upcoming_appointments'] as $appointment)
                    <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ $appointment['customer'] }}</h6>
                            <p class="mb-1 text-muted">{{ $appointment['service'] }} • {{ $appointment['duration'] }}</p>
                            <small class="text-muted">{{ $appointment['phone'] }}</small>
                        </div>
                        <div class="text-right">
                            <h6 class="mb-0 text-primary">{{ $appointment['time'] }}</h6>
                            <div class="btn-group btn-group-sm mt-1">
                                <button class="btn btn-outline-success btn-sm" data-toggle="tooltip" title="Call Customer">
                                    <i class="fas fa-phone"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" data-toggle="tooltip" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    @endforeach

                    <div class="text-center">
                        <a href="{{ route('owner.schedule.today') }}" class="btn btn-sm btn-outline-warning">
                            View Full Schedule
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Business Insights Row --}}
    <div class="row">
        <div class="col-lg-12">
            <div class="card dashboard-widget">
                <div class="widget-header">
                    <h5 class="widget-title">
                        <i class="fas fa-lightbulb text-success mr-2"></i>
                        Business Insights
                    </h5>
                </div>
                <div class="widget-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-users"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Customers</span>
                                    <span class="info-box-number">{{ $metrics['total_customers'] }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-dollar-sign"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Monthly Revenue</span>
                                    <span class="info-box-number">${{ number_format($metrics['monthly_revenue'], 0) }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-calendar-check"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Monthly Bookings</span>
                                    <span class="info-box-number">{{ $metrics['monthly_bookings'] }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary">
                                    <i class="fas fa-percentage"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Conversion Rate</span>
                                    <span class="info-box-number">{{ $metrics['conversion_rate'] }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="callout callout-info">
                                <h5>Most Popular Service</h5>
                                <p>{{ $metrics['popular_service'] }} is your top-performing service this month.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="callout callout-warning">
                                <h5>Peak Hours</h5>
                                <p>Your business is busiest during {{ $metrics['peak_hours'] }}.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="callout callout-success">
                                <h5>Pro Tip</h5>
                                <p>Consider offering promotions during slower periods to increase bookings.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
    <script>
        $(document).ready(function() {
            // Initialize Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            const revenueData = @json($metrics['revenue_chart_data']);

            new Chart(revenueCtx, {
                type: 'line',
                data: revenueData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value;
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 4,
                            hoverRadius: 6
                        },
                        line: {
                            tension: 0.3
                        }
                    }
                }
            });

            // Initialize Booking Status Chart
            const statusCtx = document.getElementById('bookingStatusChart').getContext('2d');
            const statusData = @json($metrics['booking_status_data']);

            new Chart(statusCtx, {
                type: 'doughnut',
                data: statusData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });

        // Function to refresh dashboard stats
        function refreshDashboardStats() {
            $.get('{{ route("owner.dashboard.quick-stats") }}')
                .done(function(data) {
                    // Update quick stats boxes
                    $('.small-box.bg-success .inner h3').text(data.today_bookings);
                    $('.small-box.bg-info .inner h3').text('$' + parseFloat(data.today_revenue).toFixed(2));
                    $('.small-box.bg-warning .inner h3').text(data.pending_bookings);
                    $('.small-box.bg-danger .inner h3').text(data.upcoming_bookings);
                })
                .fail(function() {
                    console.log('Failed to refresh dashboard stats');
                });
        }
    </script>
@stop
