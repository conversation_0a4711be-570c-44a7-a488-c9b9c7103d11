@extends('owner.layouts.app')

@section('title', 'Resource Types')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Resource Types</h1>
            <p class="text-muted">Manage resource type categories</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.resources.index') }}" class="btn btn-info">
                    <i class="fas fa-tools mr-2"></i>
                    View Resources
                </a>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createTypeModal">
                    <i class="fas fa-plus mr-2"></i>
                    Add Resource Type
                </button>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Quick Stats --}}
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_types'] }}</h3>
                    <p>Total Types</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tags"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['used_types'] }}</h3>
                    <p>Used Types</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['total_resources'] }}</h3>
                    <p>Total Resources</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tools"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $stats['active_resources'] }}</h3>
                    <p>Active Resources</p>
                </div>
                <div class="icon">
                    <i class="fas fa-play-circle"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Resource Types Grid --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-tags mr-2"></i>
                Resource Types
            </h3>
            <div class="card-tools">
                <div class="btn-group mr-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportResourceTypes()">
                        <i class="fas fa-download mr-1"></i>
                        Export
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="showAnalytics()">
                        <i class="fas fa-chart-bar mr-1"></i>
                        Analytics
                    </button>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-warning dropdown-toggle" data-toggle="dropdown">
                            <i class="fas fa-tasks mr-1"></i>
                            Bulk Actions
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="#" onclick="toggleBulkMode()">
                                <i class="fas fa-check-square mr-2"></i>
                                Select Multiple
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-danger" href="#" onclick="bulkDelete()" id="bulk-delete-btn" style="display: none;">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Selected
                            </a>
                        </div>
                    </div>
                </div>
                <form method="GET" class="form-inline">
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control" name="search"
                               value="{{ request('search') }}" placeholder="Search types...">
                        <div class="input-group-append">
                            <button type="submit" class="btn btn-default">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="card-body">
            @if($resourceTypes->count() > 0)
                <div class="row">
                    @foreach($resourceTypes as $type)
                        <div class="col-md-4 col-sm-6 mb-4">
                            <div class="card h-100 resource-type-card" data-type-id="{{ $type->id }}">
                                <div class="card-body text-center">
                                    <div class="bulk-checkbox" style="display: none;">
                                        <div class="custom-control custom-checkbox position-absolute" style="top: 10px; left: 10px;">
                                            <input type="checkbox" class="custom-control-input resource-type-checkbox" id="checkbox-{{ $type->id }}" value="{{ $type->id }}">
                                            <label class="custom-control-label" for="checkbox-{{ $type->id }}"></label>
                                        </div>
                                    </div>
                                    <div class="resource-type-icon mb-3"
                                         style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: {{ $type->color }}; margin: 0 auto;">
                                        <i class="{{ $type->icon }} fa-2x text-white"></i>
                                    </div>
                                    <h5 class="card-title">{{ $type->name }}</h5>
                                    @if($type->description)
                                        <p class="card-text text-muted">{{ Str::limit($type->description, 100) }}</p>
                                    @endif
                                    <div class="mt-3">
                                        <span class="badge badge-info">{{ $type->resources_count }} resource{{ $type->resources_count != 1 ? 's' : '' }}</span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group btn-group-sm w-100" role="group">
                                        <button type="button" class="btn btn-warning" onclick="editResourceType({{ $type->id }}, '{{ addslashes($type->name) }}', '{{ addslashes($type->description) }}', '{{ $type->icon }}', '{{ $type->color }}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="deleteResourceType({{ $type->id }}, '{{ addslashes($type->name) }}', {{ $type->resources_count }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                {{-- Pagination --}}
                @if($resourceTypes->hasPages())
                    <div class="d-flex justify-content-center">
                        {{ $resourceTypes->appends(request()->query())->links() }}
                    </div>
                @endif
            @else
                {{-- Empty State --}}
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-tags fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">No Resource Types Found</h4>
                    @if(request('search'))
                        <p class="text-muted">No resource types match your search.</p>
                        <a href="{{ route('owner.resource-types.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times mr-1"></i>
                            Clear Search
                        </a>
                    @else
                        <p class="text-muted">Start by creating your first resource type.</p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createTypeModal">
                            <i class="fas fa-plus mr-1"></i>
                            Create First Resource Type
                        </button>
                    @endif
                </div>
            @endif
        </div>
    </div>

    {{-- Create Resource Type Modal --}}
    <div class="modal fade" id="createTypeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Resource Type</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="createTypeForm">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="create_name">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="create_name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="create_description">Description</label>
                            <textarea class="form-control" id="create_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="create_icon">Icon</label>
                                    <div class="input-group">
                                        <select class="form-control" id="create_icon" name="icon">
                                            <option value="fas fa-cube">Default</option>
                                            <option value="fas fa-chair">Chair</option>
                                            <option value="fas fa-couch">Couch</option>
                                            <option value="fas fa-bed">Bed</option>
                                            <option value="fas fa-tools">Tools</option>
                                            <option value="fas fa-wrench">Wrench</option>
                                            <option value="fas fa-hammer">Hammer</option>
                                            <option value="fas fa-door-open">Room</option>
                                            <option value="fas fa-car">Vehicle</option>
                                            <option value="fas fa-laptop">Computer</option>
                                            <option value="fas fa-user-md">Medical Staff</option>
                                            <option value="fas fa-stethoscope">Medical Equipment</option>
                                            <option value="fas fa-basketball-ball">Sports Court</option>
                                            <option value="fas fa-desktop">Desktop Computer</option>
                                            <option value="fas fa-table">Table</option>
                                        </select>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i id="create_icon_preview" class="fas fa-cube"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="create_color">Color</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control" id="create_color" name="color" value="#007bff">
                                        <div class="input-group-append">
                                            <div class="input-group-text">
                                                <div class="color-palette">
                                                    <span class="color-option" data-color="#007bff" style="background-color: #007bff;" title="Blue"></span>
                                                    <span class="color-option" data-color="#28a745" style="background-color: #28a745;" title="Green"></span>
                                                    <span class="color-option" data-color="#dc3545" style="background-color: #dc3545;" title="Red"></span>
                                                    <span class="color-option" data-color="#ffc107" style="background-color: #ffc107;" title="Yellow"></span>
                                                    <span class="color-option" data-color="#17a2b8" style="background-color: #17a2b8;" title="Cyan"></span>
                                                    <span class="color-option" data-color="#6f42c1" style="background-color: #6f42c1;" title="Purple"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Type</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Edit Resource Type Modal --}}
    <div class="modal fade" id="editTypeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Resource Type</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="editTypeForm">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="edit_type_id" name="type_id">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="edit_name">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="edit_description">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_icon">Icon</label>
                                    <div class="input-group">
                                        <select class="form-control" id="edit_icon" name="icon">
                                            <option value="fas fa-cube">Default</option>
                                            <option value="fas fa-chair">Chair</option>
                                            <option value="fas fa-couch">Couch</option>
                                            <option value="fas fa-bed">Bed</option>
                                            <option value="fas fa-tools">Tools</option>
                                            <option value="fas fa-wrench">Wrench</option>
                                            <option value="fas fa-hammer">Hammer</option>
                                            <option value="fas fa-door-open">Room</option>
                                            <option value="fas fa-car">Vehicle</option>
                                            <option value="fas fa-laptop">Computer</option>
                                            <option value="fas fa-user-md">Medical Staff</option>
                                            <option value="fas fa-stethoscope">Medical Equipment</option>
                                            <option value="fas fa-basketball-ball">Sports Court</option>
                                            <option value="fas fa-desktop">Desktop Computer</option>
                                            <option value="fas fa-table">Table</option>
                                        </select>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i id="edit_icon_preview" class="fas fa-cube"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="edit_color">Color</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control" id="edit_color" name="color">
                                        <div class="input-group-append">
                                            <div class="input-group-text">
                                                <div class="color-palette">
                                                    <span class="color-option" data-color="#007bff" style="background-color: #007bff;" title="Blue"></span>
                                                    <span class="color-option" data-color="#28a745" style="background-color: #28a745;" title="Green"></span>
                                                    <span class="color-option" data-color="#dc3545" style="background-color: #dc3545;" title="Red"></span>
                                                    <span class="color-option" data-color="#ffc107" style="background-color: #ffc107;" title="Yellow"></span>
                                                    <span class="color-option" data-color="#17a2b8" style="background-color: #17a2b8;" title="Cyan"></span>
                                                    <span class="color-option" data-color="#6f42c1" style="background-color: #6f42c1;" title="Purple"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Type</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('adminlte_css')
    <style>
        .color-palette {
            display: flex;
            gap: 3px;
        }

        .color-option {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .color-option:hover {
            border-color: #333;
            transform: scale(1.1);
        }

        .color-option.active {
            border-color: #000;
            box-shadow: 0 0 0 2px rgba(0,0,0,0.2);
        }

        .resource-type-icon {
            transition: all 0.3s ease;
        }

        .card:hover .resource-type-icon {
            transform: scale(1.05);
        }

        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Bulk selection styles */
        .resource-type-card.bulk-mode {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .resource-type-card.bulk-mode:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .resource-type-card.selected {
            border: 2px solid #007bff;
            background-color: rgba(0, 123, 255, 0.05);
        }

        .bulk-checkbox {
            z-index: 10;
        }

        .custom-control-label::before {
            border-radius: 0.25rem;
        }

        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #007bff;
            border-color: #007bff;
        }
    </style>
@stop

@section('adminlte_js')
    <script>
        $(document).ready(function() {
            // Initialize UI components
            initializeIconPreviews();
            initializeColorPalettes();

            // Create resource type form submission
            $('#createTypeForm').on('submit', function(e) {
                e.preventDefault();

                const formData = $(this).serialize();
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();

                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Creating...');

                $.ajax({
                    url: '{{ route("owner.resource-types.store") }}',
                    method: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $('#createTypeModal').modal('hide');
                            showSuccessMessage('Resource type created successfully!');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showErrorMessage(response.message || 'Failed to create resource type');
                        }
                    },
                    error: function(xhr) {
                        const errors = xhr.responseJSON?.errors;
                        if (errors) {
                            let errorMessage = 'Validation errors:\n';
                            Object.keys(errors).forEach(key => {
                                errorMessage += `- ${errors[key][0]}\n`;
                            });
                            showErrorMessage(errorMessage);
                        } else {
                            showErrorMessage('Failed to create resource type. Please try again.');
                        }
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Edit resource type form submission
            $('#editTypeForm').on('submit', function(e) {
                e.preventDefault();

                const typeId = $('#edit_type_id').val();
                const formData = $(this).serialize();
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();

                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Updating...');

                $.ajax({
                    url: `/owner/resource-types/${typeId}`,
                    method: 'PUT',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $('#editTypeModal').modal('hide');
                            location.reload();
                        } else {
                            alert(response.message || 'Failed to update resource type');
                        }
                    },
                    error: function(xhr) {
                        const errors = xhr.responseJSON?.errors;
                        if (errors) {
                            let errorMessage = 'Validation errors:\n';
                            Object.keys(errors).forEach(key => {
                                errorMessage += `- ${errors[key][0]}\n`;
                            });
                            alert(errorMessage);
                        } else {
                            alert('Failed to update resource type. Please try again.');
                        }
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Reset forms when modals are hidden
            $('#createTypeModal').on('hidden.bs.modal', function() {
                $('#createTypeForm')[0].reset();
            });

            $('#editTypeModal').on('hidden.bs.modal', function() {
                $('#editTypeForm')[0].reset();
            });
        });

        // Initialize icon previews
        function initializeIconPreviews() {
            // Create icon preview functionality
            $('#create_icon').on('change', function() {
                const selectedIcon = $(this).val();
                $('#create_icon_preview').attr('class', selectedIcon);
            });

            $('#edit_icon').on('change', function() {
                const selectedIcon = $(this).val();
                $('#edit_icon_preview').attr('class', selectedIcon);
            });
        }

        // Initialize color palettes
        function initializeColorPalettes() {
            $('.color-option').on('click', function() {
                const color = $(this).data('color');
                const targetInput = $(this).closest('.input-group').find('input[type="color"]');
                targetInput.val(color);

                // Update active state
                $(this).siblings().removeClass('active');
                $(this).addClass('active');
            });
        }

        // Show success message
        function showSuccessMessage(message) {
            toastr.success(message, 'Success', {
                timeOut: 3000,
                progressBar: true
            });
        }

        // Show error message
        function showErrorMessage(message) {
            toastr.error(message, 'Error', {
                timeOut: 5000,
                progressBar: true
            });
        }

        // Edit resource type function
        function editResourceType(id, name, description, icon, color) {
            $('#edit_type_id').val(id);
            $('#edit_name').val(name);
            $('#edit_description').val(description);
            $('#edit_icon').val(icon);
            $('#edit_color').val(color);

            // Update icon preview
            $('#edit_icon_preview').attr('class', icon);

            // Update color palette active state
            $('.color-option').removeClass('active');
            $(`.color-option[data-color="${color}"]`).addClass('active');

            $('#editTypeModal').modal('show');
        }

        // Delete resource type function
        function deleteResourceType(id, name, resourceCount) {
            if (resourceCount > 0) {
                showErrorMessage(`Cannot delete "${name}" because it has ${resourceCount} resource(s) associated with it. Please remove or reassign the resources first.`);
                return;
            }

            // Use SweetAlert2 for better confirmation dialog
            Swal.fire({
                title: 'Are you sure?',
                text: `You are about to delete the resource type "${name}". This action cannot be undone.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Deleting...',
                        text: 'Please wait while we delete the resource type.',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.ajax({
                        url: `/owner/resource-types/${id}`,
                        method: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'Deleted!',
                                    text: 'Resource type has been deleted successfully.',
                                    icon: 'success',
                                    timer: 2000,
                                    showConfirmButton: false
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire('Error!', response.message || 'Failed to delete resource type', 'error');
                            }
                        },
                        error: function(xhr) {
                            let message = 'Failed to delete resource type. Please try again.';
                            let debugInfo = '';

                            if (xhr.responseJSON) {
                                message = xhr.responseJSON.message || message;
                                if (xhr.responseJSON.debug_message && window.location.hostname === 'localhost') {
                                    debugInfo = `<br><small class="text-muted">Debug: ${xhr.responseJSON.debug_message}</small>`;
                                }
                            }

                            Swal.fire({
                                title: 'Error!',
                                html: message + debugInfo,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    });
                }
            });
        }

        // Export resource types function
        function exportResourceTypes() {
            // Get current search parameter if any
            const searchParam = new URLSearchParams(window.location.search).get('search') || '';
            const exportUrl = '{{ route("owner.resource-types.export") }}' + (searchParam ? '?search=' + encodeURIComponent(searchParam) : '');

            // Create a temporary link and trigger download
            const link = document.createElement('a');
            link.href = exportUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showSuccessMessage('Resource types export started! Download will begin shortly.');
        }

        // Show analytics function
        function showAnalytics() {
            $.ajax({
                url: '{{ route("owner.resource-types.analytics") }}',
                method: 'GET',
                success: function(response) {
                    let analyticsHtml = '<div class="row">';

                    response.forEach(function(item) {
                        analyticsHtml += `
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <div class="mr-3" style="width: 40px; height: 40px; border-radius: 50%; background-color: ${item.color}; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-cube text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">${item.name}</h6>
                                                <small class="text-muted">${item.resources_count} resources • ${item.total_bookings} bookings</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    analyticsHtml += '</div>';

                    Swal.fire({
                        title: 'Resource Types Analytics',
                        html: analyticsHtml,
                        width: '800px',
                        showCloseButton: true,
                        showConfirmButton: false
                    });
                },
                error: function() {
                    showErrorMessage('Failed to load analytics data.');
                }
            });
        }

        // Bulk operations functionality
        let bulkMode = false;

        function toggleBulkMode() {
            bulkMode = !bulkMode;

            if (bulkMode) {
                $('.bulk-checkbox').show();
                $('#bulk-delete-btn').show();
                $('.resource-type-card').addClass('bulk-mode');
                showSuccessMessage('Bulk selection mode enabled. Click on cards to select them.');
            } else {
                $('.bulk-checkbox').hide();
                $('#bulk-delete-btn').hide();
                $('.resource-type-card').removeClass('bulk-mode selected');
                $('.resource-type-checkbox').prop('checked', false);
                showSuccessMessage('Bulk selection mode disabled.');
            }
        }

        // Handle card selection in bulk mode
        $(document).on('click', '.resource-type-card', function(e) {
            if (!bulkMode) return;

            // Don't trigger if clicking on buttons
            if ($(e.target).closest('.card-footer').length > 0) return;

            const checkbox = $(this).find('.resource-type-checkbox');
            const isChecked = checkbox.prop('checked');

            checkbox.prop('checked', !isChecked);
            $(this).toggleClass('selected', !isChecked);

            updateBulkActionButtons();
        });

        // Handle checkbox changes
        $(document).on('change', '.resource-type-checkbox', function() {
            const card = $(this).closest('.resource-type-card');
            card.toggleClass('selected', $(this).prop('checked'));
            updateBulkActionButtons();
        });

        function updateBulkActionButtons() {
            const selectedCount = $('.resource-type-checkbox:checked').length;

            if (selectedCount > 0) {
                $('#bulk-delete-btn').show().text(`Delete Selected (${selectedCount})`);
            } else {
                $('#bulk-delete-btn').hide();
            }
        }

        function bulkDelete() {
            const selectedIds = $('.resource-type-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            if (selectedIds.length === 0) {
                showErrorMessage('Please select at least one resource type to delete.');
                return;
            }

            // Check if any selected types have resources
            let hasResources = false;
            $('.resource-type-checkbox:checked').each(function() {
                const card = $(this).closest('.resource-type-card');
                const resourceCount = parseInt(card.find('.badge').text().split(' ')[0]);
                if (resourceCount > 0) {
                    hasResources = true;
                    return false;
                }
            });

            if (hasResources) {
                showErrorMessage('Cannot delete resource types that have associated resources. Please remove or reassign the resources first.');
                return;
            }

            Swal.fire({
                title: 'Are you sure?',
                text: `You are about to delete ${selectedIds.length} resource type(s). This action cannot be undone.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, delete them!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Deleting...',
                        text: 'Please wait while we delete the selected resource types.',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Delete each selected type
                    let deletePromises = selectedIds.map(id => {
                        return $.ajax({
                            url: `/owner/resource-types/${id}`,
                            method: 'DELETE',
                            data: {
                                _token: '{{ csrf_token() }}'
                            }
                        });
                    });

                    Promise.all(deletePromises)
                        .then(() => {
                            Swal.fire({
                                title: 'Deleted!',
                                text: `${selectedIds.length} resource type(s) have been deleted successfully.`,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        })
                        .catch((error) => {
                            let message = 'Some resource types could not be deleted. Please try again.';
                            let debugInfo = '';

                            if (error.responseJSON) {
                                message = error.responseJSON.message || message;
                                if (error.responseJSON.debug_message && window.location.hostname === 'localhost') {
                                    debugInfo = `<br><small class="text-muted">Debug: ${error.responseJSON.debug_message}</small>`;
                                }
                            }

                            Swal.fire({
                                title: 'Error!',
                                html: message + debugInfo,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        });
                }
            });
        }
    </script>
@stop