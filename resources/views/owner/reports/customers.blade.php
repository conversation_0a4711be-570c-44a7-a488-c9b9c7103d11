@extends('owner.layouts.app')

@section('title', 'Customer Reports')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Customer Reports</h1>
            <p class="text-muted">Customer analytics and behavior insights</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary" onclick="exportReport()">
                        <i class="fas fa-download mr-2"></i>
                        Export Report
                    </button>
                    <button type="button" class="btn btn-primary" onclick="refreshReports()">
                        <i class="fas fa-sync mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Report Filters --}}
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Report Filters
            </h3>
        </div>
        <div class="card-body">
            <form id="reportFilters">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_range">Date Range</label>
                            <select class="form-control" id="date_range" name="date_range">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="365">Last year</option>
                                <option value="custom">Custom range</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3" id="custom_date_range" style="display: none;">
                        <div class="form-group">
                            <label for="start_date">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                        </div>
                    </div>
                    <div class="col-md-3" id="custom_end_date" style="display: none;">
                        <div class="form-group">
                            <label for="end_date">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="customer_status">Customer Status</label>
                            <select class="form-control" id="customer_status" name="customer_status">
                                <option value="all">All Customers</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="vip">VIP</option>
                                <option value="prospect">Prospects</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-primary btn-block" onclick="updateReports()">
                                <i class="fas fa-search mr-2"></i>
                                Generate Report
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Key Metrics --}}
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="total_customers">-</h3>
                    <p>Total Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="new_customers">-</h3>
                    <p>New Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-plus"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="returning_customers">-</h3>
                    <p>Returning Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="at_risk_customers">-</h3>
                    <p>At Risk Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Charts Row --}}
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line mr-2"></i>
                        Customer Acquisition Trend
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="acquisitionChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Customer Status Distribution
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    {{-- Detailed Analytics --}}
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Loyalty Tier Distribution
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="loyaltyChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        Customer Value Segments
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="valueChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    {{-- Top Customers Table --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-crown mr-2"></i>
                Top Customers by Value
            </h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="topCustomersTable">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Customer</th>
                            <th>Total Spent</th>
                            <th>Visits</th>
                            <th>Avg Order</th>
                            <th>Last Visit</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="7" class="text-center">
                                <i class="fas fa-spinner fa-spin"></i> Loading data...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    @parent
<script>
let acquisitionChart, statusChart, loyaltyChart, valueChart;

$(document).ready(function() {
    // Initialize charts
    initializeCharts();

    // Load initial data
    updateReports();

    // Handle date range change
    $('#date_range').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom_date_range, #custom_end_date').show();
        } else {
            $('#custom_date_range, #custom_end_date').hide();
        }
    });
});

function initializeCharts() {
    // Customer Acquisition Trend Chart
    const acquisitionCtx = document.getElementById('acquisitionChart').getContext('2d');
    acquisitionChart = new Chart(acquisitionCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'New Customers',
                data: [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Customer Status Distribution Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active', 'Inactive', 'VIP', 'Prospect'],
            datasets: [{
                data: [],
                backgroundColor: ['#28a745', '#6c757d', '#ffc107', '#17a2b8']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Loyalty Tier Distribution Chart
    const loyaltyCtx = document.getElementById('loyaltyChart').getContext('2d');
    loyaltyChart = new Chart(loyaltyCtx, {
        type: 'bar',
        data: {
            labels: ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond'],
            datasets: [{
                label: 'Customers',
                data: [],
                backgroundColor: ['#cd7f32', '#c0c0c0', '#ffd700', '#e5e4e2', '#b9f2ff']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Customer Value Segments Chart
    const valueCtx = document.getElementById('valueChart').getContext('2d');
    valueChart = new Chart(valueCtx, {
        type: 'pie',
        data: {
            labels: ['High Value', 'Medium Value', 'Low Value', 'New Customer'],
            datasets: [{
                data: [],
                backgroundColor: ['#dc3545', '#ffc107', '#28a745', '#17a2b8']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function updateReports() {
    const formData = new FormData(document.getElementById('reportFilters'));

    // Show loading state
    showLoadingState();

    $.ajax({
        url: '{{ route("owner.reports.customers.data") }}',
        method: 'GET',
        data: Object.fromEntries(formData),
        success: function(response) {
            if (response.success) {
                updateMetrics(response.data.metrics);
                updateCharts(response.data.charts);
                updateTopCustomersTable(response.data.top_customers);
            } else {
                toastr.error(response.message || 'Failed to load report data');
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            toastr.error(response?.message || 'Failed to load report data');
        },
        complete: function() {
            hideLoadingState();
        }
    });
}

function updateMetrics(metrics) {
    $('#total_customers').text(metrics.total_customers.toLocaleString());
    $('#new_customers').text(metrics.new_customers.toLocaleString());
    $('#returning_customers').text(metrics.returning_customers.toLocaleString());
    $('#at_risk_customers').text(metrics.at_risk_customers.toLocaleString());
}

function updateCharts(charts) {
    // Update acquisition chart
    acquisitionChart.data.labels = charts.acquisition.labels;
    acquisitionChart.data.datasets[0].data = charts.acquisition.data;
    acquisitionChart.update();

    // Update status chart
    statusChart.data.datasets[0].data = charts.status.data;
    statusChart.update();

    // Update loyalty chart
    loyaltyChart.data.datasets[0].data = charts.loyalty.data;
    loyaltyChart.update();

    // Update value chart
    valueChart.data.datasets[0].data = charts.value.data;
    valueChart.update();
}

function updateTopCustomersTable(customers) {
    const tbody = $('#topCustomersTable tbody');
    tbody.empty();

    if (customers.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted">
                    No customers found for the selected criteria
                </td>
            </tr>
        `);
        return;
    }

    customers.forEach((customer, index) => {
        const statusBadge = getStatusBadge(customer.status);
        const row = `
            <tr>
                <td><strong>#${index + 1}</strong></td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-circle bg-primary text-white mr-2"
                             style="width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                            ${customer.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <strong>${customer.name}</strong>
                            <br><small class="text-muted">${customer.email}</small>
                        </div>
                    </div>
                </td>
                <td><strong>$${customer.total_spent.toLocaleString()}</strong></td>
                <td>${customer.total_visits}</td>
                <td>$${customer.average_order_value.toLocaleString()}</td>
                <td>${customer.last_visit_date || 'Never'}</td>
                <td>${statusBadge}</td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getStatusBadge(status) {
    const badges = {
        'active': '<span class="badge badge-success">Active</span>',
        'inactive': '<span class="badge badge-secondary">Inactive</span>',
        'vip': '<span class="badge badge-warning">VIP</span>',
        'prospect': '<span class="badge badge-info">Prospect</span>',
        'blocked': '<span class="badge badge-danger">Blocked</span>'
    };
    return badges[status] || '<span class="badge badge-secondary">Unknown</span>';
}

function showLoadingState() {
    // Update metrics with loading state
    $('#total_customers, #new_customers, #returning_customers, #at_risk_customers').html('<i class="fas fa-spinner fa-spin"></i>');

    // Show loading in table
    $('#topCustomersTable tbody').html(`
        <tr>
            <td colspan="7" class="text-center">
                <i class="fas fa-spinner fa-spin"></i> Loading data...
            </td>
        </tr>
    `);
}

function hideLoadingState() {
    // Loading state is automatically hidden when data is updated
}

function refreshReports() {
    updateReports();
    toastr.success('Reports refreshed successfully');
}

function exportReport() {
    const formData = new FormData(document.getElementById('reportFilters'));
    const params = new URLSearchParams(Object.fromEntries(formData));

    window.location.href = `{{ route("owner.reports.customers.export") }}?${params.toString()}`;
}
</script>
@stop