@extends('owner.layouts.app')

@section('title', $image->title ?: 'Gallery Image')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>{{ $image->title ?: 'Gallery Image' }}</h1>
            <p class="text-muted mb-0">View image details and information</p>
        </div>
        <div>
            <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary mr-2">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Gallery
            </a>
            <a href="{{ route('owner.gallery.edit', $image) }}" class="btn btn-primary">
                <i class="fas fa-edit mr-1"></i>
                Edit Image
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-md-8">
            <!-- Main Image Display -->
            <div class="card">
                <div class="card-body text-center">
                    <img src="{{ $image->full_url }}" alt="{{ $image->alt_text }}" class="img-fluid" style="max-height: 600px;">
                </div>
            </div>

            <!-- Image Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle mr-2"></i>
                        Image Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Title:</strong></td>
                                    <td>{{ $image->title ?: 'No title' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Alt Text:</strong></td>
                                    <td>{{ $image->alt_text ?: 'No alt text' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>
                                        @if($image->category)
                                            <span class="badge badge-info">{{ $image->category->name }}</span>
                                        @else
                                            <span class="text-muted">Uncategorized</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($image->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-secondary">Inactive</span>
                                        @endif
                                        @if($image->is_featured)
                                            <span class="badge badge-warning ml-1">Featured</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Filename:</strong></td>
                                    <td><code>{{ $image->original_name }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>File Size:</strong></td>
                                    <td>{{ $image->formatted_file_size }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Dimensions:</strong></td>
                                    <td>{{ $image->width }}x{{ $image->height }} pixels</td>
                                </tr>
                                <tr>
                                    <td><strong>Uploaded:</strong></td>
                                    <td>{{ $image->uploaded_at->format('M j, Y g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($image->description)
                        <hr>
                        <div>
                            <strong>Description:</strong>
                            <p class="mt-2">{{ $image->description }}</p>
                        </div>
                    @endif

                    @if($image->tags && count($image->tags) > 0)
                        <hr>
                        <div>
                            <strong>Tags:</strong>
                            <div class="mt-2">
                                @foreach($image->tags as $tag)
                                    <span class="badge badge-light mr-1">{{ $tag }}</span>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools mr-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('owner.gallery.edit', $image) }}" class="btn btn-primary btn-block">
                            <i class="fas fa-edit mr-1"></i>
                            Edit Image
                        </a>
                        <button class="btn btn-warning btn-block toggle-featured" data-id="{{ $image->id }}">
                            <i class="fas fa-star mr-1"></i>
                            {{ $image->is_featured ? 'Remove from Featured' : 'Mark as Featured' }}
                        </button>
                        <button class="btn btn-info btn-block toggle-active" data-id="{{ $image->id }}">
                            <i class="fas fa-eye mr-1"></i>
                            {{ $image->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                        <button class="btn btn-danger btn-block" onclick="deleteImage()">
                            <i class="fas fa-trash mr-1"></i>
                            Delete Image
                        </button>
                    </div>
                </div>
            </div>

            <!-- Image URLs -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link mr-2"></i>
                        Image URLs
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>Original Image</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ $image->full_url }}" readonly>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $image->full_url }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Thumbnail</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ $image->thumbnail_url }}" readonly>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $image->thumbnail_url }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Medium Size</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ $image->medium_url }}" readonly>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $image->medium_url }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file mr-2"></i>
                        File Details
                    </h5>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <strong>MIME Type:</strong> {{ $image->mime_type }}<br>
                        <strong>File Path:</strong> <code>{{ $image->path }}</code><br>
                        <strong>Sort Order:</strong> {{ $image->sort_order }}<br>
                        <strong>Created:</strong> {{ $image->created_at->format('M j, Y g:i A') }}<br>
                        <strong>Updated:</strong> {{ $image->updated_at->format('M j, Y g:i A') }}
                    </small>
                </div>
            </div>
        </div>
    </div>
@stop

@push('scripts')
<script>
$(document).ready(function() {
    // Toggle featured status
    $('.toggle-featured').click(function() {
        const imageId = $(this).data('id');
        const button = $(this);

        $.post(`/owner/gallery/${imageId}/toggle-featured`, {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            if (response.success) {
                toastr.success(response.message);
                location.reload();
            } else {
                toastr.error(response.message);
            }
        }).fail(function() {
            toastr.error('Failed to update image status.');
        });
    });

    // Toggle active status
    $('.toggle-active').click(function() {
        const imageId = $(this).data('id');
        const button = $(this);

        $.post(`/owner/gallery/${imageId}/toggle-active`, {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            if (response.success) {
                toastr.success(response.message);
                location.reload();
            } else {
                toastr.error(response.message);
            }
        }).fail(function() {
            toastr.error('Failed to update image status.');
        });
    });
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        toastr.success('URL copied to clipboard!');
    }, function(err) {
        toastr.error('Failed to copy URL');
    });
}

// Delete image function
function deleteImage() {
    if (confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("owner.gallery.destroy", $image) }}';

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '{{ csrf_token() }}';

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
