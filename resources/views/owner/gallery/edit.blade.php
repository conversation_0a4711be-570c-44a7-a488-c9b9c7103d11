@extends('owner.layouts.app')

@section('title', 'Edit Image')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Edit Image</h1>
            <p class="text-muted mb-0">Update image information and settings</p>
        </div>
        <div>
            <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Gallery
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit mr-2"></i>
                        Image Details
                    </h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('owner.gallery.update', $image) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="form-group">
                            <label for="title">Title</label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror"
                                   id="title" name="title" value="{{ old('title', $image->title) }}"
                                   placeholder="Enter image title">
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="alt_text">Alt Text</label>
                            <input type="text" class="form-control @error('alt_text') is-invalid @enderror"
                                   id="alt_text" name="alt_text" value="{{ old('alt_text', $image->alt_text) }}"
                                   placeholder="Describe the image for accessibility">
                            <small class="form-text text-muted">Important for SEO and accessibility</small>
                            @error('alt_text')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3"
                                      placeholder="Describe the image content">{{ old('description', $image->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="category_id">Category</label>
                            <select class="form-control @error('category_id') is-invalid @enderror"
                                    id="category_id" name="category_id">
                                <option value="">No Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}"
                                            {{ old('category_id', $image->category_id) == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="tags">Tags</label>
                            <input type="text" class="form-control @error('tags') is-invalid @enderror"
                                   id="tags" name="tags"
                                   value="{{ old('tags', is_array($image->tags) ? implode(', ', $image->tags) : '') }}"
                                   placeholder="tag1, tag2, tag3">
                            <small class="form-text text-muted">Separate tags with commas</small>
                            @error('tags')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="is_featured"
                                               name="is_featured" value="1"
                                               {{ old('is_featured', $image->is_featured) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_featured">Featured Image</label>
                                    </div>
                                    <small class="form-text text-muted">Featured images appear prominently in galleries</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="is_active"
                                               name="is_active" value="1"
                                               {{ old('is_active', $image->is_active) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">Active</label>
                                    </div>
                                    <small class="form-text text-muted">Inactive images won't be displayed publicly</small>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i>
                                Update Image
                            </button>
                            <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary">
                                Cancel
                            </a>
                            <button type="button" class="btn btn-danger float-right" onclick="deleteImage()">
                                <i class="fas fa-trash mr-1"></i>
                                Delete Image
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Image Preview -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-image mr-2"></i>
                        Image Preview
                    </h5>
                </div>
                <div class="card-body text-center">
                    <img src="{{ $image->medium_url }}" alt="{{ $image->alt_text }}" class="img-fluid mb-3" style="max-height: 300px;">

                    <div class="image-info">
                        <small class="text-muted">
                            <strong>Filename:</strong> {{ $image->original_name }}<br>
                            <strong>Size:</strong> {{ $image->formatted_file_size }}<br>
                            <strong>Dimensions:</strong> {{ $image->width }}x{{ $image->height }}<br>
                            <strong>Type:</strong> {{ strtoupper(pathinfo($image->filename, PATHINFO_EXTENSION)) }}<br>
                            <strong>Uploaded:</strong> {{ $image->uploaded_at->format('M j, Y g:i A') }}
                        </small>
                    </div>
                </div>
            </div>

            <!-- Image URLs -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link mr-2"></i>
                        Image URLs
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>Original</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ $image->full_url }}" readonly>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $image->full_url }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Thumbnail</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ $image->thumbnail_url }}" readonly>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $image->thumbnail_url }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Badges -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tags mr-2"></i>
                        Status
                    </h5>
                </div>
                <div class="card-body">
                    @if($image->is_featured)
                        <span class="badge badge-warning mb-2">Featured</span>
                    @endif
                    @if($image->is_active)
                        <span class="badge badge-success mb-2">Active</span>
                    @else
                        <span class="badge badge-secondary mb-2">Inactive</span>
                    @endif
                    @if($image->category)
                        <span class="badge badge-info mb-2">{{ $image->category->name }}</span>
                    @endif
                    @if($image->tags && count($image->tags) > 0)
                        @foreach($image->tags as $tag)
                            <span class="badge badge-light mb-2">{{ $tag }}</span>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>
@stop

@push('scripts')
<script>
// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        toastr.success('URL copied to clipboard!');
    }, function(err) {
        toastr.error('Failed to copy URL');
    });
}

// Delete image function
function deleteImage() {
    if (confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("owner.gallery.destroy", $image) }}';

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '{{ csrf_token() }}';

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
