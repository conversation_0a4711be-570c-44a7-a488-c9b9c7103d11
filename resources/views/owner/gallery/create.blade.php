@extends('owner.layouts.app')

@section('title', 'Upload Images')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Upload Images</h1>
            <p class="text-muted mb-0">Add new images to your business gallery</p>
        </div>
        <div>
            <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to Gallery
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-upload mr-2"></i>
                Upload Gallery Images
            </h3>
        </div>
        <div class="card-body">
            <form action="{{ route('owner.gallery.store') }}" method="POST" enctype="multipart/form-data" id="gallery-upload-form">
                @csrf

                <!-- File Upload Area -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="images">Select Images</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="images" name="images[]" multiple accept="image/*" required>
                                <label class="custom-file-label" for="images">Choose images...</label>
                            </div>
                            <small class="form-text text-muted">
                                Select multiple images (JPEG, PNG, GIF, WebP). Maximum 10MB per image.
                            </small>
                            @error('images')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                            @error('images.*')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Drag & Drop Area -->
                        <div class="drop-zone" id="drop-zone">
                            <div class="drop-zone-content">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>Drag & Drop Images Here</h5>
                                <p class="text-muted">or click to select files</p>
                            </div>
                        </div>

                        <!-- Image Preview Area -->
                        <div id="image-preview-container" style="display: none;">
                            <h6 class="mt-4">Selected Images:</h6>
                            <div id="image-preview-grid" class="row"></div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- Image Details -->
                        <div class="form-group">
                            <label for="category_id">Category</label>
                            <select class="form-control" id="category_id" name="category_id">
                                <option value="">No Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="title">Title (Optional)</label>
                            <input type="text" class="form-control" id="title" name="title" placeholder="Image title">
                            <small class="form-text text-muted">Will be applied to all uploaded images</small>
                            @error('title')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">Description (Optional)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Image description"></textarea>
                            <small class="form-text text-muted">Will be applied to all uploaded images</small>
                            @error('description')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="tags">Tags (Optional)</label>
                            <input type="text" class="form-control" id="tags" name="tags" placeholder="tag1, tag2, tag3">
                            <small class="form-text text-muted">Separate tags with commas</small>
                            @error('tags')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_featured" name="is_featured" value="1">
                                <label class="custom-control-label" for="is_featured">Mark as Featured</label>
                            </div>
                            <small class="form-text text-muted">Featured images appear prominently in galleries</small>
                            @error('is_featured')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Upload Progress -->
                        <div id="upload-progress" style="display: none;">
                            <div class="progress mb-3">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="upload-status"></div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary btn-block" id="upload-btn">
                            <i class="fas fa-upload mr-2"></i>
                            Upload Images
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Category Creation -->
    @if($categories->count() === 0)
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tags mr-2"></i>
                    Create Your First Category
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Organize your images by creating categories first.</p>
                <a href="{{ route('owner.gallery.categories.create') }}" class="btn btn-outline-primary">
                    <i class="fas fa-plus mr-1"></i>
                    Create Category
                </a>
            </div>
        </div>
    @endif
@stop

@push('styles')
<style>
.drop-zone {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.drop-zone:hover,
.drop-zone.dragover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.drop-zone-content {
    pointer-events: none;
}

.image-preview {
    position: relative;
    margin-bottom: 15px;
}

.image-preview img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 5px;
}

.image-preview .remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255, 0, 0, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 12px;
    cursor: pointer;
}

.custom-file-label::after {
    content: "Browse";
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    const dropZone = $('#drop-zone');
    const fileInput = $('#images');
    const previewContainer = $('#image-preview-container');
    const previewGrid = $('#image-preview-grid');
    const uploadBtn = $('#upload-btn');
    const uploadProgress = $('#upload-progress');

    let selectedFiles = [];

    // File input change
    fileInput.on('change', function(e) {
        handleFiles(e.target.files);
    });

    // Drag and drop functionality
    dropZone.on('click', function() {
        fileInput.click();
    });

    dropZone.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    dropZone.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    dropZone.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        handleFiles(e.originalEvent.dataTransfer.files);
    });

    // Handle file selection
    function handleFiles(files) {
        selectedFiles = Array.from(files);
        updateFileLabel();
        showImagePreviews();
    }

    // Update file input label
    function updateFileLabel() {
        const label = $('.custom-file-label');
        if (selectedFiles.length > 0) {
            label.text(`${selectedFiles.length} file(s) selected`);
        } else {
            label.text('Choose images...');
        }
    }

    // Show image previews
    function showImagePreviews() {
        previewGrid.empty();

        if (selectedFiles.length > 0) {
            previewContainer.show();

            selectedFiles.forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const preview = $(`
                            <div class="col-md-3 col-sm-4 col-6">
                                <div class="image-preview">
                                    <img src="${e.target.result}" alt="Preview">
                                    <button type="button" class="remove-btn" data-index="${index}">×</button>
                                    <small class="d-block text-muted mt-1">${file.name}</small>
                                </div>
                            </div>
                        `);
                        previewGrid.append(preview);
                    };
                    reader.readAsDataURL(file);
                }
            });
        } else {
            previewContainer.hide();
        }
    }

    // Remove image from selection
    $(document).on('click', '.remove-btn', function() {
        const index = $(this).data('index');
        selectedFiles.splice(index, 1);

        // Update file input
        const dt = new DataTransfer();
        selectedFiles.forEach(file => dt.items.add(file));
        fileInput[0].files = dt.files;

        updateFileLabel();
        showImagePreviews();
    });

    // Form submission with progress
    $('#gallery-upload-form').on('submit', function(e) {
        if (selectedFiles.length === 0) {
            e.preventDefault();
            toastr.error('Please select at least one image to upload.');
            return;
        }

        uploadBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...');
        uploadProgress.show();

        // Simulate progress (in real implementation, this would be handled by the server)
        let progress = 0;
        const progressInterval = setInterval(function() {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;

            $('.progress-bar').css('width', progress + '%');
            $('#upload-status').text(`Uploading... ${Math.round(progress)}%`);

            if (progress >= 90) {
                clearInterval(progressInterval);
            }
        }, 200);
    });
});
</script>
@endpush
