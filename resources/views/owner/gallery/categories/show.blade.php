@extends('layouts.owner')

@section('title', $category->name . ' - Gallery Category')

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ $category->name }}</h1>
                    <p class="text-muted">{{ $category->description ?: 'No description provided' }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('owner.gallery.index') }}">Gallery</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('owner.gallery.categories.index') }}">Categories</a></li>
                        <li class="breadcrumb-item active">{{ $category->name }}</li>
                    </ol>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <a href="{{ route('owner.gallery.categories.index') }}" class="btn btn-secondary btn-sm mr-2">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Categories
                    </a>
                    <a href="{{ route('owner.gallery.categories.edit', $category) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit mr-1"></i>
                        Edit Category
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
    <!-- Category Info -->
    <div class="row mb-3">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle mr-2"></i>
                        Category Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Name:</strong> {{ $category->name }}<br>
                            <strong>Slug:</strong> <code>{{ $category->slug }}</code><br>
                            <strong>Status:</strong>
                            @if($category->is_active)
                                <span class="badge badge-success">Active</span>
                            @else
                                <span class="badge badge-secondary">Inactive</span>
                            @endif
                        </div>
                        <div class="col-sm-6">
                            <strong>Sort Order:</strong> {{ $category->sort_order }}<br>
                            <strong>Images:</strong> {{ $images->total() }}<br>
                            <strong>Color:</strong>
                            @if($category->color)
                                <span class="color-preview" style="background-color: {{ $category->color }}; width: 20px; height: 20px; display: inline-block; border-radius: 3px; border: 1px solid #ddd; vertical-align: middle;"></span>
                                <code class="ml-1">{{ $category->color }}</code>
                            @else
                                <span class="text-muted">No color set</span>
                            @endif
                        </div>
                    </div>
                    @if($category->description)
                        <hr>
                        <strong>Description:</strong><br>
                        <p class="mb-0">{{ $category->description }}</p>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h3 class="text-primary">{{ $images->total() }}</h3>
                        <p class="text-muted mb-0">Total Images</p>
                    </div>
                    <hr>
                    <small class="text-muted">
                        Created: {{ $category->created_at->format('M j, Y') }}<br>
                        Updated: {{ $category->updated_at->format('M j, Y') }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Images -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-images mr-2"></i>
                Images in this Category
            </h5>
        </div>
        <div class="card-body">
            @if($images->count() > 0)
                <div class="row">
                    @foreach($images as $image)
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card gallery-image-card">
                                <div class="image-container position-relative">
                                    <img src="{{ $image->thumbnail_url }}" alt="{{ $image->alt_text }}" class="card-img-top gallery-thumbnail">

                                    <!-- Image Overlay -->
                                    <div class="image-overlay">
                                        <div class="overlay-actions">
                                            <button class="btn btn-sm btn-light" onclick="viewImage({{ $image->id }})" title="View">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="{{ route('owner.gallery.edit', $image) }}" class="btn btn-sm btn-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($image->is_featured)
                                                <span class="btn btn-sm btn-warning" title="Featured">
                                                    <i class="fas fa-star"></i>
                                                </span>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Status Badges -->
                                    <div class="status-badges">
                                        @if($image->is_featured)
                                            <span class="badge badge-warning">Featured</span>
                                        @endif
                                        @if(!$image->is_active)
                                            <span class="badge badge-secondary">Inactive</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="card-body p-2">
                                    <h6 class="card-title mb-1">{{ $image->title ?: 'Untitled' }}</h6>
                                    <small class="text-muted">
                                        {{ $image->formatted_file_size }} • {{ $image->width }}x{{ $image->height }}
                                        <br>
                                        {{ $image->uploaded_at->format('M j, Y') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $images->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-images fa-4x text-muted mb-3"></i>
                    <h4>No Images in this Category</h4>
                    <p class="text-muted">Images assigned to this category will appear here.</p>
                    <a href="{{ route('owner.gallery.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-1"></i>
                        Upload Images
                    </a>
                </div>
            @endif
        </div>
    </div>
        </div>
    </section>
</div>
@endsection

@push('styles')
<style>
.gallery-image-card {
    transition: transform 0.2s;
}

.gallery-image-card:hover {
    transform: translateY(-2px);
}

.image-container {
    position: relative;
    overflow: hidden;
}

.gallery-thumbnail {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.image-container:hover .image-overlay {
    opacity: 1;
}

.overlay-actions {
    display: flex;
    gap: 5px;
}

.status-badges {
    position: absolute;
    top: 10px;
    left: 10px;
}
</style>
@endpush

@push('scripts')
<script>
// View image function
function viewImage(imageId) {
    // This would load image details via AJAX or redirect to image view
    window.location.href = `/owner/gallery/${imageId}`;
}
</script>
@endpush
