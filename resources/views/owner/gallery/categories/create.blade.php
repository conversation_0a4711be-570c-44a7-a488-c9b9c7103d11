@extends('layouts.owner')

@section('title', 'Create Gallery Category')

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Create Gallery Category</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('owner.gallery.index') }}">Gallery</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('owner.gallery.categories.index') }}">Categories</a></li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6><i class="icon fas fa-ban"></i> Please fix the following errors:</h6>
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus mr-2"></i>
                        Create New Category
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('owner.gallery.categories.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Categories
                        </a>
                    </div>
                </div>
        <div class="card-body">
            <form action="{{ route('owner.gallery.categories.store') }}" method="POST">
                @csrf

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="name">Category Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3"
                                      placeholder="Describe what type of images belong in this category">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="color">Category Color</label>
                            <input type="color" class="form-control @error('color') is-invalid @enderror"
                                   id="color" name="color" value="{{ old('color', '#007bff') }}">
                            <small class="form-text text-muted">Choose a color to represent this category</small>
                            @error('color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="sort_order">Sort Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                            <small class="form-text text-muted">Lower numbers appear first</small>
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                            <small class="form-text text-muted">Inactive categories won't appear in selection lists</small>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="form-group mb-0">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i>
                        Create Category
                    </button>
                    <a href="{{ route('owner.gallery.categories.index') }}" class="btn btn-secondary">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Preview Card -->
    <div class="card mt-3">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-eye mr-2"></i>
                Preview
            </h5>
        </div>
        <div class="card-body">
            <div class="category-preview">
                <span class="badge badge-primary" id="category-preview-badge">
                    <span class="color-dot" id="preview-color-dot" style="background-color: #007bff;"></span>
                    <span id="preview-name">Category Name</span>
                </span>
                <p class="text-muted mt-2 mb-0" id="preview-description">Category description will appear here...</p>
            </div>
        </div>
    </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('styles')
<style>
.category-preview {
    padding: 20px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    text-align: center;
}

.color-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.badge {
    font-size: 14px;
    padding: 8px 12px;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Live preview updates
    $('#name').on('input', function() {
        const name = $(this).val() || 'Category Name';
        $('#preview-name').text(name);
    });

    $('#description').on('input', function() {
        const description = $(this).val() || 'Category description will appear here...';
        $('#preview-description').text(description);
    });

    $('#color').on('input', function() {
        const color = $(this).val();
        $('#preview-color-dot').css('background-color', color);
        $('#category-preview-badge').css('background-color', color);
    });

    // Auto-generate slug preview (optional)
    $('#name').on('input', function() {
        const name = $(this).val();
        const slug = name.toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/^-+|-+$/g, '');
        // You could show this in a preview if needed
    });
});
</script>
@endpush
