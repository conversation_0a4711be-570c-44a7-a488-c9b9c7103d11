@extends('layouts.owner')

@section('title', 'Gallery Categories')

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Gallery Categories</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('owner.gallery.index') }}">Gallery</a></li>
                        <li class="breadcrumb-item active">Categories</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif
            <!-- Categories Management Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tags mr-2"></i>
                        Gallery Categories
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('owner.gallery.index') }}" class="btn btn-secondary btn-sm mr-2">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Gallery
                        </a>
                        <a href="{{ route('owner.gallery.categories.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus mr-1"></i>
                            Add Category
                        </a>
                    </div>
                </div>
        <div class="card-body">
            @if($categories->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Images</th>
                                <th>Color</th>
                                <th>Status</th>
                                <th>Sort Order</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($categories as $category)
                                <tr>
                                    <td>
                                        <strong>{{ $category->name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $category->slug }}</small>
                                    </td>
                                    <td>
                                        {{ Str::limit($category->description, 50) }}
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ $category->images_count }}</span>
                                    </td>
                                    <td>
                                        @if($category->color)
                                            <span class="color-preview" style="background-color: {{ $category->color }}; width: 20px; height: 20px; display: inline-block; border-radius: 3px; border: 1px solid #ddd;"></span>
                                            <small class="ml-1">{{ $category->color }}</small>
                                        @else
                                            <span class="text-muted">No color</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($category->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>{{ $category->sort_order }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('owner.gallery.categories.show', $category) }}" class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('owner.gallery.categories.edit', $category) }}" class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-warning toggle-active" data-id="{{ $category->id }}" title="Toggle Status">
                                                <i class="fas fa-power-off"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory({{ $category->id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                    <h4>No Categories Yet</h4>
                    <p class="text-muted">Create your first category to organize your gallery images.</p>
                    <a href="{{ route('owner.gallery.categories.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-1"></i>
                        Create Your First Category
                    </a>
                </div>
            @endif
        </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Toggle category status
    $('.toggle-active').click(function() {
        const categoryId = $(this).data('id');
        const button = $(this);

        $.post(`/owner/gallery/categories/${categoryId}/toggle-active`, {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Failed to update category status.');
            }
        }).fail(function() {
            alert('Failed to update category status.');
        });
    });
});

// Delete category function
function deleteCategory(categoryId) {
    if (confirm('Are you sure you want to delete this category? Images in this category will be moved to "Uncategorized".')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/owner/gallery/categories/${categoryId}`;

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '{{ csrf_token() }}';

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
