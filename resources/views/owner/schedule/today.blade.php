@extends('owner.layouts.app')

@section('title', 'Today\'s Schedule')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Today's Schedule</h1>
            <p class="text-muted">{{ now()->format('l, F j, Y') }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('owner.calendar.index') }}" class="btn btn-info">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Full Calendar
                </a>
                <a href="{{ route('owner.bookings.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    New Booking
                </a>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Quick Stats for Today --}}
    <div class="row mb-3" id="stats-container">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="total-appointments">{{ $stats['total_appointments'] }}</h3>
                    <p>Total Appointments</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="completed-appointments">{{ $stats['completed'] }}</h3>
                    <p>Completed</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="upcoming-appointments">{{ $stats['upcoming'] }}</h3>
                    <p>Upcoming</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3 id="today-revenue">${{ number_format($stats['total_revenue'], 2) }}</h3>
                    <p>Today's Revenue</p>
                </div>
                <div class="icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- Timeline Schedule --}}
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2"></i>
                        Today's Timeline
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-info" id="current-time">{{ now()->format('g:i A') }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="timeline" id="appointments-timeline">
                        @forelse($todayAppointments as $appointment)
                            @php
                                $statusConfig = [
                                    'completed' => ['icon' => 'fas fa-check', 'color' => 'success', 'label' => 'Completed'],
                                    'in_progress' => ['icon' => 'fas fa-user', 'color' => 'warning', 'label' => 'In Progress'],
                                    'confirmed' => ['icon' => 'fas fa-clock', 'color' => 'info', 'label' => 'Confirmed'],
                                    'pending' => ['icon' => 'fas fa-clock', 'color' => 'secondary', 'label' => 'Pending'],
                                    'no_show' => ['icon' => 'fas fa-times', 'color' => 'danger', 'label' => 'No Show'],
                                    'cancelled' => ['icon' => 'fas fa-ban', 'color' => 'dark', 'label' => 'Cancelled'],
                                ];
                                $config = $statusConfig[$appointment['status']] ?? $statusConfig['pending'];
                            @endphp

                            {{-- Time Label --}}
                            <div class="time-label">
                                <span class="bg-{{ $config['color'] }}">{{ $appointment['start_time'] }}</span>
                            </div>

                            {{-- Appointment Item --}}
                            <div>
                                <i class="{{ $config['icon'] }} bg-{{ $config['color'] }}"></i>
                                <div class="timeline-item" data-booking-id="{{ $appointment['id'] }}">
                                    <span class="time">
                                        <i class="fas fa-clock"></i>
                                        {{ $appointment['start_time'] }} - {{ $appointment['end_time'] }}
                                    </span>
                                    <h3 class="timeline-header">
                                        <strong>{{ $appointment['customer_name'] }}</strong> -
                                        {{ $appointment['services']->pluck('name')->join(', ') }}
                                        <span class="badge badge-{{ $config['color'] }} ml-2">{{ $config['label'] }}</span>
                                    </h3>
                                    <div class="timeline-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                @if($appointment['customer_phone'])
                                                    <small><i class="fas fa-phone mr-1"></i> {{ $appointment['customer_phone'] }}</small><br>
                                                @endif
                                                <small><i class="fas fa-envelope mr-1"></i> {{ $appointment['customer_email'] }}</small><br>
                                                @if($appointment['notes'])
                                                    <small><i class="fas fa-sticky-note mr-1"></i> {{ Str::limit($appointment['notes'], 50) }}</small>
                                                @endif
                                            </div>
                                            <div class="col-md-6">
                                                <small><i class="fas fa-dollar-sign mr-1"></i> ${{ number_format($appointment['total_amount'], 2) }}</small><br>
                                                <small><i class="fas fa-credit-card mr-1"></i> {{ ucfirst($appointment['payment_status']) }}</small><br>
                                                <small><i class="fas fa-clock mr-1"></i> {{ $appointment['duration'] }} min</small>
                                            </div>
                                        </div>

                                        {{-- Action Buttons --}}
                                        @if($appointment['status'] !== 'completed' && $appointment['status'] !== 'cancelled' && $appointment['status'] !== 'no_show')
                                            <div class="timeline-footer mt-2">
                                                @if($appointment['status'] === 'confirmed' || $appointment['status'] === 'pending')
                                                    <button class="btn btn-sm btn-success quick-action-btn"
                                                            data-action="check_in"
                                                            data-booking-id="{{ $appointment['id'] }}">
                                                        <i class="fas fa-sign-in-alt mr-1"></i> Check In
                                                    </button>
                                                @endif

                                                @if($appointment['status'] === 'in_progress')
                                                    <button class="btn btn-sm btn-primary quick-action-btn"
                                                            data-action="check_out"
                                                            data-booking-id="{{ $appointment['id'] }}">
                                                        <i class="fas fa-sign-out-alt mr-1"></i> Check Out
                                                    </button>
                                                @endif

                                                <button class="btn btn-sm btn-warning quick-action-btn"
                                                        data-action="mark_no_show"
                                                        data-booking-id="{{ $appointment['id'] }}">
                                                    <i class="fas fa-user-times mr-1"></i> No Show
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No appointments scheduled for today</h5>
                                <p class="text-muted">Your schedule is clear for today.</p>
                                <a href="{{ route('owner.bookings.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add New Booking
                                </a>
                            </div>
                        @endforelse


                        {{-- End of timeline --}}
                        @if($todayAppointments->count() > 0)
                            <div>
                                <i class="fas fa-clock bg-gray"></i>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        {{-- Sidebar Info --}}
        <div class="col-md-4">
            {{-- Current Status --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Current Status
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-box mb-3">
                        <span class="info-box-icon bg-info">
                            <i class="fas fa-clock"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Current Time</span>
                            <span class="info-box-number" id="sidebar-time">{{ $now->format('g:i A') }}</span>
                        </div>
                    </div>

                    <div class="info-box mb-3">
                        <span class="info-box-icon bg-{{ $currentAppointment ? 'warning' : 'secondary' }}">
                            <i class="fas fa-user"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Current Client</span>
                            <span class="info-box-number" id="current-client">
                                {{ $currentAppointment ? $currentAppointment->customer_name : 'None' }}
                            </span>
                        </div>
                    </div>

                    <div class="info-box">
                        <span class="info-box-icon bg-{{ $nextAppointment ? 'success' : 'secondary' }}">
                            <i class="fas fa-calendar-check"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Next Appointment</span>
                            <span class="info-box-number" id="next-appointment">
                                {{ $nextAppointment ? $nextAppointment->start_datetime->format('g:i A') : 'None' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Resource Status --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools mr-2"></i>
                        Resource Status
                    </h3>
                </div>
                <div class="card-body" id="resource-status-container">
                    @forelse($resourceStatus as $resource)
                        <div class="row mb-2" data-resource-id="{{ $resource['id'] }}">
                            <div class="col-8">
                                <strong>{{ $resource['name'] }}</strong>
                                <br><small class="text-muted">{{ $resource['type'] }}</small>
                                @if($resource['current_booking'])
                                    <br><small class="text-info">
                                        {{ $resource['current_booking']['customer_name'] }} -
                                        {{ $resource['current_booking']['service_name'] }}
                                        (until {{ $resource['current_booking']['end_time'] }})
                                    </small>
                                @endif
                            </div>
                            <div class="col-4">
                                <span class="badge badge-{{ $resource['status'] === 'available' ? 'success' : 'warning' }}">
                                    {{ ucfirst($resource['status']) }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-3">
                            <i class="fas fa-tools fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No resources configured</p>
                            <small class="text-muted">
                                <a href="{{ route('owner.resources.index') }}">Add resources</a> to track their status
                            </small>
                        </div>
                    @endforelse
                </div>
            </div>

            {{-- Quick Actions --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('owner.bookings.create') }}" class="btn btn-primary btn-block mb-2">
                            <i class="fas fa-user-plus mr-2"></i>
                            Walk-in Customer
                        </a>
                        @if($nextAppointment)
                            <button class="btn btn-info btn-block mb-2" id="call-next-customer"
                                    data-phone="{{ $nextAppointment->customer_phone ?? '' }}"
                                    data-name="{{ $nextAppointment->customer_name }}">
                                <i class="fas fa-phone mr-2"></i>
                                Call Next Customer
                            </button>
                        @else
                            <button class="btn btn-info btn-block mb-2" disabled>
                                <i class="fas fa-phone mr-2"></i>
                                Call Next Customer
                            </button>
                        @endif
                        <button class="btn btn-warning btn-block mb-2" id="mark-break-btn">
                            <i class="fas fa-clock mr-2"></i>
                            Mark Break
                        </button>
                        <button class="btn btn-secondary btn-block" id="print-schedule-btn">
                            <i class="fas fa-print mr-2"></i>
                            Print Schedule
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    <script>
        $(document).ready(function() {
            // CSRF token for AJAX requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Update current time every minute
            function updateTime() {
                var now = new Date();
                var timeString = now.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });
                $('#current-time, #sidebar-time').text(timeString);
            }

            updateTime();
            setInterval(updateTime, 60000);

            // Auto-refresh data every 2 minutes
            function refreshData() {
                refreshStats();
                refreshResourceStatus();
            }

            setInterval(refreshData, 120000); // 2 minutes

            // Refresh statistics
            function refreshStats() {
                $.get('{{ route("owner.schedule.today.stats") }}')
                    .done(function(response) {
                        if (response.success) {
                            $('#total-appointments').text(response.stats.total_appointments);
                            $('#completed-appointments').text(response.stats.completed);
                            $('#upcoming-appointments').text(response.stats.upcoming);
                            $('#today-revenue').text('$' + parseFloat(response.stats.total_revenue).toFixed(2));
                        }
                    })
                    .fail(function() {
                        console.log('Failed to refresh stats');
                    });
            }

            // Refresh resource status
            function refreshResourceStatus() {
                $.get('{{ route("owner.schedule.today.resources") }}')
                    .done(function(response) {
                        if (response.success) {
                            updateResourceStatus(response.resources);
                        }
                    })
                    .fail(function() {
                        console.log('Failed to refresh resource status');
                    });
            }

            // Update resource status display
            function updateResourceStatus(resources) {
                resources.forEach(function(resource) {
                    var resourceElement = $('[data-resource-id="' + resource.id + '"]');
                    if (resourceElement.length) {
                        var badgeClass = resource.status === 'available' ? 'badge-success' : 'badge-warning';
                        resourceElement.find('.badge')
                            .removeClass('badge-success badge-warning')
                            .addClass(badgeClass)
                            .text(resource.status.charAt(0).toUpperCase() + resource.status.slice(1));
                    }
                });
            }

            // Handle quick action buttons
            $(document).on('click', '.quick-action-btn', function() {
                var $btn = $(this);
                var action = $btn.data('action');
                var bookingId = $btn.data('booking-id');

                // Disable button during request
                $btn.prop('disabled', true);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin mr-1"></i> Processing...');

                $.post('{{ route("owner.schedule.today.quick-action") }}', {
                    action: action,
                    booking_id: bookingId
                })
                .done(function(response) {
                    if (response.success) {
                        showToast('success', response.message);
                        // Refresh the page to show updated data
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        showToast('error', response.message || 'Action failed');
                        $btn.prop('disabled', false).html(originalText);
                    }
                })
                .fail(function(xhr) {
                    var message = 'An error occurred';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    showToast('error', message);
                    $btn.prop('disabled', false).html(originalText);
                });
            });

            // Call next customer functionality
            $('#call-next-customer').click(function() {
                var phone = $(this).data('phone');
                var name = $(this).data('name');

                if (phone) {
                    if (confirm('Call ' + name + ' at ' + phone + '?')) {
                        // Open phone dialer (works on mobile devices)
                        window.location.href = 'tel:' + phone;
                    }
                } else {
                    showToast('warning', 'No phone number available for ' + name);
                }
            });

            // Mark break functionality
            $('#mark-break-btn').click(function() {
                showToast('info', 'Break marking feature coming soon');
            });

            // Print schedule functionality
            $('#print-schedule-btn').click(function() {
                window.print();
            });

            // Toast notification function
            function showToast(type, message) {
                var bgClass = {
                    'success': 'bg-success',
                    'error': 'bg-danger',
                    'warning': 'bg-warning',
                    'info': 'bg-info'
                }[type] || 'bg-info';

                $(document).Toasts('create', {
                    class: bgClass,
                    title: type.charAt(0).toUpperCase() + type.slice(1),
                    body: message,
                    autohide: true,
                    delay: 5000
                });
            }

            // Initial data refresh after 5 seconds
            setTimeout(refreshData, 5000);
        });
    </script>
@stop
