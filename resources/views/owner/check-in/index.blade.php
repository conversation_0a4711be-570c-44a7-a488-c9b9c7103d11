@extends('owner.layouts.app')

@section('title', 'Check-In Dashboard')

@section('content_header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Check-In Dashboard</h1>
            <p class="text-muted">Manage customer check-ins and check-outs for {{ $selectedDate->format('F j, Y') }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <div class="btn-group" role="group">
                    <input type="date" id="date-filter" class="form-control" value="{{ $selectedDate->format('Y-m-d') }}" style="width: auto; display: inline-block;">
                    <a href="{{ route('owner.bookings.index') }}" class="btn btn-secondary">
                        <i class="fas fa-calendar-check mr-2"></i>
                        All Bookings
                    </a>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    {{-- Filters --}}
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" id="filter-form">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="service_id">Filter by Service:</label>
                                <select name="service_id" id="service_id" class="form-control">
                                    <option value="">All Services</option>
                                    @foreach($services as $service)
                                        <option value="{{ $service->id }}" {{ request('service_id') == $service->id ? 'selected' : '' }}>
                                            {{ $service->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status">Filter by Status:</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    <option value="waiting" {{ request('status') == 'waiting' ? 'selected' : '' }}>Waiting to Check-In</option>
                                    <option value="checked_in" {{ request('status') == 'checked_in' ? 'selected' : '' }}>Currently Checked-In</option>
                                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="no_show" {{ request('status') == 'no_show' ? 'selected' : '' }}>No Shows</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="search">Search Customer:</label>
                                <input type="text" name="search" id="search" class="form-control" placeholder="Name, email, phone, or booking number..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                    <a href="{{ route('owner.check-in.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {{-- Quick Stats --}}
    <div class="row mb-3" id="stats-row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="stat-waiting">{{ $stats['waiting'] }}</h3>
                    <p>Waiting to Check-In</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="stat-checked-in">{{ $stats['checked_in'] }}</h3>
                    <p>Currently Checked-In</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="stat-completed">{{ $stats['completed'] }}</h3>
                    <p>Completed Today</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="stat-no-show">{{ $stats['no_show'] }}</h3>
                    <p>No Shows</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-times"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- Today's Appointments --}}
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-day mr-2"></i>
                        Appointments for {{ $selectedDate->format('F j, Y') }}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-primary" id="refresh-data">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($bookings->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped" id="appointments-table">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Customer</th>
                                        <th>Services</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($bookings as $booking)
                                        <tr id="booking-{{ $booking->id }}" data-booking-id="{{ $booking->id }}">
                                            <td>
                                                <strong>{{ $booking->start_datetime->format('g:i A') }}</strong>
                                                <br><small class="text-muted">{{ $booking->formatted_duration }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ $booking->customer_name }}</strong>
                                                <br><small class="text-muted">{{ $booking->customer_email }}</small>
                                                @if($booking->customer_phone)
                                                    <br><small class="text-muted">{{ $booking->customer_phone }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @foreach($booking->services as $service)
                                                    <span class="badge badge-info">{{ $service->name }}</span>
                                                @endforeach
                                            </td>
                                            <td>
                                                @if($booking->checked_out_at)
                                                    <span class="badge badge-success">Completed</span>
                                                    <br><small class="text-muted">{{ $booking->checked_out_at->format('g:i A') }}</small>
                                                @elseif($booking->checked_in_at)
                                                    <span class="badge badge-warning">Checked In</span>
                                                    <br><small class="text-muted">{{ $booking->checked_in_at->format('g:i A') }}</small>
                                                @elseif($booking->status === 'no_show')
                                                    <span class="badge badge-danger">No Show</span>
                                                @elseif($booking->status === 'confirmed')
                                                    <span class="badge badge-info">Waiting</span>
                                                @else
                                                    <span class="badge badge-secondary">{{ ucfirst($booking->status) }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical" role="group">
                                                    @if($booking->can_be_checked_in)
                                                        <button class="btn btn-sm btn-success check-in-btn" data-booking-id="{{ $booking->id }}">
                                                            <i class="fas fa-sign-in-alt"></i> Check In
                                                        </button>
                                                    @endif
                                                    @if($booking->can_be_checked_out)
                                                        <button class="btn btn-sm btn-primary check-out-btn" data-booking-id="{{ $booking->id }}">
                                                            <i class="fas fa-sign-out-alt"></i> Check Out
                                                        </button>
                                                    @endif
                                                    @if($booking->status === 'confirmed' && !$booking->checked_in_at)
                                                        <button class="btn btn-sm btn-warning no-show-btn" data-booking-id="{{ $booking->id }}">
                                                            <i class="fas fa-user-times"></i> No Show
                                                        </button>
                                                    @endif
                                                    <a href="{{ route('owner.bookings.show', $booking) }}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No appointments found</h5>
                            <p class="text-muted">There are no appointments scheduled for this date.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        {{-- Quick Actions --}}
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('owner.bookings.create') }}" class="btn btn-primary btn-block mb-2">
                            <i class="fas fa-plus mr-2"></i>
                            Walk-in Customer
                        </a>
                        <button class="btn btn-info btn-block mb-2" id="find-booking-btn">
                            <i class="fas fa-search mr-2"></i>
                            Find Booking
                        </button>
                        <button class="btn btn-secondary btn-block" onclick="window.print()">
                            <i class="fas fa-print mr-2"></i>
                            Print Schedule
                        </button>
                    </div>
                </div>
            </div>

            {{-- Current Status --}}
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Current Status
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-box mb-3">
                        <span class="info-box-icon bg-info">
                            <i class="fas fa-clock"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Current Time</span>
                            <span class="info-box-number" id="current-time">{{ now()->format('g:i A') }}</span>
                        </div>
                    </div>

                    <div class="info-box mb-3">
                        <span class="info-box-icon bg-success">
                            <i class="fas fa-user-check"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Next Appointment</span>
                            <span class="info-box-number" id="next-appointment">
                                @if($nextAppointment)
                                    {{ $nextAppointment->start_datetime->format('g:i A') }}
                                @else
                                    None
                                @endif
                            </span>
                        </div>
                    </div>

                    <div class="info-box">
                        <span class="info-box-icon bg-warning">
                            <i class="fas fa-users"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Waiting Room</span>
                            <span class="info-box-number" id="waiting-room-count">{{ $waitingRoomCount }} customers</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

{{-- Check-out Modal --}}
<div class="modal fade" id="checkOutModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Check Out Customer</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="checkOutForm">
                    <div class="form-group">
                        <label for="checkout-notes">Notes (Optional):</label>
                        <textarea class="form-control" id="checkout-notes" name="notes" rows="3" placeholder="Add any notes about the service..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmCheckOut">Check Out</button>
            </div>
        </div>
    </div>
</div>

{{-- No Show Modal --}}
<div class="modal fade" id="noShowModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Mark as No Show</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="noShowForm">
                    <div class="form-group">
                        <label for="noshow-reason">Reason (Optional):</label>
                        <textarea class="form-control" id="noshow-reason" name="reason" rows="3" placeholder="Reason for no-show..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmNoShow">Mark No Show</button>
            </div>
        </div>
    </div>
</div>

{{-- Find Booking Modal --}}
<div class="modal fade" id="findBookingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Find Booking</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <input type="text" class="form-control" id="booking-search" placeholder="Search by customer name, email, phone, or booking number...">
                </div>
                <div id="search-results"></div>
            </div>
        </div>
    </div>
</div>

@section('adminlte_js')
    <script>
        let currentBookingId = null;

        $(document).ready(function() {
            // Update current time every minute
            function updateTime() {
                var now = new Date();
                var timeString = now.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });
                $('#current-time').text(timeString);
            }

            updateTime();
            setInterval(updateTime, 60000);

            // Date filter change
            $('#date-filter').on('change', function() {
                const selectedDate = $(this).val();
                const url = new URL(window.location);
                url.searchParams.set('date', selectedDate);
                window.location.href = url.toString();
            });

            // Filter form auto-submit
            $('#service_id, #status').on('change', function() {
                $('#filter-form').submit();
            });

            // Search with debounce
            let searchTimeout;
            $('#search').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    $('#filter-form').submit();
                }, 500);
            });

            // Check-in button click
            $(document).on('click', '.check-in-btn', function() {
                const bookingId = $(this).data('booking-id');
                checkInCustomer(bookingId);
            });

            // Check-out button click
            $(document).on('click', '.check-out-btn', function() {
                currentBookingId = $(this).data('booking-id');
                $('#checkOutModal').modal('show');
            });

            // No show button click
            $(document).on('click', '.no-show-btn', function() {
                currentBookingId = $(this).data('booking-id');
                $('#noShowModal').modal('show');
            });

            // Confirm check-out
            $('#confirmCheckOut').on('click', function() {
                const notes = $('#checkout-notes').val();
                checkOutCustomer(currentBookingId, notes);
            });

            // Confirm no show
            $('#confirmNoShow').on('click', function() {
                const reason = $('#noshow-reason').val();
                markNoShow(currentBookingId, reason);
            });

            // Find booking
            $('#find-booking-btn').on('click', function() {
                $('#findBookingModal').modal('show');
            });

            // Booking search
            let bookingSearchTimeout;
            $('#booking-search').on('input', function() {
                const query = $(this).val();
                clearTimeout(bookingSearchTimeout);

                if (query.length >= 2) {
                    bookingSearchTimeout = setTimeout(function() {
                        searchBookings(query);
                    }, 300);
                } else {
                    $('#search-results').empty();
                }
            });

            // Refresh data
            $('#refresh-data').on('click', function() {
                refreshStats();
                location.reload();
            });

            // Auto-refresh stats every 2 minutes
            setInterval(refreshStats, 120000);
        });

        function checkInCustomer(bookingId) {
            if (!confirm('Check in this customer?')) return;

            $.post(`{{ route('owner.check-in.index') }}/${bookingId}/check-in`, {
                _token: '{{ csrf_token() }}'
            })
            .done(function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    updateBookingRow(bookingId, response.booking);
                    refreshStats();
                } else {
                    showAlert('error', response.message);
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON;
                showAlert('error', response.message || 'An error occurred');
            });
        }

        function checkOutCustomer(bookingId, notes) {
            $.post(`{{ route('owner.check-in.index') }}/${bookingId}/check-out`, {
                _token: '{{ csrf_token() }}',
                notes: notes
            })
            .done(function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    updateBookingRow(bookingId, response.booking);
                    refreshStats();
                    $('#checkOutModal').modal('hide');
                    $('#checkout-notes').val('');
                } else {
                    showAlert('error', response.message);
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON;
                showAlert('error', response.message || 'An error occurred');
            });
        }

        function markNoShow(bookingId, reason) {
            $.post(`{{ route('owner.check-in.index') }}/${bookingId}/no-show`, {
                _token: '{{ csrf_token() }}',
                reason: reason
            })
            .done(function(response) {
                if (response.success) {
                    showAlert('warning', response.message);
                    updateBookingRow(bookingId, response.booking);
                    refreshStats();
                    $('#noShowModal').modal('hide');
                    $('#noshow-reason').val('');
                } else {
                    showAlert('error', response.message);
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON;
                showAlert('error', response.message || 'An error occurred');
            });
        }

        function searchBookings(query) {
            $.get('{{ route('owner.check-in.search') }}', { q: query })
            .done(function(bookings) {
                let html = '';
                if (bookings.length > 0) {
                    html = '<div class="list-group">';
                    bookings.forEach(function(booking) {
                        const startTime = new Date(booking.start_datetime).toLocaleTimeString('en-US', {
                            hour: 'numeric',
                            minute: '2-digit',
                            hour12: true
                        });
                        const startDate = new Date(booking.start_datetime).toLocaleDateString();

                        html += `
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${booking.customer_name}</h6>
                                    <small>${startDate} ${startTime}</small>
                                </div>
                                <p class="mb-1">${booking.customer_email}</p>
                                <small>Booking #${booking.booking_number} - Status: ${booking.status}</small>
                                <div class="mt-2">
                                    <a href="/owner/bookings/${booking.id}" class="btn btn-sm btn-primary">View Details</a>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                } else {
                    html = '<div class="text-center text-muted py-3">No bookings found</div>';
                }
                $('#search-results').html(html);
            });
        }

        function updateBookingRow(bookingId, booking) {
            const row = $(`#booking-${bookingId}`);
            if (row.length) {
                // Update status column
                let statusHtml = '';
                if (booking.checked_out_at) {
                    const checkoutTime = new Date(booking.checked_out_at).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                    });
                    statusHtml = `<span class="badge badge-success">Completed</span><br><small class="text-muted">${checkoutTime}</small>`;
                } else if (booking.checked_in_at) {
                    const checkinTime = new Date(booking.checked_in_at).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                    });
                    statusHtml = `<span class="badge badge-warning">Checked In</span><br><small class="text-muted">${checkinTime}</small>`;
                } else if (booking.status === 'no_show') {
                    statusHtml = '<span class="badge badge-danger">No Show</span>';
                } else {
                    statusHtml = `<span class="badge badge-info">${booking.status}</span>`;
                }

                row.find('td:nth-child(4)').html(statusHtml);

                // Update actions column
                let actionsHtml = '<div class="btn-group-vertical" role="group">';

                if (booking.can_be_checked_in) {
                    actionsHtml += `<button class="btn btn-sm btn-success check-in-btn" data-booking-id="${booking.id}">
                        <i class="fas fa-sign-in-alt"></i> Check In
                    </button>`;
                }

                if (booking.can_be_checked_out) {
                    actionsHtml += `<button class="btn btn-sm btn-primary check-out-btn" data-booking-id="${booking.id}">
                        <i class="fas fa-sign-out-alt"></i> Check Out
                    </button>`;
                }

                if (booking.status === 'confirmed' && !booking.checked_in_at) {
                    actionsHtml += `<button class="btn btn-sm btn-warning no-show-btn" data-booking-id="${booking.id}">
                        <i class="fas fa-user-times"></i> No Show
                    </button>`;
                }

                actionsHtml += `<a href="/owner/bookings/${booking.id}" class="btn btn-sm btn-info">
                    <i class="fas fa-eye"></i> View
                </a></div>`;

                row.find('td:nth-child(5)').html(actionsHtml);
            }
        }

        function refreshStats() {
            const date = $('#date-filter').val() || '{{ $selectedDate->format('Y-m-d') }}';
            $.get('{{ route('owner.check-in.stats') }}', { date: date })
            .done(function(stats) {
                $('#stat-waiting').text(stats.waiting);
                $('#stat-checked-in').text(stats.checked_in);
                $('#stat-completed').text(stats.completed);
                $('#stat-no-show').text(stats.no_show);
            });
        }

        function showAlert(type, message) {
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' : 'alert-danger';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;

            // Remove existing alerts
            $('.alert').remove();

            // Add new alert at the top of content
            $('.content-wrapper .content').prepend(alertHtml);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }
    </script>
@stop
