<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $metaTags['title'] ?? $business->name }}</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{{ $metaTags['description'] ?? $business->description }}">
    @if(isset($metaTags['keywords']))
        <meta name="keywords" content="{{ $metaTags['keywords'] }}">
    @endif
    @if(isset($metaTags['canonical']))
        <link rel="canonical" href="{{ $metaTags['canonical'] }}">
    @endif
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ $metaTags['og:title'] ?? $metaTags['title'] }}">
    <meta property="og:description" content="{{ $metaTags['og:description'] ?? $metaTags['description'] }}">
    <meta property="og:type" content="{{ $metaTags['og:type'] ?? 'business.business' }}">
    <meta property="og:url" content="{{ $landingPage->full_url }}">
    @if(isset($metaTags['og:image']))
        <meta property="og:image" content="{{ $metaTags['og:image'] }}">
    @endif
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="{{ $metaTags['twitter:card'] ?? 'summary_large_image' }}">
    <meta name="twitter:title" content="{{ $metaTags['twitter:title'] ?? $metaTags['title'] }}">
    <meta name="twitter:description" content="{{ $metaTags['twitter:description'] ?? $metaTags['description'] }}">
    @if(isset($metaTags['twitter:image']))
        <meta name="twitter:image" content="{{ $metaTags['twitter:image'] }}">
    @endif
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Theme-specific CSS -->
    @include('landing-page.themes.' . $landingPage->theme . '.styles')
    
    <!-- Custom Branding CSS -->
    @if($landingPage->branding_config)
        <style>
            :root {
                --primary-color: {{ $landingPage->branding_config['primary_color'] ?? '#007bff' }};
                --secondary-color: {{ $landingPage->branding_config['secondary_color'] ?? '#6c757d' }};
                --accent-color: {{ $landingPage->branding_config['accent_color'] ?? '#28a745' }};
            }
        </style>
    @endif
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#home">
                @if($landingPage->logo_url)
                    <img src="{{ $landingPage->logo_url }}" alt="{{ $business->name }}" height="40">
                @else
                    <strong>{{ $business->name }}</strong>
                @endif
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                    @if($landingPage->booking_enabled)
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="#booking">
                                {{ $landingPage->booking_button_text ?? 'Book Now' }}
                            </a>
                        </li>
                    @endif
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @foreach($pageData['sections'] as $section)
            @include('landing-page.sections.' . $section['type'], [
                'section' => $section,
                'business' => $business,
                'landingPage' => $landingPage
            ])
        @endforeach
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>{{ $business->name }}</h5>
                    <p>{{ $business->description }}</p>
                </div>
                <div class="col-md-4">
                    <h5>Contact Info</h5>
                    @if($business->phone)
                        <p><i class="fas fa-phone me-2"></i> {{ $business->phone }}</p>
                    @endif
                    @if($business->email)
                        <p><i class="fas fa-envelope me-2"></i> {{ $business->email }}</p>
                    @endif
                    @if($pageData['main_branch'])
                        <p><i class="fas fa-map-marker-alt me-2"></i> {{ $pageData['main_branch']->full_address }}</p>
                    @endif
                </div>
                <div class="col-md-4">
                    <h5>Business Hours</h5>
                    @if($pageData['operating_hours']->isNotEmpty())
                        @foreach($pageData['operating_hours'] as $hour)
                            <p>
                                {{ ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][$hour->day_of_week] }}:
                                @if($hour->is_closed)
                                    Closed
                                @else
                                    {{ $hour->open_time }} - {{ $hour->close_time }}
                                @endif
                            </p>
                        @endforeach
                    @else
                        <p>Hours not specified</p>
                    @endif
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; {{ date('Y') }} {{ $business->name }}. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>Powered by <a href="https://bookkei.com" class="text-white">Bookkei</a></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Smooth Scrolling -->
    <script>
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
    
    <!-- Analytics -->
    @if($landingPage->google_analytics_id)
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ $landingPage->google_analytics_id }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '{{ $landingPage->google_analytics_id }}');
        </script>
    @endif
    
    @if($landingPage->facebook_pixel_id)
        <!-- Facebook Pixel -->
        <script>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '{{ $landingPage->facebook_pixel_id }}');
            fbq('track', 'PageView');
        </script>
        <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id={{ $landingPage->facebook_pixel_id }}&ev=PageView&noscript=1"
        /></noscript>
    @endif
    
    <!-- Schema.org Structured Data -->
    @if(isset($businessSchema))
        <script type="application/ld+json">
            {!! json_encode($businessSchema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) !!}
        </script>
    @endif
</body>
</html>
