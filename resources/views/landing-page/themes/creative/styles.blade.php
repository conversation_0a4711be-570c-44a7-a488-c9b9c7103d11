<style>
/* Creative Theme Styles */
:root {
    --primary-color: #ff6b35;
    --secondary-color: #004e89;
    --accent-color: #ffd23f;
    --text-color: #2d3748;
    --text-muted: #718096;
    --bg-light: #f7fafc;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --border-color: #e2e8f0;
    --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --gradient-primary: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    --gradient-secondary: linear-gradient(135deg, #004e89 0%, #1a365d 100%);
    --gradient-accent: linear-gradient(135deg, #ffd23f 0%, #feb47b 100%);
}

/* Global Styles */
body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: #ffffff;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
}

h1 {
    font-size: 4rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
h2 { font-size: 3.25rem; }
h3 { font-size: 2.75rem; }
h4 { font-size: 2.25rem; }
h5 { font-size: 1.875rem; }
h6 { font-size: 1.5rem; }

.lead {
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 1.7;
    color: var(--text-muted);
}

/* Navigation */
.navbar {
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    padding: 1.5rem 0;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(20px);
    border-bottom: 3px solid transparent;
    border-image: var(--gradient-primary) 1;
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(25px);
    box-shadow: var(--shadow);
    padding: 1rem 0;
    border-image: var(--gradient-accent) 1;
}

.navbar-brand {
    font-weight: 900;
    font-size: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    transform: rotate(-2deg);
}

.navbar-nav .nav-link {
    font-weight: 600;
    color: var(--text-color) !important;
    margin: 0 1rem;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.9rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    transform: translateY(-3px) rotate(1deg);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%) rotate(-2deg);
    width: 0;
    height: 3px;
    background: var(--gradient-accent);
    transition: width 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border-radius: 2px;
}

.navbar-nav .nav-link:hover::after {
    width: 120%;
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 700;
    padding: 1.25rem 3rem;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border: none;
    text-decoration: none;
    display: inline-block;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
    transform: perspective(1px) translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-5px) rotate(-1deg) scale(1.05);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #f7931e 0%, #ff6b35 100%);
}

.btn-outline-light {
    border: 3px solid rgba(255, 255, 255, 0.9);
    color: white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-5px) rotate(1deg) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.btn-lg {
    padding: 1.5rem 4rem;
    font-size: 1.1rem;
    border-radius: 35px;
}

.btn-sm {
    padding: 1rem 2.5rem;
    font-size: 0.8rem;
    border-radius: 20px;
}

/* Cards */
.card {
    border: none;
    border-radius: 25px;
    box-shadow: var(--shadow);
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    overflow: hidden;
    background: white;
    position: relative;
    transform: perspective(1px) translateZ(0);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-15px) rotate(-2deg) scale(1.03);
    box-shadow: var(--shadow-lg);
}

.card-body {
    padding: 3rem;
    position: relative;
}

.card-title {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

/* Sections */
section {
    position: relative;
    overflow: hidden;
    padding: 6rem 0;
}

.section-bg {
    background: linear-gradient(135deg, var(--bg-light) 0%, #ffffff 50%, var(--bg-light) 100%);
}

/* Hero Section Enhancements */
.hero-section {
    background: var(--bg-gradient);
    position: relative;
    min-height: 100vh;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="creative" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,1 4,7 10,13 16,7" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23creative)"/></svg>');
    animation: float 20s ease-in-out infinite;
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

/* Creative Shapes */
.creative-shape {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-accent);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.creative-shape:nth-child(1) {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.creative-shape:nth-child(2) {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.creative-shape:nth-child(3) {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
    }
    66% {
        transform: translateY(10px) rotate(240deg);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(60px) rotate(-5deg);
    }
    to {
        opacity: 1;
        transform: translateY(0) rotate(0deg);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-60px) rotate(-10deg);
    }
    to {
        opacity: 1;
        transform: translateX(0) rotate(0deg);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(60px) rotate(10deg);
    }
    to {
        opacity: 1;
        transform: translateX(0) rotate(0deg);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.animate-on-scroll {
    opacity: 0;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-on-scroll.fadeInUp {
    animation-name: fadeInUp;
}

.animate-on-scroll.fadeInLeft {
    animation-name: fadeInLeft;
}

.animate-on-scroll.fadeInRight {
    animation-name: fadeInRight;
}

.bounce {
    animation: bounce 2s infinite;
}

/* Utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-primary {
    background: var(--gradient-primary) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Creative Elements */
.creative-divider {
    text-align: center;
    margin: 4rem 0;
    position: relative;
}

.creative-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
    transform: rotate(-1deg);
}

.creative-divider span {
    background: white;
    padding: 0 2rem;
    color: var(--primary-color);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transform: rotate(2deg);
    display: inline-block;
}

/* Zigzag Border */
.zigzag-border {
    position: relative;
}

.zigzag-border::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(45deg, var(--primary-color) 25%, transparent 25%),
                linear-gradient(-45deg, var(--primary-color) 25%, transparent 25%);
    background-size: 20px 20px;
    background-position: 0 0, 10px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 { font-size: 3rem; }
    h2 { font-size: 2.5rem; }
    h3 { font-size: 2rem; }

    .btn {
        padding: 1rem 2.5rem;
        font-size: 0.8rem;
    }

    .btn-lg {
        padding: 1.25rem 3rem;
        font-size: 1rem;
    }

    .card-body {
        padding: 2.5rem;
    }

    section {
        padding: 4rem 0 !important;
    }

    .navbar {
        padding: 1.25rem 0;
    }

    .navbar-brand {
        font-size: 1.75rem;
    }

    .creative-shape {
        display: none;
    }
}

@media (max-width: 576px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }

    .btn {
        width: 100%;
        margin-bottom: 1rem;
        padding: 1rem 2rem;
    }

    .card-body {
        padding: 2rem;
    }

    .hero-section {
        padding: 3rem 0;
    }

    .navbar-nav .nav-link {
        margin: 0 0.5rem;
        font-size: 0.8rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 4px solid rgba(255, 107, 53, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

/* Creative Hover Effects */
.creative-hover {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.creative-hover:hover {
    transform: scale(1.05) rotate(-2deg);
}

/* Glitch Effect */
.glitch {
    position: relative;
    color: var(--primary-color);
    font-weight: 700;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch::before {
    animation: glitch-1 0.5s infinite;
    color: var(--secondary-color);
    z-index: -1;
}

.glitch::after {
    animation: glitch-2 0.5s infinite;
    color: var(--accent-color);
    z-index: -2;
}

@keyframes glitch-1 {
    0%, 14%, 15%, 49%, 50%, 99%, 100% {
        transform: translate(0);
    }
    15%, 49% {
        transform: translate(-2px, 2px);
    }
}

@keyframes glitch-2 {
    0%, 20%, 21%, 62%, 63%, 99%, 100% {
        transform: translate(0);
    }
    21%, 62% {
        transform: translate(2px, -2px);
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .scroll-down,
    .creative-shape {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        color: #000;
        background: none;
        -webkit-text-fill-color: initial;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .text-gradient {
        background: none;
        -webkit-text-fill-color: initial;
        color: #000;
    }
}
</style>
