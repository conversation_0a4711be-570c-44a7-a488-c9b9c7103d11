<style>
/* Minimal Theme Styles */
:root {
    --primary-color: #000000;
    --secondary-color: #333333;
    --accent-color: #666666;
    --text-color: #333333;
    --text-muted: #888888;
    --bg-light: #fafafa;
    --bg-white: #ffffff;
    --border-color: #e0e0e0;
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Global Styles */
body {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: var(--bg-white);
    font-weight: 300;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 300;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    letter-spacing: -0.01em;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

.lead {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.6;
    color: var(--text-muted);
}

/* Navigation */
.navbar {
    transition: all 0.3s ease;
    padding: 2rem 0;
    background: transparent !important;
    border-bottom: 1px solid transparent;
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
    padding: 1rem 0;
    border-bottom-color: var(--border-color);
}

.navbar-brand {
    font-weight: 400;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
    letter-spacing: 0.05em;
}

.navbar-nav .nav-link {
    font-weight: 300;
    color: var(--text-color) !important;
    margin: 0 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.95rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

/* Buttons */
.btn {
    border-radius: 0;
    font-weight: 400;
    padding: 1rem 2rem;
    transition: all 0.3s ease;
    border: 1px solid var(--primary-color);
    text-decoration: none;
    display: inline-block;
    letter-spacing: 0.05em;
    font-size: 0.9rem;
    background: transparent;
    color: var(--primary-color);
}

.btn:hover {
    background: var(--primary-color);
    color: white;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: transparent;
    color: var(--primary-color);
}

.btn-outline-light {
    border: 1px solid rgba(255, 255, 255, 0.8);
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
}

.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: 1rem;
}

.btn-sm {
    padding: 0.75rem 1.5rem;
    font-size: 0.8rem;
}

/* Cards */
.card {
    border: 1px solid var(--border-color);
    border-radius: 0;
    box-shadow: none;
    transition: all 0.3s ease;
    overflow: hidden;
    background: white;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-body {
    padding: 3rem;
}

/* Sections */
section {
    position: relative;
    overflow: hidden;
    padding: 5rem 0;
}

.section-bg {
    background: var(--bg-light);
}

/* Hero Section */
.hero-section {
    background: white;
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

/* Minimal Dividers */
.minimal-divider {
    width: 50px;
    height: 1px;
    background: var(--border-color);
    margin: 3rem auto;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    animation-duration: 0.6s;
    animation-fill-mode: forwards;
    animation-timing-function: ease-out;
}

.animate-on-scroll.fadeInUp {
    animation-name: fadeInUp;
}

.animate-on-scroll.fadeInLeft {
    animation-name: fadeInLeft;
}

.animate-on-scroll.fadeInRight {
    animation-name: fadeInRight;
}

/* Utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Minimal Grid */
.minimal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

/* Clean Lists */
.clean-list {
    list-style: none;
    padding: 0;
}

.clean-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.clean-list li:last-child {
    border-bottom: none;
}

/* Minimal Forms */
.form-control {
    border: none;
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
    padding: 1rem 0;
    background: transparent;
    font-weight: 300;
}

.form-control:focus {
    border-bottom-color: var(--primary-color);
    box-shadow: none;
    background: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.75rem; }

    .btn {
        padding: 0.875rem 1.75rem;
        font-size: 0.85rem;
    }

    .btn-lg {
        padding: 1rem 2rem;
        font-size: 0.9rem;
    }

    .card-body {
        padding: 2rem;
    }

    section {
        padding: 3rem 0 !important;
    }

    .navbar {
        padding: 1.5rem 0;
    }

    .navbar-nav .nav-link {
        margin: 0 1rem;
    }
}

@media (max-width: 576px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .hero-section {
        min-height: 60vh;
        padding: 2rem 0;
    }

    .navbar-nav .nav-link {
        margin: 0 0.5rem;
        font-size: 0.9rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.5;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Minimal Spacing */
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-5 > * + * { margin-top: 1.25rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

/* Clean Typography */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }

/* Minimal Borders */
.border-t { border-top: 1px solid var(--border-color); }
.border-b { border-bottom: 1px solid var(--border-color); }
.border-l { border-left: 1px solid var(--border-color); }
.border-r { border-right: 1px solid var(--border-color); }

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .scroll-down {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        color: #000;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #ffffff;
        --secondary-color: #cccccc;
        --accent-color: #999999;
        --text-color: #cccccc;
        --text-muted: #777777;
        --bg-light: #111111;
        --bg-white: #000000;
        --border-color: #333333;
    }

    body {
        background: var(--bg-white);
        color: var(--text-color);
    }

    .card {
        background: #111111;
        border-color: var(--border-color);
    }

    .hero-section {
        background: var(--bg-white);
    }

    .section-bg {
        background: var(--bg-light);
    }
}
</style>
