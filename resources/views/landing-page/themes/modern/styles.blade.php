<style>
/* Modern Theme Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e293b;
    --accent-color: #06b6d4;
    --text-color: #1e293b;
    --text-muted: #64748b;
    --bg-light: #f8fafc;
    --bg-dark: #0f172a;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

/* Global Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: #ffffff;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.75rem; }
h3 { font-size: 2.25rem; }
h4 { font-size: 1.75rem; }
h5 { font-size: 1.5rem; }
h6 { font-size: 1.25rem; }

.lead {
    font-size: 1.375rem;
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-muted);
}

/* Navigation */
.navbar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 1.25rem 0;
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-weight: 900;
    font-size: 1.75rem;
    color: var(--primary-color) !important;
    letter-spacing: -0.025em;
}

.navbar-nav .nav-link {
    font-weight: 600;
    color: var(--text-color) !important;
    margin: 0 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    transform: translateY(-1px);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

/* Buttons */
.btn {
    border-radius: 12px;
    font-weight: 700;
    padding: 1rem 2.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    text-decoration: none;
    display: inline-block;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-lg {
    padding: 1.25rem 3rem;
    font-size: 1.125rem;
    border-radius: 16px;
}

.btn-sm {
    padding: 0.75rem 2rem;
    font-size: 0.875rem;
    border-radius: 10px;
}

/* Cards */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: white;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.card-body {
    padding: 2.5rem;
}

/* Sections */
section {
    position: relative;
    overflow: hidden;
}

.section-bg {
    background: linear-gradient(135deg, var(--bg-light) 0%, #ffffff 100%);
}

/* Hero Section Enhancements */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-on-scroll {
    opacity: 0;
    animation-duration: 0.8s;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.fadeInUp {
    animation-name: fadeInUp;
}

.animate-on-scroll.fadeInLeft {
    animation-name: fadeInLeft;
}

.animate-on-scroll.fadeInRight {
    animation-name: fadeInRight;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: var(--gradient-primary) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Glass Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 { font-size: 2.75rem; }
    h2 { font-size: 2.25rem; }
    h3 { font-size: 1.875rem; }

    .btn {
        padding: 0.875rem 2rem;
        font-size: 0.875rem;
    }

    .btn-lg {
        padding: 1rem 2.5rem;
        font-size: 1rem;
    }

    .card-body {
        padding: 2rem;
    }

    section {
        padding: 4rem 0 !important;
    }

    .navbar {
        padding: 1rem 0;
    }
}

@media (max-width: 576px) {
    h1 { font-size: 2.25rem; }
    h2 { font-size: 1.875rem; }

    .btn {
        width: 100%;
        margin-bottom: 0.75rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .hero-section {
        padding: 2rem 0;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid rgba(37, 99, 235, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #f1f5f9;
        --text-muted: #94a3b8;
        --bg-light: #1e293b;
        --border-color: #334155;
    }

    body {
        background: var(--bg-dark);
        color: var(--text-color);
    }

    .card {
        background: var(--secondary-color);
        border: 1px solid var(--border-color);
    }

    .navbar {
        background: rgba(15, 23, 42, 0.8) !important;
        border-bottom-color: var(--border-color);
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .scroll-down {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        color: #000;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
</style>