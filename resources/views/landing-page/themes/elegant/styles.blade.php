<style>
/* Elegant Theme Styles */
:root {
    --primary-color: #8b5a3c;
    --secondary-color: #2c1810;
    --accent-color: #d4af37;
    --text-color: #2c1810;
    --text-muted: #6b5b4f;
    --bg-light: #faf9f7;
    --bg-cream: #f5f3f0;
    --border-color: #e8e3dd;
    --shadow: 0 8px 25px rgba(44, 24, 16, 0.08);
    --shadow-lg: 0 25px 50px rgba(44, 24, 16, 0.15);
    --gradient-primary: linear-gradient(135deg, #8b5a3c 0%, #6d4428 100%);
    --gradient-accent: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
}

/* Global Styles */
body {
    font-family: 'Playfair Display', 'Georgia', serif;
    line-height: 1.7;
    color: var(--text-color);
    background: #ffffff;
    font-weight: 400;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
}

h1 { font-size: 3.75rem; }
h2 { font-size: 3rem; }
h3 { font-size: 2.5rem; }
h4 { font-size: 2rem; }
h5 { font-size: 1.75rem; }
h6 { font-size: 1.5rem; }

.lead {
    font-family: 'Lora', 'Georgia', serif;
    font-size: 1.5rem;
    font-weight: 300;
    line-height: 1.8;
    color: var(--text-muted);
    font-style: italic;
}

p {
    font-family: 'Lora', 'Georgia', serif;
    font-size: 1.125rem;
    line-height: 1.8;
}

/* Navigation */
.navbar {
    transition: all 0.4s ease;
    padding: 1.5rem 0;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(15px);
    border-bottom: 1px solid var(--border-color);
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow);
    padding: 1rem 0;
}

.navbar-brand {
    font-family: 'Playfair Display', serif;
    font-weight: 900;
    font-size: 2rem;
    color: var(--primary-color) !important;
    letter-spacing: -0.02em;
}

.navbar-nav .nav-link {
    font-family: 'Lora', serif;
    font-weight: 500;
    color: var(--text-color) !important;
    margin: 0 1rem;
    transition: all 0.4s ease;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-size: 0.9rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: var(--gradient-accent);
    transition: width 0.4s ease;
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

/* Buttons */
.btn {
    border-radius: 0;
    font-family: 'Lora', serif;
    font-weight: 600;
    padding: 1.25rem 3rem;
    transition: all 0.4s ease;
    border: 2px solid transparent;
    text-decoration: none;
    display: inline-block;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.9);
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-lg {
    padding: 1.5rem 3.5rem;
    font-size: 1rem;
}

.btn-sm {
    padding: 1rem 2.5rem;
    font-size: 0.8rem;
}

/* Cards */
.card {
    border: 1px solid var(--border-color);
    border-radius: 0;
    box-shadow: var(--shadow);
    transition: all 0.5s ease;
    overflow: hidden;
    background: white;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-color);
}

.card-body {
    padding: 3rem;
}

.card-title {
    font-family: 'Playfair Display', serif;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

/* Sections */
section {
    position: relative;
    overflow: hidden;
    padding: 6rem 0;
}

.section-bg {
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-cream) 100%);
}

/* Hero Section Enhancements */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    min-height: 100vh;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="damask" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23damask)"/></svg>');
    opacity: 0.4;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

/* Decorative Elements */
.decorative-line {
    width: 100px;
    height: 2px;
    background: var(--gradient-accent);
    margin: 2rem auto;
    position: relative;
}

.decorative-line::before,
.decorative-line::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 50%;
}

.decorative-line::before {
    left: -15px;
}

.decorative-line::after {
    right: -15px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes elegantFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-on-scroll {
    opacity: 0;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    animation-timing-function: ease-out;
}

.animate-on-scroll.fadeInUp {
    animation-name: fadeInUp;
}

.animate-on-scroll.fadeInLeft {
    animation-name: fadeInLeft;
}

.animate-on-scroll.fadeInRight {
    animation-name: fadeInRight;
}

.elegant-float {
    animation: elegantFloat 3s ease-in-out infinite;
}

/* Utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.text-accent {
    color: var(--accent-color) !important;
}

.bg-primary {
    background: var(--gradient-primary) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.text-gradient {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Elegant Dividers */
.elegant-divider {
    text-align: center;
    margin: 4rem 0;
    position: relative;
}

.elegant-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--border-color), transparent);
}

.elegant-divider span {
    background: white;
    padding: 0 2rem;
    color: var(--accent-color);
    font-family: 'Playfair Display', serif;
    font-style: italic;
}

/* Quote Styles */
blockquote {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-style: italic;
    color: var(--primary-color);
    border-left: 4px solid var(--accent-color);
    padding-left: 2rem;
    margin: 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 { font-size: 3rem; }
    h2 { font-size: 2.5rem; }
    h3 { font-size: 2rem; }

    .btn {
        padding: 1rem 2.5rem;
        font-size: 0.8rem;
    }

    .btn-lg {
        padding: 1.25rem 3rem;
        font-size: 0.9rem;
    }

    .card-body {
        padding: 2.5rem;
    }

    section {
        padding: 4rem 0 !important;
    }

    .navbar {
        padding: 1.25rem 0;
    }

    .lead {
        font-size: 1.25rem;
    }

    p {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }

    .btn {
        width: 100%;
        margin-bottom: 1rem;
        padding: 1rem 2rem;
    }

    .card-body {
        padding: 2rem;
    }

    .hero-section {
        padding: 3rem 0;
    }

    .navbar-nav .nav-link {
        margin: 0 0.5rem;
        font-size: 0.8rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 28px;
    height: 28px;
    border: 3px solid rgba(139, 90, 60, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1.2s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

/* Luxury Details */
.luxury-border {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-color), var(--accent-color)) 1;
}

.gold-accent {
    color: var(--accent-color);
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .scroll-down {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.6;
        color: #000;
        background: #fff;
        font-family: 'Times New Roman', serif;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        color: #000;
        font-family: 'Times New Roman', serif;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    blockquote {
        border-left: 4px solid #ccc;
    }
}
</style>
