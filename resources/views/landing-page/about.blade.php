<!DOCTYPE html>
<html lang="{{ $business->language ?? 'en' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About {{ $business->name }}</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Learn more about {{ $business->name }}. {{ $business->description }}">
    <meta name="keywords" content="about, {{ $business->name }}, business information">
    <link rel="canonical" href="{{ url()->current() }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Theme Styles -->
    @include('landing-page.themes.' . ($landingPage->theme ?? 'default') . '.styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('landing-page.index', $business->landing_page_slug) }}">
                @if($landingPage->logo_url)
                    <img src="{{ $landingPage->logo_url }}" alt="{{ $business->name }}" height="40">
                @else
                    <strong>{{ $business->name }}</strong>
                @endif
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing-page.index', $business->landing_page_slug) }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('landing-page.about', $business->landing_page_slug) }}">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing-page.services', $business->landing_page_slug) }}">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing-page.contact', $business->landing_page_slug) }}">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-primary text-white px-3 ms-2" href="{{ route('landing-page.booking', $business->landing_page_slug) }}">Book Now</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- About Content -->
    <main style="margin-top: 80px;">
        <!-- Hero Section -->
        <section class="py-5 bg-primary text-white">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="display-4 mb-3">About {{ $business->name }}</h1>
                        <p class="lead">{{ $business->description }}</p>
                    </div>
                    <div class="col-lg-4 text-center">
                        @if($landingPage->logo_url)
                            <img src="{{ $landingPage->logo_url }}" alt="{{ $business->name }}" class="img-fluid" style="max-height: 150px;">
                        @endif
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section Content -->
        @if($aboutSection && $aboutSection->content_data)
            @php $content = $aboutSection->content_data; @endphp
            <section class="py-5">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8">
                            <h2 class="mb-4">{{ $content['title'] ?? 'Our Story' }}</h2>
                            <div class="about-content">
                                {!! nl2br(e($content['content'] ?? $business->description)) !!}
                            </div>
                            
                            @if(isset($content['features']) && is_array($content['features']))
                                <div class="features mt-5">
                                    <h3>What Makes Us Special</h3>
                                    <div class="row">
                                        @foreach($content['features'] as $feature)
                                            <div class="col-md-6 mb-3">
                                                <div class="feature-item">
                                                    <i class="fas fa-check-circle text-primary me-2"></i>
                                                    {{ $feature }}
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                        
                        <div class="col-lg-4">
                            @if(isset($content['image']) && $content['image'])
                                <img src="{{ $content['image'] }}" alt="About {{ $business->name }}" class="img-fluid rounded mb-4">
                            @endif
                            
                            <!-- Business Stats -->
                            @if(isset($content['stats']) && is_array($content['stats']))
                                <div class="stats-card">
                                    <h4>Our Numbers</h4>
                                    @foreach($content['stats'] as $stat)
                                        <div class="stat-item">
                                            <div class="stat-value">{{ $stat['value'] ?? 'N/A' }}</div>
                                            <div class="stat-label">{{ $stat['label'] ?? '' }}</div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                            
                            <!-- Contact Info -->
                            <div class="contact-info-card">
                                <h4>Get In Touch</h4>
                                @if($business->phone)
                                    <p><i class="fas fa-phone text-primary me-2"></i>{{ $business->phone }}</p>
                                @endif
                                @if($business->email)
                                    <p><i class="fas fa-envelope text-primary me-2"></i>{{ $business->email }}</p>
                                @endif
                                @if($business->website)
                                    <p><i class="fas fa-globe text-primary me-2"></i><a href="{{ $business->website }}" target="_blank">{{ $business->website }}</a></p>
                                @endif
                                
                                <a href="{{ route('landing-page.contact', $business->landing_page_slug) }}" class="btn btn-primary mt-3">
                                    Contact Us
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        @else
            <!-- Default About Content -->
            <section class="py-5">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 mx-auto text-center">
                            <h2 class="mb-4">Welcome to {{ $business->name }}</h2>
                            <p class="lead">{{ $business->description }}</p>
                            
                            <div class="mt-5">
                                <a href="{{ route('landing-page.services', $business->landing_page_slug) }}" class="btn btn-primary btn-lg me-3">
                                    View Our Services
                                </a>
                                <a href="{{ route('landing-page.contact', $business->landing_page_slug) }}" class="btn btn-outline-primary btn-lg">
                                    Contact Us
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <!-- Business Hours -->
        @if($business->operatingHours && $business->operatingHours->count())
            <section class="py-5 bg-light">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-6 mx-auto">
                            <h3 class="text-center mb-4">Business Hours</h3>
                            <div class="hours-table">
                                @foreach($business->operatingHours->sortBy('day_of_week') as $hours)
                                    <div class="hours-row">
                                        <div class="day">{{ ucfirst($hours->day_name) }}</div>
                                        <div class="time">
                                            @if($hours->is_closed)
                                                <span class="text-muted">Closed</span>
                                            @else
                                                {{ $hours->open_time }} - {{ $hours->close_time }}
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <!-- Call to Action -->
        <section class="py-5 bg-primary text-white">
            <div class="container text-center">
                <h3>Ready to Get Started?</h3>
                <p class="lead">Book an appointment with us today and experience our professional service.</p>
                <a href="{{ route('landing-page.booking', $business->landing_page_slug) }}" class="btn btn-light btn-lg">
                    <i class="fas fa-calendar-plus me-2"></i>Book Now
                </a>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>{{ $business->name }}</h5>
                    <p>{{ $business->description }}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    @if($business->phone)
                        <p><i class="fas fa-phone me-2"></i>{{ $business->phone }}</p>
                    @endif
                    @if($business->email)
                        <p><i class="fas fa-envelope me-2"></i>{{ $business->email }}</p>
                    @endif
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; {{ date('Y') }} {{ $business->name }}. All rights reserved. Powered by <a href="https://bookkei.com" class="text-light">Bookkei</a></p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
    .about-content {
        font-size: 1.1rem;
        line-height: 1.7;
        color: #555;
    }
    
    .feature-item {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }
    
    .stats-card, .contact-info-card {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .stat-item {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--bs-primary);
    }
    
    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }
    
    .hours-table {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .hours-row {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    
    .hours-row:last-child {
        border-bottom: none;
    }
    
    .day {
        font-weight: 600;
    }
    
    .time {
        color: #666;
    }
    </style>
</body>
</html>
