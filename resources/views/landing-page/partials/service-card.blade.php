@php
    $cardClass = 'col-lg-' . (12 / ($settings->grid_columns ?? 3)) . ' col-md-6 mb-4';
    if (isset($masonry) && $masonry) {
        $cardClass = '';
    }
@endphp

<div class="service-card-wrapper {{ $cardClass }}" 
     data-category="{{ $service->category->slug ?? 'uncategorized' }}"
     data-service-id="{{ $service->id }}">
    <div class="service-card h-100 {{ $settings->service_card_style ?? 'modern' }}">
        
        <!-- Service Image -->
        @if($settings->show_images && $service->images && $service->images->where('is_primary', true)->first())
            <div class="service-image">
                <img src="{{ $service->images->where('is_primary', true)->first()->image_url }}" 
                     alt="{{ $service->name }}" class="card-img-top"
                     loading="{{ $settings->mobile_optimized ? 'lazy' : 'eager' }}">
                
                @if($settings->show_special_offers && $service->hasSpecialOffer())
                    <div class="service-badge">
                        <span class="badge badge-danger">Special Offer</span>
                    </div>
                @endif
                
                @if($service->featured_on_landing)
                    <div class="featured-badge">
                        <span class="badge badge-warning">Featured</span>
                    </div>
                @endif
            </div>
        @else
            <div class="service-image-placeholder">
                @if($settings->show_service_icons && $service->category && $service->category->icon)
                    <i class="{{ $service->category->icon }} fa-3x text-muted"></i>
                @else
                    <i class="fas fa-concierge-bell fa-3x text-muted"></i>
                @endif
            </div>
        @endif

        <div class="card-body">
            <!-- Service Category -->
            @if($settings->show_categories && $service->category)
                <div class="service-category mb-2">
                    <span class="badge badge-secondary">{{ $service->category->name }}</span>
                </div>
            @endif

            <!-- Service Name -->
            <h5 class="service-name card-title">{{ $service->name }}</h5>

            <!-- Service Description -->
            @if($settings->show_description && $service->short_description)
                <p class="service-description card-text text-muted">
                    {{ Str::limit($service->short_description, 100) }}
                </p>
            @endif

            <!-- Service Details Row -->
            <div class="service-details">
                <div class="row">
                    <!-- Duration -->
                    @if($settings->show_duration && $service->duration_minutes)
                        <div class="col-6">
                            <small class="text-muted">
                                <i class="fas fa-clock mr-1"></i>
                                {{ $service->duration_minutes }} min
                            </small>
                        </div>
                    @endif

                    <!-- Price -->
                    @if($settings->show_pricing && $service->base_price)
                        <div class="col-6 text-right">
                            <span class="service-price font-weight-bold text-primary">
                                {{ $business->currency ?? '$' }}{{ number_format($service->base_price, 2) }}
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Availability Status -->
            @if($settings->show_availability_status)
                <div class="availability-status mt-2">
                    @php
                        $isAvailable = $service->isAvailableToday();
                    @endphp
                    <small class="availability-indicator {{ $isAvailable ? 'text-success' : 'text-warning' }}">
                        <i class="fas fa-circle mr-1"></i>
                        {{ $isAvailable ? 'Available Today' : 'Limited Availability' }}
                    </small>
                </div>
            @endif

            <!-- Reviews and Rating -->
            @if($settings->show_reviews_rating && $service->reviews_count > 0)
                <div class="service-rating mt-2">
                    <div class="rating-stars">
                        @for($i = 1; $i <= 5; $i++)
                            <i class="fas fa-star {{ $i <= $service->average_rating ? 'text-warning' : 'text-muted' }}"></i>
                        @endfor
                        <small class="text-muted ml-1">
                            ({{ $service->reviews_count }} {{ Str::plural('review', $service->reviews_count) }})
                        </small>
                    </div>
                </div>
            @endif

            <!-- Quick Booking Button -->
            @if($settings->enable_quick_booking && $landingPage->booking_enabled && $service->online_booking_enabled)
                <div class="service-actions mt-3">
                    @if($settings->show_booking_calendar)
                        <button class="btn btn-{{ $settings->booking_button_style ?? 'primary' }} btn-sm btn-block" 
                                data-toggle="modal" data-target="#bookingModal" 
                                data-service-id="{{ $service->id }}"
                                data-service-name="{{ $service->name }}">
                            <i class="fas fa-calendar-plus mr-1"></i>
                            {{ $settings->booking_button_text ?? 'Book Now' }}
                        </button>
                    @else
                        <a href="{{ route('landing-page.booking', $landingPage->custom_slug) }}?service={{ $service->id }}" 
                           class="btn btn-{{ $settings->booking_button_style ?? 'primary' }} btn-sm btn-block">
                            <i class="fas fa-calendar-plus mr-1"></i>
                            {{ $settings->booking_button_text ?? 'Book Now' }}
                        </a>
                    @endif
                </div>
            @endif

            <!-- Service Details Link -->
            @if($settings->enable_service_seo)
                <div class="service-link mt-2">
                    <a href="{{ route('landing-page.service', [$landingPage->custom_slug, $service->slug]) }}" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-info-circle mr-1"></i>
                        Learn More
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.service-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
    position: relative;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.service-card.modern {
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.service-card.classic {
    border-radius: 5px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.service-card.minimal {
    border-radius: 0;
    box-shadow: none;
    border-bottom: 3px solid #007bff;
}

.service-card.creative {
    border-radius: 20px 5px 20px 5px;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.service-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-image-placeholder {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.service-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

.featured-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
}

.service-category .badge {
    font-size: 0.75em;
}

.service-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.service-description {
    font-size: 0.9rem;
    line-height: 1.4;
}

.service-details {
    border-top: 1px solid #f0f0f0;
    padding-top: 10px;
    margin-top: 10px;
}

.service-price {
    font-size: 1.1rem;
}

.availability-indicator {
    font-size: 0.8rem;
}

.rating-stars {
    font-size: 0.9rem;
}

.service-actions .btn {
    font-size: 0.9rem;
    padding: 8px 16px;
}

.service-link .btn {
    font-size: 0.8rem;
    padding: 5px 10px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .service-image {
        height: 150px;
    }
    
    .service-card {
        margin-bottom: 20px;
    }
    
    .service-details .col-6 {
        text-align: center !important;
        margin-bottom: 5px;
    }
}

/* Animation for loading */
.service-card-wrapper {
    opacity: 0;
    animation: fadeInUp 0.6s ease forwards;
}

.service-card-wrapper:nth-child(1) { animation-delay: 0.1s; }
.service-card-wrapper:nth-child(2) { animation-delay: 0.2s; }
.service-card-wrapper:nth-child(3) { animation-delay: 0.3s; }
.service-card-wrapper:nth-child(4) { animation-delay: 0.4s; }
.service-card-wrapper:nth-child(5) { animation-delay: 0.5s; }
.service-card-wrapper:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
