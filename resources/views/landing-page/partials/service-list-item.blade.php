<div class="service-item" 
     data-category="{{ $service->category->slug ?? 'uncategorized' }}"
     data-service-id="{{ $service->id }}">
    <div class="row align-items-center">
        
        <!-- Service Image -->
        @if($settings->show_images)
            <div class="col-md-3">
                @if($service->images && $service->images->where('is_primary', true)->first())
                    <div class="service-image-list">
                        <img src="{{ $service->images->where('is_primary', true)->first()->image_url }}" 
                             alt="{{ $service->name }}" class="img-fluid rounded"
                             loading="{{ $settings->mobile_optimized ? 'lazy' : 'eager' }}">
                        
                        @if($settings->show_special_offers && $service->hasSpecialOffer())
                            <div class="service-badge">
                                <span class="badge badge-danger">Special Offer</span>
                            </div>
                        @endif
                    </div>
                @else
                    <div class="service-image-placeholder-list">
                        @if($settings->show_service_icons && $service->category && $service->category->icon)
                            <i class="{{ $service->category->icon }} fa-2x text-muted"></i>
                        @else
                            <i class="fas fa-concierge-bell fa-2x text-muted"></i>
                        @endif
                    </div>
                @endif
            </div>
        @endif

        <!-- Service Content -->
        <div class="col-md-{{ $settings->show_images ? '6' : '8' }}">
            <div class="service-content">
                
                <!-- Service Category -->
                @if($settings->show_categories && $service->category)
                    <div class="service-category mb-1">
                        <span class="badge badge-secondary">{{ $service->category->name }}</span>
                        @if($service->featured_on_landing)
                            <span class="badge badge-warning ml-1">Featured</span>
                        @endif
                    </div>
                @endif

                <!-- Service Name -->
                <h5 class="service-name mb-2">{{ $service->name }}</h5>

                <!-- Service Description -->
                @if($settings->show_description && $service->description)
                    <p class="service-description text-muted mb-2">
                        {{ Str::limit($service->description, 150) }}
                    </p>
                @endif

                <!-- Service Details -->
                <div class="service-meta">
                    <div class="row">
                        <div class="col-sm-6">
                            <!-- Duration -->
                            @if($settings->show_duration && $service->duration_minutes)
                                <small class="text-muted d-block">
                                    <i class="fas fa-clock mr-1"></i>
                                    Duration: {{ $service->duration_minutes }} minutes
                                </small>
                            @endif

                            <!-- Availability -->
                            @if($settings->show_availability_status)
                                @php
                                    $isAvailable = $service->isAvailableToday();
                                @endphp
                                <small class="availability-indicator d-block {{ $isAvailable ? 'text-success' : 'text-warning' }}">
                                    <i class="fas fa-circle mr-1"></i>
                                    {{ $isAvailable ? 'Available Today' : 'Limited Availability' }}
                                </small>
                            @endif
                        </div>
                        
                        <div class="col-sm-6">
                            <!-- Reviews and Rating -->
                            @if($settings->show_reviews_rating && $service->reviews_count > 0)
                                <div class="service-rating">
                                    <div class="rating-stars">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star {{ $i <= $service->average_rating ? 'text-warning' : 'text-muted' }}"></i>
                                        @endfor
                                        <small class="text-muted ml-1">
                                            ({{ $service->reviews_count }})
                                        </small>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Actions -->
        <div class="col-md-{{ $settings->show_images ? '3' : '4' }}">
            <div class="service-actions text-right">
                
                <!-- Price -->
                @if($settings->show_pricing && $service->base_price)
                    <div class="service-price mb-2">
                        <span class="h5 text-primary font-weight-bold">
                            {{ $business->currency ?? '$' }}{{ number_format($service->base_price, 2) }}
                        </span>
                        @if($service->deposit_required && $service->deposit_amount)
                            <small class="text-muted d-block">
                                Deposit: {{ $business->currency ?? '$' }}{{ number_format($service->deposit_amount, 2) }}
                            </small>
                        @endif
                    </div>
                @endif

                <!-- Action Buttons -->
                <div class="service-buttons">
                    @if($settings->enable_quick_booking && $landingPage->booking_enabled && $service->online_booking_enabled)
                        @if($settings->show_booking_calendar)
                            <button class="btn btn-{{ $settings->booking_button_style ?? 'primary' }} btn-sm mb-2" 
                                    data-toggle="modal" data-target="#bookingModal" 
                                    data-service-id="{{ $service->id }}"
                                    data-service-name="{{ $service->name }}">
                                <i class="fas fa-calendar-plus mr-1"></i>
                                {{ $settings->booking_button_text ?? 'Book Now' }}
                            </button>
                        @else
                            <a href="{{ route('landing-page.booking', $landingPage->custom_slug) }}?service={{ $service->id }}" 
                               class="btn btn-{{ $settings->booking_button_style ?? 'primary' }} btn-sm mb-2">
                                <i class="fas fa-calendar-plus mr-1"></i>
                                {{ $settings->booking_button_text ?? 'Book Now' }}
                            </a>
                        @endif
                    @endif

                    @if($settings->enable_service_seo)
                        <a href="{{ route('landing-page.service', [$landingPage->custom_slug, $service->slug]) }}" 
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-info-circle mr-1"></i>
                            Details
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.service-item {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.service-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    border-left-color: #007bff;
}

.service-image-list {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    height: 120px;
}

.service-image-list img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-item:hover .service-image-list img {
    transform: scale(1.05);
}

.service-image-placeholder-list {
    height: 120px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 2;
}

.service-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.service-description {
    font-size: 0.95rem;
    line-height: 1.5;
}

.service-meta {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

.service-meta small {
    margin-bottom: 3px;
}

.availability-indicator {
    font-size: 0.85rem;
    font-weight: 500;
}

.rating-stars {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.service-price {
    text-align: right;
}

.service-price .h5 {
    margin-bottom: 2px;
}

.service-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.service-buttons .btn {
    font-size: 0.9rem;
    padding: 8px 16px;
    white-space: nowrap;
}

.service-category .badge {
    font-size: 0.75em;
    padding: 4px 8px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .service-item {
        padding: 20px 15px;
    }
    
    .service-image-list {
        height: 100px;
        margin-bottom: 15px;
    }
    
    .service-actions {
        text-align: center !important;
        margin-top: 15px;
    }
    
    .service-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }
    
    .service-buttons .btn {
        flex: 1;
        max-width: 120px;
    }
    
    .service-price {
        text-align: center;
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .service-meta .row {
        text-align: center;
    }
    
    .service-meta .col-sm-6 {
        margin-bottom: 10px;
    }
}

/* Animation */
.service-item {
    opacity: 0;
    animation: slideInLeft 0.6s ease forwards;
}

.service-item:nth-child(1) { animation-delay: 0.1s; }
.service-item:nth-child(2) { animation-delay: 0.2s; }
.service-item:nth-child(3) { animation-delay: 0.3s; }
.service-item:nth-child(4) { animation-delay: 0.4s; }
.service-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
</style>
