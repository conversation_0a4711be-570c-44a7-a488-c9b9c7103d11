<!DOCTYPE html>
<html lang="{{ $business->language ?? 'en' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $service->name }} - {{ $business->name }}</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{{ $service->description ?? 'Learn more about ' . $service->name . ' at ' . $business->name }}">
    <meta name="keywords" content="{{ $service->name }}, {{ $business->name }}, {{ $service->category->name ?? '' }}">
    <link rel="canonical" href="{{ url()->current() }}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ $service->name }} - {{ $business->name }}">
    <meta property="og:description" content="{{ $service->description }}">
    <meta property="og:type" content="service">
    <meta property="og:url" content="{{ url()->current() }}">
    @if($service->images && $service->images->count())
        <meta property="og:image" content="{{ $service->images->first()->image_url }}">
    @endif
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Theme Styles -->
    @include('landing-page.themes.' . ($landingPage->theme ?? 'default') . '.styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('landing-page.index', $business->landing_page_slug) }}">
                @if($landingPage->logo_url)
                    <img src="{{ $landingPage->logo_url }}" alt="{{ $business->name }}" height="40">
                @else
                    <strong>{{ $business->name }}</strong>
                @endif
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing-page.index', $business->landing_page_slug) }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing-page.services', $business->landing_page_slug) }}">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing-page.booking', $business->landing_page_slug) }}">Book Now</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Service Detail Content -->
    <main style="margin-top: 80px;">
        <!-- Breadcrumb -->
        <section class="py-3 bg-light">
            <div class="container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{{ route('landing-page.index', $business->landing_page_slug) }}">Home</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('landing-page.services', $business->landing_page_slug) }}">Services</a>
                        </li>
                        <li class="breadcrumb-item active">{{ $service->name }}</li>
                    </ol>
                </nav>
            </div>
        </section>

        <!-- Service Hero -->
        <section class="py-5">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 mb-3">{{ $service->name }}</h1>
                        @if($service->category)
                            <span class="badge bg-primary mb-3">{{ $service->category->name }}</span>
                        @endif
                        <p class="lead">{{ $service->description }}</p>
                        
                        <div class="service-details mb-4">
                            @if($service->duration)
                                <div class="detail-item mb-2">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <strong>Duration:</strong> {{ $service->duration }} minutes
                                </div>
                            @endif
                            @if($service->base_price)
                                <div class="detail-item mb-2">
                                    <i class="fas fa-tag text-primary me-2"></i>
                                    <strong>Price:</strong> {{ $business->currency ?? '$' }}{{ number_format($service->base_price, 2) }}
                                </div>
                            @endif
                            @if($service->staff_required)
                                <div class="detail-item mb-2">
                                    <i class="fas fa-user text-primary me-2"></i>
                                    <strong>Staff Required:</strong> {{ $service->staff_required }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{{ route('landing-page.book-service', [$business->landing_page_slug, $service->id]) }}" 
                               class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-calendar-plus me-2"></i>Book This Service
                            </a>
                            <a href="{{ route('landing-page.services', $business->landing_page_slug) }}" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Services
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        @if($service->images && $service->images->count())
                            <div class="service-gallery">
                                <div id="serviceCarousel" class="carousel slide" data-bs-ride="carousel">
                                    <div class="carousel-inner">
                                        @foreach($service->images as $index => $image)
                                            <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                                <img src="{{ $image->image_url }}" 
                                                     alt="{{ $image->alt_text ?? $service->name }}" 
                                                     class="d-block w-100 rounded">
                                            </div>
                                        @endforeach
                                    </div>
                                    @if($service->images->count() > 1)
                                        <button class="carousel-control-prev" type="button" data-bs-target="#serviceCarousel" data-bs-slide="prev">
                                            <span class="carousel-control-prev-icon"></span>
                                        </button>
                                        <button class="carousel-control-next" type="button" data-bs-target="#serviceCarousel" data-bs-slide="next">
                                            <span class="carousel-control-next-icon"></span>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        @else
                            <div class="service-placeholder">
                                <div class="placeholder-content text-center p-5 bg-light rounded">
                                    <i class="fas fa-image fa-4x text-muted mb-3"></i>
                                    <h5 class="text-muted">Service Image Coming Soon</h5>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </section>

        <!-- Service Details -->
        @if($service->long_description || $service->preparation_instructions || $service->aftercare_instructions)
            <section class="py-5 bg-light">
                <div class="container">
                    <div class="row">
                        @if($service->long_description)
                            <div class="col-lg-8 mb-4">
                                <h3>About This Service</h3>
                                <div class="service-content">
                                    {!! nl2br(e($service->long_description)) !!}
                                </div>
                            </div>
                        @endif
                        
                        <div class="col-lg-4">
                            @if($service->preparation_instructions)
                                <div class="info-card mb-4">
                                    <h5><i class="fas fa-list-check text-primary me-2"></i>Preparation</h5>
                                    <p>{{ $service->preparation_instructions }}</p>
                                </div>
                            @endif
                            
                            @if($service->aftercare_instructions)
                                <div class="info-card mb-4">
                                    <h5><i class="fas fa-heart text-primary me-2"></i>Aftercare</h5>
                                    <p>{{ $service->aftercare_instructions }}</p>
                                </div>
                            @endif
                            
                            @if($service->cancellation_policy)
                                <div class="info-card mb-4">
                                    <h5><i class="fas fa-info-circle text-primary me-2"></i>Cancellation Policy</h5>
                                    <p>{{ $service->cancellation_policy }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <!-- Related Services -->
        @if($relatedServices && $relatedServices->count())
            <section class="py-5">
                <div class="container">
                    <h3 class="text-center mb-5">Related Services</h3>
                    <div class="row">
                        @foreach($relatedServices as $relatedService)
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card h-100 service-card">
                                    @if($relatedService->images && $relatedService->images->count())
                                        <img src="{{ $relatedService->images->first()->image_url }}" 
                                             class="card-img-top" 
                                             alt="{{ $relatedService->name }}"
                                             style="height: 200px; object-fit: cover;">
                                    @endif
                                    <div class="card-body">
                                        <h5 class="card-title">{{ $relatedService->name }}</h5>
                                        <p class="card-text">{{ Str::limit($relatedService->description, 100) }}</p>
                                        <div class="service-meta">
                                            @if($relatedService->duration)
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>{{ $relatedService->duration }} min
                                                </small>
                                            @endif
                                            @if($relatedService->base_price)
                                                <small class="text-muted ms-3">
                                                    <i class="fas fa-tag me-1"></i>{{ $business->currency ?? '$' }}{{ number_format($relatedService->base_price, 2) }}
                                                </small>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <a href="{{ route('landing-page.service', [$business->landing_page_slug, $relatedService->slug]) }}" 
                                           class="btn btn-outline-primary btn-sm me-2">Learn More</a>
                                        <a href="{{ route('landing-page.book-service', [$business->landing_page_slug, $relatedService->id]) }}" 
                                           class="btn btn-primary btn-sm">Book Now</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </section>
        @endif

        <!-- Call to Action -->
        <section class="py-5 bg-primary text-white">
            <div class="container text-center">
                <h3>Ready to Book {{ $service->name }}?</h3>
                <p class="lead">Schedule your appointment today and experience our professional service.</p>
                <a href="{{ route('landing-page.book-service', [$business->landing_page_slug, $service->id]) }}" 
                   class="btn btn-light btn-lg">
                    <i class="fas fa-calendar-plus me-2"></i>Book Now
                </a>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>{{ $business->name }}</h5>
                    <p>{{ $business->description }}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    @if($business->phone)
                        <p><i class="fas fa-phone me-2"></i>{{ $business->phone }}</p>
                    @endif
                    @if($business->email)
                        <p><i class="fas fa-envelope me-2"></i>{{ $business->email }}</p>
                    @endif
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; {{ date('Y') }} {{ $business->name }}. All rights reserved. Powered by <a href="https://bookkei.com" class="text-light">Bookkei</a></p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
    .service-card {
        transition: transform 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }
    
    .info-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .detail-item {
        font-size: 1.1rem;
    }
    
    .service-content {
        font-size: 1.1rem;
        line-height: 1.7;
    }
    </style>
</body>
</html>
