<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview: {{ $landingPage->page_title ?? $business->name }}</title>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Theme-specific CSS -->
    @include('landing-page.themes.' . ($landingPage->theme ?? 'default') . '.styles')

    <!-- Preview-specific styles -->
    <style>
        body {
            position: relative;
        }

        .preview-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #ff6b35;
            color: white;
            text-align: center;
            padding: 8px;
            font-size: 14px;
            font-weight: 600;
            z-index: 9999;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .preview-content {
            margin-top: 40px;
        }

        .preview-banner .btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 4px 12px;
            font-size: 12px;
            margin-left: 10px;
            border-radius: 4px;
            text-decoration: none;
        }

        .preview-banner .btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Preview Banner -->
    <div class="preview-banner">
        <i class="fas fa-eye mr-2"></i>
        PREVIEW MODE - This is how your landing page will look to visitors
        <a href="{{ route('owner.landing-page.edit') }}" class="btn">
            <i class="fas fa-edit mr-1"></i> Edit Page
        </a>
        @if(!$landingPage->is_published)
            <a href="{{ route('owner.landing-page.publish') }}" class="btn" onclick="return confirm('Are you sure you want to publish this landing page?')">
                <i class="fas fa-rocket mr-1"></i> Publish Now
            </a>
        @endif
    </div>

    <!-- Preview Content -->
    <div class="preview-content">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="#home">
                    @if($landingPage->logo_url)
                        <img src="{{ $landingPage->logo_url }}" alt="{{ $business->name }}" height="40">
                    @else
                        <strong>{{ $business->name }}</strong>
                    @endif
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="#home">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#about">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#services">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#contact">Contact</a>
                        </li>
                        @if($landingPage->booking_enabled)
                            <li class="nav-item">
                                <a class="btn btn-primary ms-2" href="#booking">
                                    {{ $landingPage->booking_button_text ?? 'Book Now' }}
                                </a>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <section id="home" class="hero-section d-flex align-items-center min-vh-100"
                 style="background: linear-gradient(135deg, var(--primary-color, #007bff) 0%, var(--secondary-color, #6c757d) 100%);">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6 text-center text-white">
                        <h1 class="display-4 fw-bold mb-4">
                            {{ $landingPage->page_title ?? $business->name }}
                        </h1>

                        @if($landingPage->page_description)
                            <p class="lead mb-4">
                                {{ $landingPage->page_description }}
                            </p>
                        @endif

                        <div class="hero-buttons">
                            @if($landingPage->booking_enabled)
                                <a href="#booking" class="btn btn-primary btn-lg me-3 mb-2">
                                    {{ $landingPage->booking_button_text ?? 'Book Now' }}
                                </a>
                            @endif
                            <a href="#about" class="btn btn-outline-light btn-lg mb-2">
                                Learn More
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="text-center">
                            <i class="fas fa-building fa-5x text-white opacity-50"></i>
                            <p class="text-white mt-3">Your business image will appear here</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="py-5">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="text-center">
                            <i class="fas fa-image fa-4x text-muted"></i>
                            <p class="text-muted mt-3">About section image</p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <h2 class="mb-4">About {{ $business->name }}</h2>
                        <p class="lead">{{ $business->description ?? 'Your business description will appear here. You can customize this content in the landing page editor.' }}</p>

                        <div class="mt-4">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check text-primary me-3"></i>
                                <span>Professional Service</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check text-primary me-3"></i>
                                <span>Experienced Team</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-check text-primary me-3"></i>
                                <span>Customer Satisfaction</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="py-5 bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="mb-3">Our Services</h2>
                        <p class="lead text-muted">Discover what we offer</p>
                    </div>
                </div>

                <div class="row">
                    @if($business->services && $business->services->count() > 0)
                        @foreach($business->services->take(3) as $service)
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-concierge-bell fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">{{ $service->name }}</h5>
                                        <p class="card-text">{{ Str::limit($service->description ?? 'Service description', 100) }}</p>
                                        @if($service->price)
                                            <p class="text-primary fw-bold">${{ number_format($service->price, 2) }}</p>
                                        @endif
                                        @if($landingPage->booking_enabled)
                                            <a href="#booking" class="btn btn-primary">Book Now</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="col-12 text-center">
                            <i class="fas fa-concierge-bell fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">Services Coming Soon</h4>
                            <p class="text-muted">Add your services to showcase them here.</p>
                        </div>
                    @endif
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="mb-3">Get in Touch</h2>
                        <p class="lead text-muted">We'd love to hear from you</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-6 mb-4">
                        <h4 class="mb-4">Contact Information</h4>

                        @if($business->phone)
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-phone text-primary me-3"></i>
                                <span>{{ $business->phone }}</span>
                            </div>
                        @endif

                        @if($business->email)
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-envelope text-primary me-3"></i>
                                <span>{{ $business->email }}</span>
                            </div>
                        @endif

                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-map-marker-alt text-primary me-3"></i>
                            <span>Your business address will appear here</span>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <h4 class="mb-4">Send us a Message</h4>
                        <form>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <input type="text" class="form-control" placeholder="Your Name" disabled>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="email" class="form-control" placeholder="Your Email" disabled>
                                </div>
                            </div>
                            <div class="mb-3">
                                <input type="text" class="form-control" placeholder="Subject" disabled>
                            </div>
                            <div class="mb-3">
                                <textarea class="form-control" rows="5" placeholder="Your Message" disabled></textarea>
                            </div>
                            <button type="button" class="btn btn-primary" disabled>Send Message (Preview)</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Booking Section -->
        @if($landingPage->booking_enabled)
            <section id="booking" class="py-5 bg-primary text-white">
                <div class="container">
                    <div class="row">
                        <div class="col-12 text-center">
                            <h2 class="mb-4">Book Your Appointment</h2>
                            <p class="lead mb-4">Schedule your visit with us</p>
                            <div class="bg-white text-dark p-4 rounded">
                                <i class="fas fa-calendar-alt fa-3x text-primary mb-3"></i>
                                <h5>Booking Widget</h5>
                                <p class="text-muted">Your booking system will be integrated here</p>
                                <button class="btn btn-primary" disabled>Book Appointment (Preview)</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        @endif
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Smooth Scrolling -->
    <script>
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
