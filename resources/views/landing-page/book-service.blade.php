<!DOCTYPE html>
<html lang="{{ $business->language ?? 'en' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book {{ $service->name }} - {{ $business->name }}</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Book {{ $service->name }} at {{ $business->name }}. Easy online booking system.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Theme Styles -->
    @include('landing-page.themes.' . ($landingPage->theme ?? 'default') . '.styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('landing-page.index', $business->landing_page_slug) }}">
                @if($landingPage->logo_url)
                    <img src="{{ $landingPage->logo_url }}" alt="{{ $business->name }}" height="40">
                @else
                    <strong>{{ $business->name }}</strong>
                @endif
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing-page.index', $business->landing_page_slug) }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('landing-page.services', $business->landing_page_slug) }}">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('landing-page.booking', $business->landing_page_slug) }}">Book Now</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Booking Content -->
    <main style="margin-top: 80px;">
        <!-- Breadcrumb -->
        <section class="py-3 bg-light">
            <div class="container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{{ route('landing-page.index', $business->landing_page_slug) }}">Home</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('landing-page.services', $business->landing_page_slug) }}">Services</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('landing-page.service', [$business->landing_page_slug, $service->slug]) }}">{{ $service->name }}</a>
                        </li>
                        <li class="breadcrumb-item active">Book Appointment</li>
                    </ol>
                </nav>
            </div>
        </section>

        <!-- Service Summary -->
        <section class="py-4 bg-primary text-white">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">Book {{ $service->name }}</h2>
                        <p class="mb-0">{{ $service->description }}</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="service-summary">
                            @if($service->duration)
                                <div class="mb-1">
                                    <i class="fas fa-clock me-2"></i>{{ $service->duration }} minutes
                                </div>
                            @endif
                            @if($service->base_price)
                                <div class="h4 mb-0">
                                    <i class="fas fa-tag me-2"></i>{{ $business->currency ?? '$' }}{{ number_format($service->base_price, 2) }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Booking Form -->
        <section class="py-5">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        @if($business->online_booking_enabled)
                            <div class="booking-form-container">
                                <form id="bookingForm" class="booking-form">
                                    @csrf
                                    <input type="hidden" name="business_id" value="{{ $business->id }}">
                                    <input type="hidden" name="service_id" value="{{ $service->id }}">
                                    
                                    <!-- Step 1: Date & Time Selection -->
                                    <div class="booking-step active" id="step-datetime">
                                        <div class="card">
                                            <div class="card-header">
                                                <h4 class="mb-0">
                                                    <i class="fas fa-calendar-alt me-2"></i>
                                                    Select Date & Time
                                                </h4>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="calendar-container">
                                                            <div id="bookingCalendar"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="time-slots-container">
                                                            <h5>Available Times</h5>
                                                            <div id="timeSlots" class="time-slots">
                                                                <!-- Time slots will be loaded dynamically -->
                                                                <div class="text-center text-muted py-4">
                                                                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                                                                    <p>Please select a date to view available times</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="text-center mt-4">
                                                    <button type="button" class="btn btn-primary" id="nextToDetails" disabled>
                                                        Next: Your Details
                                                        <i class="fas fa-arrow-right ms-2"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Step 2: Customer Details -->
                                    <div class="booking-step" id="step-details">
                                        <div class="card">
                                            <div class="card-header">
                                                <h4 class="mb-0">
                                                    <i class="fas fa-user me-2"></i>
                                                    Your Details
                                                </h4>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label for="firstName" class="form-label">First Name *</label>
                                                        <input type="text" class="form-control" id="firstName" name="first_name" required>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label for="lastName" class="form-label">Last Name *</label>
                                                        <input type="text" class="form-control" id="lastName" name="last_name" required>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label for="email" class="form-label">Email *</label>
                                                        <input type="email" class="form-control" id="email" name="email" required>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label for="phone" class="form-label">Phone *</label>
                                                        <input type="tel" class="form-control" id="phone" name="phone" required>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <label for="notes" class="form-label">Special Requests or Notes</label>
                                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                                              placeholder="Any special requests or information we should know..."></textarea>
                                                </div>
                                                
                                                <div class="text-center">
                                                    <button type="button" class="btn btn-outline-secondary me-2" id="backToDateTime">
                                                        <i class="fas fa-arrow-left me-2"></i>Back
                                                    </button>
                                                    <button type="button" class="btn btn-primary" id="nextToConfirm">
                                                        Review Booking
                                                        <i class="fas fa-arrow-right ms-2"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Step 3: Confirmation -->
                                    <div class="booking-step" id="step-confirm">
                                        <div class="card">
                                            <div class="card-header">
                                                <h4 class="mb-0">
                                                    <i class="fas fa-check-circle me-2"></i>
                                                    Confirm Your Booking
                                                </h4>
                                            </div>
                                            <div class="card-body">
                                                <div class="booking-summary">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h5>Service Details</h5>
                                                            <div class="summary-item">
                                                                <strong>Service:</strong> {{ $service->name }}
                                                            </div>
                                                            <div class="summary-item">
                                                                <strong>Duration:</strong> {{ $service->duration }} minutes
                                                            </div>
                                                            <div class="summary-item">
                                                                <strong>Price:</strong> {{ $business->currency ?? '$' }}{{ number_format($service->base_price, 2) }}
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h5>Appointment Details</h5>
                                                            <div id="bookingSummary">
                                                                <!-- Summary will be populated dynamically -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="terms-acceptance mt-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="acceptTerms" required>
                                                        <label class="form-check-label" for="acceptTerms">
                                                            I agree to the <a href="#" target="_blank">terms and conditions</a> and 
                                                            <a href="#" target="_blank">cancellation policy</a>
                                                        </label>
                                                    </div>
                                                </div>
                                                
                                                <div class="text-center mt-4">
                                                    <button type="button" class="btn btn-outline-secondary me-2" id="backToDetails">
                                                        <i class="fas fa-arrow-left me-2"></i>Back
                                                    </button>
                                                    <button type="submit" class="btn btn-success btn-lg" id="confirmBooking">
                                                        <i class="fas fa-check me-2"></i>Confirm Booking
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Success Message -->
                                    <div class="booking-step" id="step-success" style="display: none;">
                                        <div class="card">
                                            <div class="card-body text-center py-5">
                                                <i class="fas fa-check-circle fa-4x text-success mb-4"></i>
                                                <h3>Booking Confirmed!</h3>
                                                <p class="lead">Thank you for your booking! We'll confirm your appointment shortly.</p>
                                                <div class="booking-reference">
                                                    <p>Your booking reference: <strong id="bookingReference"></strong></p>
                                                </div>
                                                <div class="mt-4">
                                                    <a href="{{ route('landing-page.index', $business->landing_page_slug) }}" class="btn btn-primary me-2">
                                                        Back to Home
                                                    </a>
                                                    <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
                                                        Book Another Service
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        @else
                            <!-- Booking Disabled -->
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                                    <h4>Online Booking Currently Unavailable</h4>
                                    <p class="lead">Please contact us directly to schedule your {{ $service->name }} appointment.</p>
                                    <div class="contact-options mt-4">
                                        @if($business->phone)
                                            <a href="tel:{{ $business->phone }}" class="btn btn-primary btn-lg me-3 mb-2">
                                                <i class="fas fa-phone me-2"></i>Call {{ $business->phone }}
                                            </a>
                                        @endif
                                        @if($business->email)
                                            <a href="mailto:{{ $business->email }}" class="btn btn-outline-primary btn-lg mb-2">
                                                <i class="fas fa-envelope me-2"></i>Email Us
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="text-center">
                <p>&copy; {{ date('Y') }} {{ $business->name }}. All rights reserved. Powered by <a href="https://bookkei.com" class="text-light">Bookkei</a></p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
    .booking-step {
        display: none;
    }
    
    .booking-step.active {
        display: block;
    }
    
    .time-slots {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
        margin-top: 15px;
    }
    
    .time-slot {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .time-slot:hover,
    .time-slot.selected {
        background: var(--bs-primary);
        border-color: var(--bs-primary);
        color: white;
    }
    
    .time-slot.unavailable {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .summary-item {
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    
    .calendar-container {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }
    </style>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const bookingForm = document.getElementById('bookingForm');
        const steps = document.querySelectorAll('.booking-step');
        let currentStep = 0;
        let selectedDateTime = null;
        
        // Step navigation
        document.getElementById('nextToDetails')?.addEventListener('click', () => goToStep(1));
        document.getElementById('backToDateTime')?.addEventListener('click', () => goToStep(0));
        document.getElementById('nextToConfirm')?.addEventListener('click', () => goToStep(2));
        document.getElementById('backToDetails')?.addEventListener('click', () => goToStep(1));
        
        function goToStep(stepIndex) {
            steps.forEach(step => step.classList.remove('active'));
            steps[stepIndex].classList.add('active');
            currentStep = stepIndex;
            
            if (stepIndex === 2) {
                updateBookingSummary();
            }
        }
        
        function updateBookingSummary() {
            const summary = document.getElementById('bookingSummary');
            summary.innerHTML = `
                <div class="summary-item">
                    <strong>Date & Time:</strong> Selected Date & Time
                </div>
                <div class="summary-item">
                    <strong>Customer:</strong> ${document.getElementById('firstName').value} ${document.getElementById('lastName').value}
                </div>
                <div class="summary-item">
                    <strong>Email:</strong> ${document.getElementById('email').value}
                </div>
                <div class="summary-item">
                    <strong>Phone:</strong> ${document.getElementById('phone').value}
                </div>
            `;
        }
        
        // Form submission
        bookingForm?.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success step
            steps.forEach(step => step.classList.remove('active'));
            document.getElementById('step-success').style.display = 'block';
            
            // Generate booking reference
            document.getElementById('bookingReference').textContent = 'BK' + Date.now().toString().slice(-6);
        });
    });
    </script>
</body>
</html>
