<!-- Services Section -->
<section id="services" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Our Services' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>
        
        @if($pageData['services']->isNotEmpty())
            <div class="row">
                @foreach($pageData['services'] as $service)
                    <div class="col-lg-{{ 12 / ($section['content']['columns'] ?? 3) }} col-md-6 mb-4">
                        <div class="service-card h-100">
                            @if($service->images && $service->images->where('is_primary', true)->first())
                                <div class="service-image">
                                    <img src="{{ $service->images->where('is_primary', true)->first()->image_url }}" 
                                         alt="{{ $service->name }}" class="card-img-top">
                                </div>
                            @else
                                <div class="service-image-placeholder">
                                    <i class="fas fa-concierge-bell fa-3x text-muted"></i>
                                </div>
                            @endif
                            
                            <div class="card-body">
                                <h5 class="card-title">{{ $service->name }}</h5>
                                
                                @if(isset($section['content']['show_description']) && $section['content']['show_description'] && $service->description)
                                    <p class="card-text text-muted">{{ Str::limit($service->description, 100) }}</p>
                                @endif
                                
                                <div class="service-details">
                                    @if(isset($section['content']['show_duration']) && $section['content']['show_duration'] && $service->duration_minutes)
                                        <div class="service-detail">
                                            <i class="fas fa-clock text-primary me-2"></i>
                                            <span>{{ $service->duration_minutes }} minutes</span>
                                        </div>
                                    @endif
                                    
                                    @if(isset($section['content']['show_prices']) && $section['content']['show_prices'] && $service->price)
                                        <div class="service-detail">
                                            <i class="fas fa-tag text-primary me-2"></i>
                                            <span class="price">{{ $business->currency }} {{ number_format($service->price, 2) }}</span>
                                        </div>
                                    @endif
                                </div>
                                
                                @if($landingPage->booking_enabled && $service->online_booking_enabled)
                                    <div class="mt-3">
                                        <a href="#booking" class="btn btn-primary btn-sm">
                                            Book {{ $service->name }}
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- No services available -->
            <div class="row">
                <div class="col-12 text-center">
                    <div class="no-services py-5">
                        <i class="fas fa-concierge-bell fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Services Coming Soon</h4>
                        <p class="text-muted">We're working on adding our services. Please check back soon!</p>
                    </div>
                </div>
            </div>
        @endif
        
        @if(isset($section['content']['cta_button']) && $section['content']['cta_button'])
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <a href="{{ $section['content']['cta_url'] ?? '#contact' }}" class="btn btn-primary btn-lg">
                        {{ $section['content']['cta_text'] ?? 'View All Services' }}
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
.service-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.service-image {
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-image-placeholder {
    height: 200px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: var(--primary-color, #007bff);
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-details {
    margin: 1rem 0;
}

.service-detail {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.price {
    font-weight: 600;
    color: var(--accent-color, #28a745);
    font-size: 1.1rem;
}

.btn-primary {
    background: var(--primary-color, #007bff);
    border-color: var(--primary-color, #007bff);
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--secondary-color, #6c757d);
    border-color: var(--secondary-color, #6c757d);
    transform: translateY(-2px);
}

.no-services {
    background: white;
    border-radius: 15px;
    margin: 2rem 0;
}

/* Grid Layout Adjustments */
@media (max-width: 768px) {
    .service-card {
        margin-bottom: 2rem;
    }
    
    .service-card:hover {
        transform: none;
    }
}

/* Featured Services Highlight */
.service-card.featured {
    border: 2px solid var(--primary-color, #007bff);
    position: relative;
}

.service-card.featured::before {
    content: "Popular";
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color, #007bff);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1;
}
</style>
