<!-- Features Section -->
<section id="features" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Why Choose Us' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>

        @php
            $features = $section['content']['features'] ?? [];
            $layout = $section['content']['layout'] ?? 'grid';
            $columns = $section['content']['columns'] ?? 3;
        @endphp

        @if(!empty($features))
            @if($layout === 'grid')
                <div class="row">
                    @foreach($features as $feature)
                        <div class="col-lg-{{ 12 / $columns }} col-md-6 mb-4">
                            <div class="feature-card text-center h-100">
                                @if(isset($feature['icon']))
                                    <div class="feature-icon mb-3">
                                        <i class="{{ $feature['icon'] }} fa-3x text-primary"></i>
                                    </div>
                                @endif
                                
                                <h5 class="feature-title mb-3">{{ $feature['title'] ?? '' }}</h5>
                                
                                @if(isset($feature['description']))
                                    <p class="text-muted">{{ $feature['description'] }}</p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            
            @elseif($layout === 'list')
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        @foreach($features as $feature)
                            <div class="feature-item d-flex align-items-start mb-4">
                                @if(isset($feature['icon']))
                                    <div class="feature-icon me-4">
                                        <i class="{{ $feature['icon'] }} fa-2x text-primary"></i>
                                    </div>
                                @endif
                                
                                <div class="feature-content">
                                    <h5 class="feature-title mb-2">{{ $feature['title'] ?? '' }}</h5>
                                    
                                    @if(isset($feature['description']))
                                        <p class="text-muted mb-0">{{ $feature['description'] }}</p>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            
            @elseif($layout === 'carousel')
                <div id="featuresCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        @foreach($features->chunk(3) as $chunkIndex => $featureChunk)
                            <div class="carousel-item {{ $chunkIndex === 0 ? 'active' : '' }}">
                                <div class="row">
                                    @foreach($featureChunk as $feature)
                                        <div class="col-lg-4 col-md-6 mb-4">
                                            <div class="feature-card text-center h-100">
                                                @if(isset($feature['icon']))
                                                    <div class="feature-icon mb-3">
                                                        <i class="{{ $feature['icon'] }} fa-3x text-primary"></i>
                                                    </div>
                                                @endif
                                                
                                                <h5 class="feature-title mb-3">{{ $feature['title'] ?? '' }}</h5>
                                                
                                                @if(isset($feature['description']))
                                                    <p class="text-muted">{{ $feature['description'] }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    @if(count($features) > 3)
                        <button class="carousel-control-prev" type="button" data-bs-target="#featuresCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#featuresCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    @endif
                </div>
            @endif
        @else
            <!-- Default features when none are configured -->
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card text-center h-100">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-star fa-3x text-primary"></i>
                        </div>
                        <h5 class="feature-title mb-3">Quality Service</h5>
                        <p class="text-muted">We provide top-notch service with attention to detail.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card text-center h-100">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-clock fa-3x text-primary"></i>
                        </div>
                        <h5 class="feature-title mb-3">Convenient Hours</h5>
                        <p class="text-muted">Flexible scheduling to fit your busy lifestyle.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card text-center h-100">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-users fa-3x text-primary"></i>
                        </div>
                        <h5 class="feature-title mb-3">Expert Team</h5>
                        <p class="text-muted">Our experienced professionals are here to help you.</p>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
.feature-card {
    padding: 2rem 1rem;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: #fff;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-title {
    font-weight: 600;
    color: #333;
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    color: #333;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: #333;
    border-radius: 50%;
    width: 40px;
    height: 40px;
}

@media (max-width: 768px) {
    .feature-card {
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
    }
    
    .feature-icon i {
        font-size: 2rem !important;
    }
}
</style>
