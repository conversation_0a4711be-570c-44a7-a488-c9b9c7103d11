<!-- FAQ Section -->
<section id="faq" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Frequently Asked Questions' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>

        @php
            $faqs = $section['content']['faqs'] ?? [];
            $layout = $section['content']['layout'] ?? 'accordion';
            $searchEnabled = $section['content']['search_enabled'] ?? true;
            $categoriesEnabled = $section['content']['categories_enabled'] ?? false;
            
            // Default FAQs if none provided
            if (empty($faqs)) {
                $faqs = [
                    [
                        'question' => 'How do I book an appointment?',
                        'answer' => 'You can book an appointment through our online booking system by clicking the "Book Now" button, calling us directly, or visiting our location.',
                        'category' => 'Booking'
                    ],
                    [
                        'question' => 'What is your cancellation policy?',
                        'answer' => 'We require at least 24 hours notice for cancellations. Cancellations made less than 24 hours in advance may be subject to a cancellation fee.',
                        'category' => 'Policies'
                    ],
                    [
                        'question' => 'Do you accept walk-ins?',
                        'answer' => 'While we prefer appointments to ensure availability, we do accept walk-ins when possible. However, appointment customers will always have priority.',
                        'category' => 'Booking'
                    ],
                    [
                        'question' => 'What payment methods do you accept?',
                        'answer' => 'We accept cash, credit cards (Visa, MasterCard, American Express), and digital payments including Apple Pay and Google Pay.',
                        'category' => 'Payment'
                    ],
                    [
                        'question' => 'How far in advance can I book?',
                        'answer' => 'You can book appointments up to 3 months in advance. For special events or peak seasons, we recommend booking as early as possible.',
                        'category' => 'Booking'
                    ]
                ];
            }
            
            $categories = collect($faqs)->pluck('category')->unique()->filter();
        @endphp

        @if($searchEnabled && count($faqs) > 3)
            <!-- FAQ Search -->
            <div class="row mb-4">
                <div class="col-lg-6 mx-auto">
                    <div class="faq-search">
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   id="faqSearch" 
                                   placeholder="Search frequently asked questions...">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if($categoriesEnabled && $categories->count() > 1)
            <!-- FAQ Categories -->
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <div class="faq-categories">
                        <button class="btn btn-outline-primary active me-2 mb-2" data-category="all">All</button>
                        @foreach($categories as $category)
                            <button class="btn btn-outline-primary me-2 mb-2" data-category="{{ Str::slug($category) }}">
                                {{ $category }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        @if($layout === 'accordion')
            <!-- Accordion Layout -->
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="accordion" id="faqAccordion">
                        @foreach($faqs as $index => $faq)
                            <div class="accordion-item faq-item" 
                                 data-category="{{ Str::slug($faq['category'] ?? 'general') }}"
                                 data-question="{{ strtolower($faq['question']) }}"
                                 data-answer="{{ strtolower($faq['answer']) }}">
                                <h3 class="accordion-header" id="faqHeading{{ $index }}">
                                    <button class="accordion-button {{ $index === 0 ? '' : 'collapsed' }}" 
                                            type="button" 
                                            data-bs-toggle="collapse" 
                                            data-bs-target="#faqCollapse{{ $index }}"
                                            aria-expanded="{{ $index === 0 ? 'true' : 'false' }}" 
                                            aria-controls="faqCollapse{{ $index }}">
                                        {{ $faq['question'] }}
                                    </button>
                                </h3>
                                <div id="faqCollapse{{ $index }}" 
                                     class="accordion-collapse collapse {{ $index === 0 ? 'show' : '' }}" 
                                     aria-labelledby="faqHeading{{ $index }}" 
                                     data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        {{ $faq['answer'] }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @else
            <!-- Card Layout -->
            <div class="row">
                @foreach($faqs as $index => $faq)
                    <div class="col-lg-6 mb-4">
                        <div class="faq-card faq-item" 
                             data-category="{{ Str::slug($faq['category'] ?? 'general') }}"
                             data-question="{{ strtolower($faq['question']) }}"
                             data-answer="{{ strtolower($faq['answer']) }}">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-question-circle text-primary me-2"></i>
                                        {{ $faq['question'] }}
                                    </h5>
                                    <p class="card-text">{{ $faq['answer'] }}</p>
                                    @if(isset($faq['category']))
                                        <span class="badge bg-primary">{{ $faq['category'] }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif

        <!-- No Results Message -->
        <div id="noResults" class="row" style="display: none;">
            <div class="col-12 text-center">
                <div class="no-results py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No matching questions found</h4>
                    <p class="text-muted">Try adjusting your search terms or browse all questions.</p>
                </div>
            </div>
        </div>

        <!-- Contact for More Questions -->
        <div class="row mt-5">
            <div class="col-12 text-center">
                <div class="faq-contact">
                    <h4>Still have questions?</h4>
                    <p class="text-muted mb-4">Can't find the answer you're looking for? We're here to help!</p>
                    <div class="contact-buttons">
                        @if($business->phone)
                            <a href="tel:{{ $business->phone }}" class="btn btn-primary me-3 mb-2">
                                <i class="fas fa-phone me-2"></i>Call Us
                            </a>
                        @endif
                        @if($business->email)
                            <a href="mailto:{{ $business->email }}" class="btn btn-outline-primary me-3 mb-2">
                                <i class="fas fa-envelope me-2"></i>Email Us
                            </a>
                        @endif
                        <a href="#contact" class="btn btn-outline-primary mb-2">
                            <i class="fas fa-comments me-2"></i>Contact Form
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.faq-search .form-control {
    border-radius: 25px;
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    font-size: 1rem;
}

.faq-search .input-group-text {
    border-radius: 0 25px 25px 0;
    border: 2px solid #e9ecef;
    border-left: none;
    background: white;
}

.faq-categories .btn {
    border-radius: 25px;
    padding: 8px 20px;
    transition: all 0.3s ease;
}

.faq-categories .btn.active {
    background: var(--primary-color, #007bff);
    border-color: var(--primary-color, #007bff);
    color: white;
}

.accordion-item {
    border: none;
    margin-bottom: 15px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.accordion-button {
    background: white;
    border: none;
    padding: 20px 25px;
    font-weight: 600;
    color: var(--text-color, #333);
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color, #007bff);
    color: white;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-body {
    padding: 20px 25px;
    background: #f8f9fa;
    color: var(--text-muted, #666);
    line-height: 1.6;
}

.faq-card .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.faq-card .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.faq-card .card-title {
    color: var(--text-color, #333);
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.faq-card .card-text {
    color: var(--text-muted, #666);
    line-height: 1.6;
}

.faq-contact {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.no-results {
    background: white;
    border-radius: 15px;
    margin: 0 auto;
    max-width: 400px;
}

@media (max-width: 768px) {
    .faq-search .form-control {
        font-size: 0.9rem;
        padding: 10px 15px;
    }
    
    .accordion-button {
        padding: 15px 20px;
        font-size: 0.95rem;
    }
    
    .accordion-body {
        padding: 15px 20px;
    }
    
    .faq-contact {
        padding: 30px 20px;
    }
    
    .contact-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('faqSearch');
    const categoryButtons = document.querySelectorAll('[data-category]');
    const faqItems = document.querySelectorAll('.faq-item');
    const noResults = document.getElementById('noResults');
    
    let currentCategory = 'all';
    let currentSearch = '';
    
    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            currentSearch = this.value.toLowerCase();
            filterFAQs();
        });
    }
    
    // Category filtering
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            currentCategory = this.getAttribute('data-category');
            filterFAQs();
        });
    });
    
    function filterFAQs() {
        let visibleCount = 0;
        
        faqItems.forEach(item => {
            const category = item.getAttribute('data-category');
            const question = item.getAttribute('data-question');
            const answer = item.getAttribute('data-answer');
            
            const matchesCategory = currentCategory === 'all' || category === currentCategory;
            const matchesSearch = currentSearch === '' || 
                                question.includes(currentSearch) || 
                                answer.includes(currentSearch);
            
            if (matchesCategory && matchesSearch) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });
        
        // Show/hide no results message
        if (noResults) {
            noResults.style.display = visibleCount === 0 ? 'block' : 'none';
        }
    }
});
</script>
