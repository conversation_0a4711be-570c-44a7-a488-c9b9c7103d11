<!-- Team Section -->
<section id="team" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Meet Our Team' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>

        @php
            $teamMembers = $pageData['team'] ?? collect();
            $layout = $section['content']['layout'] ?? 'grid';
            $columns = $section['content']['columns'] ?? 3;
            $showBio = $section['content']['show_bio'] ?? true;
            $showSocialLinks = $section['content']['show_social_links'] ?? true;
        @endphp

        @if($teamMembers->isNotEmpty())
            <div class="row team-grid">
                @foreach($teamMembers as $member)
                    <div class="col-lg-{{ 12 / $columns }} col-md-6 mb-4">
                        <div class="team-card h-100">
                            <div class="team-image-container">
                                @if($member->photo_url)
                                    <img src="{{ $member->photo_url }}" 
                                         alt="{{ $member->name }}" 
                                         class="team-image">
                                @else
                                    <div class="team-image-placeholder">
                                        <i class="fas fa-user fa-4x text-muted"></i>
                                    </div>
                                @endif
                                
                                @if($showSocialLinks && ($member->social_links || $member->email))
                                    <div class="team-social-overlay">
                                        <div class="social-links">
                                            @if($member->email)
                                                <a href="mailto:{{ $member->email }}" class="social-link">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                            @endif
                                            @if($member->social_links)
                                                @foreach($member->social_links as $platform => $url)
                                                    <a href="{{ $url }}" target="_blank" class="social-link">
                                                        @switch($platform)
                                                            @case('facebook')
                                                                <i class="fab fa-facebook-f"></i>
                                                                @break
                                                            @case('instagram')
                                                                <i class="fab fa-instagram"></i>
                                                                @break
                                                            @case('twitter')
                                                                <i class="fab fa-twitter"></i>
                                                                @break
                                                            @case('linkedin')
                                                                <i class="fab fa-linkedin-in"></i>
                                                                @break
                                                            @default
                                                                <i class="fas fa-link"></i>
                                                        @endswitch
                                                    </a>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="team-content">
                                <h4 class="team-name">{{ $member->name }}</h4>
                                
                                @if($member->position || $member->title)
                                    <p class="team-position">{{ $member->position ?? $member->title }}</p>
                                @endif
                                
                                @if($member->specializations && $member->specializations->count())
                                    <div class="team-specializations mb-3">
                                        @foreach($member->specializations->take(3) as $specialization)
                                            <span class="badge bg-primary me-1 mb-1">{{ $specialization->name }}</span>
                                        @endforeach
                                    </div>
                                @endif
                                
                                @if($showBio && $member->bio)
                                    <p class="team-bio">{{ Str::limit($member->bio, 120) }}</p>
                                @endif
                                
                                @if($member->years_experience)
                                    <div class="team-experience">
                                        <small class="text-muted">
                                            <i class="fas fa-award me-1"></i>
                                            {{ $member->years_experience }} {{ Str::plural('year', $member->years_experience) }} experience
                                        </small>
                                    </div>
                                @endif
                                
                                @if($member->languages && $member->languages->count())
                                    <div class="team-languages mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-language me-1"></i>
                                            {{ $member->languages->pluck('name')->join(', ') }}
                                        </small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- No team members available -->
            <div class="row">
                <div class="col-12 text-center">
                    <div class="no-team py-5">
                        <i class="fas fa-users fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Team Information Coming Soon</h4>
                        <p class="text-muted">We're working on adding our team member profiles. Check back soon!</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Team Stats -->
        @if($teamMembers->isNotEmpty())
            <div class="row mt-5">
                <div class="col-12">
                    <div class="team-stats">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <div class="stat-item">
                                    <h3 class="stat-number">{{ $teamMembers->count() }}</h3>
                                    <p class="stat-label">Team {{ Str::plural('Member', $teamMembers->count()) }}</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stat-item">
                                    <h3 class="stat-number">{{ $teamMembers->sum('years_experience') ?: 'N/A' }}</h3>
                                    <p class="stat-label">Combined Years Experience</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stat-item">
                                    <h3 class="stat-number">{{ $teamMembers->flatMap->specializations->unique('id')->count() ?: 'N/A' }}</h3>
                                    <p class="stat-label">{{ Str::plural('Specialization', $teamMembers->flatMap->specializations->unique('id')->count()) }}</p>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stat-item">
                                    <h3 class="stat-number">{{ $teamMembers->flatMap->languages->unique('id')->count() ?: 'N/A' }}</h3>
                                    <p class="stat-label">{{ Str::plural('Language', $teamMembers->flatMap->languages->unique('id')->count()) }} Spoken</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
.team-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.team-image-container {
    position: relative;
    overflow: hidden;
}

.team-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.team-image-placeholder {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-light, #f8f9fa);
}

.team-card:hover .team-image {
    transform: scale(1.05);
}

.team-social-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-card:hover .team-social-overlay {
    opacity: 1;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color, #007bff);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color, #007bff);
    color: white;
    transform: scale(1.1);
}

.team-content {
    padding: 25px;
}

.team-name {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-color, #333);
}

.team-position {
    color: var(--primary-color, #007bff);
    font-weight: 500;
    margin-bottom: 15px;
}

.team-specializations .badge {
    font-size: 0.75rem;
}

.team-bio {
    color: var(--text-muted, #666);
    line-height: 1.6;
    margin-bottom: 15px;
}

.team-experience,
.team-languages {
    font-size: 0.9rem;
}

.team-stats {
    background: var(--bg-light, #f8f9fa);
    padding: 40px 20px;
    border-radius: 15px;
    margin-top: 30px;
}

.stat-item {
    padding: 20px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color, #007bff);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-muted, #666);
    font-weight: 500;
    margin-bottom: 0;
}

.no-team {
    background: white;
    border-radius: 15px;
    margin: 0 auto;
    max-width: 400px;
}

@media (max-width: 768px) {
    .team-image,
    .team-image-placeholder {
        height: 250px;
    }
    
    .team-content {
        padding: 20px;
    }
    
    .team-social-overlay {
        opacity: 1;
        background: rgba(0,0,0,0.5);
    }
    
    .social-links {
        gap: 10px;
    }
    
    .social-link {
        width: 35px;
        height: 35px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}
</style>
