<!-- Enhanced Services Section with Advanced Features -->
<section id="services" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Our Services' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>

        @php
            $serviceSettings = $business->landingServiceSettings;
            $services = $pageData['services'] ?? collect();
            $visibleServices = $services->where('is_public', true)->take($serviceSettings->homepage_display_count ?? 6);
        @endphp

        <!-- Service Search and Filters -->
        @if($serviceSettings->enable_search || $serviceSettings->enable_filtering)
            <div class="row mb-4">
                <div class="col-12">
                    <div class="service-controls">
                        @if($serviceSettings->enable_search)
                            <div class="service-search mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="service-search" 
                                           placeholder="Search services...">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($serviceSettings->enable_filtering && $serviceSettings->show_categories)
                            <div class="service-filters">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary active" data-filter="all">
                                        All Services
                                    </button>
                                    @foreach($services->pluck('category')->unique()->filter() as $category)
                                        <button type="button" class="btn btn-outline-primary" 
                                                data-filter="{{ $category->slug }}">
                                            {{ $category->name }}
                                        </button>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        @if($visibleServices->isNotEmpty())
            <!-- Services Grid/List Display -->
            <div class="services-container" data-layout="{{ $serviceSettings->layout_type }}">
                @if($serviceSettings->layout_type === 'grid')
                    <div class="row services-grid" data-columns="{{ $serviceSettings->grid_columns ?? 3 }}">
                        @foreach($visibleServices as $service)
                            @include('landing-page.partials.service-card', [
                                'service' => $service,
                                'settings' => $serviceSettings,
                                'landingPage' => $landingPage
                            ])
                        @endforeach
                    </div>
                @elseif($serviceSettings->layout_type === 'list')
                    <div class="services-list">
                        @foreach($visibleServices as $service)
                            @include('landing-page.partials.service-list-item', [
                                'service' => $service,
                                'settings' => $serviceSettings,
                                'landingPage' => $landingPage
                            ])
                        @endforeach
                    </div>
                @elseif($serviceSettings->layout_type === 'carousel')
                    <div class="services-carousel">
                        <div id="servicesCarousel" class="carousel slide" data-ride="carousel">
                            <div class="carousel-inner">
                                @foreach($visibleServices->chunk(3) as $index => $serviceChunk)
                                    <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                        <div class="row">
                                            @foreach($serviceChunk as $service)
                                                @include('landing-page.partials.service-card', [
                                                    'service' => $service,
                                                    'settings' => $serviceSettings,
                                                    'landingPage' => $landingPage
                                                ])
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <a class="carousel-control-prev" href="#servicesCarousel" role="button" data-slide="prev">
                                <span class="carousel-control-prev-icon"></span>
                            </a>
                            <a class="carousel-control-next" href="#servicesCarousel" role="button" data-slide="next">
                                <span class="carousel-control-next-icon"></span>
                            </a>
                        </div>
                    </div>
                @elseif($serviceSettings->layout_type === 'masonry')
                    <div class="services-masonry">
                        @foreach($visibleServices as $service)
                            @include('landing-page.partials.service-card', [
                                'service' => $service,
                                'settings' => $serviceSettings,
                                'landingPage' => $landingPage,
                                'masonry' => true
                            ])
                        @endforeach
                    </div>
                @endif
            </div>

            <!-- Load More Button -->
            @if($services->count() > $serviceSettings->homepage_display_count)
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button class="btn btn-outline-primary" id="load-more-services">
                            Load More Services
                        </button>
                    </div>
                </div>
            @endif
        @else
            <!-- No services available -->
            <div class="row">
                <div class="col-12 text-center">
                    <div class="no-services py-5">
                        <i class="fas fa-concierge-bell fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Services Coming Soon</h4>
                        <p class="text-muted">We're working on adding our services. Please check back soon!</p>
                    </div>
                </div>
            </div>
        @endif
        
        @if(isset($section['content']['cta_button']) && $section['content']['cta_button'])
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <a href="{{ $section['content']['cta_url'] ?? '#contact' }}" class="btn btn-primary btn-lg">
                        {{ $section['content']['cta_text'] ?? 'View All Services' }}
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Service Schema Markup for SEO -->
@if($serviceSettings->enable_service_seo)
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "itemListElement": [
            @foreach($visibleServices as $index => $service)
            {
                "@type": "Service",
                "position": {{ $index + 1 }},
                "name": "{{ $service->name }}",
                "description": "{{ $service->description }}",
                @if($serviceSettings->show_pricing && $service->base_price)
                "offers": {
                    "@type": "Offer",
                    "price": "{{ $service->base_price }}",
                    "priceCurrency": "{{ $business->currency ?? 'USD' }}"
                },
                @endif
                "provider": {
                    "@type": "{{ $business->seoSettings->business_type ?? 'LocalBusiness' }}",
                    "name": "{{ $business->name }}"
                }
            }{{ !$loop->last ? ',' : '' }}
            @endforeach
        ]
    }
    </script>
@endif

<style>
.service-controls {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.services-grid[data-columns="2"] .service-card-wrapper {
    flex: 0 0 50%;
    max-width: 50%;
}

.services-grid[data-columns="3"] .service-card-wrapper {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.services-grid[data-columns="4"] .service-card-wrapper {
    flex: 0 0 25%;
    max-width: 25%;
}

.services-masonry {
    column-count: 3;
    column-gap: 20px;
}

.services-masonry .service-card {
    break-inside: avoid;
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .services-masonry {
        column-count: 1;
    }
    
    .services-grid[data-columns="3"] .service-card-wrapper,
    .services-grid[data-columns="4"] .service-card-wrapper {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.service-filters .btn {
    margin: 2px;
}

.service-search {
    max-width: 400px;
    margin: 0 auto;
}

.services-list .service-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.services-list .service-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Service search functionality
    const searchInput = document.getElementById('service-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const serviceCards = document.querySelectorAll('.service-card, .service-item');
            
            serviceCards.forEach(card => {
                const serviceName = card.querySelector('.service-name')?.textContent.toLowerCase() || '';
                const serviceDesc = card.querySelector('.service-description')?.textContent.toLowerCase() || '';
                
                if (serviceName.includes(searchTerm) || serviceDesc.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }

    // Service filtering functionality
    const filterButtons = document.querySelectorAll('[data-filter]');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter services
            const serviceCards = document.querySelectorAll('.service-card, .service-item');
            serviceCards.forEach(card => {
                if (filter === 'all' || card.getAttribute('data-category') === filter) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });

    // Load more services functionality
    const loadMoreBtn = document.getElementById('load-more-services');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            // This would typically make an AJAX request to load more services
            console.log('Loading more services...');
        });
    }
});
</script>
