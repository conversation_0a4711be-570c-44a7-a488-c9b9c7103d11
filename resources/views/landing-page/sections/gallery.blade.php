<!-- Gallery Section -->
<section id="gallery" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Gallery' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>

        @php
            $images = $pageData['gallery'] ?? collect();
            $layout = $section['content']['layout'] ?? 'masonry';
            $columns = $section['content']['columns'] ?? 4;
            $showCaptions = $section['content']['show_captions'] ?? true;
            $lightboxEnabled = $section['content']['lightbox_enabled'] ?? true;
        @endphp

        @if($images->isNotEmpty())
            @if($layout === 'masonry')
                <!-- Masonry Layout -->
                <div class="gallery-masonry" data-columns="{{ $columns }}">
                    @foreach($images as $image)
                        <div class="gallery-item">
                            <div class="gallery-card">
                                <img src="{{ $image->image_url }}" 
                                     alt="{{ $image->alt_text ?? $image->title }}" 
                                     class="gallery-image"
                                     @if($lightboxEnabled) 
                                     data-bs-toggle="modal" 
                                     data-bs-target="#galleryModal" 
                                     data-image="{{ $image->image_url }}"
                                     data-title="{{ $image->title }}"
                                     data-description="{{ $image->description }}"
                                     @endif>
                                
                                @if($showCaptions && ($image->title || $image->description))
                                    <div class="gallery-overlay">
                                        <div class="gallery-content">
                                            @if($image->title)
                                                <h5 class="gallery-title">{{ $image->title }}</h5>
                                            @endif
                                            @if($image->description)
                                                <p class="gallery-description">{{ $image->description }}</p>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                                
                                @if($lightboxEnabled)
                                    <div class="gallery-zoom">
                                        <i class="fas fa-search-plus"></i>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Grid Layout -->
                <div class="row gallery-grid">
                    @foreach($images as $image)
                        <div class="col-lg-{{ 12 / $columns }} col-md-6 col-sm-6 mb-4">
                            <div class="gallery-card">
                                <img src="{{ $image->image_url }}" 
                                     alt="{{ $image->alt_text ?? $image->title }}" 
                                     class="gallery-image"
                                     @if($lightboxEnabled) 
                                     data-bs-toggle="modal" 
                                     data-bs-target="#galleryModal" 
                                     data-image="{{ $image->image_url }}"
                                     data-title="{{ $image->title }}"
                                     data-description="{{ $image->description }}"
                                     @endif>
                                
                                @if($showCaptions && ($image->title || $image->description))
                                    <div class="gallery-overlay">
                                        <div class="gallery-content">
                                            @if($image->title)
                                                <h5 class="gallery-title">{{ $image->title }}</h5>
                                            @endif
                                            @if($image->description)
                                                <p class="gallery-description">{{ $image->description }}</p>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                                
                                @if($lightboxEnabled)
                                    <div class="gallery-zoom">
                                        <i class="fas fa-search-plus"></i>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        @else
            <!-- No images available -->
            <div class="row">
                <div class="col-12 text-center">
                    <div class="no-gallery py-5">
                        <i class="fas fa-images fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Gallery Coming Soon</h4>
                        <p class="text-muted">We're working on adding photos to showcase our work. Check back soon!</p>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

@if($lightboxEnabled && $images->isNotEmpty())
    <!-- Gallery Modal -->
    <div class="modal fade" id="galleryModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="galleryModalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="galleryModalImage" src="" alt="" class="img-fluid">
                    <p id="galleryModalDescription" class="mt-3 text-muted"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endif

<style>
.gallery-masonry {
    column-count: 4;
    column-gap: 20px;
}

.gallery-masonry[data-columns="2"] {
    column-count: 2;
}

.gallery-masonry[data-columns="3"] {
    column-count: 3;
}

.gallery-masonry[data-columns="4"] {
    column-count: 4;
}

.gallery-masonry[data-columns="5"] {
    column-count: 5;
}

.gallery-item {
    break-inside: avoid;
    margin-bottom: 20px;
}

.gallery-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.gallery-image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-image {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.gallery-description {
    font-size: 0.9rem;
    margin-bottom: 0;
    opacity: 0.9;
}

.gallery-zoom {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255,255,255,0.9);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: var(--primary-color, #007bff);
    font-size: 1.2rem;
}

.gallery-card:hover .gallery-zoom {
    opacity: 1;
}

.no-gallery {
    background: white;
    border-radius: 15px;
    margin: 0 auto;
    max-width: 400px;
}

@media (max-width: 1200px) {
    .gallery-masonry {
        column-count: 3;
    }
    
    .gallery-masonry[data-columns="2"] {
        column-count: 2;
    }
    
    .gallery-masonry[data-columns="3"] {
        column-count: 3;
    }
    
    .gallery-masonry[data-columns="4"] {
        column-count: 3;
    }
    
    .gallery-masonry[data-columns="5"] {
        column-count: 3;
    }
}

@media (max-width: 768px) {
    .gallery-masonry {
        column-count: 2;
    }
    
    .gallery-masonry[data-columns="2"],
    .gallery-masonry[data-columns="3"],
    .gallery-masonry[data-columns="4"],
    .gallery-masonry[data-columns="5"] {
        column-count: 2;
    }
    
    .gallery-overlay {
        transform: translateY(0);
        background: rgba(0,0,0,0.6);
    }
    
    .gallery-zoom {
        opacity: 1;
    }
}

@media (max-width: 576px) {
    .gallery-masonry {
        column-count: 1;
    }
    
    .gallery-masonry[data-columns="2"],
    .gallery-masonry[data-columns="3"],
    .gallery-masonry[data-columns="4"],
    .gallery-masonry[data-columns="5"] {
        column-count: 1;
    }
}
</style>

@if($lightboxEnabled)
<script>
document.addEventListener('DOMContentLoaded', function() {
    const galleryModal = document.getElementById('galleryModal');
    const modalImage = document.getElementById('galleryModalImage');
    const modalTitle = document.getElementById('galleryModalTitle');
    const modalDescription = document.getElementById('galleryModalDescription');
    
    galleryModal.addEventListener('show.bs.modal', function(event) {
        const trigger = event.relatedTarget;
        const imageUrl = trigger.getAttribute('data-image');
        const title = trigger.getAttribute('data-title');
        const description = trigger.getAttribute('data-description');
        
        modalImage.src = imageUrl;
        modalImage.alt = title || '';
        modalTitle.textContent = title || '';
        modalDescription.textContent = description || '';
        
        if (!description) {
            modalDescription.style.display = 'none';
        } else {
            modalDescription.style.display = 'block';
        }
    });
});
</script>
@endif
