<!-- About Section -->
<section id="about" class="py-5">
    <div class="container">
        <div class="row align-items-center">
            @if(isset($section['content']['layout']) && $section['content']['layout'] === 'image-right')
                <!-- Content Left, Image Right -->
                <div class="col-lg-6">
                    <div class="about-content">
                        <h2 class="mb-4">{{ $section['content']['title'] ?? 'About Us' }}</h2>
                        <div class="about-text">
                            {!! nl2br(e($section['content']['content'] ?? $business->description)) !!}
                        </div>
                        
                        @if(isset($section['content']['features']) && is_array($section['content']['features']))
                            <div class="about-features mt-4">
                                @foreach($section['content']['features'] as $feature)
                                    <div class="feature-item d-flex align-items-start mb-3">
                                        @if(isset($feature['icon']))
                                            <i class="{{ $feature['icon'] }} text-primary me-3 mt-1"></i>
                                        @else
                                            <i class="fas fa-check text-primary me-3 mt-1"></i>
                                        @endif
                                        <div>
                                            <h6 class="mb-1">{{ $feature['title'] ?? '' }}</h6>
                                            <p class="text-muted mb-0">{{ $feature['description'] ?? '' }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                        
                        @if(isset($section['content']['cta_button']) && $section['content']['cta_button'])
                            <div class="mt-4">
                                <a href="{{ $section['content']['cta_url'] ?? '#contact' }}" class="btn btn-primary">
                                    {{ $section['content']['cta_text'] ?? 'Learn More' }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="col-lg-6">
                    @if(isset($section['content']['image']))
                        <div class="about-image">
                            <img src="{{ $section['content']['image'] }}" alt="{{ $section['content']['title'] ?? 'About Us' }}" class="img-fluid rounded shadow">
                        </div>
                    @else
                        <div class="about-placeholder bg-light rounded d-flex align-items-center justify-content-center" style="height: 400px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    @endif
                </div>
            @else
                <!-- Content Right, Image Left (default) -->
                <div class="col-lg-6">
                    @if(isset($section['content']['image']))
                        <div class="about-image">
                            <img src="{{ $section['content']['image'] }}" alt="{{ $section['content']['title'] ?? 'About Us' }}" class="img-fluid rounded shadow">
                        </div>
                    @else
                        <div class="about-placeholder bg-light rounded d-flex align-items-center justify-content-center" style="height: 400px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    @endif
                </div>
                <div class="col-lg-6">
                    <div class="about-content">
                        <h2 class="mb-4">{{ $section['content']['title'] ?? 'About Us' }}</h2>
                        <div class="about-text">
                            {!! nl2br(e($section['content']['content'] ?? $business->description)) !!}
                        </div>
                        
                        @if(isset($section['content']['features']) && is_array($section['content']['features']))
                            <div class="about-features mt-4">
                                @foreach($section['content']['features'] as $feature)
                                    <div class="feature-item d-flex align-items-start mb-3">
                                        @if(isset($feature['icon']))
                                            <i class="{{ $feature['icon'] }} text-primary me-3 mt-1"></i>
                                        @else
                                            <i class="fas fa-check text-primary me-3 mt-1"></i>
                                        @endif
                                        <div>
                                            <h6 class="mb-1">{{ $feature['title'] ?? '' }}</h6>
                                            <p class="text-muted mb-0">{{ $feature['description'] ?? '' }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                        
                        @if(isset($section['content']['cta_button']) && $section['content']['cta_button'])
                            <div class="mt-4">
                                <a href="{{ $section['content']['cta_url'] ?? '#contact' }}" class="btn btn-primary">
                                    {{ $section['content']['cta_text'] ?? 'Learn More' }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
        
        @if(isset($section['content']['stats']) && is_array($section['content']['stats']))
            <div class="row mt-5">
                <div class="col-12">
                    <div class="stats-section bg-primary text-white rounded p-4">
                        <div class="row text-center">
                            @foreach($section['content']['stats'] as $stat)
                                <div class="col-md-3 mb-3">
                                    <h3 class="display-6 fw-bold">{{ $stat['number'] ?? '0' }}</h3>
                                    <p class="mb-0">{{ $stat['label'] ?? '' }}</p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
.about-content h2 {
    color: var(--primary-color, #007bff);
    font-weight: 700;
}

.about-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
}

.about-image img {
    transition: transform 0.3s ease;
}

.about-image:hover img {
    transform: scale(1.05);
}

.feature-item {
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 8px;
}

.feature-item:hover {
    background-color: #f8f9fa;
    transform: translateX(10px);
}

.stats-section {
    background: linear-gradient(135deg, var(--primary-color, #007bff) 0%, var(--secondary-color, #6c757d) 100%) !important;
}

@media (max-width: 768px) {
    .about-content {
        text-align: center;
        margin-top: 30px;
    }
    
    .feature-item:hover {
        transform: none;
    }
}
</style>
