<!-- Contact Section -->
<section id="contact" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Get in Touch' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>
        
        <div class="row">
            <!-- Contact Information -->
            <div class="col-lg-6 mb-4">
                <div class="contact-info">
                    <h4 class="mb-4">Contact Information</h4>
                    
                    @if($business->phone)
                        <div class="contact-item d-flex align-items-center mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="mb-1">Phone</h6>
                                <a href="tel:{{ $business->phone }}" class="text-decoration-none">{{ $business->phone }}</a>
                            </div>
                        </div>
                    @endif
                    
                    @if($business->email)
                        <div class="contact-item d-flex align-items-center mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="mb-1">Email</h6>
                                <a href="mailto:{{ $business->email }}" class="text-decoration-none">{{ $business->email }}</a>
                            </div>
                        </div>
                    @endif
                    
                    @if($pageData['main_branch'])
                        <div class="contact-item d-flex align-items-start mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="mb-1">Address</h6>
                                <p class="mb-0">{{ $pageData['main_branch']->full_address }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if(isset($section['content']['show_hours']) && $section['content']['show_hours'] && $pageData['operating_hours']->isNotEmpty())
                        <div class="contact-item d-flex align-items-start mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="mb-1">Business Hours</h6>
                                <div class="hours-list">
                                    @foreach($pageData['operating_hours'] as $hour)
                                        <div class="hour-item d-flex justify-content-between">
                                            <span>{{ ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][$hour->day_of_week] }}:</span>
                                            <span>
                                                @if($hour->is_closed)
                                                    <span class="text-muted">Closed</span>
                                                @else
                                                    {{ $hour->open_time }} - {{ $hour->close_time }}
                                                @endif
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif
                    
                    @if($business->website)
                        <div class="contact-item d-flex align-items-center mb-3">
                            <div class="contact-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="contact-details">
                                <h6 class="mb-1">Website</h6>
                                <a href="{{ $business->website }}" target="_blank" class="text-decoration-none">{{ $business->website }}</a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Contact Form -->
            @if(isset($section['content']['show_contact_form']) && $section['content']['show_contact_form'])
                <div class="col-lg-6">
                    <div class="contact-form">
                        <h4 class="mb-4">Send us a Message</h4>
                        
                        @if(session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        
                        <form action="{{ route('landing-page.contact', $landingPage->custom_slug) }}" method="POST">
                            @csrf
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="subject" name="subject">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-paper-plane me-2"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>
            @endif
        </div>
        
        <!-- Map -->
        @if(isset($section['content']['show_map']) && $section['content']['show_map'] && $pageData['main_branch'] && $pageData['main_branch']->latitude && $pageData['main_branch']->longitude)
            <div class="row mt-5">
                <div class="col-12">
                    <div class="map-container">
                        <h4 class="mb-3">Find Us</h4>
                        <div class="map-embed">
                            <iframe 
                                src="https://www.google.com/maps/embed/v1/place?key=YOUR_GOOGLE_MAPS_API_KEY&q={{ $pageData['main_branch']->latitude }},{{ $pageData['main_branch']->longitude }}&zoom={{ $section['content']['map_zoom'] ?? 15 }}"
                                width="100%" 
                                height="400" 
                                style="border:0;" 
                                allowfullscreen="" 
                                loading="lazy">
                            </iframe>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
.contact-info {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: fit-content;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-item {
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color, #007bff);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-details h6 {
    color: var(--primary-color, #007bff);
    font-weight: 600;
}

.contact-details a {
    color: #666;
    transition: color 0.3s ease;
}

.contact-details a:hover {
    color: var(--primary-color, #007bff);
}

.hours-list {
    font-size: 0.9rem;
}

.hour-item {
    padding: 2px 0;
}

.form-control {
    border: 2px solid #f0f0f0;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.btn-primary {
    background: var(--primary-color, #007bff);
    border-color: var(--primary-color, #007bff);
    border-radius: 10px;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--secondary-color, #6c757d);
    border-color: var(--secondary-color, #6c757d);
    transform: translateY(-2px);
}

.map-container {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.map-embed {
    border-radius: 10px;
    overflow: hidden;
}

@media (max-width: 768px) {
    .contact-info,
    .contact-form {
        margin-bottom: 2rem;
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-icon {
        margin: 0 auto 1rem auto;
    }
}
</style>
