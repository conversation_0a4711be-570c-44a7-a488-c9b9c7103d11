<!-- Hero Section -->
<section id="home" class="hero-section d-flex align-items-center min-vh-100" 
         @if(isset($section['content']['background_image']))
         style="background-image: linear-gradient(rgba(0,0,0,{{ $section['content']['overlay_opacity'] ?? 0.5 }}), rgba(0,0,0,{{ $section['content']['overlay_opacity'] ?? 0.5 }})), url('{{ $section['content']['background_image'] }}'); background-size: cover; background-position: center;"
         @else
         style="background: linear-gradient(135deg, var(--primary-color, #007bff) 0%, var(--secondary-color, #6c757d) 100%);"
         @endif>
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 text-{{ $section['content']['text_alignment'] ?? 'center' }} text-white">
                <h1 class="display-4 fw-bold mb-4">
                    {{ $section['content']['title'] ?? $business->name }}
                </h1>
                
                @if(isset($section['content']['subtitle']))
                    <p class="lead mb-4">
                        {{ $section['content']['subtitle'] }}
                    </p>
                @endif
                
                <div class="hero-buttons">
                    @if(isset($section['content']['cta_text']) && isset($section['content']['cta_url']))
                        <a href="{{ $section['content']['cta_url'] }}" class="btn btn-primary btn-lg me-3 mb-2">
                            {{ $section['content']['cta_text'] }}
                        </a>
                    @endif
                    
                    @if(isset($section['content']['secondary_cta_text']) && isset($section['content']['secondary_cta_url']))
                        <a href="{{ $section['content']['secondary_cta_url'] }}" class="btn btn-outline-light btn-lg mb-2">
                            {{ $section['content']['secondary_cta_text'] }}
                        </a>
                    @endif
                </div>
            </div>
            
            @if(isset($section['content']['features']) && is_array($section['content']['features']))
                <div class="col-lg-6">
                    <div class="hero-features">
                        @foreach($section['content']['features'] as $feature)
                            <div class="feature-item d-flex align-items-center mb-3 text-white">
                                @if(isset($feature['icon']))
                                    <i class="{{ $feature['icon'] }} fa-2x me-3"></i>
                                @endif
                                <div>
                                    <h5 class="mb-1">{{ $feature['title'] ?? '' }}</h5>
                                    <p class="mb-0">{{ $feature['description'] ?? '' }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
        
        @if(isset($section['content']['stats']) && is_array($section['content']['stats']))
            <div class="row mt-5">
                @foreach($section['content']['stats'] as $stat)
                    <div class="col-md-3 text-center text-white mb-3">
                        <h3 class="display-6 fw-bold">{{ $stat['number'] ?? '0' }}</h3>
                        <p>{{ $stat['label'] ?? '' }}</p>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
    
    <!-- Scroll Down Arrow -->
    <div class="scroll-down text-center">
        <a href="#about" class="text-white">
            <i class="fas fa-chevron-down fa-2x"></i>
        </a>
    </div>
</section>

<style>
.hero-section {
    position: relative;
    padding-top: 80px; /* Account for fixed navbar */
}

.scroll-down {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

.hero-buttons .btn {
    transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.feature-item {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

@media (max-width: 768px) {
    .hero-section {
        text-align: center !important;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>
