<!-- Booking Section -->
<section id="booking" class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Book Your Appointment' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>

        @php
            $showCalendar = $section['content']['show_calendar'] ?? true;
            $showServices = $section['content']['show_services'] ?? true;
            $showStaff = $section['content']['show_staff'] ?? false;
            $requirePhone = $section['content']['require_phone'] ?? true;
            $requireEmail = $section['content']['require_email'] ?? true;
            $allowNotes = $section['content']['allow_notes'] ?? true;
            $confirmationMessage = $section['content']['confirmation_message'] ?? 'Thank you for your booking! We\'ll confirm your appointment shortly.';
            
            $services = $business->services()->where('is_active', true)->where('online_booking_enabled', true)->get();
            $staff = $business->staff()->where('is_active', true)->where('accepts_bookings', true)->get();
        @endphp

        @if($landingPage->booking_enabled && $business->online_booking_enabled)
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="booking-form-container">
                        <form id="bookingForm" class="booking-form">
                            @csrf
                            <input type="hidden" name="business_id" value="{{ $business->id }}">
                            
                            <!-- Step 1: Service Selection -->
                            @if($showServices && $services->count())
                                <div class="booking-step active" id="step-service">
                                    <h4 class="step-title">
                                        <span class="step-number">1</span>
                                        Choose Your Service
                                    </h4>
                                    
                                    <div class="services-selection">
                                        @foreach($services as $service)
                                            <div class="service-option" data-service-id="{{ $service->id }}">
                                                <div class="service-card">
                                                    <div class="service-info">
                                                        <h5 class="service-name">{{ $service->name }}</h5>
                                                        @if($service->description)
                                                            <p class="service-description">{{ Str::limit($service->description, 100) }}</p>
                                                        @endif
                                                        <div class="service-details">
                                                            @if($service->duration)
                                                                <span class="service-duration">
                                                                    <i class="fas fa-clock me-1"></i>
                                                                    {{ $service->duration }} min
                                                                </span>
                                                            @endif
                                                            @if($service->base_price)
                                                                <span class="service-price">
                                                                    <i class="fas fa-tag me-1"></i>
                                                                    {{ $business->currency ?? '$' }}{{ number_format($service->base_price, 2) }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="service-select">
                                                        <button type="button" class="btn btn-light">Select</button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    
                                    <div class="step-navigation">
                                        <button type="button" class="btn btn-light" id="nextToDateTime" disabled>
                                            Next: Choose Date & Time
                                        </button>
                                    </div>
                                </div>
                            @endif

                            <!-- Step 2: Date & Time Selection -->
                            <div class="booking-step" id="step-datetime">
                                <h4 class="step-title">
                                    <span class="step-number">{{ $showServices ? '2' : '1' }}</span>
                                    Select Date & Time
                                </h4>
                                
                                @if($showCalendar)
                                    <div class="calendar-container">
                                        <div id="bookingCalendar"></div>
                                    </div>
                                @endif
                                
                                <div class="time-slots-container">
                                    <h5>Available Times</h5>
                                    <div id="timeSlots" class="time-slots">
                                        <!-- Time slots will be loaded dynamically -->
                                    </div>
                                </div>
                                
                                <div class="step-navigation">
                                    @if($showServices)
                                        <button type="button" class="btn btn-outline-light me-2" id="backToService">
                                            Back
                                        </button>
                                    @endif
                                    <button type="button" class="btn btn-light" id="nextToDetails" disabled>
                                        Next: Your Details
                                    </button>
                                </div>
                            </div>

                            <!-- Step 3: Customer Details -->
                            <div class="booking-step" id="step-details">
                                <h4 class="step-title">
                                    <span class="step-number">{{ $showServices ? '3' : '2' }}</span>
                                    Your Details
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="firstName" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="firstName" name="first_name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="lastName" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="lastName" name="last_name" required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    @if($requireEmail)
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email *</label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>
                                    @endif
                                    @if($requirePhone)
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone *</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" required>
                                        </div>
                                    @endif
                                </div>
                                
                                @if($showStaff && $staff->count())
                                    <div class="mb-3">
                                        <label for="staffMember" class="form-label">Preferred Staff Member</label>
                                        <select class="form-select" id="staffMember" name="staff_id">
                                            <option value="">No preference</option>
                                            @foreach($staff as $member)
                                                <option value="{{ $member->id }}">{{ $member->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                @endif
                                
                                @if($allowNotes)
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">Special Requests or Notes</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                                  placeholder="Any special requests or information we should know..."></textarea>
                                    </div>
                                @endif
                                
                                <div class="step-navigation">
                                    <button type="button" class="btn btn-outline-light me-2" id="backToDateTime">
                                        Back
                                    </button>
                                    <button type="button" class="btn btn-light" id="nextToConfirm">
                                        Review Booking
                                    </button>
                                </div>
                            </div>

                            <!-- Step 4: Confirmation -->
                            <div class="booking-step" id="step-confirm">
                                <h4 class="step-title">
                                    <span class="step-number">{{ $showServices ? '4' : '3' }}</span>
                                    Confirm Your Booking
                                </h4>
                                
                                <div class="booking-summary">
                                    <div class="summary-card">
                                        <h5>Booking Summary</h5>
                                        <div id="bookingSummary">
                                            <!-- Summary will be populated dynamically -->
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="terms-acceptance">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="acceptTerms" required>
                                        <label class="form-check-label" for="acceptTerms">
                                            I agree to the <a href="#" class="text-light">terms and conditions</a> and 
                                            <a href="#" class="text-light">cancellation policy</a>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="step-navigation">
                                    <button type="button" class="btn btn-outline-light me-2" id="backToDetails">
                                        Back
                                    </button>
                                    <button type="submit" class="btn btn-light" id="confirmBooking">
                                        <i class="fas fa-check me-2"></i>
                                        Confirm Booking
                                    </button>
                                </div>
                            </div>

                            <!-- Success Message -->
                            <div class="booking-step" id="step-success" style="display: none;">
                                <div class="text-center">
                                    <i class="fas fa-check-circle fa-4x mb-4"></i>
                                    <h3>Booking Confirmed!</h3>
                                    <p class="lead">{{ $confirmationMessage }}</p>
                                    <div class="booking-reference">
                                        <p>Your booking reference: <strong id="bookingReference"></strong></p>
                                    </div>
                                    <button type="button" class="btn btn-light" onclick="location.reload()">
                                        Book Another Appointment
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @else
            <!-- Booking Disabled -->
            <div class="row">
                <div class="col-12 text-center">
                    <div class="booking-disabled">
                        <i class="fas fa-calendar-times fa-4x mb-4"></i>
                        <h4>Online Booking Currently Unavailable</h4>
                        <p class="lead">Please contact us directly to schedule your appointment.</p>
                        <div class="contact-options mt-4">
                            @if($business->phone)
                                <a href="tel:{{ $business->phone }}" class="btn btn-light btn-lg me-3 mb-2">
                                    <i class="fas fa-phone me-2"></i>
                                    Call {{ $business->phone }}
                                </a>
                            @endif
                            @if($business->email)
                                <a href="mailto:{{ $business->email }}" class="btn btn-outline-light btn-lg mb-2">
                                    <i class="fas fa-envelope me-2"></i>
                                    Email Us
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
.booking-form-container {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255,255,255,0.2);
}

.booking-step {
    display: none;
}

.booking-step.active {
    display: block;
}

.step-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    color: white;
}

.step-number {
    background: white;
    color: var(--primary-color, #007bff);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
}

.service-option {
    margin-bottom: 15px;
    cursor: pointer;
}

.service-card {
    background: rgba(255,255,255,0.1);
    border: 2px solid transparent;
    border-radius: 15px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.service-card:hover,
.service-card.selected {
    background: rgba(255,255,255,0.2);
    border-color: white;
}

.service-name {
    color: white;
    margin-bottom: 10px;
}

.service-description {
    color: rgba(255,255,255,0.8);
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.service-details {
    display: flex;
    gap: 20px;
    color: rgba(255,255,255,0.9);
    font-size: 0.9rem;
}

.calendar-container {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.time-slots {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.time-slot {
    background: rgba(255,255,255,0.1);
    border: 2px solid transparent;
    border-radius: 10px;
    padding: 12px;
    text-align: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.time-slot:hover,
.time-slot.selected {
    background: rgba(255,255,255,0.2);
    border-color: white;
}

.time-slot.unavailable {
    opacity: 0.5;
    cursor: not-allowed;
}

.form-label {
    color: white;
    font-weight: 500;
}

.form-control,
.form-select {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 10px;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.form-control:focus,
.form-select:focus {
    background: rgba(255,255,255,0.2);
    border-color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
    color: white;
}

.summary-card {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
}

.summary-card h5 {
    color: white;
    margin-bottom: 20px;
}

.form-check-label {
    color: rgba(255,255,255,0.9);
}

.form-check-label a {
    color: white;
    text-decoration: underline;
}

.step-navigation {
    margin-top: 30px;
    text-align: center;
}

.booking-disabled {
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    padding: 60px 40px;
}

@media (max-width: 768px) {
    .booking-form-container {
        padding: 30px 20px;
    }
    
    .service-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .service-details {
        justify-content: center;
    }
    
    .time-slots {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .step-navigation .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const bookingForm = document.getElementById('bookingForm');
    const steps = document.querySelectorAll('.booking-step');
    let currentStep = 0;
    let selectedService = null;
    let selectedDateTime = null;
    
    // Initialize first step
    if (steps.length > 0) {
        steps[0].classList.add('active');
    }
    
    // Service selection
    document.querySelectorAll('.service-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove previous selection
            document.querySelectorAll('.service-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Select current service
            this.querySelector('.service-card').classList.add('selected');
            selectedService = this.getAttribute('data-service-id');
            
            // Enable next button
            document.getElementById('nextToDateTime').disabled = false;
        });
    });
    
    // Step navigation
    document.getElementById('nextToDateTime')?.addEventListener('click', () => goToStep(1));
    document.getElementById('backToService')?.addEventListener('click', () => goToStep(0));
    document.getElementById('nextToDetails')?.addEventListener('click', () => goToStep(2));
    document.getElementById('backToDateTime')?.addEventListener('click', () => goToStep(1));
    document.getElementById('nextToConfirm')?.addEventListener('click', () => goToStep(3));
    document.getElementById('backToDetails')?.addEventListener('click', () => goToStep(2));
    
    function goToStep(stepIndex) {
        steps.forEach(step => step.classList.remove('active'));
        steps[stepIndex].classList.add('active');
        currentStep = stepIndex;
        
        if (stepIndex === 3) {
            updateBookingSummary();
        }
    }
    
    function updateBookingSummary() {
        // This would be populated with actual booking data
        const summary = document.getElementById('bookingSummary');
        summary.innerHTML = `
            <div class="summary-item">
                <strong>Service:</strong> Selected Service
            </div>
            <div class="summary-item">
                <strong>Date & Time:</strong> Selected Date & Time
            </div>
            <div class="summary-item">
                <strong>Customer:</strong> ${document.getElementById('firstName').value} ${document.getElementById('lastName').value}
            </div>
        `;
    }
    
    // Form submission
    bookingForm?.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show success step
        steps.forEach(step => step.classList.remove('active'));
        document.getElementById('step-success').style.display = 'block';
        
        // Generate booking reference
        document.getElementById('bookingReference').textContent = 'BK' + Date.now().toString().slice(-6);
    });
});
</script>
