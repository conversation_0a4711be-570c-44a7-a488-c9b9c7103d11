<!-- Call to Action Section -->
@php
    $backgroundColor = $section['content']['background_color'] ?? '#007bff';
    $textColor = $section['content']['text_color'] ?? '#ffffff';
    $backgroundImage = $section['content']['background_image'] ?? null;
    $textAlignment = $section['content']['text_alignment'] ?? 'center';
    $sectionPadding = $section['content']['section_padding'] ?? 'large';
    $fullWidth = $section['content']['full_width'] ?? false;
    $enableAnimation = $section['content']['enable_animation'] ?? true;
    
    $paddingClass = match($sectionPadding) {
        'small' => 'py-4',
        'medium' => 'py-5',
        'large' => 'py-6',
        default => 'py-5'
    };
@endphp

<section id="cta" class="{{ $paddingClass }} position-relative overflow-hidden" 
         style="background-color: {{ $backgroundColor }}; color: {{ $textColor }};">
    
    @if($backgroundImage)
        <div class="position-absolute top-0 start-0 w-100 h-100" 
             style="background-image: url('{{ $backgroundImage }}'); background-size: cover; background-position: center; opacity: 0.3; z-index: 1;"></div>
    @endif
    
    <div class="container position-relative" style="z-index: 2;">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-{{ $textAlignment }}">
                <div class="cta-content {{ $enableAnimation ? 'animate-on-scroll' : '' }}">
                    <h2 class="cta-title mb-4" style="color: {{ $textColor }};">
                        {{ $section['content']['title'] ?? 'Ready to Get Started?' }}
                    </h2>
                    
                    @if(isset($section['content']['subtitle']))
                        <p class="cta-subtitle lead mb-4" style="color: {{ $textColor }};">
                            {{ $section['content']['subtitle'] }}
                        </p>
                    @endif
                    
                    <div class="cta-buttons">
                        @if(isset($section['content']['button_text']) && isset($section['content']['button_url']))
                            <a href="{{ $section['content']['button_url'] }}" 
                               class="btn btn-cta-primary btn-lg me-3 mb-3 {{ $enableAnimation ? 'btn-animate' : '' }}">
                                {{ $section['content']['button_text'] }}
                            </a>
                        @endif
                        
                        @if(isset($section['content']['secondary_button_text']) && isset($section['content']['secondary_button_url']))
                            <a href="{{ $section['content']['secondary_button_url'] }}" 
                               class="btn btn-cta-secondary btn-lg mb-3 {{ $enableAnimation ? 'btn-animate' : '' }}">
                                {{ $section['content']['secondary_button_text'] }}
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Decorative elements -->
    <div class="cta-decoration">
        <div class="decoration-circle decoration-1"></div>
        <div class="decoration-circle decoration-2"></div>
        <div class="decoration-circle decoration-3"></div>
    </div>
</section>

<style>
.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
}

.cta-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.btn-cta-primary {
    background-color: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: {{ $textColor }};
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-cta-primary:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: {{ $textColor }};
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-cta-secondary {
    background-color: transparent;
    border: 2px solid rgba(255, 255, 255, 0.5);
    color: {{ $textColor }};
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-cta-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.7);
    color: {{ $textColor }};
    transform: translateY(-2px);
}

.btn-animate {
    position: relative;
    overflow: hidden;
}

.btn-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animate:hover::before {
    left: 100%;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cta-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.decoration-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.decoration-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.decoration-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

.py-6 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .cta-title {
        font-size: 2rem;
    }
    
    .cta-subtitle {
        font-size: 1.1rem;
    }
    
    .btn-cta-primary,
    .btn-cta-secondary {
        padding: 10px 25px;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        display: block;
        width: 100%;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .decoration-circle {
        display: none;
    }
}

@media (max-width: 576px) {
    .cta-title {
        font-size: 1.75rem;
    }
    
    .me-3 {
        margin-right: 0 !important;
    }
}

/* Dark background adjustments */
@if(in_array($backgroundColor, ['#000000', '#212529', '#343a40']))
    .btn-cta-primary {
        background-color: rgba(255, 255, 255, 0.9);
        color: #333;
    }
    
    .btn-cta-primary:hover {
        background-color: #fff;
        color: #333;
    }
@endif
</style>

<!-- Intersection Observer for animations -->
@if($enableAnimation)
<script>
document.addEventListener('DOMContentLoaded', function() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, observerOptions);
    
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach(el => {
        el.style.animationPlayState = 'paused';
        observer.observe(el);
    });
});
</script>
@endif
