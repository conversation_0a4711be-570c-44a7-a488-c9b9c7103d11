<!-- Testimonials Section -->
<section id="testimonials" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'What Our Customers Say' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>

        @php
            $testimonials = $pageData['testimonials'] ?? collect();
            $layout = $section['content']['layout'] ?? 'carousel';
            $showRatings = $section['content']['show_ratings'] ?? true;
            $showPhotos = $section['content']['show_photos'] ?? true;
        @endphp

        @if($testimonials->isNotEmpty())
            @if($layout === 'carousel')
                <!-- Carousel Layout -->
                <div id="testimonialsCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        @foreach($testimonials->chunk(3) as $chunkIndex => $testimonialChunk)
                            <div class="carousel-item {{ $chunkIndex === 0 ? 'active' : '' }}">
                                <div class="row">
                                    @foreach($testimonialChunk as $testimonial)
                                        <div class="col-lg-4 mb-4">
                                            <div class="testimonial-card h-100">
                                                <div class="card-body text-center">
                                                    @if($showPhotos && $testimonial->customer_photo)
                                                        <img src="{{ $testimonial->customer_photo }}" 
                                                             alt="{{ $testimonial->customer_name }}" 
                                                             class="testimonial-photo mb-3">
                                                    @else
                                                        <div class="testimonial-photo-placeholder mb-3">
                                                            <i class="fas fa-user-circle fa-3x text-muted"></i>
                                                        </div>
                                                    @endif
                                                    
                                                    @if($showRatings && $testimonial->rating)
                                                        <div class="rating mb-3">
                                                            @for($i = 1; $i <= 5; $i++)
                                                                <i class="fas fa-star {{ $i <= $testimonial->rating ? 'text-warning' : 'text-muted' }}"></i>
                                                            @endfor
                                                        </div>
                                                    @endif
                                                    
                                                    <blockquote class="blockquote mb-3">
                                                        <p class="mb-0">"{{ $testimonial->review_text }}"</p>
                                                    </blockquote>
                                                    
                                                    <footer class="blockquote-footer">
                                                        <strong>{{ $testimonial->customer_name }}</strong>
                                                        @if($testimonial->service_name)
                                                            <br><small class="text-muted">{{ $testimonial->service_name }}</small>
                                                        @endif
                                                    </footer>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    @if($testimonials->count() > 3)
                        <button class="carousel-control-prev" type="button" data-bs-target="#testimonialsCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#testimonialsCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                    @endif
                </div>
            @else
                <!-- Grid Layout -->
                <div class="row">
                    @foreach($testimonials->take(6) as $testimonial)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="testimonial-card h-100">
                                <div class="card-body text-center">
                                    @if($showPhotos && $testimonial->customer_photo)
                                        <img src="{{ $testimonial->customer_photo }}" 
                                             alt="{{ $testimonial->customer_name }}" 
                                             class="testimonial-photo mb-3">
                                    @else
                                        <div class="testimonial-photo-placeholder mb-3">
                                            <i class="fas fa-user-circle fa-3x text-muted"></i>
                                        </div>
                                    @endif
                                    
                                    @if($showRatings && $testimonial->rating)
                                        <div class="rating mb-3">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $testimonial->rating ? 'text-warning' : 'text-muted' }}"></i>
                                            @endfor
                                        </div>
                                    @endif
                                    
                                    <blockquote class="blockquote mb-3">
                                        <p class="mb-0">"{{ $testimonial->review_text }}"</p>
                                    </blockquote>
                                    
                                    <footer class="blockquote-footer">
                                        <strong>{{ $testimonial->customer_name }}</strong>
                                        @if($testimonial->service_name)
                                            <br><small class="text-muted">{{ $testimonial->service_name }}</small>
                                        @endif
                                    </footer>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        @else
            <!-- No testimonials available -->
            <div class="row">
                <div class="col-12 text-center">
                    <div class="no-testimonials py-5">
                        <i class="fas fa-comments fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Customer Reviews Coming Soon</h4>
                        <p class="text-muted">We're working on gathering customer feedback. Check back soon!</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Overall Rating Summary -->
        @if($testimonials->isNotEmpty() && $showRatings)
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <div class="rating-summary">
                        <h4>Overall Rating</h4>
                        @php
                            $averageRating = $testimonials->avg('rating');
                            $totalReviews = $testimonials->count();
                        @endphp
                        <div class="average-rating mb-2">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= round($averageRating) ? 'text-warning' : 'text-muted' }} fa-lg"></i>
                            @endfor
                        </div>
                        <p class="text-muted">
                            {{ number_format($averageRating, 1) }} out of 5 stars ({{ $totalReviews }} {{ Str::plural('review', $totalReviews) }})
                        </p>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
.testimonial-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: none;
    padding: 20px;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.testimonial-photo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color, #007bff);
}

.testimonial-photo-placeholder {
    display: inline-block;
}

.rating {
    font-size: 1.1rem;
}

.blockquote {
    font-style: italic;
    color: #555;
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    color: var(--primary-color, #007bff);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: var(--primary-color, #007bff);
    border-radius: 50%;
    width: 40px;
    height: 40px;
}

.rating-summary {
    background: var(--bg-light, #f8f9fa);
    padding: 30px;
    border-radius: 15px;
    display: inline-block;
}

.average-rating {
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .testimonial-card {
        margin-bottom: 20px;
    }
    
    .carousel-control-prev,
    .carousel-control-next {
        display: none;
    }
}
</style>
