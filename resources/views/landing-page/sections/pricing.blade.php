<!-- Pricing Section -->
<section id="pricing" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="mb-3">{{ $section['content']['title'] ?? 'Our Pricing' }}</h2>
                @if(isset($section['content']['subtitle']))
                    <p class="lead text-muted">{{ $section['content']['subtitle'] }}</p>
                @endif
            </div>
        </div>

        @php
            $packages = $section['content']['packages'] ?? [];
            $layout = $section['content']['layout'] ?? 'cards';
            $columns = $section['content']['columns'] ?? 3;
            $showFeatures = $section['content']['show_features'] ?? true;
            $showPopularBadge = $section['content']['show_popular_badge'] ?? true;
            $showBookingButton = $section['content']['show_booking_button'] ?? true;
        @endphp

        @if(!empty($packages))
            @if($layout === 'cards')
                <div class="row justify-content-center">
                    @foreach($packages as $package)
                        <div class="col-lg-{{ 12 / $columns }} col-md-6 mb-4">
                            <div class="pricing-card h-100 {{ isset($package['popular']) && $package['popular'] ? 'popular' : '' }}">
                                @if($showPopularBadge && isset($package['popular']) && $package['popular'])
                                    <div class="popular-badge">
                                        <span class="badge bg-primary">Most Popular</span>
                                    </div>
                                @endif
                                
                                <div class="card-header text-center">
                                    <h4 class="package-name">{{ $package['name'] ?? 'Package' }}</h4>
                                    
                                    @if(isset($package['description']))
                                        <p class="package-description text-muted">{{ $package['description'] }}</p>
                                    @endif
                                </div>
                                
                                <div class="card-body text-center">
                                    <div class="price-display mb-4">
                                        <span class="price">{{ $package['price'] ?? '$0' }}</span>
                                        @if(isset($package['period']))
                                            <span class="period text-muted">{{ $package['period'] }}</span>
                                        @endif
                                    </div>
                                    
                                    @if($showFeatures && isset($package['features']))
                                        <ul class="features-list list-unstyled mb-4">
                                            @php
                                                $features = is_array($package['features']) ? $package['features'] : explode("\n", $package['features']);
                                            @endphp
                                            @foreach($features as $feature)
                                                @if(trim($feature))
                                                    <li class="feature-item mb-2">
                                                        <i class="fas fa-check text-success me-2"></i>
                                                        {{ trim($feature) }}
                                                    </li>
                                                @endif
                                            @endforeach
                                        </ul>
                                    @endif
                                </div>
                                
                                <div class="card-footer text-center">
                                    @if($showBookingButton)
                                        <a href="{{ $package['button_url'] ?? '#booking' }}" class="btn btn-primary btn-lg w-100">
                                            {{ $package['button_text'] ?? 'Choose Plan' }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            
            @elseif($layout === 'table')
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="pricing-table">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>Features</th>
                                            @foreach($packages as $package)
                                                <th class="text-center">
                                                    <div class="package-header">
                                                        <h5>{{ $package['name'] ?? 'Package' }}</h5>
                                                        <div class="price-display">
                                                            <span class="price">{{ $package['price'] ?? '$0' }}</span>
                                                            @if(isset($package['period']))
                                                                <br><small class="text-muted">{{ $package['period'] }}</small>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </th>
                                            @endforeach
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                            $allFeatures = [];
                                            foreach($packages as $package) {
                                                if (isset($package['features'])) {
                                                    $features = is_array($package['features']) ? $package['features'] : explode("\n", $package['features']);
                                                    $allFeatures = array_merge($allFeatures, $features);
                                                }
                                            }
                                            $allFeatures = array_unique(array_filter(array_map('trim', $allFeatures)));
                                        @endphp
                                        
                                        @foreach($allFeatures as $feature)
                                            <tr>
                                                <td>{{ $feature }}</td>
                                                @foreach($packages as $package)
                                                    @php
                                                        $packageFeatures = isset($package['features']) 
                                                            ? (is_array($package['features']) ? $package['features'] : explode("\n", $package['features']))
                                                            : [];
                                                        $hasFeature = in_array($feature, array_map('trim', $packageFeatures));
                                                    @endphp
                                                    <td class="text-center">
                                                        @if($hasFeature)
                                                            <i class="fas fa-check text-success"></i>
                                                        @else
                                                            <i class="fas fa-times text-muted"></i>
                                                        @endif
                                                    </td>
                                                @endforeach
                                            </tr>
                                        @endforeach
                                        
                                        @if($showBookingButton)
                                            <tr>
                                                <td><strong>Choose Plan</strong></td>
                                                @foreach($packages as $package)
                                                    <td class="text-center">
                                                        <a href="{{ $package['button_url'] ?? '#booking' }}" class="btn btn-primary">
                                                            {{ $package['button_text'] ?? 'Choose Plan' }}
                                                        </a>
                                                    </td>
                                                @endforeach
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            
            @else
                <!-- List layout -->
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        @foreach($packages as $package)
                            <div class="pricing-item mb-4">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="package-info">
                                                <h5 class="package-name">{{ $package['name'] ?? 'Package' }}</h5>
                                                
                                                @if(isset($package['description']))
                                                    <p class="package-description text-muted">{{ $package['description'] }}</p>
                                                @endif
                                                
                                                @if($showFeatures && isset($package['features']))
                                                    <ul class="features-list list-unstyled">
                                                        @php
                                                            $features = is_array($package['features']) ? $package['features'] : explode("\n", $package['features']);
                                                        @endphp
                                                        @foreach($features as $feature)
                                                            @if(trim($feature))
                                                                <li class="feature-item">
                                                                    <i class="fas fa-check text-success me-2"></i>
                                                                    {{ trim($feature) }}
                                                                </li>
                                                            @endif
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                            
                                            <div class="package-pricing text-end">
                                                <div class="price-display mb-3">
                                                    <span class="price">{{ $package['price'] ?? '$0' }}</span>
                                                    @if(isset($package['period']))
                                                        <br><small class="text-muted">{{ $package['period'] }}</small>
                                                    @endif
                                                </div>
                                                
                                                @if($showBookingButton)
                                                    <a href="{{ $package['button_url'] ?? '#booking' }}" class="btn btn-primary">
                                                        {{ $package['button_text'] ?? 'Choose Plan' }}
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        @else
            <!-- Default pricing when no packages are configured -->
            <div class="row justify-content-center">
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Pricing information coming soon!</strong><br>
                        We're preparing our pricing packages. Please contact us for current rates.
                    </div>
                    <a href="#contact" class="btn btn-primary">
                        <i class="fas fa-phone me-1"></i>
                        Contact for Pricing
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<style>
.pricing-card {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.pricing-card.popular {
    border-color: #007bff;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: 20px;
    right: -30px;
    transform: rotate(45deg);
    width: 120px;
    text-align: center;
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
}

.period {
    font-size: 1rem;
    display: block;
}

.features-list .feature-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.features-list .feature-item:last-child {
    border-bottom: none;
}

.package-name {
    font-weight: 600;
    color: #333;
}

.pricing-table .price {
    font-size: 1.5rem;
    font-weight: 600;
}

@media (max-width: 768px) {
    .pricing-card.popular {
        transform: none;
    }
    
    .price {
        font-size: 2rem;
    }
}
</style>
