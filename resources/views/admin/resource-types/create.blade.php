@extends('admin.layouts.app')

@section('title', 'Create Resource Type')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Create Resource Type</h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.resource-types.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Resource Types
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Resource Type Information</h3>
                </div>
                <form action="{{ route('admin.resource-types.store') }}" method="POST">
                    @csrf
                    <div class="card-body">
                        <div class="form-group">
                            <label for="name">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Provide a brief description of this resource type.</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="icon">Icon Class</label>
                                    <input type="text" class="form-control @error('icon') is-invalid @enderror" 
                                           id="icon" name="icon" value="{{ old('icon') }}" placeholder="fas fa-tools">
                                    @error('icon')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">FontAwesome icon class (e.g., fas fa-tools)</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="color">Color</label>
                                    <input type="color" class="form-control @error('color') is-invalid @enderror" 
                                           id="color" name="color" value="{{ old('color', '#007bff') }}">
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Color for this resource type</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Create Resource Type
                        </button>
                        <a href="{{ route('admin.resource-types.index') }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Preview</h3>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div id="icon-preview" style="font-size: 3em; margin-bottom: 15px; color: #007bff;">
                            <i class="fas fa-cube"></i>
                        </div>
                        <h5 id="name-preview">Resource Type Name</h5>
                        <p id="description-preview" class="text-muted">Resource type description will appear here.</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Common Icons</h3>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4 mb-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm icon-select" data-icon="fas fa-door-open">
                                <i class="fas fa-door-open"></i><br><small>Room</small>
                            </button>
                        </div>
                        <div class="col-4 mb-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm icon-select" data-icon="fas fa-tools">
                                <i class="fas fa-tools"></i><br><small>Equipment</small>
                            </button>
                        </div>
                        <div class="col-4 mb-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm icon-select" data-icon="fas fa-user-md">
                                <i class="fas fa-user-md"></i><br><small>Staff</small>
                            </button>
                        </div>
                        <div class="col-4 mb-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm icon-select" data-icon="fas fa-car">
                                <i class="fas fa-car"></i><br><small>Vehicle</small>
                            </button>
                        </div>
                        <div class="col-4 mb-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm icon-select" data-icon="fas fa-table">
                                <i class="fas fa-table"></i><br><small>Table</small>
                            </button>
                        </div>
                        <div class="col-4 mb-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm icon-select" data-icon="fas fa-bed">
                                <i class="fas fa-bed"></i><br><small>Bed</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Update preview when form fields change
    $('#name').on('input', function() {
        var name = $(this).val() || 'Resource Type Name';
        $('#name-preview').text(name);
    });
    
    $('#description').on('input', function() {
        var description = $(this).val() || 'Resource type description will appear here.';
        $('#description-preview').text(description);
    });
    
    $('#icon').on('input', function() {
        var icon = $(this).val() || 'fas fa-cube';
        var color = $('#color').val() || '#007bff';
        $('#icon-preview').html('<i class="' + icon + '"></i>').css('color', color);
    });
    
    $('#color').on('input', function() {
        var color = $(this).val() || '#007bff';
        $('#icon-preview').css('color', color);
    });
    
    // Icon selection buttons
    $('.icon-select').click(function() {
        var icon = $(this).data('icon');
        $('#icon').val(icon).trigger('input');
    });
});
</script>
@stop
