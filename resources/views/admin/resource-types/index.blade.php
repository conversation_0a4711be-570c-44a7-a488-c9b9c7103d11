@extends('admin.layouts.app')

@section('title', 'Resource Types')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Resource Types</h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('manage resources')
                    <a href="{{ route('admin.resource-types.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add Resource Type
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">All Resource Types</h3>
            <div class="card-tools">
                <form method="GET" action="{{ route('admin.resource-types.index') }}" class="form-inline">
                    <div class="input-group input-group-sm" style="width: 250px;">
                        <input type="text" name="search" class="form-control" placeholder="Search resource types..." value="{{ request('search') }}">
                        <div class="input-group-append">
                            <button type="submit" class="btn btn-default">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card-body table-responsive p-0">
            @if($resourceTypes->count() > 0)
                <table class="table table-hover text-nowrap">
                    <thead>
                        <tr>
                            <th>Icon</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Resources Count</th>
                            <th>Color</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($resourceTypes as $resourceType)
                            <tr>
                                <td>
                                    @if($resourceType->icon)
                                        <i class="{{ $resourceType->icon }}" style="color: {{ $resourceType->color ?? '#6c757d' }}; font-size: 1.2em;"></i>
                                    @else
                                        <i class="fas fa-cube" style="color: #6c757d; font-size: 1.2em;"></i>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.resource-types.show', $resourceType) }}" class="text-decoration-none">
                                        <strong>{{ $resourceType->name }}</strong>
                                    </a>
                                </td>
                                <td>
                                    <span class="text-muted">{{ Str::limit($resourceType->description, 50) }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ $resourceType->resources_count }}</span>
                                </td>
                                <td>
                                    @if($resourceType->color)
                                        <span class="badge" style="background-color: {{ $resourceType->color }}; color: white;">
                                            {{ $resourceType->color }}
                                        </span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <small class="text-muted">{{ $resourceType->created_at->format('M d, Y') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.resource-types.show', $resourceType) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @can('manage resources')
                                            <a href="{{ route('admin.resource-types.edit', $resourceType) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($resourceType->resources_count == 0)
                                                <form action="{{ route('admin.resource-types.destroy', $resourceType) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this resource type?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Resource Types Found</h5>
                    <p class="text-muted">
                        @if(request('search'))
                            No resource types match your search criteria.
                            <a href="{{ route('admin.resource-types.index') }}">Clear search</a>
                        @else
                            Get started by creating your first resource type.
                        @endif
                    </p>
                    @can('manage resources')
                        @if(!request('search'))
                            <a href="{{ route('admin.resource-types.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-2"></i>
                                Add Resource Type
                            </a>
                        @endif
                    @endcan
                </div>
            @endif
        </div>
        
        @if($resourceTypes->hasPages())
            <div class="card-footer">
                {{ $resourceTypes->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
@stop
