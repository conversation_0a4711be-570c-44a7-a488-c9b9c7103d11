@extends('admin.layouts.app')

@section('title', 'Resource Type Details')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Resource Type Details</h1>
            <p class="text-muted">{{ $resourceType->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('manage resources')
                    <a href="{{ route('admin.resource-types.edit', $resourceType) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Resource Type
                    </a>
                @endcan
                <a href="{{ route('admin.resource-types.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Resource Types
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        @if($resourceType->icon)
                            <i class="{{ $resourceType->icon }}" style="color: {{ $resourceType->color ?? '#6c757d' }}; margin-right: 8px;"></i>
                        @endif
                        {{ $resourceType->name }}
                    </h3>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="30%">ID:</th>
                            <td>{{ $resourceType->id }}</td>
                        </tr>
                        <tr>
                            <th>Name:</th>
                            <td>{{ $resourceType->name }}</td>
                        </tr>
                        <tr>
                            <th>Slug:</th>
                            <td><code>{{ $resourceType->slug }}</code></td>
                        </tr>
                        <tr>
                            <th>Description:</th>
                            <td>{{ $resourceType->description ?: 'No description provided' }}</td>
                        </tr>
                        <tr>
                            <th>Icon:</th>
                            <td>
                                @if($resourceType->icon)
                                    <i class="{{ $resourceType->icon }}" style="color: {{ $resourceType->color ?? '#6c757d' }}; font-size: 1.5em; margin-right: 8px;"></i>
                                    <code>{{ $resourceType->icon }}</code>
                                @else
                                    <span class="text-muted">No icon set</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Color:</th>
                            <td>
                                @if($resourceType->color)
                                    <span class="badge" style="background-color: {{ $resourceType->color }}; color: white; margin-right: 8px;">
                                        {{ $resourceType->color }}
                                    </span>
                                    <code>{{ $resourceType->color }}</code>
                                @else
                                    <span class="text-muted">No color set</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Resources Count:</th>
                            <td>
                                <span class="badge badge-info">{{ $resourceType->resources->count() }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>{{ $resourceType->created_at->format('F j, Y \a\t g:i A') }}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>{{ $resourceType->updated_at->format('F j, Y \a\t g:i A') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Associated Resources</h3>
                    <div class="card-tools">
                        <span class="badge badge-info">{{ $resourceType->resources->count() }}</span>
                    </div>
                </div>
                <div class="card-body">
                    @if($resourceType->resources->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($resourceType->resources as $resource)
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">
                                            <a href="{{ route('admin.resources.show', $resource) }}" class="text-decoration-none">
                                                {{ $resource->name }}
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            {{ $resource->business->name }}
                                            @if($resource->branch)
                                                - {{ $resource->branch->name }}
                                            @endif
                                        </small>
                                    </div>
                                    <div>
                                        <span class="badge badge-{{ $resource->is_active ? 'success' : 'secondary' }}">
                                            {{ $resource->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-tools fa-2x text-muted mb-3"></i>
                            <p class="text-muted">No resources are using this resource type yet.</p>
                            @can('manage resources')
                                <a href="{{ route('admin.resources.create') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-2"></i>
                                    Create Resource
                                </a>
                            @endcan
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@stop
