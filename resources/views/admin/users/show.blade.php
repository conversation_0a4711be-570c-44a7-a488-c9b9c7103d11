@extends('admin.layouts.app')

@section('title', 'View User')

@section('header')
    <h1>User Details</h1>
@stop

@section('main-content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">{{ $user->name }}</h3>
        <div class="card-tools">
            @can('manage users')
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning btn-sm">
                <i class="fas fa-edit"></i> Edit User
            </a>
            @endcan
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <th width="30%">ID:</th>
                        <td>{{ $user->id }}</td>
                    </tr>
                    <tr>
                        <th>Name:</th>
                        <td>{{ $user->name }}</td>
                    </tr>
                    <tr>
                        <th>Email:</th>
                        <td>{{ $user->email }}</td>
                    </tr>
                    <tr>
                        <th>Phone:</th>
                        <td>{{ $user->phone ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <th>Email Verified:</th>
                        <td>
                            @if($user->email_verified_at)
                                <span class="badge badge-success">Verified</span>
                                <small class="text-muted d-block">{{ $user->email_verified_at->format('M d, Y H:i') }}</small>
                            @else
                                <span class="badge badge-warning">Not Verified</span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <th width="30%">Created At:</th>
                        <td>{{ $user->created_at->format('M d, Y H:i') }}</td>
                    </tr>
                    <tr>
                        <th>Updated At:</th>
                        <td>{{ $user->updated_at->format('M d, Y H:i') }}</td>
                    </tr>
                    <tr>
                        <th>Roles:</th>
                        <td>
                            @forelse($user->roles as $role)
                                <span class="badge badge-primary">{{ $role->name }}</span>
                            @empty
                                <span class="text-muted">No roles assigned</span>
                            @endforelse
                        </td>
                    </tr>
                    <tr>
                        <th>Permissions:</th>
                        <td>
                            @php
                                $permissions = $user->getAllPermissions();
                            @endphp
                            @forelse($permissions as $permission)
                                <span class="badge badge-info">{{ $permission->name }}</span>
                            @empty
                                <span class="text-muted">No permissions</span>
                            @endforelse
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="card-footer">
        @can('manage users')
        <div class="btn-group">
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit User
            </a>
            @if($user->id !== auth()->id())
            <form action="{{ route('admin.users.destroy', $user) }}" method="POST" style="display: inline;" 
                  onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Delete User
                </button>
            </form>
            @endif
        </div>
        @endcan
    </div>
</div>
@stop
