@extends('adminlte::page')

@section('title', 'Dashboard')

@section('content_header')
    <h1>
        Dashboard
        <small class="text-muted">Admin Panel</small>
    </h1>
    <p class="text-muted">
        Welcome back, {{ $user->name ?? 'Admin' }}!
        @if($metrics['today_bookings'] > 0)
            You have {{ $metrics['today_bookings'] }} booking{{ $metrics['today_bookings'] > 1 ? 's' : '' }} scheduled for today.
        @else
            No bookings scheduled for today.
        @endif
    </p>
@stop

@section('content')
    <!-- Main Statistics -->
    <div class="row dashboard-stats">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $metrics['total_businesses'] ?? 0 }}</h3>
                    <p>Total Businesses</p>
                </div>
                <div class="icon">
                    <i class="fas fa-building"></i>
                </div>
                <a href="{{ route('admin.businesses.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $metrics['total_services'] ?? 0 }}</h3>
                    <p>Total Services</p>
                </div>
                <div class="icon">
                    <i class="fas fa-concierge-bell"></i>
                </div>
                <a href="{{ route('admin.services.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $metrics['today_bookings'] ?? 0 }}</h3>
                    <p>Today's Bookings</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <a href="{{ route('admin.bookings.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $metrics['total_users'] ?? 0 }}</h3>
                    <p>Total Users</p>
                </div>
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
                <a href="{{ route('admin.users.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- New Features Statistics -->
    <div class="row dashboard-stats">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-purple">
                <div class="inner">
                    <h3>{{ $metrics['total_resources'] ?? 0 }}</h3>
                    <p>Total Resources</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tools"></i>
                </div>
                <a href="{{ route('admin.resources.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-teal">
                <div class="inner">
                    <h3>{{ $metrics['active_waiting_lists'] ?? 0 }}</h3>
                    <p>Active Waiting Lists</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
                <a href="{{ route('admin.waiting-lists.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-orange">
                <div class="inner">
                    <h3>{{ $metrics['pending_reminders'] ?? 0 }}</h3>
                    <p>Pending Reminders</p>
                </div>
                <div class="icon">
                    <i class="fas fa-bell"></i>
                </div>
                <a href="{{ route('admin.notifications.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-pink">
                <div class="inner">
                    <h3>{{ $metrics['pending_checkins'] ?? 0 }}</h3>
                    <p>Pending Check-ins</p>
                </div>
                <div class="icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <a href="{{ route('admin.bookings.index', ['status' => 'confirmed', 'date' => date('Y-m-d')]) }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row quick-actions-section">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-rocket mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <a href="{{ route('admin.businesses.wizard') }}" class="btn btn-primary btn-block btn-lg mb-2">
                                <i class="fas fa-magic mr-2"></i>
                                Setup New Business
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ route('admin.calendar.index') }}" class="btn btn-info btn-block btn-lg mb-2">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                View Calendar
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ route('admin.bookings.create') }}" class="btn btn-success btn-block btn-lg mb-2">
                                <i class="fas fa-plus mr-2"></i>
                                New Booking
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ route('admin.services.create') }}" class="btn btn-warning btn-block btn-lg mb-2">
                                <i class="fas fa-concierge-bell mr-2"></i>
                                Add Service
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ route('admin.resources.create') }}" class="btn btn-purple btn-block btn-lg mb-2">
                                <i class="fas fa-tools mr-2"></i>
                                Add Resource
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ route('admin.notifications.index') }}" class="btn btn-orange btn-block btn-lg mb-2">
                                <i class="fas fa-bell mr-2"></i>
                                Notifications
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ route('admin.waiting-lists.index') }}" class="btn btn-teal btn-block btn-lg">
                                <i class="fas fa-clock mr-2"></i>
                                Waiting Lists
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.check-in.index', ['date' => date('Y-m-d')]) }}" class="btn btn-pink btn-block btn-lg">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                Check-In Dashboard
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.notifications.reminders') }}" class="btn btn-indigo btn-block btn-lg">
                                <i class="fas fa-clock mr-2"></i>
                                Manage Reminders
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.businesses.index') }}" class="btn btn-dark btn-block btn-lg">
                                <i class="fas fa-building mr-2"></i>
                                Manage Businesses
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Bookings</h3>
                </div>
                <div class="card-body">
                    @if($recentBookings && $recentBookings->count() > 0)
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Service</th>
                                    <th>Business</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentBookings as $booking)
                                    <tr>
                                        <td>
                                            {{ $booking->customer ? $booking->customer->name : 'N/A' }}
                                            @if($booking->customer && $booking->customer->email)
                                                <br><small class="text-muted">{{ $booking->customer->email }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($booking->services && $booking->services->count() > 0)
                                                {{ $booking->services->first()->name }}
                                                @if($booking->services->count() > 1)
                                                    <br><small class="text-muted">+{{ $booking->services->count() - 1 }} more</small>
                                                @endif
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                        <td>{{ $booking->business ? $booking->business->name : 'N/A' }}</td>
                                        <td>
                                            {{ $booking->start_datetime ? $booking->start_datetime->format('M d, Y H:i') : 'N/A' }}
                                            @if($booking->end_datetime)
                                                <br><small class="text-muted">to {{ $booking->end_datetime->format('H:i') }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            @switch($booking->status)
                                                @case('confirmed')
                                                    <span class="badge bg-info">Confirmed</span>
                                                    @break
                                                @case('completed')
                                                    <span class="badge bg-success">Completed</span>
                                                    @break
                                                @case('cancelled')
                                                    <span class="badge bg-danger">Cancelled</span>
                                                    @break
                                                @case('pending')
                                                    <span class="badge bg-warning">Pending</span>
                                                    @break
                                                @case('no_show')
                                                    <span class="badge bg-secondary">No Show</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-light">{{ ucfirst($booking->status) }}</span>
                                            @endswitch
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.bookings.show', $booking->id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No bookings found</h5>
                            <p class="text-muted">Recent bookings will appear here once they are created.</p>
                            <a href="{{ route('admin.bookings.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-2"></i>Create First Booking
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">System Status</h3>
                </div>
                <div class="card-body">
                    <div class="info-box bg-gradient-success">
                        <span class="info-box-icon"><i class="fas fa-check"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">System</span>
                            <span class="info-box-number">Online</span>
                        </div>
                    </div>

                    <div class="info-box bg-gradient-info">
                        <span class="info-box-icon"><i class="fas fa-database"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Database</span>
                            <span class="info-box-number">Connected</span>
                        </div>
                    </div>

                    <div class="info-box bg-gradient-warning">
                        <span class="info-box-icon"><i class="fas fa-envelope"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Email System</span>
                            <span class="info-box-number">Active</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css?v={{ time() }}">
    <style>
        /* Inline CSS to ensure button colors work */
        .btn-purple {
            background-color: #6f42c1 !important;
            border-color: #6f42c1 !important;
            color: #fff !important;
        }
        .btn-orange {
            background-color: #fd7e14 !important;
            border-color: #fd7e14 !important;
            color: #fff !important;
        }
        .btn-teal {
            background-color: #20c997 !important;
            border-color: #20c997 !important;
            color: #fff !important;
        }
        .btn-pink {
            background-color: #e83e8c !important;
            border-color: #e83e8c !important;
            color: #fff !important;
        }
        .btn-indigo {
            background-color: #6610f2 !important;
            border-color: #6610f2 !important;
            color: #fff !important;
        }
        .btn-purple:hover, .btn-purple:focus, .btn-purple:active {
            background-color: #5a359a !important;
            border-color: #533085 !important;
            color: #fff !important;
        }
        .btn-orange:hover, .btn-orange:focus, .btn-orange:active {
            background-color: #e8690b !important;
            border-color: #dc6002 !important;
            color: #fff !important;
        }
        .btn-teal:hover, .btn-teal:focus, .btn-teal:active {
            background-color: #1ca085 !important;
            border-color: #198c7a !important;
            color: #fff !important;
        }
        .btn-pink:hover, .btn-pink:focus, .btn-pink:active {
            background-color: #e21b7a !important;
            border-color: #d91a72 !important;
            color: #fff !important;
        }
        .btn-indigo:hover, .btn-indigo:focus, .btn-indigo:active {
            background-color: #560bd0 !important;
            border-color: #510bc4 !important;
            color: #fff !important;
        }
    </style>
@stop

@section('js')
    <script> console.log('Hi!'); </script>
@stop
