@extends('admin.layouts.app')

@section('title', 'Booking Reminders')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Booking Reminders</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.notifications.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.notifications.reminders') }}">
                <div class="row">
                    @if(!$business)
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="business_id">Business</label>
                                <select class="form-control" id="business_id" name="business_id">
                                    <option value="">All Businesses</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}" 
                                                {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    @endif
                    
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                @foreach($statuses as $status)
                                    <option value="{{ $status }}" 
                                            {{ request('status') == $status ? 'selected' : '' }}>
                                        {{ ucfirst($status) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">Type</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">All Types</option>
                                @foreach($types as $type)
                                    <option value="{{ $type }}" 
                                            {{ request('type') == $type ? 'selected' : '' }}>
                                        {{ ucfirst($type) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="{{ request('date_from') }}">
                        </div>
                    </div>
                    
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="{{ request('date_to') }}">
                        </div>
                    </div>
                    
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                @if(request()->hasAny(['business_id', 'status', 'type', 'date_from', 'date_to']))
                    <div class="row">
                        <div class="col-12">
                            <a href="{{ route('admin.notifications.reminders') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times mr-1"></i>
                                Clear Filters
                            </a>
                        </div>
                    </div>
                @endif
            </form>
        </div>
    </div>

    <!-- Reminders Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-clock mr-2"></i>
                Reminders ({{ $reminders->total() }})
            </h3>
        </div>
        <div class="card-body p-0">
            @if($reminders->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Customer</th>
                                <th>Business</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Scheduled At</th>
                                <th>Sent At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($reminders as $reminder)
                                <tr>
                                    <td>
                                        <a href="{{ route('admin.bookings.show', $reminder->booking) }}">
                                            {{ $reminder->booking->booking_number }}
                                        </a>
                                    </td>
                                    <td>{{ $reminder->booking->customer_name }}</td>
                                    <td>
                                        <small>{{ $reminder->booking->business->name }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            <i class="{{ $reminder->type_icon ?? 'fas fa-bell' }} mr-1"></i>
                                            {{ ucfirst($reminder->reminder_type) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $reminder->status_color ?? 'secondary' }}">
                                            {{ ucfirst($reminder->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ $reminder->scheduled_at->format('M d, Y H:i') }}
                                    </td>
                                    <td>
                                        @if($reminder->sent_at)
                                            {{ $reminder->sent_at->format('M d, Y H:i') }}
                                        @else
                                            <span class="text-muted">Not sent</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($reminder->status === 'pending')
                                            <form action="{{ route('admin.notifications.cancel-reminder', $reminder) }}" 
                                                  method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-xs btn-danger" 
                                                        title="Cancel" onclick="return confirm('Are you sure?')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                        @elseif($reminder->status === 'failed')
                                            <form action="{{ route('admin.notifications.resend-reminder', $reminder) }}" 
                                                  method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-xs btn-warning" 
                                                        title="Resend">
                                                    <i class="fas fa-redo"></i>
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Reminders Found</h5>
                    <p class="text-muted">
                        @if(request()->hasAny(['business_id', 'status', 'type', 'date_from', 'date_to']))
                            No reminders match your current filters.
                        @else
                            No reminders have been scheduled yet.
                        @endif
                    </p>
                </div>
            @endif
        </div>
        
        @if($reminders->hasPages())
            <div class="card-footer">
                {{ $reminders->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
@stop

@section('scripts')
<script>
// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const selects = form.querySelectorAll('select');
    const dateInputs = form.querySelectorAll('input[type="date"]');
    
    selects.forEach(select => {
        select.addEventListener('change', function() {
            form.submit();
        });
    });
    
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            form.submit();
        });
    });
});
</script>
@stop
