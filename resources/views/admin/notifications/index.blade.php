@extends('admin.layouts.app')

@section('title', 'Notifications Dashboard')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Notifications Dashboard</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.notifications.reminders') }}" class="btn btn-info">
                    <i class="fas fa-clock mr-2"></i>
                    View All Reminders
                </a>
                <button type="button" class="btn btn-primary" onclick="processDueReminders()">
                    <i class="fas fa-play mr-2"></i>
                    Process Due Reminders
                </button>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Business Filter -->
    @if(!$business)
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.notifications.index') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="business_id">Filter by Business</label>
                                <select class="form-control" id="business_id" name="business_id" onchange="this.form.submit()">
                                    <option value="">All Businesses</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}" 
                                                {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="pending-count">{{ $pendingReminders->count() }}</h3>
                    <p>Pending Reminders</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="due-count">{{ $dueReminders->count() }}</h3>
                    <p>Due Reminders</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="sent-count">{{ $recentNotifications->where('status', 'sent')->count() }}</h3>
                    <p>Recently Sent</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check"></i>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="failed-count">{{ $recentNotifications->where('status', 'failed')->count() }}</h3>
                    <p>Failed</p>
                </div>
                <div class="icon">
                    <i class="fas fa-times"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Due Reminders -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle mr-2 text-warning"></i>
                        Due Reminders
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-primary" onclick="processDueReminders()">
                            <i class="fas fa-play mr-1"></i>
                            Process All
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($dueReminders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Booking</th>
                                        <th>Type</th>
                                        <th>Scheduled</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($dueReminders as $reminder)
                                        <tr>
                                            <td>
                                                <small>
                                                    <strong>{{ $reminder->booking->booking_number }}</strong><br>
                                                    {{ $reminder->booking->customer_name }}
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge badge-info">
                                                    <i class="{{ $reminder->type_icon }} mr-1"></i>
                                                    {{ ucfirst($reminder->reminder_type) }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>{{ $reminder->scheduled_at->format('M d, H:i') }}</small>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-xs btn-success" 
                                                        onclick="sendReminder({{ $reminder->id }})">
                                                    <i class="fas fa-paper-plane"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted">No due reminders</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Pending Reminders -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2 text-info"></i>
                        Upcoming Reminders
                    </h3>
                </div>
                <div class="card-body p-0">
                    @if($pendingReminders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Booking</th>
                                        <th>Type</th>
                                        <th>Scheduled</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pendingReminders as $reminder)
                                        <tr>
                                            <td>
                                                <small>
                                                    <strong>{{ $reminder->booking->booking_number }}</strong><br>
                                                    {{ $reminder->booking->customer_name }}
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">
                                                    <i class="{{ $reminder->type_icon }} mr-1"></i>
                                                    {{ ucfirst($reminder->reminder_type) }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>{{ $reminder->scheduled_at->format('M d, H:i') }}</small>
                                            </td>
                                            <td>
                                                <form action="{{ route('admin.notifications.cancel-reminder', $reminder) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-xs btn-danger" 
                                                            title="Cancel">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-calendar-check fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No pending reminders</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Notifications -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-history mr-2"></i>
                Recent Notifications
            </h3>
        </div>
        <div class="card-body p-0">
            @if($recentNotifications->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Customer</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Sent At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentNotifications as $notification)
                                <tr>
                                    <td>
                                        <a href="{{ route('admin.bookings.show', $notification->booking) }}">
                                            {{ $notification->booking->booking_number }}
                                        </a>
                                    </td>
                                    <td>{{ $notification->booking->customer_name }}</td>
                                    <td>
                                        <span class="badge badge-info">
                                            <i class="{{ $notification->type_icon }} mr-1"></i>
                                            {{ ucfirst($notification->reminder_type) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $notification->status_color }}">
                                            {{ ucfirst($notification->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($notification->sent_at)
                                            {{ $notification->sent_at->format('M d, Y H:i') }}
                                        @else
                                            <span class="text-muted">Not sent</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($notification->status === 'failed')
                                            <form action="{{ route('admin.notifications.resend-reminder', $notification) }}" 
                                                  method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-xs btn-warning" title="Resend">
                                                    <i class="fas fa-redo"></i>
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                    <h4>No Recent Notifications</h4>
                    <p class="text-muted">No notifications have been sent recently.</p>
                </div>
            @endif
        </div>
    </div>
@stop

@section('scripts')
<script>
function processDueReminders() {
    if (!confirm('Are you sure you want to process all due reminders?')) {
        return;
    }
    
    fetch('{{ route('admin.notifications.process-due-reminders') }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Successfully processed ${data.processed} reminders.`);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while processing reminders.');
    });
}

function sendReminder(reminderId) {
    // Implementation for sending individual reminder
    alert('Send reminder functionality would be implemented here');
}

// Auto-refresh every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);
</script>
@stop
