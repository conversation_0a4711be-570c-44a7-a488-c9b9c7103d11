@extends('admin.layouts.app')

@section('title', 'Security Dashboard')

@section('header')
    <h1>
        <i class="fas fa-shield-alt"></i> Security Dashboard
        <small class="text-muted">Real-time security monitoring and compliance</small>
    </h1>
@stop

@section('main-content')
<!-- Security Overview Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3 id="critical-alerts">{{ $alertStatistics['critical'] ?? 0 }}</h3>
                <p>Critical Alerts</p>
            </div>
            <div class="icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <a href="{{ route('admin.security.alerts', ['severity' => 'critical']) }}" class="small-box-footer">
                View Details <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3 id="high-alerts">{{ $alertStatistics['high'] ?? 0 }}</h3>
                <p>High Priority Alerts</p>
            </div>
            <div class="icon">
                <i class="fas fa-exclamation"></i>
            </div>
            <a href="{{ route('admin.security.alerts', ['severity' => 'high']) }}" class="small-box-footer">
                View Details <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3 id="total-alerts">{{ $alertStatistics['total'] ?? 0 }}</h3>
                <p>Total Alerts (24h)</p>
            </div>
            <div class="icon">
                <i class="fas fa-bell"></i>
            </div>
            <a href="{{ route('admin.security.alerts') }}" class="small-box-footer">
                View All <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>
    <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3 id="compliance-score">{{ number_format($complianceScore, 1) }}%</h3>
                <p>Compliance Score</p>
            </div>
            <div class="icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <a href="{{ route('admin.security.compliance-reports') }}" class="small-box-footer">
                View Report <i class="fas fa-arrow-circle-right"></i>
            </a>
        </div>
    </div>
</div>

<!-- Real-time Monitoring -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-line"></i> Security Trends (Last 30 Days)
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" id="refresh-trends">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="securityTrendsChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-tachometer-alt"></i> Real-time Metrics
                </h3>
                <div class="card-tools">
                    <span class="badge badge-success" id="live-indicator">LIVE</span>
                </div>
            </div>
            <div class="card-body">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-danger">
                        <i class="fas fa-fire"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Active Threats</span>
                        <span class="info-box-number" id="active-threats">0</span>
                    </div>
                </div>
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning">
                        <i class="fas fa-user-shield"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Blocked IPs</span>
                        <span class="info-box-number" id="blocked-ips">{{ count($dashboardData['blocked_ips'] ?? []) }}</span>
                    </div>
                </div>
                <div class="info-box">
                    <span class="info-box-icon bg-info">
                        <i class="fas fa-lock"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Locked Users</span>
                        <span class="info-box-number" id="locked-users">{{ count($dashboardData['locked_users'] ?? []) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Security Activities -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history"></i> Recent Security Alerts
                </h3>
                <div class="card-tools">
                    <a href="{{ route('admin.security.alerts') }}" class="btn btn-tool">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Severity</th>
                                <th>Time</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="recent-alerts">
                            <!-- Will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-clipboard-list"></i> Recent Audit Activities
                </h3>
                <div class="card-tools">
                    <a href="{{ route('admin.security.audit-logs') }}" class="btn btn-tool">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Action</th>
                                <th>User</th>
                                <th>Risk</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($dashboardData['recent_activities'] ?? [] as $activity)
                            <tr>
                                <td>
                                    <span class="badge badge-info">{{ $activity->action_name }}</span>
                                </td>
                                <td>{{ $activity->user->name ?? 'System' }}</td>
                                <td>
                                    @php
                                        $riskColors = [1 => 'success', 2 => 'info', 3 => 'warning', 4 => 'danger', 5 => 'dark'];
                                        $riskColor = $riskColors[$activity->risk_level] ?? 'secondary';
                                    @endphp
                                    <span class="badge badge-{{ $riskColor }}">{{ $activity->risk_level_name }}</span>
                                </td>
                                <td>{{ $activity->created_at->diffForHumans() }}</td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="4" class="text-center text-muted">No recent activities</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bolt"></i> Quick Security Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary btn-block" id="run-security-scan">
                            <i class="fas fa-search"></i> Run Security Scan
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-warning btn-block" id="execute-retention">
                            <i class="fas fa-archive"></i> Execute Data Retention
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('admin.security.permission-inheritance') }}" class="btn btn-info btn-block">
                            <i class="fas fa-sitemap"></i> View Permission Tree
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('admin.security.compliance-reports') }}" class="btn btn-success btn-block">
                            <i class="fas fa-file-alt"></i> Generate Report
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize security trends chart
    initializeSecurityTrendsChart();
    
    // Start real-time updates
    startRealTimeUpdates();
    
    // Quick action handlers
    setupQuickActions();
});

function initializeSecurityTrendsChart() {
    const ctx = document.getElementById('securityTrendsChart').getContext('2d');
    
    // Fetch trends data
    $.get('{{ route("admin.security.trends") }}', function(data) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.alerts_by_day.map(item => item.date),
                datasets: [{
                    label: 'Security Alerts',
                    data: data.alerts_by_day.map(item => item.count),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
}

function startRealTimeUpdates() {
    // Update metrics every 30 seconds
    setInterval(updateRealTimeMetrics, 30000);
    
    // Initial update
    updateRealTimeMetrics();
}

function updateRealTimeMetrics() {
    $.get('{{ route("admin.security.metrics") }}', function(data) {
        $('#critical-alerts').text(data.critical_alerts);
        $('#total-alerts').text(data.alerts_last_24h);
        $('#active-threats').text(data.open_alerts);
        
        // Update live indicator
        $('#live-indicator').removeClass('badge-secondary').addClass('badge-success');
        setTimeout(function() {
            $('#live-indicator').removeClass('badge-success').addClass('badge-secondary');
        }, 1000);
    }).fail(function() {
        $('#live-indicator').removeClass('badge-success').addClass('badge-danger');
    });
}

function setupQuickActions() {
    // Run security scan
    $('#run-security-scan').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Scanning...');
        
        $.post('{{ route("admin.security.scan") }}', {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            toastr.success(`Security scan completed. ${response.alerts_generated} alerts generated.`);
            updateRealTimeMetrics();
        }).fail(function() {
            toastr.error('Security scan failed.');
        }).always(function() {
            btn.prop('disabled', false).html('<i class="fas fa-search"></i> Run Security Scan');
        });
    });
    
    // Execute data retention
    $('#execute-retention').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
        
        $.post('{{ route("admin.security.retention.execute") }}', {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            toastr.success('Data retention policies executed successfully.');
        }).fail(function() {
            toastr.error('Data retention execution failed.');
        }).always(function() {
            btn.prop('disabled', false).html('<i class="fas fa-archive"></i> Execute Data Retention');
        });
    });
    
    // Refresh trends chart
    $('#refresh-trends').click(function() {
        initializeSecurityTrendsChart();
        toastr.info('Security trends refreshed.');
    });
}

// Auto-refresh page every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);
</script>
@stop

@section('css')
<style>
.live-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.small-box .icon {
    transition: all 0.3s ease;
}

.small-box:hover .icon {
    transform: scale(1.1);
}

.info-box {
    transition: all 0.3s ease;
}

.info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

#securityTrendsChart {
    max-height: 300px;
}
</style>
@stop
