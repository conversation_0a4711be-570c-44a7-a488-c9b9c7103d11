@extends('admin.layouts.app')

@section('title', 'Security Alerts')

@section('header')
    <h1>
        <i class="fas fa-exclamation-triangle"></i> Security Alerts
        <small class="text-muted">Monitor and manage security incidents</small>
    </h1>
@stop

@section('main-content')
<!-- Alert Statistics -->
<div class="row mb-4">
    <div class="col-lg-2 col-6">
        <div class="small-box bg-danger">
            <div class="inner">
                <h3>{{ $statistics['critical'] ?? 0 }}</h3>
                <p>Critical</p>
            </div>
            <div class="icon">
                <i class="fas fa-fire"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3>{{ $statistics['high'] ?? 0 }}</h3>
                <p>High</p>
            </div>
            <div class="icon">
                <i class="fas fa-exclamation"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3>{{ $statistics['total'] ?? 0 }}</h3>
                <p>Total (24h)</p>
            </div>
            <div class="icon">
                <i class="fas fa-bell"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3>{{ $statistics['resolved'] ?? 0 }}</h3>
                <p>Resolved</p>
            </div>
            <div class="icon">
                <i class="fas fa-check"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body">
                <h5>Alert Types (24h)</h5>
                <div class="row">
                    @foreach($statistics['by_type'] ?? [] as $type => $count)
                    <div class="col-6">
                        <small>{{ ucfirst(str_replace('_', ' ', $type)) }}: <strong>{{ $count }}</strong></small>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-filter"></i> Filters
        </h3>
        <div class="card-tools">
            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.security.alerts') }}" class="row">
            <div class="col-md-2">
                <div class="form-group">
                    <label for="severity">Severity</label>
                    <select name="severity" id="severity" class="form-control">
                        <option value="">All Severities</option>
                        <option value="critical" {{ request('severity') === 'critical' ? 'selected' : '' }}>Critical</option>
                        <option value="high" {{ request('severity') === 'high' ? 'selected' : '' }}>High</option>
                        <option value="medium" {{ request('severity') === 'medium' ? 'selected' : '' }}>Medium</option>
                        <option value="low" {{ request('severity') === 'low' ? 'selected' : '' }}>Low</option>
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="status">Status</label>
                    <select name="status" id="status" class="form-control">
                        <option value="">All Statuses</option>
                        <option value="open" {{ request('status') === 'open' ? 'selected' : '' }}>Open</option>
                        <option value="investigating" {{ request('status') === 'investigating' ? 'selected' : '' }}>Investigating</option>
                        <option value="resolved" {{ request('status') === 'resolved' ? 'selected' : '' }}>Resolved</option>
                        <option value="false_positive" {{ request('status') === 'false_positive' ? 'selected' : '' }}>False Positive</option>
                        <option value="ignored" {{ request('status') === 'ignored' ? 'selected' : '' }}>Ignored</option>
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="type">Type</label>
                    <select name="type" id="type" class="form-control">
                        <option value="">All Types</option>
                        <option value="privilege_escalation" {{ request('type') === 'privilege_escalation' ? 'selected' : '' }}>Privilege Escalation</option>
                        <option value="brute_force" {{ request('type') === 'brute_force' ? 'selected' : '' }}>Brute Force</option>
                        <option value="unusual_access" {{ request('type') === 'unusual_access' ? 'selected' : '' }}>Unusual Access</option>
                        <option value="sensitive_access" {{ request('type') === 'sensitive_access' ? 'selected' : '' }}>Sensitive Access</option>
                        <option value="hierarchy_violation" {{ request('type') === 'hierarchy_violation' ? 'selected' : '' }}>Hierarchy Violation</option>
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="date_from">Date From</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label for="date_to">Date To</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('admin.security.alerts') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Alerts Table -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-list"></i> Security Alerts
        </h3>
        <div class="card-tools">
            <div class="btn-group">
                <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown">
                    <i class="fas fa-download"></i> Export
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#" onclick="exportData('csv')">
                        <i class="fas fa-file-csv"></i> Export as CSV
                    </a>
                    <a class="dropdown-item" href="#" onclick="exportData('json')">
                        <i class="fas fa-file-code"></i> Export as JSON
                    </a>
                </div>
            </div>
            <button type="button" class="btn btn-primary btn-sm" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="thead-dark">
                    <tr>
                        <th width="5%">ID</th>
                        <th width="15%">Type</th>
                        <th width="10%">Severity</th>
                        <th width="10%">Status</th>
                        <th width="15%">User</th>
                        <th width="25%">Message</th>
                        <th width="10%">IP Address</th>
                        <th width="10%">Created</th>
                        <th width="10%">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($alerts as $alert)
                    <tr class="{{ $alert->severity === 'critical' ? 'table-danger' : ($alert->severity === 'high' ? 'table-warning' : '') }}">
                        <td>{{ $alert->id }}</td>
                        <td>
                            <span class="badge badge-secondary">{{ $alert->type_name }}</span>
                        </td>
                        <td>
                            <span class="badge badge-{{ $alert->severity_color }}">{{ $alert->severity_name }}</span>
                        </td>
                        <td>
                            <span class="badge badge-{{ $alert->status_color }}">{{ $alert->status_name }}</span>
                        </td>
                        <td>
                            @if($alert->user)
                                <a href="{{ route('admin.users.show', $alert->user) }}">{{ $alert->user->name }}</a>
                                <br><small class="text-muted">{{ $alert->user->email }}</small>
                            @else
                                <span class="text-muted">System</span>
                            @endif
                        </td>
                        <td>
                            <span title="{{ $alert->message }}">
                                {{ Str::limit($alert->message, 50) }}
                            </span>
                            @if($alert->details)
                                <br><small class="text-muted">
                                    <i class="fas fa-info-circle"></i> Additional details available
                                </small>
                            @endif
                        </td>
                        <td>
                            @if($alert->ip_address)
                                <code>{{ $alert->ip_address }}</code>
                            @else
                                <span class="text-muted">N/A</span>
                            @endif
                        </td>
                        <td>
                            <span title="{{ $alert->created_at->format('Y-m-d H:i:s') }}">
                                {{ $alert->created_at->diffForHumans() }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.security.alerts.show', $alert) }}" class="btn btn-info btn-xs" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @if(!$alert->isResolved())
                                    <button type="button" class="btn btn-success btn-xs" onclick="resolveAlert({{ $alert->id }})" title="Resolve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="markFalsePositive({{ $alert->id }})" title="False Positive">
                                        <i class="fas fa-times"></i>
                                    </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="fas fa-shield-alt fa-3x mb-3"></i>
                            <br>No security alerts found.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    @if($alerts->hasPages())
    <div class="card-footer">
        {{ $alerts->appends(request()->query())->links() }}
    </div>
    @endif
</div>

<!-- Resolve Alert Modal -->
<div class="modal fade" id="resolveAlertModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Resolve Security Alert</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="resolveAlertForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="resolution_notes">Resolution Notes</label>
                        <textarea name="resolution_notes" id="resolution_notes" class="form-control" rows="4" placeholder="Describe how this alert was resolved..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Resolve Alert</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- False Positive Modal -->
<div class="modal fade" id="falsePositiveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Mark as False Positive</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="falsePositiveForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="fp_resolution_notes">Reason for False Positive</label>
                        <textarea name="resolution_notes" id="fp_resolution_notes" class="form-control" rows="4" placeholder="Explain why this is a false positive..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Mark as False Positive</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
function resolveAlert(alertId) {
    $('#resolveAlertForm').attr('action', `/admin/security/alerts/${alertId}/resolve`);
    $('#resolveAlertModal').modal('show');
}

function markFalsePositive(alertId) {
    $('#falsePositiveForm').attr('action', `/admin/security/alerts/${alertId}/false-positive`);
    $('#falsePositiveModal').modal('show');
}

function exportData(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    params.set('type', 'alerts');
    
    window.location.href = `{{ route('admin.security.export') }}?${params.toString()}`;
}

// Auto-refresh every 2 minutes
setInterval(function() {
    location.reload();
}, 120000);

// Handle form submissions
$('#resolveAlertForm').submit(function(e) {
    e.preventDefault();
    
    $.post($(this).attr('action'), $(this).serialize())
        .done(function() {
            toastr.success('Alert resolved successfully.');
            location.reload();
        })
        .fail(function() {
            toastr.error('Failed to resolve alert.');
        });
});

$('#falsePositiveForm').submit(function(e) {
    e.preventDefault();
    
    $.post($(this).attr('action'), $(this).serialize())
        .done(function() {
            toastr.success('Alert marked as false positive.');
            location.reload();
        })
        .fail(function() {
            toastr.error('Failed to mark alert as false positive.');
        });
});
</script>
@stop

@section('css')
<style>
.table-danger {
    animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
    0% { background-color: rgba(220, 53, 69, 0.1); }
    50% { background-color: rgba(220, 53, 69, 0.2); }
    100% { background-color: rgba(220, 53, 69, 0.1); }
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.btn-group-sm .btn {
    margin-right: 2px;
}

.badge {
    font-size: 0.75em;
}
</style>
@stop
