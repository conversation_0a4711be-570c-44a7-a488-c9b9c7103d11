@extends('adminlte::page')

@section('title', 'Security Alert Details')

@section('content_header')
    <h1>
        <i class="fas fa-exclamation-triangle"></i> Security Alert Details
        <small class="ml-3">
            <span class="badge badge-{{ $alert->severity_color }}">{{ $alert->severity_name }}</span>
        </small>
    </h1>
@stop

@section('main-content')
<!-- Alert Overview -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle"></i> Alert Information
                </h3>
                <div class="card-tools">
                    <span class="badge badge-{{ $alert->status_color }}">{{ $alert->status_name }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Alert ID:</th>
                                <td>#{{ $alert->id }}</td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>
                                    <span class="badge badge-secondary">{{ $alert->type_name }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th>Severity:</th>
                                <td>
                                    <span class="badge badge-{{ $alert->severity_color }}">{{ $alert->severity_name }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <span class="badge badge-{{ $alert->status_color }}">{{ $alert->status_name }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th>Created:</th>
                                <td>
                                    {{ $alert->created_at->format('Y-m-d H:i:s') }}
                                    <small class="text-muted">({{ $alert->created_at->diffForHumans() }})</small>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">User:</th>
                                <td>
                                    @if($alert->user)
                                        <a href="{{ route('admin.users.show', $alert->user) }}" class="text-decoration-none">
                                            {{ $alert->user->name }}
                                        </a>
                                        <small class="text-muted d-block">{{ $alert->user->email }}</small>
                                    @else
                                        <span class="text-muted">System</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>IP Address:</th>
                                <td>
                                    <code>{{ $alert->ip_address ?? 'N/A' }}</code>
                                </td>
                            </tr>
                            <tr>
                                <th>User Agent:</th>
                                <td>
                                    <small class="text-muted">{{ $alert->user_agent ?? 'N/A' }}</small>
                                </td>
                            </tr>
                            @if($alert->resolver)
                            <tr>
                                <th>Resolved By:</th>
                                <td>
                                    {{ $alert->resolver->name }}
                                    <small class="text-muted d-block">{{ $alert->resolved_at->format('Y-m-d H:i:s') }}</small>
                                </td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Message -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-comment-alt"></i> Alert Message
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-{{ $alert->severity_color }} alert-dismissible">
                    <h5><i class="icon fas fa-{{ $alert->severity === 'critical' ? 'fire' : ($alert->severity === 'high' ? 'exclamation' : 'info') }}"></i> {{ $alert->type_name }}</h5>
                    {{ $alert->message }}
                </div>
            </div>
        </div>

        <!-- Additional Data -->
        @if($alert->details)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-database"></i> Additional Data
                </h3>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded"><code>{{ json_encode($alert->details, JSON_PRETTY_PRINT) }}</code></pre>
            </div>
        </div>
        @endif

        <!-- Resolution Notes -->
        @if($alert->resolution_notes)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-clipboard-check"></i> Resolution Notes
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    {{ $alert->resolution_notes }}
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Actions Sidebar -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs"></i> Actions
                </h3>
            </div>
            <div class="card-body">
                @if(!$alert->isResolved())
                    <button type="button" class="btn btn-success btn-block mb-2" onclick="resolveAlert({{ $alert->id }})">
                        <i class="fas fa-check"></i> Resolve Alert
                    </button>
                    <button type="button" class="btn btn-warning btn-block mb-2" onclick="markFalsePositive({{ $alert->id }})">
                        <i class="fas fa-times"></i> Mark as False Positive
                    </button>
                @else
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> This alert has been resolved.
                    </div>
                @endif

                <a href="{{ route('admin.security.alerts') }}" class="btn btn-secondary btn-block">
                    <i class="fas fa-arrow-left"></i> Back to Alerts
                </a>
            </div>
        </div>

        <!-- Related Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-link"></i> Related Information
                </h3>
            </div>
            <div class="card-body">
                @if($alert->user)
                    <p><strong>User's Recent Activity:</strong></p>
                    <small class="text-muted">
                        Last login: {{ $alert->user->last_login_at ? $alert->user->last_login_at->diffForHumans() : 'Never' }}
                    </small>
                @endif

                <hr>

                <p><strong>Similar Alerts:</strong></p>
                <small class="text-muted">
                    Check for other alerts of type "{{ $alert->type_name }}" in the
                    <a href="{{ route('admin.security.alerts', ['type' => $alert->type]) }}">alerts list</a>.
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Resolve Alert Modal -->
<div class="modal fade" id="resolveAlertModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="resolveAlertForm" method="POST">
                @csrf
                <div class="modal-header">
                    <h4 class="modal-title">Resolve Security Alert</h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="resolution_notes">Resolution Notes (Optional)</label>
                        <textarea class="form-control" id="resolution_notes" name="resolution_notes" rows="3"
                                  placeholder="Describe how this alert was resolved..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Resolve Alert</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- False Positive Modal -->
<div class="modal fade" id="falsePositiveModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="falsePositiveForm" method="POST">
                @csrf
                <div class="modal-header">
                    <h4 class="modal-title">Mark as False Positive</h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to mark this alert as a false positive?</p>
                    <div class="form-group">
                        <label for="false_positive_notes">Notes (Optional)</label>
                        <textarea class="form-control" id="false_positive_notes" name="resolution_notes" rows="2"
                                  placeholder="Explain why this is a false positive..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Mark as False Positive</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
function resolveAlert(alertId) {
    $('#resolveAlertForm').attr('action', `/admin/security/alerts/${alertId}/resolve`);
    $('#resolveAlertModal').modal('show');
}

function markFalsePositive(alertId) {
    $('#falsePositiveForm').attr('action', `/admin/security/alerts/${alertId}/false-positive`);
    $('#falsePositiveModal').modal('show');
}

// Handle form submissions
$('#resolveAlertForm').submit(function(e) {
    e.preventDefault();

    $.post($(this).attr('action'), $(this).serialize())
        .done(function() {
            toastr.success('Alert resolved successfully.');
            location.reload();
        })
        .fail(function() {
            toastr.error('Failed to resolve alert.');
        });
});

$('#falsePositiveForm').submit(function(e) {
    e.preventDefault();

    $.post($(this).attr('action'), $(this).serialize())
        .done(function() {
            toastr.success('Alert marked as false positive.');
            location.reload();
        })
        .fail(function() {
            toastr.error('Failed to mark alert as false positive.');
        });
});
</script>
@stop
