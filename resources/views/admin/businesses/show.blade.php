@extends('admin.layouts.app')

@section('title', 'Business Details')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>{{ $business->name }}</h1>
            <p class="text-muted">Business Details</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('edit businesses')
                    <a href="{{ route('admin.businesses.edit', $business) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Business
                    </a>
                @endcan
                <a href="{{ route('admin.businesses.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Businesses
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <!-- Business Information -->
    <div class="col-md-8">
        <!-- Basic Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle mr-2"></i>
                    Business Information
                </h3>
                <div class="card-tools">
                    <span class="badge badge-{{ $business->is_active ? 'success' : 'secondary' }} badge-lg">
                        {{ $business->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        @if($business->logo)
                            <img src="{{ Storage::url($business->logo) }}" alt="{{ $business->name }}"
                                 class="img-fluid rounded mb-3" style="max-height: 150px;">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3"
                                 style="height: 150px;">
                                <i class="fas fa-building fa-3x text-muted"></i>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-9">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Business Name:</strong></td>
                                <td>{{ $business->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Owner:</strong></td>
                                <td>
                                    <a href="{{ route('admin.users.show', $business->owner) }}">
                                        {{ $business->owner->name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>
                                    @if($business->email)
                                        <a href="mailto:{{ $business->email }}">{{ $business->email }}</a>
                                    @else
                                        <span class="text-muted">Not provided</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>
                                    @if($business->phone)
                                        <a href="tel:{{ $business->phone }}">{{ $business->phone }}</a>
                                    @else
                                        <span class="text-muted">Not provided</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Website:</strong></td>
                                <td>
                                    @if($business->website)
                                        <a href="{{ $business->website }}" target="_blank">{{ $business->website }}</a>
                                    @else
                                        <span class="text-muted">Not provided</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($business->description)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6><i class="fas fa-align-left mr-2"></i>Description:</h6>
                        <p class="text-muted">{{ $business->description }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Business Settings -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs mr-2"></i>
                    Business Settings
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Timezone:</strong></td>
                                <td>{{ $business->timezone }}</td>
                            </tr>
                            <tr>
                                <td><strong>Currency:</strong></td>
                                <td>{{ $business->currency }}</td>
                            </tr>
                            <tr>
                                <td><strong>Language:</strong></td>
                                <td>{{ strtoupper($business->language) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Multi-Branch:</strong></td>
                                <td>
                                    <span class="badge badge-{{ $business->multi_branch ? 'success' : 'secondary' }}">
                                        {{ $business->multi_branch ? 'Yes' : 'No' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Online Booking:</strong></td>
                                <td>
                                    <span class="badge badge-{{ $business->online_booking_enabled ? 'success' : 'secondary' }}">
                                        {{ $business->online_booking_enabled ? 'Enabled' : 'Disabled' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Max Advance Booking:</strong></td>
                                <td>{{ $business->booking_advance_days }} days</td>
                            </tr>
                            <tr>
                                <td><strong>Min Advance Booking:</strong></td>
                                <td>{{ $business->booking_advance_hours }} hours</td>
                            </tr>
                            <tr>
                                <td><strong>Cancellation Notice:</strong></td>
                                <td>{{ $business->cancellation_hours }} hours</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Categories & Tags -->
        @if($business->categories->count() > 0 || $business->tags->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-tags mr-2"></i>
                    Categories & Tags
                </h3>
            </div>
            <div class="card-body">
                @if($business->categories->count() > 0)
                    <div class="mb-3">
                        <h6>Categories:</h6>
                        @foreach($business->categories as $category)
                            <span class="badge badge-primary mr-1">{{ $category->name }}</span>
                        @endforeach
                    </div>
                @endif

                @if($business->tags->count() > 0)
                    <div>
                        <h6>Tags:</h6>
                        @foreach($business->tags as $tag)
                            <span class="badge badge-info mr-1">{{ $tag->name }}</span>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Services -->
        @if($business->services->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-concierge-bell mr-2"></i>
                    Services ({{ $business->services->count() }})
                </h3>
                <div class="card-tools">
                    <a href="{{ route('admin.services.create', ['business_id' => $business->id]) }}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus mr-1"></i>
                        Add Service
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Service</th>
                                <th>Category</th>
                                <th>Duration</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($business->services->take(10) as $service)
                                <tr>
                                    <td>
                                        <strong>{{ $service->name }}</strong>
                                        @if($service->short_description)
                                            <br><small class="text-muted">{{ $service->short_description }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $service->category->name ?? 'Uncategorized' }}</td>
                                    <td>{{ $service->formatted_duration }}</td>
                                    <td>${{ number_format($service->base_price, 2) }}</td>
                                    <td>
                                        <span class="badge badge-{{ $service->is_active ? 'success' : 'secondary' }}">
                                            {{ $service->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.services.show', $service) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @if($business->services->count() > 10)
                    <div class="card-footer text-center">
                        <a href="{{ route('admin.services.index', ['business_id' => $business->id]) }}" class="btn btn-sm btn-primary">
                            View All Services ({{ $business->services->count() }})
                        </a>
                    </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Recent Bookings -->
        @if($business->bookings->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-calendar-check mr-2"></i>
                    Recent Bookings
                </h3>
                <div class="card-tools">
                    <a href="{{ route('admin.bookings.create', ['business_id' => $business->id]) }}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus mr-1"></i>
                        New Booking
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Service</th>
                                <th>Status</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($business->bookings as $booking)
                                <tr>
                                    <td>{{ $booking->start_datetime->format('M d, Y g:i A') }}</td>
                                    <td>{{ $booking->customer_name }}</td>
                                    <td>
                                        @if($booking->bookingServices->count() > 0)
                                            {{ $booking->bookingServices->first()->service->name }}
                                            @if($booking->bookingServices->count() > 1)
                                                <small class="text-muted">(+{{ $booking->bookingServices->count() - 1 }} more)</small>
                                            @endif
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $booking->status_color }}">
                                            {{ ucfirst($booking->status) }}
                                        </span>
                                    </td>
                                    <td>${{ number_format($booking->total_amount, 2) }}</td>
                                    <td>
                                        <a href="{{ route('admin.bookings.show', $booking) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ route('admin.bookings.index', ['business_id' => $business->id]) }}" class="btn btn-sm btn-primary">
                        View All Bookings
                    </a>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Quick Stats
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="info-box bg-info">
                            <span class="info-box-icon"><i class="fas fa-concierge-bell"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Services</span>
                                <span class="info-box-number">{{ $business->services->count() }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="info-box bg-success">
                            <span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Bookings</span>
                                <span class="info-box-number">{{ $business->bookings()->count() }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                @if($business->branches->count() > 0)
                <div class="row">
                    <div class="col-6">
                        <div class="info-box bg-warning">
                            <span class="info-box-icon"><i class="fas fa-map-marker-alt"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Branches</span>
                                <span class="info-box-number">{{ $business->branches->count() }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="info-box bg-danger">
                            <span class="info-box-icon"><i class="fas fa-tools"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Resources</span>
                                <span class="info-box-number">{{ $business->resources->count() }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <table class="table table-sm">
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>{{ $business->created_at->format('M d, Y') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Last Updated:</strong></td>
                        <td>{{ $business->updated_at->format('M d, Y') }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bolt mr-2"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="btn-group-vertical w-100" role="group">
                    <a href="{{ route('admin.services.create', ['business_id' => $business->id]) }}"
                       class="btn btn-success btn-block">
                        <i class="fas fa-plus mr-2"></i>
                        Add Service
                    </a>

                    <a href="{{ route('admin.bookings.create', ['business_id' => $business->id]) }}"
                       class="btn btn-primary btn-block">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Create Booking
                    </a>

                    <a href="{{ route('admin.calendar.index', ['business_id' => $business->id]) }}"
                       class="btn btn-info btn-block">
                        <i class="fas fa-calendar mr-2"></i>
                        View Calendar
                    </a>

                    <div class="dropdown">
                        <button class="btn btn-secondary btn-block dropdown-toggle" type="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-cogs mr-2"></i>
                            Business Settings
                        </button>
                        <div class="dropdown-menu w-100">
                            <a class="dropdown-item" href="{{ route('admin.businesses.operating-hours.index', $business) }}">
                                <i class="fas fa-clock mr-2"></i>
                                Operating Hours
                            </a>
                            <a class="dropdown-item" href="{{ route('admin.businesses.holidays.index', $business) }}">
                                <i class="fas fa-calendar-times mr-2"></i>
                                Holidays & Closures
                            </a>
                            <a class="dropdown-item" href="{{ route('admin.businesses.locations.index', $business) }}">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                Locations & Maps
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ route('admin.check-in.index', ['business_id' => $business->id]) }}">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                Check-In Dashboard
                            </a>
                        </div>
                    </div>

                    @if($business->is_active)
                        <form action="{{ route('admin.businesses.update', $business) }}" method="POST" class="mb-2">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_active" value="0">
                            <button type="submit" class="btn btn-warning btn-block">
                                <i class="fas fa-pause mr-2"></i>
                                Deactivate Business
                            </button>
                        </form>
                    @else
                        <form action="{{ route('admin.businesses.update', $business) }}" method="POST" class="mb-2">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_active" value="1">
                            <button type="submit" class="btn btn-success btn-block">
                                <i class="fas fa-play mr-2"></i>
                                Activate Business
                            </button>
                        </form>
                    @endif

                    @can('delete businesses')
                        <form action="{{ route('admin.businesses.destroy', $business) }}" method="POST">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-block"
                                    onclick="return confirm('Are you sure you want to delete this business? This action cannot be undone.')">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Business
                            </button>
                        </form>
                    @endcan
                </div>
            </div>
        </div>

        <!-- Branding -->
        @if($business->branding)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-palette mr-2"></i>
                    Branding
                </h3>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    @if(isset($business->branding['primary_color']))
                    <tr>
                        <td><strong>Primary Color:</strong></td>
                        <td>
                            <span class="badge" style="background-color: {{ $business->branding['primary_color'] }}; color: white;">
                                {{ $business->branding['primary_color'] }}
                            </span>
                        </td>
                    </tr>
                    @endif
                    @if(isset($business->branding['secondary_color']))
                    <tr>
                        <td><strong>Secondary Color:</strong></td>
                        <td>
                            <span class="badge" style="background-color: {{ $business->branding['secondary_color'] }}; color: white;">
                                {{ $business->branding['secondary_color'] }}
                            </span>
                        </td>
                    </tr>
                    @endif
                    @if(isset($business->branding['theme']))
                    <tr>
                        <td><strong>Theme:</strong></td>
                        <td>{{ ucfirst($business->branding['theme']) }}</td>
                    </tr>
                    @endif
                </table>
            </div>
        </div>
        @endif
    </div>
</div>
@stop
