@extends('admin.layouts.app')

@section('title', 'Operating Hours')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Operating Hours</h1>
            <p class="text-muted">{{ $business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.show', $business) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Business
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2"></i>
                        Weekly Operating Hours
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#templatesModal">
                            <i class="fas fa-magic mr-2"></i>
                            Apply Template
                        </button>
                    </div>
                </div>
                
                <form action="{{ route('admin.businesses.operating-hours.update', $business) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="card-body">
                        @foreach($operatingHours as $dayNumber => $hours)
                            <div class="day-row mb-4 p-3 border rounded">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <h5 class="mb-0">{{ $daysOfWeek[$dayNumber] }}</h5>
                                        <input type="hidden" name="hours[{{ $dayNumber }}][day_of_week]" value="{{ $dayNumber }}">
                                    </div>
                                    
                                    <div class="col-md-2">
                                        <div class="form-check">
                                            <input class="form-check-input day-closed-toggle" type="checkbox" 
                                                   name="hours[{{ $dayNumber }}][is_closed]" value="1"
                                                   id="closed_{{ $dayNumber }}" 
                                                   {{ $hours->is_closed ? 'checked' : '' }}
                                                   data-day="{{ $dayNumber }}">
                                            <label class="form-check-label" for="closed_{{ $dayNumber }}">
                                                Closed
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-8 time-inputs" id="time_inputs_{{ $dayNumber }}" 
                                         style="{{ $hours->is_closed ? 'display: none;' : '' }}">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <label for="open_{{ $dayNumber }}" class="form-label">Open</label>
                                                <input type="time" class="form-control" 
                                                       name="hours[{{ $dayNumber }}][open_time]" 
                                                       id="open_{{ $dayNumber }}"
                                                       value="{{ $hours->open_time ? $hours->open_time->format('H:i') : '' }}">
                                            </div>
                                            <div class="col-md-3">
                                                <label for="close_{{ $dayNumber }}" class="form-label">Close</label>
                                                <input type="time" class="form-control" 
                                                       name="hours[{{ $dayNumber }}][close_time]" 
                                                       id="close_{{ $dayNumber }}"
                                                       value="{{ $hours->close_time ? $hours->close_time->format('H:i') : '' }}">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Break Times</label>
                                                <div class="break-times-container" id="breaks_{{ $dayNumber }}">
                                                    @if($hours->break_times)
                                                        @foreach($hours->break_times as $index => $break)
                                                            <div class="break-time-row mb-2">
                                                                <div class="row">
                                                                    <div class="col-5">
                                                                        <input type="time" class="form-control form-control-sm" 
                                                                               name="hours[{{ $dayNumber }}][break_times][{{ $index }}][start]"
                                                                               value="{{ $break['start'] }}" placeholder="Start">
                                                                    </div>
                                                                    <div class="col-5">
                                                                        <input type="time" class="form-control form-control-sm" 
                                                                               name="hours[{{ $dayNumber }}][break_times][{{ $index }}][end]"
                                                                               value="{{ $break['end'] }}" placeholder="End">
                                                                    </div>
                                                                    <div class="col-2">
                                                                        <button type="button" class="btn btn-sm btn-danger remove-break">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    @endif
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-primary add-break" 
                                                        data-day="{{ $dayNumber }}">
                                                    <i class="fas fa-plus mr-1"></i>
                                                    Add Break
                                                </button>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">&nbsp;</label>
                                                <div>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary copy-day" 
                                                            data-day="{{ $dayNumber }}" title="Copy to other days">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Operating Hours
                        </button>
                        <a href="{{ route('admin.businesses.show', $business) }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Information
                    </h3>
                </div>
                <div class="card-body">
                    <p><strong>Operating Hours:</strong></p>
                    <p class="text-muted">Set your business operating hours for each day of the week. You can also add break times for lunch or other breaks.</p>
                    
                    <p><strong>Break Times:</strong></p>
                    <p class="text-muted">Add break periods when your business is temporarily closed during operating hours.</p>
                    
                    <p><strong>Templates:</strong></p>
                    <p class="text-muted">Use predefined templates to quickly set common operating hour patterns.</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-eye mr-2"></i>
                        Current Schedule Preview
                    </h3>
                </div>
                <div class="card-body">
                    <div id="schedule-preview">
                        @foreach($operatingHours as $dayNumber => $hours)
                            <div class="mb-2">
                                <strong>{{ $daysOfWeek[$dayNumber] }}:</strong>
                                @if($hours->is_closed)
                                    <span class="text-muted">Closed</span>
                                @else
                                    <span class="text-success">
                                        {{ $hours->open_time ? $hours->open_time->format('g:i A') : 'Not set' }} - 
                                        {{ $hours->close_time ? $hours->close_time->format('g:i A') : 'Not set' }}
                                    </span>
                                    @if($hours->break_times)
                                        <br><small class="text-info">
                                            Breaks: 
                                            @foreach($hours->break_times as $break)
                                                {{ date('g:i A', strtotime($break['start'])) }}-{{ date('g:i A', strtotime($break['end'])) }}
                                                @if(!$loop->last), @endif
                                            @endforeach
                                        </small>
                                    @endif
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates Modal -->
<div class="modal fade" id="templatesModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Apply Operating Hours Template</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.businesses.operating-hours.set-template', $business) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="template">Select Template</label>
                        <select name="template" id="template" class="form-control" required>
                            <option value="">Choose a template</option>
                            <option value="standard">Standard Business Hours (Mon-Fri 9AM-5PM)</option>
                            <option value="extended">Extended Hours (Mon-Fri 8AM-8PM, Sat 9AM-5PM)</option>
                            <option value="weekend_only">Weekend Only (Sat-Sun 10AM-6PM)</option>
                            <option value="24_7">24/7 Operation</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Warning:</strong> This will overwrite all current operating hours.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Apply Template</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Copy Hours Modal -->
<div class="modal fade" id="copyModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Copy Operating Hours</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.businesses.operating-hours.copy', $business) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="from_day" id="copy_from_day">
                    <p>Copy <strong id="copy_from_day_name"></strong> hours to:</p>
                    <div class="form-group">
                        @foreach($daysOfWeek as $dayNumber => $dayName)
                            <div class="form-check">
                                <input class="form-check-input copy-to-day" type="checkbox" 
                                       name="to_days[]" value="{{ $dayNumber }}" id="copy_to_{{ $dayNumber }}">
                                <label class="form-check-label" for="copy_to_{{ $dayNumber }}">
                                    {{ $dayName }}
                                </label>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Copy Hours</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle time inputs when closed checkbox changes
    $('.day-closed-toggle').change(function() {
        var day = $(this).data('day');
        var timeInputs = $('#time_inputs_' + day);
        
        if ($(this).is(':checked')) {
            timeInputs.hide();
        } else {
            timeInputs.show();
        }
    });
    
    // Add break time
    $('.add-break').click(function() {
        var day = $(this).data('day');
        var container = $('#breaks_' + day);
        var index = container.find('.break-time-row').length;
        
        var breakHtml = `
            <div class="break-time-row mb-2">
                <div class="row">
                    <div class="col-5">
                        <input type="time" class="form-control form-control-sm" 
                               name="hours[${day}][break_times][${index}][start]" placeholder="Start">
                    </div>
                    <div class="col-5">
                        <input type="time" class="form-control form-control-sm" 
                               name="hours[${day}][break_times][${index}][end]" placeholder="End">
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn btn-sm btn-danger remove-break">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        container.append(breakHtml);
    });
    
    // Remove break time
    $(document).on('click', '.remove-break', function() {
        $(this).closest('.break-time-row').remove();
    });
    
    // Copy day hours
    $('.copy-day').click(function() {
        var day = $(this).data('day');
        var dayName = @json($daysOfWeek)[day];
        
        $('#copy_from_day').val(day);
        $('#copy_from_day_name').text(dayName);
        
        // Uncheck all copy-to checkboxes and disable the source day
        $('.copy-to-day').prop('checked', false).prop('disabled', false);
        $('#copy_to_' + day).prop('disabled', true);
        
        $('#copyModal').modal('show');
    });
});
</script>
@stop
