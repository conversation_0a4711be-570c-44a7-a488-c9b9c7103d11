@extends('admin.layouts.app')

@section('title', 'Create Business')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Create New Business</h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Businesses
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-12">
        <form action="{{ route('admin.businesses.store') }}" method="POST" enctype="multipart/form-data" id="business-form">
            @csrf

            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Basic Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="name">Business Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="logo">Business Logo</label>
                                <input type="file" class="form-control-file @error('logo') is-invalid @enderror"
                                       id="logo" name="logo" accept="image/*">
                                @error('logo')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror"
                                          id="description" name="description" rows="3"
                                          placeholder="Brief description of your business">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-address-book mr-2"></i>
                        Contact Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror"
                                       id="email" name="email" value="{{ old('email') }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">Phone</label>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                       id="phone" name="phone" value="{{ old('phone') }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="website">Website</label>
                                <input type="url" class="form-control @error('website') is-invalid @enderror"
                                       id="website" name="website" value="{{ old('website') }}"
                                       placeholder="https://example.com">
                                @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Settings -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs mr-2"></i>
                        Business Settings
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="timezone">Timezone <span class="text-danger">*</span></label>
                                <select class="form-control @error('timezone') is-invalid @enderror"
                                        id="timezone" name="timezone" required>
                                    <option value="">Select Timezone</option>
                                    <option value="UTC" {{ old('timezone') == 'UTC' ? 'selected' : '' }}>UTC</option>
                                    <option value="America/New_York" {{ old('timezone') == 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                    <option value="America/Chicago" {{ old('timezone') == 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                    <option value="America/Denver" {{ old('timezone') == 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                    <option value="America/Los_Angeles" {{ old('timezone') == 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                    <option value="Europe/London" {{ old('timezone') == 'Europe/London' ? 'selected' : '' }}>London</option>
                                    <option value="Europe/Paris" {{ old('timezone') == 'Europe/Paris' ? 'selected' : '' }}>Paris</option>
                                    <option value="Asia/Tokyo" {{ old('timezone') == 'Asia/Tokyo' ? 'selected' : '' }}>Tokyo</option>
                                </select>
                                @error('timezone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="currency">Currency <span class="text-danger">*</span></label>
                                <select class="form-control @error('currency') is-invalid @enderror"
                                        id="currency" name="currency" required>
                                    <option value="">Select Currency</option>
                                    <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                    <option value="EUR" {{ old('currency') == 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                    <option value="GBP" {{ old('currency') == 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                    <option value="CAD" {{ old('currency') == 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                                    <option value="AUD" {{ old('currency') == 'AUD' ? 'selected' : '' }}>AUD - Australian Dollar</option>
                                    <option value="JPY" {{ old('currency') == 'JPY' ? 'selected' : '' }}>JPY - Japanese Yen</option>
                                </select>
                                @error('currency')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="language">Language <span class="text-danger">*</span></label>
                                <select class="form-control @error('language') is-invalid @enderror"
                                        id="language" name="language" required>
                                    <option value="">Select Language</option>
                                    <option value="en" {{ old('language') == 'en' ? 'selected' : '' }}>English</option>
                                    <option value="es" {{ old('language') == 'es' ? 'selected' : '' }}>Spanish</option>
                                    <option value="fr" {{ old('language') == 'fr' ? 'selected' : '' }}>French</option>
                                    <option value="de" {{ old('language') == 'de' ? 'selected' : '' }}>German</option>
                                    <option value="it" {{ old('language') == 'it' ? 'selected' : '' }}>Italian</option>
                                    <option value="pt" {{ old('language') == 'pt' ? 'selected' : '' }}>Portuguese</option>
                                </select>
                                @error('language')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Settings -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Booking Settings
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="booking_advance_days">Max Advance Booking (days) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('booking_advance_days') is-invalid @enderror"
                                       id="booking_advance_days" name="booking_advance_days"
                                       value="{{ old('booking_advance_days', 30) }}" min="1" max="365" required>
                                @error('booking_advance_days')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">How far in advance customers can book</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="booking_advance_hours">Min Advance Booking (hours) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('booking_advance_hours') is-invalid @enderror"
                                       id="booking_advance_hours" name="booking_advance_hours"
                                       value="{{ old('booking_advance_hours', 2) }}" min="1" max="72" required>
                                @error('booking_advance_hours')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Minimum notice required</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="cancellation_hours">Cancellation Notice (hours) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('cancellation_hours') is-invalid @enderror"
                                       id="cancellation_hours" name="cancellation_hours"
                                       value="{{ old('cancellation_hours', 24) }}" min="1" max="168" required>
                                @error('cancellation_hours')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Hours before booking for free cancellation</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="multi_branch"
                                       name="multi_branch" value="1" {{ old('multi_branch') ? 'checked' : '' }}>
                                <label class="form-check-label" for="multi_branch">
                                    Multi-Branch Business
                                </label>
                                <small class="form-text text-muted">Enable if you have multiple locations</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="online_booking_enabled"
                                       name="online_booking_enabled" value="1" {{ old('online_booking_enabled', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="online_booking_enabled">
                                    Enable Online Booking
                                </label>
                                <small class="form-text text-muted">Allow customers to book online</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories & Tags -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tags mr-2"></i>
                        Categories & Tags
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="categories">Business Categories</label>
                                <select class="form-control @error('categories') is-invalid @enderror"
                                        id="categories" name="categories[]" multiple>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}"
                                                {{ in_array($category->id, old('categories', [])) ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('categories')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Hold Ctrl/Cmd to select multiple</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tags">Business Tags</label>
                                <select class="form-control @error('tags') is-invalid @enderror"
                                        id="tags" name="tags[]" multiple>
                                    @foreach($tags as $tag)
                                        <option value="{{ $tag->id }}"
                                                {{ in_array($tag->id, old('tags', [])) ? 'selected' : '' }}>
                                            {{ $tag->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('tags')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Hold Ctrl/Cmd to select multiple</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Branding -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-palette mr-2"></i>
                        Branding (Optional)
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="primary_color">Primary Color</label>
                                <input type="color" class="form-control @error('branding.primary_color') is-invalid @enderror"
                                       id="primary_color" name="branding[primary_color]"
                                       value="{{ old('branding.primary_color', '#007bff') }}">
                                @error('branding.primary_color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="secondary_color">Secondary Color</label>
                                <input type="color" class="form-control @error('branding.secondary_color') is-invalid @enderror"
                                       id="secondary_color" name="branding[secondary_color]"
                                       value="{{ old('branding.secondary_color', '#6c757d') }}">
                                @error('branding.secondary_color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="theme">Theme</label>
                                <select class="form-control @error('branding.theme') is-invalid @enderror"
                                        id="theme" name="branding[theme]">
                                    <option value="">Default</option>
                                    <option value="light" {{ old('branding.theme') == 'light' ? 'selected' : '' }}>Light</option>
                                    <option value="dark" {{ old('branding.theme') == 'dark' ? 'selected' : '' }}>Dark</option>
                                    <option value="modern" {{ old('branding.theme') == 'modern' ? 'selected' : '' }}>Modern</option>
                                </select>
                                @error('branding.theme')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="card">
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save mr-2"></i>
                                Create Business
                            </button>
                            <a href="{{ route('admin.businesses.index') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                        <div class="col-md-6">
                            <div class="text-right">
                                <a href="{{ route('admin.businesses.wizard') }}" class="btn btn-info">
                                    <i class="fas fa-magic mr-2"></i>
                                    Use Setup Wizard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // Form validation
    $('#business-form').on('submit', function(e) {
        // Add any custom validation logic here
    });

    // Logo preview
    $('#logo').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // You can add logo preview functionality here
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@stop
