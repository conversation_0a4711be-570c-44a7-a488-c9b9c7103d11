@extends('admin.layouts.app')

@section('title', 'Edit Location')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Edit Location</h1>
            <p class="text-muted">{{ $business->name }} - {{ $branch->name ?: 'Main Location' }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.locations.show', [$business, $branch]) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Location
                </a>
                <a href="{{ route('admin.businesses.locations.index', $business) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Locations
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit mr-2"></i>
                        Location Information
                    </h3>
                    @if($branch->is_main_branch)
                        <div class="card-tools">
                            <span class="badge badge-primary">Main Branch</span>
                        </div>
                    @endif
                </div>

                <form action="{{ route('admin.businesses.locations.update', [$business, $branch]) }}" method="POST" id="location-form">
                    @csrf
                    @method('PUT')

                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Location Name</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name" value="{{ old('name', $branch->name) }}"
                                           placeholder="e.g., Downtown Branch, Main Office">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Leave blank to use "Main Location"</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox"
                                               id="is_active" name="is_active" value="1"
                                               {{ old('is_active', $branch->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active Location
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="address_line_1">Street Address <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('address_line_1') is-invalid @enderror"
                                   id="address_line_1" name="address_line_1" value="{{ old('address_line_1', $branch->address) }}" required
                                   placeholder="123 Main Street">
                            @error('address_line_1')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="address_line_2">Address Line 2</label>
                            <input type="text" class="form-control @error('address_line_2') is-invalid @enderror"
                                   id="address_line_2" name="address_line_2" value="{{ old('address_line_2') }}"
                                   placeholder="Apartment, suite, etc.">
                            @error('address_line_2')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="city">City <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('city') is-invalid @enderror"
                                           id="city" name="city" value="{{ old('city', $branch->city) }}" required>
                                    @error('city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="state">State/Province</label>
                                    <input type="text" class="form-control @error('state') is-invalid @enderror"
                                           id="state" name="state" value="{{ old('state', $location->state) }}">
                                    @error('state')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="postal_code">Postal Code</label>
                                    <input type="text" class="form-control @error('postal_code') is-invalid @enderror"
                                           id="postal_code" name="postal_code" value="{{ old('postal_code', $location->postal_code) }}">
                                    @error('postal_code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="country">Country</label>
                            <select class="form-control @error('country') is-invalid @enderror" id="country" name="country">
                                <option value="">Select Country</option>
                                <option value="US" {{ old('country', $location->country) == 'US' ? 'selected' : '' }}>United States</option>
                                <option value="CA" {{ old('country', $location->country) == 'CA' ? 'selected' : '' }}>Canada</option>
                                <option value="GB" {{ old('country', $location->country) == 'GB' ? 'selected' : '' }}>United Kingdom</option>
                                <option value="AU" {{ old('country', $location->country) == 'AU' ? 'selected' : '' }}>Australia</option>
                                <option value="DE" {{ old('country', $location->country) == 'DE' ? 'selected' : '' }}>Germany</option>
                                <option value="FR" {{ old('country', $location->country) == 'FR' ? 'selected' : '' }}>France</option>
                                <option value="ES" {{ old('country', $location->country) == 'ES' ? 'selected' : '' }}>Spain</option>
                                <option value="IT" {{ old('country', $location->country) == 'IT' ? 'selected' : '' }}>Italy</option>
                            </select>
                            @error('country')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr>

                        <h5>Contact Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                           id="phone" name="phone" value="{{ old('phone', $location->phone) }}"
                                           placeholder="+****************">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email', $location->email) }}"
                                           placeholder="<EMAIL>">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <hr>

                        <h5>Map Coordinates (Optional)</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="latitude">Latitude</label>
                                    <input type="number" step="any" class="form-control @error('latitude') is-invalid @enderror"
                                           id="latitude" name="latitude" value="{{ old('latitude', $location->latitude) }}"
                                           placeholder="40.7128">
                                    @error('latitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="longitude">Longitude</label>
                                    <input type="number" step="any" class="form-control @error('longitude') is-invalid @enderror"
                                           id="longitude" name="longitude" value="{{ old('longitude', $location->longitude) }}"
                                           placeholder="-74.0060">
                                    @error('longitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="button" class="btn btn-outline-info" id="geocode-btn">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                Auto-detect Coordinates
                            </button>
                            <small class="form-text text-muted">Click to automatically find coordinates based on the address</small>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Update Location
                        </button>
                        <a href="{{ route('admin.businesses.locations.show', [$business, $location]) }}" class="btn btn-secondary">
                            Cancel
                        </a>
                        @if(!$location->is_main)
                            <form action="{{ route('admin.businesses.locations.destroy', [$business, $location]) }}"
                                  method="POST" class="d-inline float-right"
                                  onsubmit="return confirm('Are you sure you want to delete this location? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Location
                                </button>
                            </form>
                        @endif
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Location Guidelines
                    </h3>
                </div>
                <div class="card-body">
                    <p><strong>Location Name:</strong></p>
                    <p class="text-muted">Give your location a descriptive name to distinguish it from other branches.</p>

                    <p><strong>Address:</strong></p>
                    <p class="text-muted">Provide a complete address for accurate mapping and customer directions.</p>

                    <p><strong>Contact Info:</strong></p>
                    <p class="text-muted">Add phone and email for location-specific customer inquiries.</p>

                    <p><strong>Coordinates:</strong></p>
                    <p class="text-muted">Precise coordinates help with map accuracy. Use auto-detect for convenience.</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history mr-2"></i>
                        Location History
                    </h3>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>
                                {{ $location->created_at->format('M j, Y') }}
                                <br><small class="text-muted">{{ $location->created_at->diffForHumans() }}</small>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Last Updated:</strong></td>
                            <td>
                                {{ $location->updated_at->format('M j, Y') }}
                                <br><small class="text-muted">{{ $location->updated_at->diffForHumans() }}</small>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            @if($location->is_main)
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title">
                            <i class="fas fa-star mr-2"></i>
                            Main Branch
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            This is your main business location. It cannot be deleted and serves as the primary address for your business.
                        </p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Auto-detect coordinates based on address
    $('#geocode-btn').click(function() {
        var address = $('#address').val();
        var city = $('#city').val();
        var state = $('#state').val();
        var country = $('#country').val();

        if (!address || !city) {
            alert('Please enter at least the street address and city before auto-detecting coordinates.');
            return;
        }

        var fullAddress = address + ', ' + city;
        if (state) fullAddress += ', ' + state;
        if (country) fullAddress += ', ' + country;

        // This would integrate with a geocoding service
        // For now, show a placeholder message
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Detecting...');

        setTimeout(function() {
            alert('Geocoding integration coming soon. Please enter coordinates manually for now.');
            $('#geocode-btn').prop('disabled', false).html('<i class="fas fa-map-marker-alt mr-2"></i>Auto-detect Coordinates');
        }, 2000);
    });

    // Format phone number as user types
    $('#phone').on('input', function() {
        var value = $(this).val().replace(/\D/g, '');
        var formattedValue = '';

        if (value.length > 0) {
            if (value.length <= 3) {
                formattedValue = '(' + value;
            } else if (value.length <= 6) {
                formattedValue = '(' + value.substring(0, 3) + ') ' + value.substring(3);
            } else {
                formattedValue = '(' + value.substring(0, 3) + ') ' + value.substring(3, 6) + '-' + value.substring(6, 10);
            }
        }

        $(this).val(formattedValue);
    });
});
</script>
@stop
