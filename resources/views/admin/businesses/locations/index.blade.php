@extends('admin.layouts.app')

@section('title', 'Business Locations')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Business Locations</h1>
            <p class="text-muted">{{ $business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.locations.create', $business) }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    Add Location
                </a>
                <a href="{{ route('admin.businesses.show', $business) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Business
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        Business Branches & Locations
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-info" onclick="loadMapView()">
                            <i class="fas fa-map mr-2"></i>
                            Map View
                        </button>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    @if($branches->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Location Name</th>
                                        <th>Address</th>
                                        <th>Contact</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($branches as $branch)
                                        <tr>
                                            <td>
                                                <strong>{{ $branch->name ?: 'Main Location' }}</strong>
                                                @if($branch->is_main)
                                                    <br><span class="badge badge-primary">Main Branch</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($branch->address)
                                                    {{ $branch->address }}
                                                    @if($branch->city), {{ $branch->city }}@endif
                                                    @if($branch->state), {{ $branch->state }}@endif
                                                    @if($branch->postal_code) {{ $branch->postal_code }}@endif
                                                    @if($branch->country)
                                                        <br><small class="text-muted">{{ $branch->country }}</small>
                                                    @endif
                                                @else
                                                    <span class="text-muted">No address set</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($branch->phone)
                                                    <i class="fas fa-phone mr-1"></i>{{ $branch->phone }}<br>
                                                @endif
                                                @if($branch->email)
                                                    <i class="fas fa-envelope mr-1"></i>{{ $branch->email }}
                                                @endif
                                                @if(!$branch->phone && !$branch->email)
                                                    <span class="text-muted">No contact info</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($branch->is_active)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.businesses.locations.show', [$business, $branch]) }}"
                                                       class="btn btn-sm btn-info" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.businesses.locations.edit', [$business, $branch]) }}"
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if(!$branch->is_main)
                                                        <form action="{{ route('admin.businesses.locations.destroy', [$business, $branch]) }}"
                                                              method="POST" class="d-inline"
                                                              onsubmit="return confirm('Are you sure you want to delete this location?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h4>No locations found</h4>
                            <p class="text-muted">Add your business locations to help customers find you.</p>
                            <a href="{{ route('admin.businesses.locations.create', $business) }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-2"></i>
                                Add First Location
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Location Management
                    </h3>
                </div>
                <div class="card-body">
                    <p><strong>Business Locations:</strong></p>
                    <p class="text-muted">Manage all your business branches and locations from here.</p>
                    
                    <p><strong>Main Branch:</strong></p>
                    <p class="text-muted">Your primary business location. This cannot be deleted.</p>
                    
                    <p><strong>Map Integration:</strong></p>
                    <p class="text-muted">Locations with complete addresses will appear on maps for customers.</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Quick Stats
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">{{ $branches->count() }}</h4>
                                <small class="text-muted">Total Locations</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success">{{ $branches->where('is_active', true)->count() }}</h4>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-info">{{ $branches->whereNotNull('latitude')->count() }}</h4>
                                <small class="text-muted">On Map</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-warning">{{ $branches->whereNotNull('phone')->count() }}</h4>
                                <small class="text-muted">With Phone</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card" id="map-container" style="display: none;">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-map mr-2"></i>
                        Locations Map
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="hideMapView()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="locations-map" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize map functionality if needed
});

function loadMapView() {
    // Show map container
    $('#map-container').show();
    
    // Load map data via AJAX
    $.get('{{ route("admin.businesses.locations.map-data", $business) }}')
        .done(function(data) {
            // Initialize map with location data
            initializeMap(data);
        })
        .fail(function() {
            alert('Failed to load map data');
        });
}

function hideMapView() {
    $('#map-container').hide();
}

function initializeMap(locations) {
    // This would integrate with Google Maps or another mapping service
    // For now, just show a placeholder
    $('#locations-map').html('<div class="text-center p-4"><i class="fas fa-map fa-3x text-muted"></i><br><p class="text-muted mt-2">Map integration coming soon</p></div>');
}
</script>
@stop
