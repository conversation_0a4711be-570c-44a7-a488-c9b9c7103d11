@extends('admin.layouts.app')

@section('title', 'Location Details')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>{{ $branch->name ?: 'Main Location' }}</h1>
            <p class="text-muted">{{ $business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.locations.edit', [$business, $branch]) }}" class="btn btn-primary">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Location
                </a>
                <a href="{{ route('admin.businesses.locations.index', $business) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Locations
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        Location Information
                    </h3>
                    <div class="card-tools">
                        @if($branch->is_active)
                            <span class="badge badge-success">Active</span>
                        @else
                            <span class="badge badge-danger">Inactive</span>
                        @endif
                        @if($branch->is_main_branch)
                            <span class="badge badge-primary">Main Branch</span>
                        @endif
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Basic Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $branch->name ?: 'Main Location' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($branch->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Type:</strong></td>
                                    <td>
                                        @if($branch->is_main_branch)
                                            <span class="badge badge-primary">Main Branch</span>
                                        @else
                                            <span class="badge badge-secondary">Branch</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>
                                        {{ $branch->created_at->format('M j, Y') }}
                                        <br><small class="text-muted">{{ $branch->created_at->diffForHumans() }}</small>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Contact Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>
                                        @if($location->phone)
                                            <a href="tel:{{ $location->phone }}">{{ $location->phone }}</a>
                                        @else
                                            <span class="text-muted">Not provided</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>
                                        @if($location->email)
                                            <a href="mailto:{{ $location->email }}">{{ $location->email }}</a>
                                        @else
                                            <span class="text-muted">Not provided</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td>
                                        {{ $location->updated_at->format('M j, Y') }}
                                        <br><small class="text-muted">{{ $location->updated_at->diffForHumans() }}</small>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-12">
                            <h5>Address</h5>
                            @if($location->address)
                                <div class="alert alert-info">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    <strong>{{ $location->address }}</strong><br>
                                    {{ $location->city }}@if($location->state), {{ $location->state }}@endif @if($location->postal_code){{ $location->postal_code }}@endif<br>
                                    @if($location->country){{ $location->country }}@endif
                                </div>

                                @if($location->latitude && $location->longitude)
                                    <p class="text-muted">
                                        <i class="fas fa-crosshairs mr-2"></i>
                                        Coordinates: {{ $location->latitude }}, {{ $location->longitude }}
                                    </p>
                                @endif
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    No address information provided.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <a href="{{ route('admin.businesses.locations.edit', [$business, $location]) }}" class="btn btn-primary">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Location
                    </a>
                    @if(!$location->is_main)
                        <form action="{{ route('admin.businesses.locations.destroy', [$business, $location]) }}"
                              method="POST" class="d-inline ml-2"
                              onsubmit="return confirm('Are you sure you want to delete this location? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Location
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            @if($location->latitude && $location->longitude)
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map mr-2"></i>
                            Map Location
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        <div id="location-map" style="height: 250px;">
                            <div class="text-center p-4">
                                <i class="fas fa-map fa-3x text-muted"></i>
                                <br><p class="text-muted mt-2">Map integration coming soon</p>
                                <small class="text-muted">{{ $location->latitude }}, {{ $location->longitude }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <a href="https://maps.google.com/?q={{ $location->latitude }},{{ $location->longitude }}"
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            View on Google Maps
                        </a>
                    </div>
                </div>
            @endif

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Quick Info
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">
                                    @if($location->is_active)
                                        <i class="fas fa-check-circle"></i>
                                    @else
                                        <i class="fas fa-times-circle"></i>
                                    @endif
                                </h4>
                                <small class="text-muted">Status</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-info">
                                    @if($location->latitude && $location->longitude)
                                        <i class="fas fa-map-marker-alt"></i>
                                    @else
                                        <i class="fas fa-map-marker text-muted"></i>
                                    @endif
                                </h4>
                                <small class="text-muted">Mapped</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.businesses.locations.edit', [$business, $location]) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Details
                        </a>
                        <a href="{{ route('admin.businesses.locations.create', $business) }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus mr-2"></i>
                            Add Another Location
                        </a>
                        <a href="{{ route('admin.businesses.locations.index', $business) }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list mr-2"></i>
                            View All Locations
                        </a>
                        @if($location->address)
                            <a href="https://maps.google.com/?q={{ urlencode($location->address . ', ' . $location->city) }}"
                               target="_blank" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-directions mr-2"></i>
                                Get Directions
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            @if($location->is_main)
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title">
                            <i class="fas fa-star mr-2"></i>
                            Main Branch
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            This is your main business location. It cannot be deleted and serves as the primary address for your business.
                        </p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@stop
