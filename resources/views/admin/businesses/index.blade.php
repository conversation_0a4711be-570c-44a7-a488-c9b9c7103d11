@extends('admin.layouts.app')

@section('title', 'Businesses')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Businesses</h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.wizard') }}" class="btn btn-success">
                    <i class="fas fa-magic mr-2"></i>
                    Setup Wizard
                </a>
                @can('create businesses')
                    <a href="{{ route('admin.businesses.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add Business
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.businesses.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ request('search') }}" placeholder="Business name or email">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select class="form-control" id="category" name="category">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}"
                                            {{ request('category') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search mr-2"></i>
                                    Filter
                                </button>
                                <a href="{{ route('admin.businesses.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times mr-2"></i>
                                    Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Businesses List -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-building mr-2"></i>
                Businesses ({{ $businesses->total() }})
            </h3>
        </div>
        <div class="card-body p-0">
            @if($businesses->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Business</th>
                                <th>Owner</th>
                                <th>Categories</th>
                                <th>Branches</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($businesses as $business)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($business->logo)
                                                <img src="{{ asset('storage/' . $business->logo) }}"
                                                     alt="{{ $business->name }}"
                                                     class="img-circle mr-3"
                                                     style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-3"
                                                     style="width: 40px; height: 40px;">
                                                    {{ strtoupper(substr($business->name, 0, 1)) }}
                                                </div>
                                            @endif
                                            <div>
                                                <strong>{{ $business->name }}</strong>
                                                @if($business->email)
                                                    <br><small class="text-muted">{{ $business->email }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ $business->owner->name }}</strong>
                                        <br><small class="text-muted">{{ $business->owner->email }}</small>
                                    </td>
                                    <td>
                                        @foreach($business->categories as $category)
                                            <span class="badge badge-secondary" style="background-color: {{ $category->color }}">
                                                <i class="{{ $category->icon }} mr-1"></i>
                                                {{ $category->name }}
                                            </span>
                                        @endforeach
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ $business->branches->count() }}
                                            {{ Str::plural('branch', $business->branches->count()) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($business->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $business->created_at->format('M d, Y') }}
                                        <br><small class="text-muted">{{ $business->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.businesses.show', $business) }}"
                                               class="btn btn-sm btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @can('edit businesses')
                                                <a href="{{ route('admin.businesses.edit', $business) }}"
                                                   class="btn btn-sm btn-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                            @can('delete businesses')
                                                <form action="{{ route('admin.businesses.destroy', $business) }}"
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this business?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h4>No businesses found</h4>
                    <p class="text-muted">Get started by creating your first business.</p>
                    <a href="{{ route('admin.businesses.wizard') }}" class="btn btn-primary">
                        <i class="fas fa-magic mr-2"></i>
                        Start Setup Wizard
                    </a>
                </div>
            @endif
        </div>
        @if($businesses->hasPages())
            <div class="card-footer">
                {{ $businesses->links() }}
            </div>
        @endif
    </div>
</div>
@stop
