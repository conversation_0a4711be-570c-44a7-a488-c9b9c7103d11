@extends('admin.layouts.app')

@section('title', 'Edit Holiday')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Edit Holiday</h1>
            <p class="text-muted">{{ $business->name }} - {{ $holiday->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.holidays.show', [$business, $holiday]) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Holiday
                </a>
                <a href="{{ route('admin.businesses.holidays.index', $business) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Holidays
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit mr-2"></i>
                        Holiday Information
                    </h3>
                </div>
                
                <form action="{{ route('admin.businesses.holidays.update', [$business, $holiday]) }}" method="POST" id="holiday-form">
                    @csrf
                    @method('PUT')
                    
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Holiday Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $holiday->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" 
                                               id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', $holiday->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active Holiday
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $holiday->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">Start Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                           id="start_date" name="start_date" 
                                           value="{{ old('start_date', $holiday->start_date->format('Y-m-d')) }}" required>
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">End Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                           id="end_date" name="end_date" 
                                           value="{{ old('end_date', $holiday->end_date->format('Y-m-d')) }}" required>
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       id="is_recurring" name="is_recurring" value="1" 
                                       {{ old('is_recurring', $holiday->is_recurring) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_recurring">
                                    Recurring Holiday
                                </label>
                            </div>
                        </div>

                        <div class="form-group" id="recurrence-options" 
                             style="{{ old('is_recurring', $holiday->is_recurring) ? '' : 'display: none;' }}">
                            <label for="recurrence_type">Recurrence Type</label>
                            <select class="form-control @error('recurrence_type') is-invalid @enderror" 
                                    id="recurrence_type" name="recurrence_type">
                                <option value="">Select recurrence type...</option>
                                <option value="yearly" {{ old('recurrence_type', $holiday->recurrence_type) == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                <option value="monthly" {{ old('recurrence_type', $holiday->recurrence_type) == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                <option value="weekly" {{ old('recurrence_type', $holiday->recurrence_type) == 'weekly' ? 'selected' : '' }}>Weekly</option>
                            </select>
                            @error('recurrence_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Update Holiday
                        </button>
                        <a href="{{ route('admin.businesses.holidays.show', [$business, $holiday]) }}" class="btn btn-secondary">
                            Cancel
                        </a>
                        <form action="{{ route('admin.businesses.holidays.destroy', [$business, $holiday]) }}" 
                              method="POST" class="d-inline float-right"
                              onsubmit="return confirm('Are you sure you want to delete this holiday? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Holiday
                            </button>
                        </form>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Holiday Guidelines
                    </h3>
                </div>
                <div class="card-body">
                    <p><strong>Holiday Name:</strong></p>
                    <p class="text-muted">Give your holiday a clear, descriptive name.</p>
                    
                    <p><strong>Date Range:</strong></p>
                    <p class="text-muted">Set the start and end dates for the holiday. For single-day holidays, use the same date for both.</p>
                    
                    <p><strong>Recurring Holidays:</strong></p>
                    <p class="text-muted">Enable this for holidays that repeat annually (like Christmas), monthly, or weekly.</p>
                    
                    <p><strong>Active Status:</strong></p>
                    <p class="text-muted">Inactive holidays won't affect booking availability but remain in your records.</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history mr-2"></i>
                        Holiday History
                    </h3>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>
                                {{ $holiday->created_at->format('M j, Y') }}
                                <br><small class="text-muted">{{ $holiday->created_at->diffForHumans() }}</small>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Last Updated:</strong></td>
                            <td>
                                {{ $holiday->updated_at->format('M j, Y') }}
                                <br><small class="text-muted">{{ $holiday->updated_at->diffForHumans() }}</small>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.businesses.holidays.show', [$business, $holiday]) }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-eye mr-2"></i>
                            View Details
                        </a>
                        <a href="{{ route('admin.businesses.holidays.create', $business) }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus mr-2"></i>
                            Add Another Holiday
                        </a>
                        <a href="{{ route('admin.businesses.holidays.index', $business) }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list mr-2"></i>
                            View All Holidays
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle recurrence options
    $('#is_recurring').change(function() {
        if ($(this).is(':checked')) {
            $('#recurrence-options').show();
            $('#recurrence_type').prop('required', true);
        } else {
            $('#recurrence-options').hide();
            $('#recurrence_type').prop('required', false).val('');
        }
    });

    // Validate date range
    $('#end_date').change(function() {
        var startDate = $('#start_date').val();
        var endDate = $(this).val();
        
        if (startDate && endDate && endDate < startDate) {
            alert('End date cannot be before start date.');
            $(this).val(startDate);
        }
    });
});
</script>
@stop
