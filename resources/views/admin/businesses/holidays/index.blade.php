@extends('admin.layouts.app')

@section('title', 'Business Holidays')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Business Holidays</h1>
            <p class="text-muted">{{ $business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.holidays.create', $business) }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    Add Holiday
                </a>
                <a href="{{ route('admin.businesses.show', $business) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Business
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-times mr-2"></i>
                        Holidays for {{ $year }}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#importModal">
                            <i class="fas fa-download mr-2"></i>
                            Import Common Holidays
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                Year: {{ $year }}
                            </button>
                            <div class="dropdown-menu">
                                @for($y = date('Y') - 2; $y <= date('Y') + 2; $y++)
                                    <a class="dropdown-item" href="{{ route('admin.businesses.holidays.index', ['business' => $business, 'year' => $y]) }}">
                                        {{ $y }}
                                    </a>
                                @endfor
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    @if($holidays->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Holiday Name</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($holidays as $holiday)
                                        <tr>
                                            <td>
                                                <strong>{{ $holiday->name }}</strong>
                                                @if($holiday->description)
                                                    <br><small class="text-muted">{{ Str::limit($holiday->description, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                {{ $holiday->start_date->format('M d, Y') }}
                                                <br><small class="text-muted">{{ $holiday->start_date->format('l') }}</small>
                                            </td>
                                            <td>
                                                {{ $holiday->end_date->format('M d, Y') }}
                                                @if($holiday->start_date->format('Y-m-d') !== $holiday->end_date->format('Y-m-d'))
                                                    <br><small class="text-muted">{{ $holiday->end_date->diffInDays($holiday->start_date) + 1 }} days</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($holiday->is_recurring)
                                                    <span class="badge badge-info">
                                                        <i class="fas fa-redo mr-1"></i>
                                                        {{ ucfirst($holiday->recurrence_type) }}
                                                    </span>
                                                @else
                                                    <span class="badge badge-secondary">One-time</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($holiday->is_active)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.businesses.holidays.show', [$business, $holiday]) }}"
                                                       class="btn btn-sm btn-info" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.businesses.holidays.edit', [$business, $holiday]) }}"
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.businesses.holidays.destroy', [$business, $holiday]) }}"
                                                          method="POST" class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to delete this holiday?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h4>No holidays found for {{ $year }}</h4>
                            <p class="text-muted">Add holidays to mark days when your business is closed.</p>
                            <a href="{{ route('admin.businesses.holidays.create', $business) }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-2"></i>
                                Add First Holiday
                            </a>
                        </div>
                    @endif
                </div>
                
                @if($holidays->hasPages())
                    <div class="card-footer">
                        {{ $holidays->appends(['year' => $year])->links() }}
                    </div>
                @endif
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Information
                    </h3>
                </div>
                <div class="card-body">
                    <p><strong>Business Holidays:</strong></p>
                    <p class="text-muted">Mark days when your business is closed for holidays, special events, or maintenance.</p>
                    
                    <p><strong>Recurring Holidays:</strong></p>
                    <p class="text-muted">Set holidays that repeat annually, monthly, or weekly.</p>
                    
                    <p><strong>Import Common Holidays:</strong></p>
                    <p class="text-muted">Quickly add standard holidays for your country or region.</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar mr-2"></i>
                        Quick Stats
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">{{ $holidays->total() }}</h4>
                                <small class="text-muted">Total Holidays</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-info">{{ $holidays->where('is_recurring', true)->count() }}</h4>
                                <small class="text-muted">Recurring</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Common Holidays Modal -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Common Holidays</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.businesses.holidays.import-common', $business) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="country">Country/Region</label>
                        <select name="country" id="country" class="form-control" required>
                            <option value="">Select country...</option>
                            <option value="US">United States</option>
                            <option value="CA">Canada</option>
                            <option value="UK">United Kingdom</option>
                            <option value="AU">Australia</option>
                            <option value="DE">Germany</option>
                            <option value="FR">France</option>
                            <option value="ES">Spain</option>
                            <option value="IT">Italy</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="import_year">Year</label>
                        <select name="year" id="import_year" class="form-control" required>
                            @for($y = date('Y'); $y <= date('Y') + 2; $y++)
                                <option value="{{ $y }}" {{ $y == $year ? 'selected' : '' }}>{{ $y }}</option>
                            @endfor
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        This will import standard holidays for the selected country and year.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Import Holidays</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Any additional JavaScript for holidays management
});
</script>
@stop
