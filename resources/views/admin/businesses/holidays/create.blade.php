@extends('admin.layouts.app')

@section('title', 'Add Holiday')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Add Holiday</h1>
            <p class="text-muted">{{ $business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.holidays.index', $business) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Holidays
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Holiday Information
                    </h3>
                </div>
                
                <form action="{{ route('admin.businesses.holidays.store', $business) }}" method="POST" id="holiday-form">
                    @csrf
                    
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Holiday Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" 
                                               id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active Holiday
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">Start Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                           id="start_date" name="start_date" value="{{ old('start_date') }}" required>
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="end_date">End Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                           id="end_date" name="end_date" value="{{ old('end_date') }}" required>
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       id="is_recurring" name="is_recurring" value="1" 
                                       {{ old('is_recurring') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_recurring">
                                    Recurring Holiday
                                </label>
                            </div>
                        </div>

                        <div class="form-group" id="recurrence-options" style="{{ old('is_recurring') ? '' : 'display: none;' }}">
                            <label for="recurrence_type">Recurrence Type</label>
                            <select class="form-control @error('recurrence_type') is-invalid @enderror" 
                                    id="recurrence_type" name="recurrence_type">
                                <option value="">Select recurrence type...</option>
                                <option value="yearly" {{ old('recurrence_type') == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                <option value="monthly" {{ old('recurrence_type') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                <option value="weekly" {{ old('recurrence_type') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                            </select>
                            @error('recurrence_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Holiday
                        </button>
                        <a href="{{ route('admin.businesses.holidays.index', $business) }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Holiday Guidelines
                    </h3>
                </div>
                <div class="card-body">
                    <p><strong>Holiday Name:</strong></p>
                    <p class="text-muted">Give your holiday a clear, descriptive name.</p>
                    
                    <p><strong>Date Range:</strong></p>
                    <p class="text-muted">Set the start and end dates for the holiday. For single-day holidays, use the same date for both.</p>
                    
                    <p><strong>Recurring Holidays:</strong></p>
                    <p class="text-muted">Enable this for holidays that repeat annually (like Christmas), monthly, or weekly.</p>
                    
                    <p><strong>Active Status:</strong></p>
                    <p class="text-muted">Inactive holidays won't affect booking availability but remain in your records.</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-lightbulb mr-2"></i>
                        Quick Tips
                    </h3>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success mr-2"></i>Use recurring holidays for annual events</li>
                        <li><i class="fas fa-check text-success mr-2"></i>Set multi-day holidays for vacation periods</li>
                        <li><i class="fas fa-check text-success mr-2"></i>Add descriptions for special instructions</li>
                        <li><i class="fas fa-check text-success mr-2"></i>Import common holidays to save time</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle recurrence options
    $('#is_recurring').change(function() {
        if ($(this).is(':checked')) {
            $('#recurrence-options').show();
            $('#recurrence_type').prop('required', true);
        } else {
            $('#recurrence-options').hide();
            $('#recurrence_type').prop('required', false).val('');
        }
    });

    // Set end date to start date if not set
    $('#start_date').change(function() {
        var startDate = $(this).val();
        var endDate = $('#end_date').val();
        
        if (startDate && !endDate) {
            $('#end_date').val(startDate);
        }
    });

    // Validate date range
    $('#end_date').change(function() {
        var startDate = $('#start_date').val();
        var endDate = $(this).val();
        
        if (startDate && endDate && endDate < startDate) {
            alert('End date cannot be before start date.');
            $(this).val(startDate);
        }
    });
});
</script>
@stop
