@extends('admin.layouts.app')

@section('title', 'Holiday Details')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>{{ $holiday->name }}</h1>
            <p class="text-muted">{{ $business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.holidays.edit', [$business, $holiday]) }}" class="btn btn-primary">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Holiday
                </a>
                <a href="{{ route('admin.businesses.holidays.index', $business) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Holidays
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-times mr-2"></i>
                        Holiday Information
                    </h3>
                    <div class="card-tools">
                        @if($holiday->is_active)
                            <span class="badge badge-success">Active</span>
                        @else
                            <span class="badge badge-danger">Inactive</span>
                        @endif
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Basic Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $holiday->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Start Date:</strong></td>
                                    <td>
                                        {{ $holiday->start_date->format('F j, Y') }}
                                        <br><small class="text-muted">{{ $holiday->start_date->format('l') }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>End Date:</strong></td>
                                    <td>
                                        {{ $holiday->end_date->format('F j, Y') }}
                                        @if($holiday->start_date->format('Y-m-d') !== $holiday->end_date->format('Y-m-d'))
                                            <br><small class="text-muted">{{ $holiday->end_date->diffInDays($holiday->start_date) + 1 }} days total</small>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($holiday->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Recurrence Settings</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Recurring:</strong></td>
                                    <td>
                                        @if($holiday->is_recurring)
                                            <span class="badge badge-info">
                                                <i class="fas fa-redo mr-1"></i>
                                                Yes
                                            </span>
                                        @else
                                            <span class="badge badge-secondary">One-time</span>
                                        @endif
                                    </td>
                                </tr>
                                @if($holiday->is_recurring)
                                    <tr>
                                        <td><strong>Recurrence Type:</strong></td>
                                        <td>
                                            <span class="badge badge-primary">{{ ucfirst($holiday->recurrence_type) }}</span>
                                        </td>
                                    </tr>
                                @endif
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>
                                        {{ $holiday->created_at->format('M j, Y') }}
                                        <br><small class="text-muted">{{ $holiday->created_at->diffForHumans() }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td>
                                        {{ $holiday->updated_at->format('M j, Y') }}
                                        <br><small class="text-muted">{{ $holiday->updated_at->diffForHumans() }}</small>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($holiday->description)
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>Description</h5>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    {{ $holiday->description }}
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
                
                <div class="card-footer">
                    <a href="{{ route('admin.businesses.holidays.edit', [$business, $holiday]) }}" class="btn btn-primary">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Holiday
                    </a>
                    <form action="{{ route('admin.businesses.holidays.destroy', [$business, $holiday]) }}" 
                          method="POST" class="d-inline ml-2"
                          onsubmit="return confirm('Are you sure you want to delete this holiday? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Holiday
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar mr-2"></i>
                        Calendar Preview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="fas fa-calendar-times fa-3x text-warning"></i>
                        </div>
                        <h5>{{ $holiday->name }}</h5>
                        <p class="text-muted">
                            {{ $holiday->start_date->format('M j') }} - {{ $holiday->end_date->format('M j, Y') }}
                        </p>
                        @if($holiday->is_recurring)
                            <p class="text-info">
                                <i class="fas fa-redo mr-1"></i>
                                Repeats {{ $holiday->recurrence_type }}
                            </p>
                        @endif
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Impact
                    </h3>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        During this holiday period, your business will be marked as closed and:
                    </p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-times text-danger mr-2"></i>No new bookings can be made</li>
                        <li><i class="fas fa-times text-danger mr-2"></i>Existing bookings may be affected</li>
                        <li><i class="fas fa-times text-danger mr-2"></i>Staff schedules will show as unavailable</li>
                        <li><i class="fas fa-check text-success mr-2"></i>Customers will see closure notice</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.businesses.holidays.edit', [$business, $holiday]) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Details
                        </a>
                        <a href="{{ route('admin.businesses.holidays.create', $business) }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus mr-2"></i>
                            Add Another Holiday
                        </a>
                        <a href="{{ route('admin.businesses.holidays.index', $business) }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list mr-2"></i>
                            View All Holidays
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop
