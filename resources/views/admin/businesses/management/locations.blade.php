@extends('admin.layouts.app')

@section('title', 'Locations Management')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>
                <i class="fas fa-map-marker-alt mr-2"></i>
                Locations Management
            </h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.businesses.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Businesses
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building mr-2"></i>
                        Select Business
                    </h3>
                </div>
                <div class="card-body">
                    @if($businesses->count() > 0)
                        <p class="text-muted mb-4">
                            Choose a business to manage its locations and branches. You can add new locations, update addresses, and configure map settings.
                        </p>

                        <form action="{{ route('admin.business-management.locations') }}" method="GET">
                            <div class="form-group">
                                <label for="business_id">Business</label>
                                <select name="business_id" id="business_id" class="form-control" required>
                                    <option value="">Select a business...</option>
                                    @foreach($businesses as $business)
                                        <option value="{{ $business->id }}">
                                            {{ $business->name }}
                                            @if($business->branches->count() > 0)
                                                ({{ $business->branches->count() }} {{ Str::plural('location', $business->branches->count()) }})
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group mb-0">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    Manage Locations
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="row">
                            @foreach($businesses as $business)
                                <div class="col-md-6 mb-3">
                                    <div class="card border-left-success">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                @if($business->logo)
                                                    <img src="{{ asset('storage/' . $business->logo) }}"
                                                         alt="{{ $business->name }}"
                                                         class="img-circle mr-3"
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                @else
                                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center mr-3"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-building text-white"></i>
                                                    </div>
                                                @endif
                                                <div class="flex-grow-1">
                                                    <h5 class="mb-1">{{ $business->name }}</h5>
                                                    <p class="text-muted mb-2">
                                                        {{ $business->branches->count() }} {{ Str::plural('location', $business->branches->count()) }}
                                                    </p>
                                                    <a href="{{ route('admin.businesses.locations.index', $business) }}" 
                                                       class="btn btn-sm btn-success">
                                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                                        Manage Locations
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4>No businesses found</h4>
                            <p class="text-muted">You need to create a business first before managing locations.</p>
                            <a href="{{ route('admin.businesses.wizard') }}" class="btn btn-primary">
                                <i class="fas fa-magic mr-2"></i>
                                Start Business Setup
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // Auto-submit form when business is selected
    $('#business_id').change(function() {
        if ($(this).val()) {
            $(this).closest('form').submit();
        }
    });
});
</script>
@stop
