@extends('admin.layouts.app')

@section('title', 'Create Service')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Create New Service</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Services
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-12">
        <form action="{{ route('admin.services.store') }}" method="POST" id="service-form">
            @csrf

            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Basic Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="business_id">Business <span class="text-danger">*</span></label>
                                <select class="form-control @error('business_id') is-invalid @enderror"
                                        id="business_id" name="business_id" required>
                                    <option value="">Select a business</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}"
                                                {{ (old('business_id', $business?->id) == $businessOption->id) ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('business_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="service_category_id">Category (Optional)</label>
                                <select class="form-control @error('service_category_id') is-invalid @enderror"
                                        id="service_category_id" name="service_category_id">
                                    <option value="">No Category</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}"
                                                {{ old('service_category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('service_category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="name">Service Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="short_description">Short Description</label>
                                <input type="text" class="form-control @error('short_description') is-invalid @enderror"
                                       id="short_description" name="short_description"
                                       value="{{ old('short_description') }}" maxlength="500"
                                       placeholder="Brief description for listings and previews">
                                @error('short_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">Full Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror"
                                          id="description" name="description" rows="4"
                                          placeholder="Detailed description of the service">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timing & Capacity -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2"></i>
                        Timing & Capacity
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="duration_minutes">Duration (minutes) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('duration_minutes') is-invalid @enderror"
                                       id="duration_minutes" name="duration_minutes"
                                       value="{{ old('duration_minutes', 60) }}" min="1" max="1440" required>
                                @error('duration_minutes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Duration in minutes (1-1440)</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="buffer_time_before">Buffer Before (minutes)</label>
                                <input type="number" class="form-control @error('buffer_time_before') is-invalid @enderror"
                                       id="buffer_time_before" name="buffer_time_before"
                                       value="{{ old('buffer_time_before', 0) }}" min="0" max="120">
                                @error('buffer_time_before')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Setup time before service</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="buffer_time_after">Buffer After (minutes)</label>
                                <input type="number" class="form-control @error('buffer_time_after') is-invalid @enderror"
                                       id="buffer_time_after" name="buffer_time_after"
                                       value="{{ old('buffer_time_after', 0) }}" min="0" max="120">
                                @error('buffer_time_after')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Cleanup time after service</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max_participants">Maximum Participants <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('max_participants') is-invalid @enderror"
                                       id="max_participants" name="max_participants"
                                       value="{{ old('max_participants', 1) }}" min="1" max="100" required>
                                @error('max_participants')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sort_order">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                                       id="sort_order" name="sort_order"
                                       value="{{ old('sort_order', 0) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Lower numbers appear first</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        Pricing
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="base_price">Base Price <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control @error('base_price') is-invalid @enderror"
                                           id="base_price" name="base_price"
                                           value="{{ old('base_price') }}" step="0.01" min="0" required>
                                    @error('base_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="deposit_amount">Deposit Amount</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control @error('deposit_amount') is-invalid @enderror"
                                           id="deposit_amount" name="deposit_amount"
                                           value="{{ old('deposit_amount') }}" step="0.01" min="0">
                                    @error('deposit_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <small class="text-muted">Leave empty for no deposit</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="deposit_required"
                                       name="deposit_required" value="1" {{ old('deposit_required') ? 'checked' : '' }}>
                                <label class="form-check-label" for="deposit_required">
                                    Deposit Required
                                </label>
                                <small class="form-text text-muted">Check if deposit is mandatory for booking</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Rules -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Booking Rules
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max_advance_booking_days">Max Advance Booking (days)</label>
                                <input type="number" class="form-control @error('max_advance_booking_days') is-invalid @enderror"
                                       id="max_advance_booking_days" name="max_advance_booking_days"
                                       value="{{ old('max_advance_booking_days') }}" min="1" max="365">
                                @error('max_advance_booking_days')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">How far in advance can customers book?</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="min_advance_booking_hours">Min Advance Booking (hours)</label>
                                <input type="number" class="form-control @error('min_advance_booking_hours') is-invalid @enderror"
                                       id="min_advance_booking_hours" name="min_advance_booking_hours"
                                       value="{{ old('min_advance_booking_hours') }}" min="1" max="72">
                                @error('min_advance_booking_hours')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Minimum notice required for booking</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="online_booking_enabled"
                                       name="online_booking_enabled" value="1" {{ old('online_booking_enabled', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="online_booking_enabled">
                                    Enable Online Booking
                                </label>
                                <small class="form-text text-muted">Allow customers to book this service online</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="requires_approval"
                                       name="requires_approval" value="1" {{ old('requires_approval') ? 'checked' : '' }}>
                                <label class="form-check-label" for="requires_approval">
                                    Requires Approval
                                </label>
                                <small class="form-text text-muted">Bookings need manual approval</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            @include('admin.services.pricing-variables')

            <!-- Resources -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools mr-2"></i>
                        Required Resources
                    </h3>
                </div>
                <div class="card-body">
                    <div id="resources-container">
                        @if($resources->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Resource</th>
                                            <th>Required</th>
                                            <th>Quantity</th>
                                            <th>Setup Time (min)</th>
                                            <th>Cleanup Time (min)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($resources as $resource)
                                            <tr>
                                                <td>
                                                    <strong>{{ $resource->name }}</strong>
                                                    @if($resource->description)
                                                        <br><small class="text-muted">{{ $resource->description }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input"
                                                               name="resources[{{ $loop->index }}][is_required]"
                                                               value="1" id="resource_required_{{ $resource->id }}">
                                                        <input type="hidden" name="resources[{{ $loop->index }}][resource_id]" value="{{ $resource->id }}">
                                                    </div>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm"
                                                           name="resources[{{ $loop->index }}][quantity_required]"
                                                           value="1" min="1" max="{{ $resource->total_quantity }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm"
                                                           name="resources[{{ $loop->index }}][setup_time_minutes]"
                                                           value="0" min="0" max="60">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm"
                                                           name="resources[{{ $loop->index }}][cleanup_time_minutes]"
                                                           value="0" min="0" max="60">
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                Please select a business first to see available resources.
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Status -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-toggle-on mr-2"></i>
                        Status
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="is_active"
                               name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            <strong>Service Active</strong>
                        </label>
                        <small class="form-text text-muted">Inactive services are hidden from customers</small>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="card">
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save mr-2"></i>
                                Create Service
                            </button>
                            <a href="{{ route('admin.services.index') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                        <div class="col-md-6">
                            <div class="text-right">
                                <div id="service-summary" class="text-muted">
                                    <small>Fill in the details to see service summary</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // Handle business selection change
    $('#business_id').change(function() {
        const businessId = $(this).val();
        if (businessId) {
            // Reload page with business_id parameter
            window.location.href = '{{ route("admin.services.create") }}?business_id=' + businessId;
        }
    });

    // Update service summary
    function updateServiceSummary() {
        const name = $('#name').val();
        const duration = $('#duration_minutes').val();
        const price = $('#base_price').val();
        const maxParticipants = $('#max_participants').val();

        if (name && duration && price) {
            const hours = Math.floor(duration / 60);
            const minutes = duration % 60;
            const durationText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;

            $('#service-summary').html(`
                <strong>Service:</strong> ${name}<br>
                <strong>Duration:</strong> ${durationText}<br>
                <strong>Price:</strong> $${parseFloat(price).toFixed(2)}<br>
                <strong>Max Participants:</strong> ${maxParticipants}
            `);
        } else {
            $('#service-summary').html('<small>Fill in the details to see service summary</small>');
        }
    }

    // Update summary on input change
    $('#name, #duration_minutes, #base_price, #max_participants').on('input', updateServiceSummary);

    // Initialize summary
    updateServiceSummary();

    // Form validation
    $('#service-form').on('submit', function(e) {
        // Add any custom validation logic here
    });
});
</script>
@stop
