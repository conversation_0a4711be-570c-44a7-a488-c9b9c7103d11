@extends('admin.layouts.app')

@section('title', 'Service Details')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Service Details</h1>
            <p class="text-muted">{{ $service->business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('edit services')
                    <a href="{{ route('admin.services.edit', $service) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Service
                    </a>
                @endcan
                @can('manage services')
                    <form action="{{ route('admin.services.duplicate', $service) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-copy mr-2"></i>
                            Duplicate
                        </button>
                    </form>
                @endcan
                <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Services
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <!-- Service Information -->
    <div class="col-md-8">
        <!-- Basic Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle mr-2"></i>
                    Service Information
                </h3>
                <div class="card-tools">
                    <span class="badge badge-{{ $service->is_active ? 'success' : 'secondary' }} badge-lg">
                        {{ $service->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Service Name:</strong></td>
                                <td>{{ $service->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Business:</strong></td>
                                <td>
                                    <a href="{{ route('admin.businesses.show', $service->business) }}">
                                        {{ $service->business->name }}
                                    </a>
                                </td>
                            </tr>
                            @if($service->category)
                            <tr>
                                <td><strong>Category:</strong></td>
                                <td>{{ $service->category->name }}</td>
                            </tr>
                            @endif
                            <tr>
                                <td><strong>Duration:</strong></td>
                                <td>{{ $service->formatted_duration }}</td>
                            </tr>
                            <tr>
                                <td><strong>Max Participants:</strong></td>
                                <td>{{ $service->max_participants }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Base Price:</strong></td>
                                <td>${{ number_format($service->base_price, 2) }}</td>
                            </tr>
                            @if($service->deposit_amount > 0)
                            <tr>
                                <td><strong>Deposit:</strong></td>
                                <td>
                                    ${{ number_format($service->deposit_amount, 2) }}
                                    @if($service->deposit_required)
                                        <span class="badge badge-warning">Required</span>
                                    @endif
                                </td>
                            </tr>
                            @endif
                            <tr>
                                <td><strong>Online Booking:</strong></td>
                                <td>
                                    <span class="badge badge-{{ $service->online_booking_enabled ? 'success' : 'secondary' }}">
                                        {{ $service->online_booking_enabled ? 'Enabled' : 'Disabled' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Requires Approval:</strong></td>
                                <td>
                                    <span class="badge badge-{{ $service->requires_approval ? 'warning' : 'success' }}">
                                        {{ $service->requires_approval ? 'Yes' : 'No' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Sort Order:</strong></td>
                                <td>{{ $service->sort_order }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($service->short_description)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6><i class="fas fa-quote-left mr-2"></i>Short Description:</h6>
                        <p class="text-muted">{{ $service->short_description }}</p>
                    </div>
                </div>
                @endif

                @if($service->description)
                <div class="row">
                    <div class="col-md-12">
                        <h6><i class="fas fa-align-left mr-2"></i>Full Description:</h6>
                        <div class="text-muted">
                            {!! nl2br(e($service->description)) !!}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Timing Details -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-clock mr-2"></i>
                    Timing Details
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Service Duration:</strong></td>
                                <td>{{ $service->formatted_duration }}</td>
                            </tr>
                            <tr>
                                <td><strong>Buffer Before:</strong></td>
                                <td>{{ $service->buffer_time_before }} minutes</td>
                            </tr>
                            <tr>
                                <td><strong>Buffer After:</strong></td>
                                <td>{{ $service->buffer_time_after }} minutes</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Total Time Slot:</strong></td>
                                <td>{{ $service->buffer_time_before + $service->duration_minutes + $service->buffer_time_after }} minutes</td>
                            </tr>
                            @if($service->max_advance_booking_days)
                            <tr>
                                <td><strong>Max Advance Booking:</strong></td>
                                <td>{{ $service->max_advance_booking_days }} days</td>
                            </tr>
                            @endif
                            @if($service->min_advance_booking_hours)
                            <tr>
                                <td><strong>Min Advance Booking:</strong></td>
                                <td>{{ $service->min_advance_booking_hours }} hours</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resources -->
        @if($service->resources->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-tools mr-2"></i>
                    Required Resources
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Resource</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Quantity</th>
                                <th>Setup Time</th>
                                <th>Cleanup Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($service->resources as $resource)
                                <tr>
                                    <td>
                                        <strong>{{ $resource->name }}</strong>
                                        @if($resource->description)
                                            <br><small class="text-muted">{{ $resource->description }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $resource->resourceType->name ?? 'N/A' }}</td>
                                    <td>
                                        <span class="badge badge-{{ $resource->pivot->is_required ? 'danger' : 'info' }}">
                                            {{ $resource->pivot->is_required ? 'Required' : 'Optional' }}
                                        </span>
                                    </td>
                                    <td>{{ $resource->pivot->quantity_required }}</td>
                                    <td>{{ $resource->pivot->setup_time_minutes }} min</td>
                                    <td>{{ $resource->pivot->cleanup_time_minutes }} min</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <!-- Add-ons -->
        @if($service->addons->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-plus-circle mr-2"></i>
                    Available Add-ons
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Add-on</th>
                                <th>Price</th>
                                <th>Duration</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($service->addons as $addon)
                                <tr>
                                    <td>
                                        <strong>{{ $addon->name }}</strong>
                                        @if($addon->description)
                                            <br><small class="text-muted">{{ $addon->description }}</small>
                                        @endif
                                    </td>
                                    <td>${{ number_format($addon->price, 2) }}</td>
                                    <td>{{ $addon->duration_minutes }} min</td>
                                    <td>
                                        <span class="badge badge-{{ $addon->is_active ? 'success' : 'secondary' }}">
                                            {{ $addon->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Quick Stats
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="info-box bg-info">
                            <span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Bookings</span>
                                <span class="info-box-number">{{ $service->bookingServices->count() }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="info-box bg-warning">
                            <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Waiting List</span>
                                <span class="info-box-number">{{ $service->waitingLists->count() }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <table class="table table-sm">
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>{{ $service->created_at->format('M d, Y') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Last Updated:</strong></td>
                        <td>{{ $service->updated_at->format('M d, Y') }}</td>
                    </tr>
                    @if($service->bookingServices->count() > 0)
                    <tr>
                        <td><strong>Last Booking:</strong></td>
                        <td>{{ $service->bookingServices->sortByDesc('created_at')->first()->created_at->format('M d, Y') }}</td>
                    </tr>
                    @endif
                </table>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bolt mr-2"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="btn-group-vertical w-100" role="group">
                    <a href="{{ route('admin.bookings.create', ['business_id' => $service->business_id]) }}"
                       class="btn btn-success btn-block">
                        <i class="fas fa-plus mr-2"></i>
                        Create Booking
                    </a>

                    @if($service->is_active)
                        <form action="{{ route('admin.services.update', $service) }}" method="POST" class="mb-2">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_active" value="0">
                            <button type="submit" class="btn btn-warning btn-block">
                                <i class="fas fa-pause mr-2"></i>
                                Deactivate Service
                            </button>
                        </form>
                    @else
                        <form action="{{ route('admin.services.update', $service) }}" method="POST" class="mb-2">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_active" value="1">
                            <button type="submit" class="btn btn-success btn-block">
                                <i class="fas fa-play mr-2"></i>
                                Activate Service
                            </button>
                        </form>
                    @endif

                    @can('delete services')
                        <form action="{{ route('admin.services.destroy', $service) }}" method="POST">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-block"
                                    onclick="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Service
                            </button>
                        </form>
                    @endcan
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        @if($service->bookingServices->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history mr-2"></i>
                    Recent Bookings
                </h3>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($service->bookingServices->take(5) as $bookingService)
                                <tr>
                                    <td>
                                        <a href="{{ route('admin.bookings.show', $bookingService->booking) }}">
                                            {{ $bookingService->booking->start_datetime->format('M d') }}
                                        </a>
                                    </td>
                                    <td>{{ $bookingService->booking->customer_name }}</td>
                                    <td>
                                        <span class="badge badge-{{ $bookingService->booking->status_color }}">
                                            {{ ucfirst($bookingService->booking->status) }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @if($service->bookingServices->count() > 5)
                    <div class="card-footer text-center">
                        <a href="{{ route('admin.bookings.index', ['service_id' => $service->id]) }}" class="btn btn-sm btn-primary">
                            View All Bookings
                        </a>
                    </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>
@stop
