@extends('admin.layouts.app')

@section('title', 'Services')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Services</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('create services')
                    <a href="{{ route('admin.services.create', ['business_id' => $business?->id]) }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add Service
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.services.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select class="form-control" id="business_id" name="business_id" onchange="this.form.submit()">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Service name or description">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select class="form-control" id="category" name="category">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" 
                                            {{ request('category') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search mr-2"></i>
                            Filter
                        </button>
                        <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times mr-2"></i>
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Services List -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-concierge-bell mr-2"></i>
                Services ({{ $services->total() }})
            </h3>
        </div>
        <div class="card-body p-0">
            @if($services->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Service</th>
                                <th>Business</th>
                                <th>Category</th>
                                <th>Duration</th>
                                <th>Price</th>
                                <th>Resources</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($services as $service)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $service->name }}</strong>
                                            @if($service->short_description)
                                                <br><small class="text-muted">{{ Str::limit($service->short_description, 50) }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ $service->business->name }}</strong>
                                    </td>
                                    <td>
                                        @if($service->category)
                                            <span class="badge badge-secondary" style="background-color: {{ $service->category->color }}">
                                                <i class="{{ $service->category->icon }} mr-1"></i>
                                                {{ $service->category->name }}
                                            </span>
                                        @else
                                            <span class="text-muted">No category</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ $service->formatted_duration }}
                                        </span>
                                        @if($service->buffer_time_before > 0 || $service->buffer_time_after > 0)
                                            <br><small class="text-muted">
                                                +{{ $service->buffer_time_before + $service->buffer_time_after }}m buffer
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>${{ $service->formatted_price }}</strong>
                                        @if($service->deposit_required && $service->deposit_amount > 0)
                                            <br><small class="text-muted">
                                                ${{ number_format($service->deposit_amount, 2) }} deposit
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($service->resources->count() > 0)
                                            <span class="badge badge-primary">
                                                {{ $service->resources->count() }} 
                                                {{ Str::plural('resource', $service->resources->count()) }}
                                            </span>
                                        @else
                                            <span class="text-muted">No resources</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($service->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                        @if($service->online_booking_enabled)
                                            <br><span class="badge badge-info">Online</span>
                                        @endif
                                        @if($service->requires_approval)
                                            <br><span class="badge badge-warning">Approval</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.services.show', $service) }}" 
                                               class="btn btn-sm btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @can('edit services')
                                                <a href="{{ route('admin.services.edit', $service) }}" 
                                                   class="btn btn-sm btn-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                            @can('create services')
                                                <form action="{{ route('admin.services.duplicate', $service) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-secondary" title="Duplicate">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </form>
                                            @endcan
                                            @can('delete services')
                                                <form action="{{ route('admin.services.destroy', $service) }}" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this service?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                    <h4>No services found</h4>
                    @if($business)
                        <p class="text-muted">Start by creating your first service for {{ $business->name }}.</p>
                        <a href="{{ route('admin.services.create', ['business_id' => $business->id]) }}" class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Create Service
                        </a>
                    @else
                        <p class="text-muted">Select a business to view and manage services.</p>
                    @endif
                </div>
            @endif
        </div>
        @if($services->hasPages())
            <div class="card-footer">
                {{ $services->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</div>
@stop
