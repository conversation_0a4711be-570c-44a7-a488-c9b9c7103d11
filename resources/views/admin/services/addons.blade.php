<!-- Service Addons Section -->
<div class="card mt-4">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-plus-circle mr-2"></i>
            Service Add-ons
        </h3>
        <div class="card-tools">
            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="form-group">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="enable_addons" name="enable_addons"
                       {{ (isset($service) && $service->addons->count() > 0) ? 'checked' : '' }}>
                <label class="form-check-label" for="enable_addons">
                    Enable Service Add-ons
                </label>
            </div>
            <small class="text-muted">Allow customers to add additional services to their booking</small>
        </div>

        <div id="addons-container" style="{{ (isset($service) && $service->addons->count() > 0) ? '' : 'display: none;' }}">
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Available Add-on Services</label>
                        <div id="addon-services-list">
                            @if(isset($availableServices) && $availableServices->count() > 0)
                                @foreach($availableServices as $availableService)
                                    @php
                                        $existingAddon = isset($service) ? $service->addons->where('addon_service_id', $availableService->id)->first() : null;
                                        $isSelected = $existingAddon !== null;
                                        $addonPrice = $existingAddon ? $existingAddon->addon_price : $availableService->base_price;
                                        $isRequired = $existingAddon ? $existingAddon->is_required : false;
                                        $maxQuantity = $existingAddon ? $existingAddon->max_quantity : 1;
                                    @endphp
                                    <div class="card mb-3 addon-service-card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input addon-checkbox" type="checkbox"
                                                               name="addons[{{ $loop->index }}][enabled]"
                                                               value="1"
                                                               id="addon_{{ $availableService->id }}"
                                                               data-service-id="{{ $availableService->id }}"
                                                               {{ $isSelected ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="addon_{{ $availableService->id }}">
                                                            <strong>{{ $availableService->name }}</strong>
                                                            <br><small class="text-muted">{{ $availableService->short_description }}</small>
                                                            <br><small class="text-info">Duration: {{ $availableService->formatted_duration }} | Base Price: ${{ $availableService->formatted_price }}</small>
                                                        </label>
                                                        <input type="hidden" name="addons[{{ $loop->index }}][addon_service_id]" value="{{ $availableService->id }}">
                                                    </div>
                                                </div>
                                                <div class="col-md-6 addon-options" style="{{ $isSelected ? '' : 'display: none;' }}">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="addon_price_{{ $availableService->id }}">Custom Price</label>
                                                                <div class="input-group">
                                                                    <div class="input-group-prepend">
                                                                        <span class="input-group-text">$</span>
                                                                    </div>
                                                                    <input type="number" class="form-control form-control-sm"
                                                                           id="addon_price_{{ $availableService->id }}"
                                                                           name="addons[{{ $loop->index }}][addon_price]"
                                                                           value="{{ $addonPrice }}"
                                                                           step="0.01" min="0"
                                                                           placeholder="{{ $availableService->base_price }}">
                                                                </div>
                                                                <small class="text-muted">Leave empty to use base price</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="addon_max_quantity_{{ $availableService->id }}">Max Quantity</label>
                                                                <input type="number" class="form-control form-control-sm"
                                                                       id="addon_max_quantity_{{ $availableService->id }}"
                                                                       name="addons[{{ $loop->index }}][max_quantity]"
                                                                       value="{{ $maxQuantity }}"
                                                                       min="1" max="10">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                               id="addon_required_{{ $availableService->id }}"
                                                               name="addons[{{ $loop->index }}][is_required]"
                                                               value="1" {{ $isRequired ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="addon_required_{{ $availableService->id }}">
                                                            Required Add-on
                                                        </label>
                                                        <small class="form-text text-muted">Customers must select this add-on</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    No other services available as add-ons. Create more services first.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add-ons Preview -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-eye mr-2"></i>
                        Add-ons Preview
                    </h5>
                </div>
                <div class="card-body">
                    <div id="addons-preview">
                        <small class="text-muted">Select add-ons above to see preview</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const enableAddons = document.getElementById('enable_addons');
    const addonsContainer = document.getElementById('addons-container');
    const addonCheckboxes = document.querySelectorAll('.addon-checkbox');

    enableAddons.addEventListener('change', function() {
        addonsContainer.style.display = this.checked ? 'block' : 'none';
        updateAddonsPreview();
    });

    addonCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const serviceId = this.dataset.serviceId;
            const addonOptions = this.closest('.addon-service-card').querySelector('.addon-options');

            if (this.checked) {
                addonOptions.style.display = 'block';
            } else {
                addonOptions.style.display = 'none';
            }

            updateAddonsPreview();
        });
    });

    // Update preview when addon options change
    const addonInputs = addonsContainer.querySelectorAll('input[type="number"], input[type="checkbox"]');
    addonInputs.forEach(input => {
        input.addEventListener('change', updateAddonsPreview);
    });

    function updateAddonsPreview() {
        if (!enableAddons.checked) {
            document.getElementById('addons-preview').innerHTML = '<small class="text-muted">Add-ons are disabled</small>';
            return;
        }

        let preview = '';
        let totalAddons = 0;
        let totalPrice = 0;

        addonCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const serviceCard = checkbox.closest('.addon-service-card');
                const serviceName = checkbox.nextElementSibling.querySelector('strong').textContent;
                const priceInput = serviceCard.querySelector('input[name*="[addon_price]"]');
                const maxQuantityInput = serviceCard.querySelector('input[name*="[max_quantity]"]');
                const requiredCheckbox = serviceCard.querySelector('input[name*="[is_required]"]');

                const price = parseFloat(priceInput.value) || 0;
                const maxQuantity = parseInt(maxQuantityInput.value) || 1;
                const isRequired = requiredCheckbox.checked;

                totalAddons++;
                totalPrice += price;

                preview += `<div class="mb-2">
                    <strong>${serviceName}</strong> - $${price.toFixed(2)}
                    ${isRequired ? '<span class="badge badge-warning ml-2">Required</span>' : ''}
                    <br><small class="text-muted">Max quantity: ${maxQuantity}</small>
                </div>`;
            }
        });

        if (totalAddons === 0) {
            preview = '<small class="text-muted">No add-ons selected</small>';
        } else {
            preview += `<hr><strong>Total Add-ons: ${totalAddons} | Total Price: $${totalPrice.toFixed(2)}</strong>`;
        }

        document.getElementById('addons-preview').innerHTML = preview;
    }

    // Initial preview update
    updateAddonsPreview();
});
</script>
