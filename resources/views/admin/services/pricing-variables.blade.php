<!-- Dynamic Pricing Variables Section -->
<div class="card mt-4">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-calculator mr-2"></i>
            Dynamic Pricing Variables
        </h3>
        <div class="card-tools">
            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="form-group">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="enable_dynamic_pricing" name="enable_dynamic_pricing"
                       {{ (isset($service) && $service->pricing_variables) ? 'checked' : '' }}>
                <label class="form-check-label" for="enable_dynamic_pricing">
                    Enable Dynamic Pricing
                </label>
            </div>
            <small class="text-muted">Allow variable pricing based on different conditions</small>
        </div>

        <div id="pricing-variables-container" style="{{ (isset($service) && $service->pricing_variables) ? '' : 'display: none;' }}">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="participant_pricing">Participant-Based Pricing</label>
                        <select class="form-control" id="participant_pricing" name="pricing_variables[participant_type]">
                            <option value="fixed" {{ (isset($service) && isset($service->pricing_variables['participant_type']) && $service->pricing_variables['participant_type'] == 'fixed') ? 'selected' : '' }}>
                                Fixed Price (same regardless of participants)
                            </option>
                            <option value="per_participant" {{ (isset($service) && isset($service->pricing_variables['participant_type']) && $service->pricing_variables['participant_type'] == 'per_participant') ? 'selected' : '' }}>
                                Per Participant
                            </option>
                            <option value="group_rate" {{ (isset($service) && isset($service->pricing_variables['participant_type']) && $service->pricing_variables['participant_type'] == 'group_rate') ? 'selected' : '' }}>
                                Group Rate (discount for more participants)
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="group_discount_threshold">Group Discount Threshold</label>
                        <input type="number" class="form-control" id="group_discount_threshold"
                               name="pricing_variables[group_discount_threshold]" min="2" max="100"
                               value="{{ isset($service) && isset($service->pricing_variables['group_discount_threshold']) ? $service->pricing_variables['group_discount_threshold'] : 3 }}">
                        <small class="text-muted">Minimum participants for group discount</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="group_discount_percent">Group Discount Percentage</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="group_discount_percent"
                                   name="pricing_variables[group_discount_percent]" min="0" max="50" step="0.01"
                                   value="{{ isset($service) && isset($service->pricing_variables['group_discount_percent']) ? $service->pricing_variables['group_discount_percent'] : 10 }}">
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="time_based_pricing">Time-Based Pricing</label>
                        <select class="form-control" id="time_based_pricing" name="pricing_variables[time_based_pricing]">
                            <option value="none" {{ (isset($service) && isset($service->pricing_variables['time_based_pricing']) && $service->pricing_variables['time_based_pricing'] == 'none') ? 'selected' : '' }}>
                                No Time-Based Pricing
                            </option>
                            <option value="peak_hours" {{ (isset($service) && isset($service->pricing_variables['time_based_pricing']) && $service->pricing_variables['time_based_pricing'] == 'peak_hours') ? 'selected' : '' }}>
                                Peak Hours Surcharge
                            </option>
                            <option value="off_peak" {{ (isset($service) && isset($service->pricing_variables['time_based_pricing']) && $service->pricing_variables['time_based_pricing'] == 'off_peak') ? 'selected' : '' }}>
                                Off-Peak Discount
                            </option>
                        </select>
                    </div>
                </div>
            </div>

            <div id="time-pricing-details" style="{{ (isset($service) && isset($service->pricing_variables['time_based_pricing']) && $service->pricing_variables['time_based_pricing'] != 'none') ? '' : 'display: none;' }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="peak_start_time">Peak Start Time</label>
                            <input type="time" class="form-control" id="peak_start_time"
                                   name="pricing_variables[peak_start_time]"
                                   value="{{ isset($service) && isset($service->pricing_variables['peak_start_time']) ? $service->pricing_variables['peak_start_time'] : '18:00' }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="peak_end_time">Peak End Time</label>
                            <input type="time" class="form-control" id="peak_end_time"
                                   name="pricing_variables[peak_end_time]"
                                   value="{{ isset($service) && isset($service->pricing_variables['peak_end_time']) ? $service->pricing_variables['peak_end_time'] : '22:00' }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="peak_adjustment">Peak Price Adjustment</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="peak_adjustment"
                                       name="pricing_variables[peak_adjustment]" min="0" max="100" step="0.01"
                                       value="{{ isset($service) && isset($service->pricing_variables['peak_adjustment']) ? $service->pricing_variables['peak_adjustment'] : 20 }}">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="seasonal_pricing">Seasonal Pricing</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="enable_seasonal"
                           name="pricing_variables[enable_seasonal]"
                           {{ (isset($service) && isset($service->pricing_variables['enable_seasonal']) && $service->pricing_variables['enable_seasonal']) ? 'checked' : '' }}>
                    <label class="form-check-label" for="enable_seasonal">
                        Enable seasonal pricing adjustments
                    </label>
                </div>
            </div>

            <div id="seasonal-pricing-details" style="{{ (isset($service) && isset($service->pricing_variables['enable_seasonal']) && $service->pricing_variables['enable_seasonal']) ? '' : 'display: none;' }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="winter_adjustment">Winter Adjustment (%)</label>
                            <input type="number" class="form-control" id="winter_adjustment"
                                   name="pricing_variables[winter_adjustment]" min="-50" max="100" step="0.01"
                                   value="{{ isset($service) && isset($service->pricing_variables['winter_adjustment']) ? $service->pricing_variables['winter_adjustment'] : 0 }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="spring_adjustment">Spring Adjustment (%)</label>
                            <input type="number" class="form-control" id="spring_adjustment"
                                   name="pricing_variables[spring_adjustment]" min="-50" max="100" step="0.01"
                                   value="{{ isset($service) && isset($service->pricing_variables['spring_adjustment']) ? $service->pricing_variables['spring_adjustment'] : 0 }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="summer_adjustment">Summer Adjustment (%)</label>
                            <input type="number" class="form-control" id="summer_adjustment"
                                   name="pricing_variables[summer_adjustment]" min="-50" max="100" step="0.01"
                                   value="{{ isset($service) && isset($service->pricing_variables['summer_adjustment']) ? $service->pricing_variables['summer_adjustment'] : 0 }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="fall_adjustment">Fall Adjustment (%)</label>
                            <input type="number" class="form-control" id="fall_adjustment"
                                   name="pricing_variables[fall_adjustment]" min="-50" max="100" step="0.01"
                                   value="{{ isset($service) && isset($service->pricing_variables['fall_adjustment']) ? $service->pricing_variables['fall_adjustment'] : 0 }}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="advance_booking_discount">Advance Booking Discount</label>
                <div class="row">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="number" class="form-control" id="advance_booking_days"
                                   name="pricing_variables[advance_booking_days]" min="1" max="365"
                                   value="{{ isset($service) && isset($service->pricing_variables['advance_booking_days']) ? $service->pricing_variables['advance_booking_days'] : 7 }}">
                            <div class="input-group-append">
                                <span class="input-group-text">days</span>
                            </div>
                        </div>
                        <small class="text-muted">Minimum days in advance</small>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="number" class="form-control" id="advance_booking_discount"
                                   name="pricing_variables[advance_booking_discount]" min="0" max="50" step="0.01"
                                   value="{{ isset($service) && isset($service->pricing_variables['advance_booking_discount']) ? $service->pricing_variables['advance_booking_discount'] : 5 }}">
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <small class="text-muted">Discount percentage</small>
                    </div>
                </div>
            </div>

            <!-- Pricing Preview -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-eye mr-2"></i>
                        Pricing Preview
                    </h5>
                </div>
                <div class="card-body">
                    <div id="pricing-preview">
                        <small class="text-muted">Configure pricing variables above to see preview</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const enableDynamicPricing = document.getElementById('enable_dynamic_pricing');
    const pricingContainer = document.getElementById('pricing-variables-container');
    const timePricing = document.getElementById('time_based_pricing');
    const timePricingDetails = document.getElementById('time-pricing-details');
    const enableSeasonal = document.getElementById('enable_seasonal');
    const seasonalDetails = document.getElementById('seasonal-pricing-details');

    enableDynamicPricing.addEventListener('change', function() {
        pricingContainer.style.display = this.checked ? 'block' : 'none';
        updatePricingPreview();
    });

    timePricing.addEventListener('change', function() {
        timePricingDetails.style.display = this.value !== 'none' ? 'block' : 'none';
        updatePricingPreview();
    });

    enableSeasonal.addEventListener('change', function() {
        seasonalDetails.style.display = this.checked ? 'block' : 'none';
        updatePricingPreview();
    });

    // Update pricing preview when any input changes
    const pricingInputs = pricingContainer.querySelectorAll('input, select');
    pricingInputs.forEach(input => {
        input.addEventListener('change', updatePricingPreview);
    });

    function updatePricingPreview() {
        if (!enableDynamicPricing.checked) return;

        const basePrice = parseFloat(document.getElementById('base_price')?.value || 0);
        if (basePrice === 0) return;

        let preview = `<strong>Base Price:</strong> $${basePrice.toFixed(2)}<br>`;

        // Participant pricing
        const participantType = document.getElementById('participant_pricing').value;
        if (participantType === 'per_participant') {
            preview += `<strong>2 participants:</strong> $${(basePrice * 2).toFixed(2)}<br>`;
        } else if (participantType === 'group_rate') {
            const threshold = parseInt(document.getElementById('group_discount_threshold').value);
            const discount = parseFloat(document.getElementById('group_discount_percent').value);
            const groupPrice = basePrice * (1 - discount / 100);
            preview += `<strong>${threshold}+ participants:</strong> $${groupPrice.toFixed(2)} each (${discount}% off)<br>`;
        }

        // Time-based pricing
        const timeBased = timePricing.value;
        if (timeBased === 'peak_hours') {
            const adjustment = parseFloat(document.getElementById('peak_adjustment').value);
            const peakPrice = basePrice * (1 + adjustment / 100);
            preview += `<strong>Peak hours:</strong> $${peakPrice.toFixed(2)} (+${adjustment}%)<br>`;
        } else if (timeBased === 'off_peak') {
            const adjustment = parseFloat(document.getElementById('peak_adjustment').value);
            const offPeakPrice = basePrice * (1 - adjustment / 100);
            preview += `<strong>Off-peak:</strong> $${offPeakPrice.toFixed(2)} (-${adjustment}%)<br>`;
        }

        // Advance booking discount
        const advanceDiscount = parseFloat(document.getElementById('advance_booking_discount').value);
        const advanceDays = parseInt(document.getElementById('advance_booking_days').value);
        if (advanceDiscount > 0) {
            const advancePrice = basePrice * (1 - advanceDiscount / 100);
            preview += `<strong>${advanceDays}+ days advance:</strong> $${advancePrice.toFixed(2)} (-${advanceDiscount}%)<br>`;
        }

        document.getElementById('pricing-preview').innerHTML = preview;
    }

    // Initial preview update
    updatePricingPreview();
});
</script>
