@extends('admin.layouts.app')

@section('title', 'Business Category: ' . $businessCategory->name)

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>
                @if($businessCategory->icon)
                    <i class="{{ $businessCategory->icon }}" style="color: {{ $businessCategory->color ?? '#6c757d' }}"></i>
                @endif
                {{ $businessCategory->name }}
            </h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('update businesses')
                    <a href="{{ route('admin.business-categories.edit', $businessCategory) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Category
                    </a>
                @endcan
                <form method="POST" action="{{ route('admin.business-categories.duplicate', $businessCategory) }}" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-copy mr-2"></i>
                        Duplicate
                    </button>
                </form>
                <a href="{{ route('admin.business-categories.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Categories
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <div class="row">
        <div class="col-md-8">
            <!-- Category Details -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Category Details</h3>
                    <div class="card-tools">
                        <form method="POST" action="{{ route('admin.business-categories.toggle-status', $businessCategory) }}" style="display: inline;">
                            @csrf
                            <button type="submit" class="btn btn-sm {{ $businessCategory->is_active ? 'btn-success' : 'btn-secondary' }}">
                                {{ $businessCategory->is_active ? 'Active' : 'Inactive' }}
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Name:</strong>
                            <p>{{ $businessCategory->name }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>Slug:</strong>
                            <p><code>{{ $businessCategory->slug }}</code></p>
                        </div>
                    </div>

                    @if($businessCategory->description)
                        <div class="row">
                            <div class="col-12">
                                <strong>Description:</strong>
                                <p>{{ $businessCategory->description }}</p>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-4">
                            <strong>Icon:</strong>
                            <p>
                                @if($businessCategory->icon)
                                    <i class="{{ $businessCategory->icon }}" style="color: {{ $businessCategory->color ?? '#6c757d' }}; font-size: 20px;"></i>
                                    <br>
                                    <small class="text-muted">{{ $businessCategory->icon }}</small>
                                @else
                                    <span class="text-muted">No icon set</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-4">
                            <strong>Color:</strong>
                            <p>
                                @if($businessCategory->color)
                                    <div class="d-flex align-items-center">
                                        <div style="width: 30px; height: 30px; background-color: {{ $businessCategory->color }}; border-radius: 5px; margin-right: 10px; border: 1px solid #dee2e6;"></div>
                                        <code>{{ $businessCategory->color }}</code>
                                    </div>
                                @else
                                    <span class="text-muted">No color set</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-4">
                            <strong>Sort Order:</strong>
                            <p>{{ $businessCategory->sort_order }}</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <strong>Created:</strong>
                            <p>{{ $businessCategory->created_at->format('M j, Y g:i A') }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>Last Updated:</strong>
                            <p>{{ $businessCategory->updated_at->format('M j, Y g:i A') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Associated Businesses -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">Associated Businesses</h3>
                    <div class="card-tools">
                        <span class="badge badge-info">{{ $businessCategory->businesses->count() }} businesses</span>
                    </div>
                </div>
                <div class="card-body">
                    @if($businessCategory->businesses->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Business Name</th>
                                        <th>Owner</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($businessCategory->businesses as $business)
                                        <tr>
                                            <td>
                                                <strong>{{ $business->name }}</strong>
                                                @if($business->logo)
                                                    <img src="{{ Storage::url($business->logo) }}" alt="Logo" class="img-thumbnail ml-2" style="width: 30px; height: 30px;">
                                                @endif
                                            </td>
                                            <td>{{ $business->owner->name ?? 'N/A' }}</td>
                                            <td>
                                                <span class="badge {{ $business->is_active ? 'badge-success' : 'badge-secondary' }}">
                                                    {{ $business->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                            <td>{{ $business->created_at->format('M j, Y') }}</td>
                                            <td>
                                                <a href="{{ route('admin.businesses.show', $business) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No businesses in this category</h5>
                            <p class="text-muted">Businesses will appear here when they are assigned to this category.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Quick Stats -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Stats</h3>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <span class="info-box-icon bg-info">
                            <i class="fas fa-building"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Total Businesses</span>
                            <span class="info-box-number">{{ $businessCategory->businesses->count() }}</span>
                        </div>
                    </div>

                    <div class="info-box">
                        <span class="info-box-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Active Businesses</span>
                            <span class="info-box-number">{{ $businessCategory->businesses->where('is_active', true)->count() }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">Actions</h3>
                </div>
                <div class="card-body">
                    @can('update businesses')
                        <a href="{{ route('admin.business-categories.edit', $businessCategory) }}" class="btn btn-warning btn-block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Category
                        </a>
                    @endcan

                    <form method="POST" action="{{ route('admin.business-categories.duplicate', $businessCategory) }}" class="mt-2">
                        @csrf
                        <button type="submit" class="btn btn-secondary btn-block">
                            <i class="fas fa-copy mr-2"></i>
                            Duplicate Category
                        </button>
                    </form>

                    <form method="POST" action="{{ route('admin.business-categories.toggle-status', $businessCategory) }}" class="mt-2">
                        @csrf
                        <button type="submit" class="btn {{ $businessCategory->is_active ? 'btn-outline-secondary' : 'btn-outline-success' }} btn-block">
                            <i class="fas {{ $businessCategory->is_active ? 'fa-pause' : 'fa-play' }} mr-2"></i>
                            {{ $businessCategory->is_active ? 'Deactivate' : 'Activate' }} Category
                        </button>
                    </form>

                    @can('delete businesses')
                        @if($businessCategory->businesses->count() === 0)
                            <form method="POST" action="{{ route('admin.business-categories.destroy', $businessCategory) }}"
                                  class="mt-2" onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-block">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Category
                                </button>
                            </form>
                        @else
                            <button class="btn btn-danger btn-block mt-2" disabled title="Cannot delete category with associated businesses">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Category
                            </button>
                            <small class="text-muted">Remove all associated businesses first</small>
                        @endif
                    @endcan
                </div>
            </div>
        </div>
    </div>
@stop

@section('styles')
    <style>
        .info-box {
            margin-bottom: 15px;
        }
    </style>
@stop
