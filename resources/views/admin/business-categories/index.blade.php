@extends('admin.layouts.app')

@section('title', 'Business Categories')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Business Categories</h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('create businesses')
                    <a href="{{ route('admin.business-categories.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add Category
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Manage Business Categories</h3>
            <div class="card-tools">
                <form method="GET" action="{{ route('admin.business-categories.index') }}" class="form-inline">
                    <div class="input-group input-group-sm mr-2">
                        <input type="text" name="search" class="form-control" placeholder="Search categories..." value="{{ request('search') }}">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <select name="status" class="form-control form-control-sm mr-2" onchange="this.form.submit()">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                    @if(request('search') || request('status'))
                        <a href="{{ route('admin.business-categories.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    @endif
                </form>
            </div>
        </div>
        <div class="card-body table-responsive p-0">
            @if($categories->count() > 0)
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 18%; min-width: 150px;">Name</th>
                            <th style="width: 30%; min-width: 250px;">Description</th>
                            <th style="width: 10%; min-width: 100px; text-align: center;">Icon</th>
                            <th style="width: 8%; min-width: 80px;">Color</th>
                            <th style="width: 7%; text-align: center;">Businesses</th>
                            <th style="width: 7%; text-align: center;">Sort Order</th>
                            <th style="width: 8%; text-align: center;">Status</th>
                            <th style="width: 12%; min-width: 120px; text-align: center;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($categories as $category)
                            <tr>
                                <td>
                                    <strong>{{ $category->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $category->slug }}</small>
                                </td>
                                <td>
                                    <div class="description-text" title="{{ $category->description }}">
                                        {{ $category->description ?: 'No description provided' }}
                                    </div>
                                </td>
                                <td>
                                    @if($category->icon)
                                        <div title="{{ $category->icon }}">
                                            <i class="{{ $category->icon }}" style="color: {{ $category->color ?? '#6c757d' }}; font-size: 20px;"></i>
                                        </div>
                                    @else
                                        <span class="text-muted">No icon</span>
                                    @endif
                                </td>
                                <td>
                                    @if($category->color)
                                        <div class="text-center" title="{{ $category->color }}">
                                            <div style="width: 24px; height: 24px; background-color: {{ $category->color }}; border-radius: 4px; margin: 0 auto; border: 1px solid #dee2e6;"></div>
                                        </div>
                                    @else
                                        <span class="text-muted">No color</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-info">{{ $category->businesses()->count() }}</span>
                                </td>
                                <td class="text-center">{{ $category->sort_order }}</td>
                                <td class="text-center">
                                    <form method="POST" action="{{ route('admin.business-categories.toggle-status', $category) }}" style="display: inline;">
                                        @csrf
                                        <button type="submit" class="btn btn-sm {{ $category->is_active ? 'btn-success' : 'btn-secondary' }}">
                                            {{ $category->is_active ? 'Active' : 'Inactive' }}
                                        </button>
                                    </form>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.business-categories.show', $category) }}" class="btn btn-sm btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @can('update businesses')
                                            <a href="{{ route('admin.business-categories.edit', $category) }}" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        @endcan
                                        <form method="POST" action="{{ route('admin.business-categories.duplicate', $category) }}" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-secondary" title="Duplicate">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </form>
                                        @can('delete businesses')
                                            @if($category->businesses()->count() === 0)
                                                <form method="POST" action="{{ route('admin.business-categories.destroy', $category) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @else
                                                <button class="btn btn-sm btn-danger" disabled title="Cannot delete - has associated businesses">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No business categories found</h5>
                    <p class="text-muted">
                        @if(request('search') || request('status'))
                            Try adjusting your search criteria.
                        @else
                            Get started by creating your first business category.
                        @endif
                    </p>
                    @can('create businesses')
                        @if(!request('search') && !request('status'))
                            <a href="{{ route('admin.business-categories.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-2"></i>
                                Create First Category
                            </a>
                        @endif
                    @endcan
                </div>
            @endif
        </div>
        @if($categories->hasPages())
            <div class="card-footer">
                {{ $categories->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
@stop

@section('styles')
    <style>
        .table {
            table-layout: fixed;
            width: 100%;
        }
        .table td {
            vertical-align: middle;
            padding: 12px 8px;
            overflow: hidden;
        }
        .table th {
            white-space: nowrap;
            font-weight: 600;
        }
        .table td:nth-child(1) { /* Name column */
            width: 18%;
            min-width: 150px;
        }
        .table td:nth-child(2) { /* Description column */
            width: 30%;
            min-width: 250px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        .table td:nth-child(3) { /* Icon column */
            width: 10%;
            min-width: 100px;
            text-align: center;
        }
        .table td:nth-child(4) { /* Color column */
            width: 8%;
            min-width: 80px;
        }
        .table td:nth-child(5) { /* Businesses column */
            width: 7%;
            text-align: center;
        }
        .table td:nth-child(6) { /* Sort Order column */
            width: 7%;
            text-align: center;
        }
        .table td:nth-child(7) { /* Status column */
            width: 8%;
            text-align: center;
        }
        .table td:nth-child(8) { /* Actions column */
            width: 12%;
            min-width: 120px;
            text-align: center;
        }

        /* Ensure description text doesn't overflow */
        .description-text {
            max-height: 4.5em;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            line-height: 1.5;
            word-break: break-word;
        }
    </style>
@stop

@section('scripts')
    <script>
        // Auto-submit form when status filter changes
        document.addEventListener('DOMContentLoaded', function() {
            // Any additional JavaScript can go here
        });
    </script>
@stop
