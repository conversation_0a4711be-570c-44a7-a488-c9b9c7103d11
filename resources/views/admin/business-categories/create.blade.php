@extends('admin.layouts.app')

@section('title', 'Create Business Category')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Create Business Category</h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.business-categories.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Categories
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Category Information</h3>
                </div>
                <form method="POST" action="{{ route('admin.business-categories.store') }}">
                    @csrf
                    <div class="card-body">
                        <div class="form-group">
                            <label for="name">Category Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">The name of the business category (e.g., "Restaurant", "Salon", "Fitness")</small>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Optional description of what this category represents</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="icon">Icon Class</label>
                                    <input type="text" class="form-control @error('icon') is-invalid @enderror"
                                           id="icon" name="icon" value="{{ old('icon') }}" placeholder="fas fa-utensils">
                                    @error('icon')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">FontAwesome icon class (e.g., "fas fa-utensils")</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="color">Color</label>
                                    <input type="color" class="form-control @error('color') is-invalid @enderror"
                                           id="color" name="color" value="{{ old('color', '#007bff') }}">
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Color for the category icon and labels</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="sort_order">Sort Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Lower numbers appear first in lists</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1"
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                            <small class="form-text text-muted">Only active categories will be available for selection</small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Create Category
                        </button>
                        <a href="{{ route('admin.business-categories.index') }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Preview</h3>
                </div>
                <div class="card-body">
                    <div class="preview-container">
                        <div class="category-preview">
                            <i id="preview-icon" class="fas fa-tag" style="color: #007bff; font-size: 24px;"></i>
                            <h5 id="preview-name" class="mt-2">Category Name</h5>
                            <p id="preview-description" class="text-muted">Category description will appear here...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">Icon Examples</h3>
                </div>
                <div class="card-body">
                    <div class="icon-examples">
                        <div class="row">
                            <div class="col-6 mb-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary icon-example" data-icon="fas fa-utensils">
                                    <i class="fas fa-utensils"></i> Restaurant
                                </button>
                            </div>
                            <div class="col-6 mb-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary icon-example" data-icon="fas fa-cut">
                                    <i class="fas fa-cut"></i> Salon
                                </button>
                            </div>
                            <div class="col-6 mb-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary icon-example" data-icon="fas fa-dumbbell">
                                    <i class="fas fa-dumbbell"></i> Fitness
                                </button>
                            </div>
                            <div class="col-6 mb-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary icon-example" data-icon="fas fa-stethoscope">
                                    <i class="fas fa-stethoscope"></i> Medical
                                </button>
                            </div>
                            <div class="col-6 mb-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary icon-example" data-icon="fas fa-car">
                                    <i class="fas fa-car"></i> Automotive
                                </button>
                            </div>
                            <div class="col-6 mb-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary icon-example" data-icon="fas fa-graduation-cap">
                                    <i class="fas fa-graduation-cap"></i> Education
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('styles')
    <style>
        .preview-container {
            text-align: center;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 5px;
        }
        .icon-example {
            width: 100%;
            text-align: left;
        }
        .icon-examples .btn {
            font-size: 12px;
        }
    </style>
@stop

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const nameInput = document.getElementById('name');
            const descriptionInput = document.getElementById('description');
            const iconInput = document.getElementById('icon');
            const colorInput = document.getElementById('color');

            const previewName = document.getElementById('preview-name');
            const previewDescription = document.getElementById('preview-description');
            const previewIcon = document.getElementById('preview-icon');

            // Update preview when inputs change
            function updatePreview() {
                previewName.textContent = nameInput.value || 'Category Name';
                previewDescription.textContent = descriptionInput.value || 'Category description will appear here...';

                if (iconInput.value) {
                    previewIcon.className = iconInput.value;
                } else {
                    previewIcon.className = 'fas fa-tag';
                }

                previewIcon.style.color = colorInput.value;
            }

            nameInput.addEventListener('input', updatePreview);
            descriptionInput.addEventListener('input', updatePreview);
            iconInput.addEventListener('input', updatePreview);
            colorInput.addEventListener('input', updatePreview);

            // Icon example buttons
            document.querySelectorAll('.icon-example').forEach(button => {
                button.addEventListener('click', function() {
                    iconInput.value = this.dataset.icon;
                    updatePreview();
                });
            });

            // Initial preview update
            updatePreview();
        });
    </script>
@stop
