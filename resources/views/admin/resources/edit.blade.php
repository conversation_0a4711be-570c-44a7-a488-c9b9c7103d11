@extends('admin.layouts.app')

@section('title', 'Edit Resource')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Edit Resource</h1>
            <p class="text-muted">{{ $resource->business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.resources.show', $resource) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Resource
                </a>
                <a href="{{ route('admin.resources.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Resources
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-12">
        <form action="{{ route('admin.resources.update', $resource) }}" method="POST" id="resource-form">
            @csrf
            @method('PUT')
            
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Basic Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="business_id">Business <span class="text-danger">*</span></label>
                                <select class="form-control @error('business_id') is-invalid @enderror" 
                                        id="business_id" name="business_id" required>
                                    <option value="">Select Business</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}" 
                                                {{ (old('business_id', $resource->business_id) == $businessOption->id) ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('business_id')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="business_branch_id">Branch</label>
                                <select class="form-control @error('business_branch_id') is-invalid @enderror" 
                                        id="business_branch_id" name="business_branch_id">
                                    <option value="">No specific branch</option>
                                    @foreach($branches as $branch)
                                        <option value="{{ $branch->id }}" 
                                                {{ old('business_branch_id', $resource->business_branch_id) == $branch->id ? 'selected' : '' }}>
                                            {{ $branch->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('business_branch_id')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="resource_type_id">Resource Type <span class="text-danger">*</span></label>
                                <select class="form-control @error('resource_type_id') is-invalid @enderror" 
                                        id="resource_type_id" name="resource_type_id" required>
                                    <option value="">Select Resource Type</option>
                                    @foreach($resourceTypes as $type)
                                        <option value="{{ $type->id }}" 
                                                {{ old('resource_type_id', $resource->resource_type_id) == $type->id ? 'selected' : '' }}>
                                            {{ $type->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('resource_type_id')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Resource Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $resource->name) }}" required>
                                @error('name')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3">{{ old('description', $resource->description) }}</textarea>
                                @error('description')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resource Details -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs mr-2"></i>
                        Resource Details
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="capacity">Capacity <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                       id="capacity" name="capacity" value="{{ old('capacity', $resource->capacity) }}" 
                                       min="1" max="1000" required>
                                <small class="form-text text-muted">Maximum number of people/items this resource can accommodate</small>
                                @error('capacity')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="hourly_rate">Hourly Rate</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control @error('hourly_rate') is-invalid @enderror" 
                                           id="hourly_rate" name="hourly_rate" value="{{ old('hourly_rate', $resource->hourly_rate) }}" 
                                           step="0.01" min="0">
                                </div>
                                <small class="form-text text-muted">Optional hourly rate for this resource</small>
                                @error('hourly_rate')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" 
                                           id="requires_approval" name="requires_approval" value="1"
                                           {{ old('requires_approval', $resource->requires_approval) ? 'checked' : '' }}>
                                    <label class="custom-control-label" for="requires_approval">
                                        Requires Approval
                                    </label>
                                </div>
                                <small class="form-text text-muted">Check if bookings for this resource need manual approval</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" 
                                           id="is_active" name="is_active" value="1"
                                           {{ old('is_active', $resource->is_active) ? 'checked' : '' }}>
                                    <label class="custom-control-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                                <small class="form-text text-muted">Uncheck to disable this resource</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="card">
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Update Resource
                            </button>
                            <a href="{{ route('admin.resources.show', $resource) }}" class="btn btn-secondary ml-2">
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // Handle business change (if needed)
    $('#business_id').change(function() {
        if (confirm('Changing the business will reload the page and may lose unsaved changes. Continue?')) {
            const businessId = $(this).val();
            window.location.href = '{{ route("admin.resources.edit", $resource) }}?business_id=' + businessId;
        }
    });
    
    // Form validation
    $('#resource-form').on('submit', function(e) {
        // Add any custom validation logic here
    });
});
</script>
@stop
