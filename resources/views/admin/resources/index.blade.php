@extends('admin.layouts.app')

@section('title', 'Resources')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Resources</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('create resources')
                    <a href="{{ route('admin.resources.create', ['business_id' => $business?->id]) }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add Resource
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.resources.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select class="form-control" id="business_id" name="business_id">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="type">Resource Type</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">All Types</option>
                                @foreach($resourceTypes as $type)
                                    <option value="{{ $type->id }}" 
                                            {{ request('type') == $type->id ? 'selected' : '' }}>
                                        {{ $type->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="branch">Branch</label>
                            <select class="form-control" id="branch" name="branch">
                                <option value="">All Branches</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}" 
                                            {{ request('branch') == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Search resources...">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-block">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search mr-2"></i>
                                    Search
                                </button>
                                <a href="{{ route('admin.resources.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times mr-2"></i>
                                    Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Resources List -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-tools mr-2"></i>
                Resources ({{ $resources->total() }})
            </h3>
        </div>
        <div class="card-body p-0">
            @if($resources->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Resource</th>
                                <th>Type</th>
                                <th>Business</th>
                                <th>Branch</th>
                                <th>Capacity</th>
                                <th>Rate</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($resources as $resource)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $resource->name }}</strong>
                                            @if($resource->description)
                                                <br><small class="text-muted">{{ Str::limit($resource->description, 50) }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($resource->resourceType)
                                            <span class="badge badge-info">{{ $resource->resourceType->name }}</span>
                                        @else
                                            <span class="text-muted">No type</span>
                                        @endif
                                    </td>
                                    <td>{{ $resource->business->name }}</td>
                                    <td>
                                        @if($resource->branch)
                                            {{ $resource->branch->name }}
                                        @else
                                            <span class="text-muted">All branches</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ $resource->capacity }}</span>
                                    </td>
                                    <td>
                                        @if($resource->hourly_rate)
                                            ${{ number_format($resource->hourly_rate, 2) }}/hr
                                        @else
                                            <span class="text-muted">Free</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($resource->is_active)
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.resources.show', $resource) }}"
                                               class="btn btn-sm btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @can('edit resources')
                                                <a href="{{ route('admin.resources.edit', $resource) }}"
                                                   class="btn btn-sm btn-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                            @can('create resources')
                                                <form action="{{ route('admin.resources.duplicate', $resource) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-secondary" title="Duplicate">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </form>
                                            @endcan
                                            @can('delete resources')
                                                <form action="{{ route('admin.resources.destroy', $resource) }}" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this resource?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                    <h4>No Resources Found</h4>
                    <p class="text-muted">
                        @if(request()->hasAny(['search', 'business_id', 'type', 'branch', 'status']))
                            No resources match your current filters.
                        @else
                            You haven't created any resources yet.
                        @endif
                    </p>
                    @can('create resources')
                        <a href="{{ route('admin.resources.create', ['business_id' => $business?->id]) }}" 
                           class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Create Your First Resource
                        </a>
                    @endcan
                </div>
            @endif
        </div>
        
        @if($resources->hasPages())
            <div class="card-footer">
                {{ $resources->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Auto-submit form when business changes to update dependent dropdowns
    $('#business_id').change(function() {
        var businessId = $(this).val();
        
        // Clear and reload resource types
        $('#type').empty().append('<option value="">All Types</option>');
        $('#branch').empty().append('<option value="">All Branches</option>');
        
        if (businessId) {
            // Load resource types for selected business
            // This would require an AJAX endpoint
            
            // Load branches for selected business
            // This would require an AJAX endpoint
        }
    });
});
</script>
@stop
