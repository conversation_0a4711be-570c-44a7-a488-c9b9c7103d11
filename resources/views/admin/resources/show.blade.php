@extends('admin.layouts.app')

@section('title', 'Resource Details')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Resource Details</h1>
            <p class="text-muted">{{ $resource->business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('edit resources')
                    <a href="{{ route('admin.resources.edit', $resource) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Resource
                    </a>
                @endcan
                <a href="{{ route('admin.resources.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Resources
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <!-- Resource Information -->
    <div class="col-md-8">
        <!-- Basic Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle mr-2"></i>
                    Resource Information
                </h3>
                <div class="card-tools">
                    <span class="badge badge-{{ $resource->is_active ? 'success' : 'secondary' }} badge-lg">
                        {{ $resource->is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Resource Name:</strong></td>
                                <td>{{ $resource->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Business:</strong></td>
                                <td>
                                    <a href="{{ route('admin.businesses.show', $resource->business) }}">
                                        {{ $resource->business->name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Branch:</strong></td>
                                <td>
                                    @if($resource->branch)
                                        {{ $resource->branch->name }}
                                    @else
                                        <span class="text-muted">No specific branch</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Resource Type:</strong></td>
                                <td>
                                    @if($resource->resourceType)
                                        <span class="badge badge-info">{{ $resource->resourceType->name }}</span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Capacity:</strong></td>
                                <td>{{ $resource->capacity }}</td>
                            </tr>
                            <tr>
                                <td><strong>Hourly Rate:</strong></td>
                                <td>
                                    @if($resource->hourly_rate)
                                        ${{ number_format($resource->hourly_rate, 2) }}
                                    @else
                                        <span class="text-muted">Not set</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Requires Approval:</strong></td>
                                <td>
                                    @if($resource->requires_approval)
                                        <span class="badge badge-warning">Yes</span>
                                    @else
                                        <span class="badge badge-success">No</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $resource->created_at->format('M d, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($resource->description)
                <div class="row">
                    <div class="col-md-12">
                        <hr>
                        <h5>Description</h5>
                        <p>{{ $resource->description }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Services Using This Resource -->
        @if($resource->services->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-concierge-bell mr-2"></i>
                    Services Using This Resource
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Service</th>
                                <th>Required</th>
                                <th>Quantity</th>
                                <th>Setup Time</th>
                                <th>Cleanup Time</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($resource->services as $service)
                                <tr>
                                    <td>
                                        <strong>{{ $service->name }}</strong>
                                        @if($service->description)
                                            <br><small class="text-muted">{{ Str::limit($service->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($service->pivot->is_required)
                                            <span class="badge badge-danger">Required</span>
                                        @else
                                            <span class="badge badge-secondary">Optional</span>
                                        @endif
                                    </td>
                                    <td>{{ $service->pivot->quantity_required ?? 1 }}</td>
                                    <td>{{ $service->pivot->setup_time_minutes ?? 0 }} min</td>
                                    <td>{{ $service->pivot->cleanup_time_minutes ?? 0 }} min</td>
                                    <td>
                                        <a href="{{ route('admin.services.show', $service) }}" 
                                           class="btn btn-sm btn-info" title="View Service">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <!-- Recent Bookings -->
        @if($resource->bookingServiceResources->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-calendar-check mr-2"></i>
                    Recent Bookings
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Customer</th>
                                <th>Date & Time</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($resource->bookingServiceResources->take(10) as $bookingResource)
                                @php $booking = $bookingResource->bookingService->booking; @endphp
                                <tr>
                                    <td>
                                        <strong>{{ $booking->booking_number }}</strong>
                                        <br><small class="text-muted">{{ $bookingResource->bookingService->service->name }}</small>
                                    </td>
                                    <td>{{ $booking->customer_name }}</td>
                                    <td>
                                        {{ $booking->booking_date->format('M d, Y') }}
                                        <br><small class="text-muted">{{ $booking->booking_time->format('g:i A') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $booking->status === 'confirmed' ? 'success' : ($booking->status === 'pending' ? 'warning' : 'secondary') }}">
                                            {{ ucfirst($booking->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.bookings.show', $booking) }}" 
                                           class="btn btn-sm btn-info" title="View Booking">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Quick Stats
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="description-block border-right">
                            <span class="description-percentage text-success">
                                <i class="fas fa-concierge-bell"></i>
                            </span>
                            <h5 class="description-header">{{ $resource->services->count() }}</h5>
                            <span class="description-text">Services</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="description-block">
                            <span class="description-percentage text-info">
                                <i class="fas fa-calendar-check"></i>
                            </span>
                            <h5 class="description-header">{{ $resource->bookingServiceResources->count() }}</h5>
                            <span class="description-text">Bookings</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bolt mr-2"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="card-body">
                @can('edit resources')
                    <a href="{{ route('admin.resources.edit', $resource) }}" class="btn btn-warning btn-block mb-2">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Resource
                    </a>
                @endcan
                
                @can('create resources')
                    <form action="{{ route('admin.resources.duplicate', $resource) }}" method="POST" class="d-inline w-100">
                        @csrf
                        <button type="submit" class="btn btn-info btn-block mb-2">
                            <i class="fas fa-copy mr-2"></i>
                            Duplicate Resource
                        </button>
                    </form>
                @endcan
                
                <a href="{{ route('admin.resources.index', ['business_id' => $resource->business_id]) }}" 
                   class="btn btn-secondary btn-block mb-2">
                    <i class="fas fa-list mr-2"></i>
                    View All Resources
                </a>
                
                @can('delete resources')
                    <form action="{{ route('admin.resources.destroy', $resource) }}" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this resource?')" class="d-inline w-100">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-block">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Resource
                        </button>
                    </form>
                @endcan
            </div>
        </div>
    </div>
</div>
@stop
