@extends('admin.layouts.app')

@section('title', 'Edit Setting')

@section('header')
    <h1 class="m-0">Edit Setting: {{ $setting->display_name }}</h1>
@stop

@section('main-content')
    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.settings.update-setting', $setting) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="form-group">
                    <label for="key">Key</label>
                    <input type="text" class="form-control" id="key" value="{{ $setting->key }}" disabled>
                    <small class="form-text text-muted">The setting key cannot be changed</small>
                </div>

                <div class="form-group">
                    <label for="group">Group</label>
                    <input type="text" class="form-control" id="group" value="{{ $setting->group }}" disabled>
                    <small class="form-text text-muted">The setting group cannot be changed</small>
                </div>

                <div class="form-group">
                    <label for="display_name">Display Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('display_name') is-invalid @enderror" id="display_name" name="display_name" value="{{ old('display_name', $setting->display_name) }}" required>
                    @error('display_name')
                        <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="type">Type <span class="text-danger">*</span></label>
                    <select class="form-control @error('type') is-invalid @enderror" id="type" name="type" required>
                        <option value="text" {{ old('type', $setting->type) == 'text' ? 'selected' : '' }}>Text</option>
                        <option value="textarea" {{ old('type', $setting->type) == 'textarea' ? 'selected' : '' }}>Textarea</option>
                        <option value="checkbox" {{ old('type', $setting->type) == 'checkbox' ? 'selected' : '' }}>Checkbox</option>
                        <option value="select" {{ old('type', $setting->type) == 'select' ? 'selected' : '' }}>Select</option>
                        <option value="file" {{ old('type', $setting->type) == 'file' ? 'selected' : '' }}>File</option>
                    </select>
                    @error('type')
                        <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group" id="options-container" style="{{ $setting->type === 'select' ? '' : 'display: none;' }}">
                    <label for="options">Options <span class="text-danger">*</span></label>
                    <textarea class="form-control @error('options') is-invalid @enderror" id="options" name="options" rows="3" placeholder="key1:value1&#10;key2:value2">{{ old('options', $setting->options) }}</textarea>
                    <small class="form-text text-muted">Enter options in JSON format</small>
                    @error('options')
                        <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="2">{{ old('description', $setting->description) }}</textarea>
                </div>

                <div class="form-group">
                    <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" id="is_public" name="is_public" {{ old('is_public', $setting->is_public) ? 'checked' : '' }}>
                        <label class="custom-control-label" for="is_public">Public</label>
                    </div>
                    <small class="form-text text-muted">If enabled, this setting can be accessed publicly</small>
                </div>

                <div class="form-group">
                    <label for="order">Order</label>
                    <input type="number" class="form-control" id="order" name="order" value="{{ old('order', $setting->order) }}">
                    <small class="form-text text-muted">Display order (lower numbers appear first)</small>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Update Setting
                    </button>
                    <a href="{{ route('admin.settings.index', $setting->group) }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const typeSelect = document.getElementById('type');
        const optionsContainer = document.getElementById('options-container');

        typeSelect.addEventListener('change', function() {
            if (this.value === 'select') {
                optionsContainer.style.display = 'block';
            } else {
                optionsContainer.style.display = 'none';
            }
        });
    });
</script>
@endsection
