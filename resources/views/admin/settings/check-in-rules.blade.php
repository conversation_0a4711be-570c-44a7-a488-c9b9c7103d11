@extends('admin.layouts.app')

@section('title', 'Check-In Rules Settings')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Check-In Rules Settings</h1>
            <p class="text-muted">Configure check-in and attendance policies</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Settings
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-check mr-2"></i>
                        Check-In Configuration
                    </h3>
                </div>

                <form action="{{ route('admin.settings.check-in-rules.update') }}" method="POST">
                    @csrf

                    <div class="card-body">
                        <!-- Business Selection -->
                        <div class="form-group">
                            <label for="business_id">Business <span class="text-danger">*</span></label>
                            <select name="business_id" id="business_id" class="form-control @error('business_id') is-invalid @enderror">
                                <option value="">Select Business</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}"
                                            {{ old('business_id', $business?->id) == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('business_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Auto Check-In Settings -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="auto_check_in_enabled"
                                       name="auto_check_in_enabled" value="1"
                                       {{ old('auto_check_in_enabled', $settings['auto_check_in_enabled']) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="auto_check_in_enabled">
                                    Enable Automatic Check-In
                                </label>
                            </div>
                            <small class="form-text text-muted">Automatically check in customers when they arrive</small>
                        </div>

                        <!-- Check-In Window -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="check_in_window_minutes">Check-In Window (minutes)</label>
                                    <input type="number" name="check_in_window_minutes" id="check_in_window_minutes"
                                           class="form-control @error('check_in_window_minutes') is-invalid @enderror"
                                           value="{{ old('check_in_window_minutes', $settings['check_in_window_minutes']) }}"
                                           min="1" max="120" required>
                                    @error('check_in_window_minutes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">How long before appointment customers can check in</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="late_arrival_grace_minutes">Late Arrival Grace Period (minutes)</label>
                                    <input type="number" name="late_arrival_grace_minutes" id="late_arrival_grace_minutes"
                                           class="form-control @error('late_arrival_grace_minutes') is-invalid @enderror"
                                           value="{{ old('late_arrival_grace_minutes', $settings['late_arrival_grace_minutes']) }}"
                                           min="0" max="60" required>
                                    @error('late_arrival_grace_minutes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Grace period for late arrivals before marking as no-show</small>
                                </div>
                            </div>
                        </div>

                        <!-- Early Check-In Settings -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="allow_early_check_in"
                                       name="allow_early_check_in" value="1"
                                       {{ old('allow_early_check_in', $settings['allow_early_check_in']) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="allow_early_check_in">
                                    Allow Early Check-In
                                </label>
                            </div>
                        </div>

                        <div class="form-group" id="early_check_in_options" style="{{ old('allow_early_check_in', $settings['allow_early_check_in']) ? '' : 'display: none;' }}">
                            <label for="early_check_in_minutes">Early Check-In Window (minutes)</label>
                            <input type="number" name="early_check_in_minutes" id="early_check_in_minutes"
                                   class="form-control @error('early_check_in_minutes') is-invalid @enderror"
                                   value="{{ old('early_check_in_minutes', $settings['early_check_in_minutes']) }}"
                                   min="1" max="60">
                            @error('early_check_in_minutes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">How early customers can check in before their appointment</small>
                        </div>

                        <!-- No-Show Settings -->
                        <div class="form-group">
                            <label for="auto_no_show_minutes">Auto No-Show After (minutes)</label>
                            <input type="number" name="auto_no_show_minutes" id="auto_no_show_minutes"
                                   class="form-control @error('auto_no_show_minutes') is-invalid @enderror"
                                   value="{{ old('auto_no_show_minutes', $settings['auto_no_show_minutes']) }}"
                                   min="5" max="180" required>
                            @error('auto_no_show_minutes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Automatically mark as no-show after this many minutes past appointment time</small>
                        </div>

                        <!-- Confirmation Settings -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="require_check_in_confirmation"
                                       name="require_check_in_confirmation" value="1"
                                       {{ old('require_check_in_confirmation', $settings['require_check_in_confirmation']) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="require_check_in_confirmation">
                                    Require Check-In Confirmation
                                </label>
                            </div>
                            <small class="form-text text-muted">Require staff confirmation for customer check-ins</small>
                        </div>

                        <!-- Notification Settings -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="send_check_in_notifications"
                                       name="send_check_in_notifications" value="1"
                                       {{ old('send_check_in_notifications', $settings['send_check_in_notifications']) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="send_check_in_notifications">
                                    Send Check-In Notifications
                                </label>
                            </div>
                            <small class="form-text text-muted">Send notifications to staff when customers check in</small>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Check-In Rules
                        </button>
                        <a href="{{ route('admin.check-in.index') }}" class="btn btn-secondary">
                            View Check-In Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Check-In Guidelines
                    </h3>
                </div>
                <div class="card-body">
                    <h6>Best Practices:</h6>
                    <ul class="text-muted">
                        <li><strong>Check-In Window:</strong> 15-30 minutes is typical</li>
                        <li><strong>Grace Period:</strong> 5-10 minutes for late arrivals</li>
                        <li><strong>Early Check-In:</strong> Useful for walk-ins and early arrivals</li>
                        <li><strong>Auto No-Show:</strong> 15-30 minutes prevents long waits</li>
                    </ul>

                    <h6>Notifications:</h6>
                    <p class="text-muted">Enable notifications to keep staff informed of customer arrivals and status changes.</p>

                    <h6>Confirmations:</h6>
                    <p class="text-muted">Require confirmations for better control over the check-in process and to prevent errors.</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2"></i>
                        Current Settings Summary
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>Check-In Window:</strong>
                        </div>
                        <div class="col-6">
                            {{ $settings['check_in_window_minutes'] }} minutes
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <strong>Grace Period:</strong>
                        </div>
                        <div class="col-6">
                            {{ $settings['late_arrival_grace_minutes'] }} minutes
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <strong>Auto No-Show:</strong>
                        </div>
                        <div class="col-6">
                            {{ $settings['auto_no_show_minutes'] }} minutes
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <strong>Early Check-In:</strong>
                        </div>
                        <div class="col-6">
                            {{ $settings['allow_early_check_in'] ? 'Enabled' : 'Disabled' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle early check-in options
    $('#allow_early_check_in').change(function() {
        if ($(this).is(':checked')) {
            $('#early_check_in_options').show();
            // Don't make it required since it's handled in the controller
        } else {
            $('#early_check_in_options').hide();
            // Clear the value when hidden
            $('#early_check_in_minutes').val('');
        }
    });

    // Business selection change
    $('#business_id').change(function() {
        if ($(this).val()) {
            window.location.href = '{{ route("admin.settings.check-in-rules") }}?business_id=' + $(this).val();
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var checkInWindow = parseInt($('#check_in_window_minutes').val());
        var graceMinutes = parseInt($('#late_arrival_grace_minutes').val());
        var noShowMinutes = parseInt($('#auto_no_show_minutes').val());

        if (noShowMinutes <= (checkInWindow + graceMinutes)) {
            e.preventDefault();
            alert('Auto no-show time should be greater than check-in window plus grace period.');
            return false;
        }
    });
});
</script>
@stop
