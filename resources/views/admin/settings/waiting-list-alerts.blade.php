@extends('admin.layouts.app')

@section('title', 'Waiting List Alerts')

@section('header')
    <h1 class="m-0">Waiting List Alerts</h1>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-hourglass-half mr-2"></i>
                        Waiting List Alert Configuration
                    </h3>
                </div>
                
                <form action="{{ route('admin.settings.waiting-list-alerts.update') }}" method="POST">
                    @csrf
                    <div class="card-body">
                        @if($businesses->count() > 1)
                            <div class="form-group">
                                <label for="business_id">Select Business</label>
                                <select name="business_id" id="business_id" class="form-control">
                                    <option value="">All Businesses</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}" 
                                                {{ $business && $business->id == $businessOption->id ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <hr>
                        @endif

                        <!-- Auto Notification Settings -->
                        <h5>Auto Notification Settings</h5>
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="waiting_list_auto_notify" 
                                       name="waiting_list_auto_notify" 
                                       {{ old('waiting_list_auto_notify', $settings['waiting_list_auto_notify']) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="waiting_list_auto_notify">
                                    Enable Auto Notifications
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                Automatically notify customers when slots become available
                            </small>
                        </div>

                        <div id="notification_settings" style="{{ old('waiting_list_auto_notify', $settings['waiting_list_auto_notify']) ? '' : 'display: none;' }}">
                            <!-- Notification Timing -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="waiting_list_notification_delay">Notification Delay (minutes)</label>
                                        <input type="number" name="waiting_list_notification_delay" 
                                               id="waiting_list_notification_delay" 
                                               class="form-control @error('waiting_list_notification_delay') is-invalid @enderror"
                                               value="{{ old('waiting_list_notification_delay', $settings['waiting_list_notification_delay']) }}"
                                               min="0" max="60">
                                        @error('waiting_list_notification_delay')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">
                                            Delay before sending notification after slot becomes available
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="waiting_list_notification_interval">Notification Interval (hours)</label>
                                        <input type="number" name="waiting_list_notification_interval" 
                                               id="waiting_list_notification_interval" 
                                               class="form-control @error('waiting_list_notification_interval') is-invalid @enderror"
                                               value="{{ old('waiting_list_notification_interval', $settings['waiting_list_notification_interval']) }}"
                                               min="1" max="168">
                                        @error('waiting_list_notification_interval')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">
                                            Time between repeated notifications (1-168 hours)
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Notification Limits -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="waiting_list_max_notifications">Max Notifications per Customer</label>
                                        <input type="number" name="waiting_list_max_notifications" 
                                               id="waiting_list_max_notifications" 
                                               class="form-control @error('waiting_list_max_notifications') is-invalid @enderror"
                                               value="{{ old('waiting_list_max_notifications', $settings['waiting_list_max_notifications']) }}"
                                               min="1" max="10">
                                        @error('waiting_list_max_notifications')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">
                                            Maximum number of notifications to send per customer
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="waiting_list_expire_days">Waiting List Expiry (days)</label>
                                        <input type="number" name="waiting_list_expire_days" 
                                               id="waiting_list_expire_days" 
                                               class="form-control @error('waiting_list_expire_days') is-invalid @enderror"
                                               value="{{ old('waiting_list_expire_days', $settings['waiting_list_expire_days']) }}"
                                               min="1" max="365">
                                        @error('waiting_list_expire_days')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">
                                            Days after which waiting list entries expire
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Priority Settings -->
                            <h5>Priority Settings</h5>
                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="waiting_list_priority_boost" 
                                           name="waiting_list_priority_boost" 
                                           {{ old('waiting_list_priority_boost', $settings['waiting_list_priority_boost']) ? 'checked' : '' }}>
                                    <label class="custom-control-label" for="waiting_list_priority_boost">
                                        Enable Priority Boost
                                    </label>
                                </div>
                                <small class="form-text text-muted">
                                    Give priority to customers who have been waiting longer
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Waiting List Settings
                        </button>
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Settings
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Settings Summary -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Current Settings
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <span class="info-box-icon bg-{{ $settings['waiting_list_auto_notify'] ? 'success' : 'secondary' }}">
                            <i class="fas fa-{{ $settings['waiting_list_auto_notify'] ? 'check' : 'times' }}"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Auto Notifications</span>
                            <span class="info-box-number">
                                {{ $settings['waiting_list_auto_notify'] ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                    </div>

                    @if($settings['waiting_list_auto_notify'])
                        <div class="info-box">
                            <span class="info-box-icon bg-info">
                                <i class="fas fa-clock"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Notification Delay</span>
                                <span class="info-box-number">
                                    {{ $settings['waiting_list_notification_delay'] }} min
                                </span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-warning">
                                <i class="fas fa-repeat"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Max Notifications</span>
                                <span class="info-box-number">
                                    {{ $settings['waiting_list_max_notifications'] }}
                                </span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-danger">
                                <i class="fas fa-calendar-times"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Expires After</span>
                                <span class="info-box-number">
                                    {{ $settings['waiting_list_expire_days'] }} days
                                </span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-{{ $settings['waiting_list_priority_boost'] ? 'primary' : 'secondary' }}">
                                <i class="fas fa-arrow-up"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Priority Boost</span>
                                <span class="info-box-number">
                                    {{ $settings['waiting_list_priority_boost'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- How It Works -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-question-circle mr-2"></i>
                        How It Works
                    </h3>
                </div>
                <div class="card-body">
                    <ol class="small">
                        <li>Customer joins waiting list for desired time slot</li>
                        <li>When slot becomes available, system waits for delay period</li>
                        <li>Notification sent to first customer in queue</li>
                        <li>If no response, next customer is notified after interval</li>
                        <li>Process repeats up to max notifications limit</li>
                        <li>Entries expire after specified days</li>
                    </ol>
                    
                    <div class="mt-3">
                        <h6>Priority Boost:</h6>
                        <p class="small text-muted">
                            When enabled, customers who have been waiting longer get priority 
                            over newer entries, even if they joined different waiting lists.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle notification settings visibility
    $('#waiting_list_auto_notify').change(function() {
        if ($(this).is(':checked')) {
            $('#notification_settings').show();
        } else {
            $('#notification_settings').hide();
        }
    });

    // Business selection change
    $('#business_id').change(function() {
        if ($(this).val()) {
            window.location.href = '{{ route("admin.settings.waiting-list-alerts") }}?business_id=' + $(this).val();
        } else {
            window.location.href = '{{ route("admin.settings.waiting-list-alerts") }}';
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var delay = parseInt($('#waiting_list_notification_delay').val());
        var interval = parseInt($('#waiting_list_notification_interval').val());
        var maxNotifications = parseInt($('#waiting_list_max_notifications').val());
        
        if (delay < 0 || delay > 60) {
            e.preventDefault();
            alert('Notification delay must be between 0 and 60 minutes.');
            return false;
        }
        
        if (interval < 1 || interval > 168) {
            e.preventDefault();
            alert('Notification interval must be between 1 and 168 hours (1 week).');
            return false;
        }
        
        if (maxNotifications < 1 || maxNotifications > 10) {
            e.preventDefault();
            alert('Max notifications must be between 1 and 10.');
            return false;
        }
    });
});
</script>
@stop
