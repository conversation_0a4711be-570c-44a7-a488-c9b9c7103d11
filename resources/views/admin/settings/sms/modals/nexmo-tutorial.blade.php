<!-- Nexmo (Vonage) Setup Tutorial Modal -->
<div class="modal fade" id="nexmoTutorialModal" tabindex="-1" role="dialog" aria-labelledby="nexmoTutorialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="nexmoTutorialModalLabel">
                    <i class="fas fa-sms mr-2"></i>
                    Nexmo (Vonage) SMS Setup Tutorial
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Introduction -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>About Nexmo (Vonage)</h6>
                    <p class="mb-0">Nexmo, now part of Vonage, is a global communications platform offering reliable SMS services. This tutorial will help you set up Nexmo for your booking system notifications.</p>
                </div>

                <!-- Step 1: Account Creation -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-plus mr-2"></i>Step 1: Create Vonage Account</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Visit <a href="https://dashboard.nexmo.com/sign-up" target="_blank" class="text-primary">https://dashboard.nexmo.com/sign-up</a></li>
                            <li>Click "Sign up" and fill out the registration form</li>
                            <li>Verify your email address</li>
                            <li>Complete phone number verification</li>
                            <li>Add payment method (required even for free trial)</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-gift mr-1"></i><strong>Free Trial:</strong> Vonage provides €2 in free credits to test their services.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: API Key -->
                <div class="card mb-3" id="nexmo********-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key mr-2"></i>Step 2: Find Your API Key</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Log in to your Vonage Dashboard at <a href="https://dashboard.nexmo.com" target="_blank" class="text-primary">dashboard.nexmo.com</a></li>
                            <li>On the main dashboard, look for the <strong>API Settings</strong> section</li>
                            <li>Your <strong>API Key</strong> is displayed (8-character alphanumeric string)</li>
                            <li>Click the copy icon next to it to copy the API Key</li>
                            <li>Paste this value into the <strong>API Key</strong> field in your SMS settings</li>
                        </ol>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-exclamation-triangle mr-1"></i><strong>Note:</strong> API Key is your public identifier and is safe to use in client-side code.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 3: API Secret -->
                <div class="card mb-3" id="nexmo-api********section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lock mr-2"></i>Step 3: Find Your API Secret</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>In the same <strong>API Settings</strong> section on your Vonage Dashboard</li>
                            <li>Look for <strong>API Secret</strong> (it's hidden by default)</li>
                            <li>Click "Reveal" or the eye icon to show the API Secret</li>
                            <li>Click the copy icon to copy the API Secret</li>
                            <li>Paste this value into the <strong>API Secret</strong> field in your SMS settings</li>
                        </ol>
                        <div class="alert alert-danger">
                            <small><i class="fas fa-shield-alt mr-1"></i><strong>Security:</strong> Keep your API Secret confidential! Never expose it in client-side code or public repositories.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Sender ID -->
                <div class="card mb-3" id="nexmo********id-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-phone mr-2"></i>Step 4: Configure Sender ID</h6>
                    </div>
                    <div class="card-body">
                        <p>You have several options for the sender ID:</p>
                        
                        <h6><i class="fas fa-text-width mr-2"></i>Option 1: Alphanumeric Sender ID</h6>
                        <ol>
                            <li>Use a custom text name (e.g., "BookingApp", "YourBrand")</li>
                            <li>Must be 3-11 characters, alphanumeric only</li>
                            <li>Recipients cannot reply to alphanumeric sender IDs</li>
                            <li>Enter your chosen name in the <strong>From Number</strong> field</li>
                        </ol>

                        <h6><i class="fas fa-phone mr-2"></i>Option 2: Virtual Phone Number</h6>
                        <ol>
                            <li>Go to <strong>Numbers</strong> → <strong>Buy numbers</strong> in your dashboard</li>
                            <li>Select your country and choose SMS-enabled numbers</li>
                            <li>Purchase a virtual number (costs vary by country)</li>
                            <li>Use the purchased number in international format (+1234567890)</li>
                            <li>Enter this number in the <strong>From Number</strong> field</li>
                        </ol>

                        <div class="alert alert-info">
                            <small><i class="fas fa-info-circle mr-1"></i><strong>Recommendation:</strong> Use alphanumeric sender ID for notifications, virtual numbers for two-way messaging.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Testing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-vial mr-2"></i>Step 5: Test Your Configuration</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Save your SMS settings in the booking system</li>
                            <li>Create a test booking to trigger an SMS notification</li>
                            <li>Check your Vonage Dashboard logs for message status</li>
                            <li>Verify the SMS was received on the test phone number</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-check-circle mr-1"></i><strong>Success:</strong> If you receive the test SMS, your Vonage integration is working correctly!</small>
                        </div>
                    </div>
                </div>

                <!-- Country-Specific Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-globe mr-2"></i>Country-Specific Requirements</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>Important Notes:</h6>
                            <ul class="mb-0">
                                <li><strong>US/Canada:</strong> Alphanumeric sender IDs are not supported. Use virtual numbers.</li>
                                <li><strong>India:</strong> Requires sender ID registration and DLT compliance.</li>
                                <li><strong>China:</strong> Special requirements and restrictions apply.</li>
                                <li><strong>EU:</strong> GDPR compliance required for personal data processing.</li>
                            </ul>
                        </div>
                        <p class="mb-0">Check the <a href="https://help.nexmo.com/hc/en-us/sections/200622473-Country-Specific-Features-and-Restrictions" target="_blank" class="text-primary">country-specific guide</a> for detailed requirements.</p>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Troubleshooting</h6>
                    </div>
                    <div class="card-body">
                        <h6>Common Issues:</h6>
                        <ul>
                            <li><strong>Authentication Error:</strong> Verify your API Key and Secret are correct</li>
                            <li><strong>Invalid Sender ID:</strong> Check country-specific sender ID requirements</li>
                            <li><strong>Message Rejected:</strong> Ensure compliance with local regulations</li>
                            <li><strong>Insufficient Balance:</strong> Add credits to your Vonage account</li>
                        </ul>
                        <div class="mt-3">
                            <a href="https://help.nexmo.com" target="_blank" class="btn btn********-primary btn-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>Vonage Support
                            </a>
                            <a href="https://developer.nexmo.com/messaging/sms/overview" target="_blank" class="btn btn********-info btn-sm ml-2">
                                <i class="fas fa-book mr-1"></i>SMS Documentation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a href="https://dashboard.nexmo.com" target="_blank" class="btn btn-success">
                    <i class="fas fa-external-link-alt mr-2"></i>Open Vonage Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
