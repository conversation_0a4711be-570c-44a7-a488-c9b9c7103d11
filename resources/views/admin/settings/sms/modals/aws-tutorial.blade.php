<!-- AWS SNS Setup Tutorial Modal -->
<div class="modal fade" id="awsTutorialModal" tabindex="-1" role="dialog" aria-labelledby="awsTutorialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="awsTutorialModalLabel">
                    <i class="fab fa-aws mr-2"></i>
                    AWS SNS Setup Tutorial
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Introduction -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>About AWS SNS</h6>
                    <p class="mb-0">Amazon Simple Notification Service (SNS) is a fully managed messaging service that provides reliable, scalable SMS delivery. This tutorial will guide you through setting up AWS SNS for your booking system.</p>
                </div>

                <!-- Step 1: AWS Account -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-plus mr-2"></i>Step 1: Create AWS Account</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Visit <a href="https://aws.amazon.com/free" target="_blank" class="text-primary">https://aws.amazon.com/free</a></li>
                            <li>Click "Create a Free Account"</li>
                            <li>Fill out the registration form with your details</li>
                            <li>Provide payment method (required even for free tier)</li>
                            <li>Complete phone verification</li>
                            <li>Choose the Basic support plan (free)</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-gift mr-1"></i><strong>Free Tier:</strong> AWS provides 100 SMS messages per month free for the first 12 months.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: IAM User -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-users-cog mr-2"></i>Step 2: Create IAM User</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Log in to AWS Console at <a href="https://console.aws.amazon.com" target="_blank" class="text-primary">console.aws.amazon.com</a></li>
                            <li>Search for "IAM" in the services search bar</li>
                            <li>Click on "IAM" to open Identity and Access Management</li>
                            <li>In the left sidebar, click "Users"</li>
                            <li>Click "Add user" button</li>
                            <li>Enter a username (e.g., "booking-sms-user")</li>
                            <li>Select "Programmatic access" for access type</li>
                            <li>Click "Next: Permissions"</li>
                        </ol>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-exclamation-triangle mr-1"></i><strong>Security:</strong> Never use your root AWS account credentials for applications.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 3: SNS Permissions -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt mr-2"></i>Step 3: Assign SNS Permissions</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>On the permissions page, click "Attach existing policies directly"</li>
                            <li>Search for "SNS" in the policy search box</li>
                            <li>Select "AmazonSNSFullAccess" policy (or create a custom policy with minimal permissions)</li>
                            <li>Click "Next: Tags" (optional, you can skip tags)</li>
                            <li>Click "Next: Review"</li>
                            <li>Review the user details and click "Create user"</li>
                        </ol>
                        <div class="alert alert-info">
                            <small><i class="fas fa-lock mr-1"></i><strong>Best Practice:</strong> For production, create a custom policy with only SNS:Publish permissions.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Access Keys -->
                <div class="card mb-3" id="aws-access-key-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key mr-2"></i>Step 4: Get Access Key ID</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>After creating the user, you'll see the success page</li>
                            <li>Copy the <strong>Access Key ID</strong> (starts with "AKIA")</li>
                            <li>Paste this value into the <strong>API Key</strong> field in your SMS settings</li>
                        </ol>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-exclamation-triangle mr-1"></i><strong>Important:</strong> This is the only time you can view these credentials. Download the CSV file as backup.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Secret Key -->
                <div class="card mb-3" id="aws-secret-key-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lock mr-2"></i>Step 5: Get Secret Access Key</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>On the same success page, copy the <strong>Secret Access Key</strong></li>
                            <li>Click "Show" if the secret key is hidden</li>
                            <li>Paste this value into the <strong>API Secret</strong> field in your SMS settings</li>
                        </ol>
                        <div class="alert alert-danger">
                            <small><i class="fas fa-shield-alt mr-1"></i><strong>Security:</strong> Keep your Secret Access Key confidential! Never commit it to version control.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 6: SMS Sandbox -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-sandbox mr-2"></i>Step 6: Configure SMS Sandbox (New Accounts)</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Go to SNS service in AWS Console</li>
                            <li>In the left sidebar, click "Text messaging (SMS)"</li>
                            <li>Click "Sandbox destination phone numbers"</li>
                            <li>Add and verify phone numbers that can receive test messages</li>
                            <li>For production use, request to move out of sandbox</li>
                        </ol>
                        <div class="alert alert-info">
                            <small><i class="fas fa-info-circle mr-1"></i><strong>Sandbox Mode:</strong> New AWS accounts start in SMS sandbox mode with restrictions.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 7: Sender ID -->
                <div class="card mb-3" id="aws-sender-id-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-phone mr-2"></i>Step 7: Configure Sender ID</h6>
                    </div>
                    <div class="card-body">
                        <p>For the sender ID, you have several options:</p>
                        
                        <h6><i class="fas fa-text-width mr-2"></i>Option 1: Alphanumeric Sender ID</h6>
                        <ul>
                            <li>Use a custom text name (e.g., "BookingApp")</li>
                            <li>Must be 3-11 characters, alphanumeric only</li>
                            <li>Not supported in US/Canada</li>
                        </ul>

                        <h6><i class="fas fa-phone mr-2"></i>Option 2: Dedicated Phone Number</h6>
                        <ul>
                            <li>Purchase a dedicated phone number through AWS Pinpoint</li>
                            <li>Use the number in international format (+1234567890)</li>
                            <li>Required for US/Canada SMS</li>
                        </ul>

                        <p>Enter your chosen sender ID in the <strong>From Number</strong> field.</p>
                    </div>
                </div>

                <!-- Step 8: Region Configuration -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-globe mr-2"></i>Step 8: Choose AWS Region</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>SMS services are available in specific AWS regions</li>
                            <li>Common regions: us-east-1 (N. Virginia), eu-west-1 (Ireland), ap-southeast-1 (Singapore)</li>
                            <li>Choose a region close to your users for better performance</li>
                            <li>Configure the region in your application's AWS SDK settings</li>
                        </ol>
                        <div class="alert alert-info">
                            <small><i class="fas fa-map-marker-alt mr-1"></i><strong>Note:</strong> The region setting is typically configured in your application code, not in these SMS settings.</small>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Troubleshooting</h6>
                    </div>
                    <div class="card-body">
                        <h6>Common Issues:</h6>
                        <ul>
                            <li><strong>Access Denied:</strong> Check IAM user permissions and policies</li>
                            <li><strong>Sandbox Restrictions:</strong> Verify phone numbers or request production access</li>
                            <li><strong>Invalid Sender ID:</strong> Check country-specific sender ID requirements</li>
                            <li><strong>Region Errors:</strong> Ensure SNS is available in your selected region</li>
                        </ul>
                        <div class="mt-3">
                            <a href="https://docs.aws.amazon.com/sns/latest/dg/sns-mobile-phone-number-as-subscriber.html" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>AWS SNS Documentation
                            </a>
                            <a href="https://aws.amazon.com/support/" target="_blank" class="btn btn-outline-info btn-sm ml-2">
                                <i class="fas fa-life-ring mr-1"></i>AWS Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a href="https://console.aws.amazon.com/sns" target="_blank" class="btn btn-warning">
                    <i class="fas fa-external-link-alt mr-2"></i>Open AWS SNS Console
                </a>
            </div>
        </div>
    </div>
</div>
