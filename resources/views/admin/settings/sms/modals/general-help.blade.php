<!-- General SMS Help Modal -->
<div class="modal fade" id="generalHelpModal" tabindex="-1" role="dialog" aria-labelledby="generalHelpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="generalHelpModalLabel">
                    <i class="fas fa-book mr-2"></i>
                    Complete SMS Setup Guide
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Overview -->
                <div class="alert alert-primary">
                    <h6><i class="fas fa-info-circle mr-2"></i>SMS Integration Overview</h6>
                    <p class="mb-0">This guide will help you choose and configure the best SMS provider for your booking system. Each provider has different strengths, pricing, and regional availability.</p>
                </div>

                <!-- Provider Comparison -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-balance-scale mr-2"></i>Provider Comparison</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Provider</th>
                                        <th>Best For</th>
                                        <th>Pricing</th>
                                        <th>Global Coverage</th>
                                        <th>Setup Difficulty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>Twilio</strong></td>
                                        <td>Reliability, US/Canada</td>
                                        <td>$0.0075/SMS</td>
                                        <td>Excellent</td>
                                        <td>Easy</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Nexmo (Vonage)</strong></td>
                                        <td>Europe, Competitive pricing</td>
                                        <td>$0.005/SMS</td>
                                        <td>Excellent</td>
                                        <td>Easy</td>
                                    </tr>
                                    <tr>
                                        <td><strong>AWS SNS</strong></td>
                                        <td>AWS ecosystem, Scale</td>
                                        <td>$0.006/SMS</td>
                                        <td>Good</td>
                                        <td>Moderate</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Choosing a Provider -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-compass mr-2"></i>How to Choose a Provider</h6>
                    </div>
                    <div class="card-body">
                        <h6>Consider these factors:</h6>
                        <ul>
                            <li><strong>Geographic Coverage:</strong> Where are your customers located?</li>
                            <li><strong>Volume:</strong> How many SMS messages will you send monthly?</li>
                            <li><strong>Budget:</strong> What's your SMS budget and pricing preference?</li>
                            <li><strong>Integration:</strong> Do you already use other services from these providers?</li>
                            <li><strong>Compliance:</strong> Do you need specific regulatory compliance?</li>
                        </ul>

                        <div class="alert alert-success mt-3">
                            <h6><i class="fas fa-thumbs-up mr-2"></i>Recommendations:</h6>
                            <ul class="mb-0">
                                <li><strong>Small Business (US/Canada):</strong> Start with Twilio</li>
                                <li><strong>European Business:</strong> Consider Nexmo (Vonage)</li>
                                <li><strong>AWS Users:</strong> AWS SNS for ecosystem integration</li>
                                <li><strong>High Volume:</strong> Compare pricing across all providers</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Setup Process -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list-ol mr-2"></i>General Setup Process</h6>
                    </div>
                    <div class="card-body">
                        <p>All SMS providers follow a similar setup process:</p>
                        <ol>
                            <li><strong>Create Account:</strong> Sign up with your chosen provider</li>
                            <li><strong>Verify Identity:</strong> Complete account verification (phone, email, sometimes ID)</li>
                            <li><strong>Get Credentials:</strong> Obtain API keys/tokens from the provider dashboard</li>
                            <li><strong>Configure Sender:</strong> Set up phone number or sender ID</li>
                            <li><strong>Test Integration:</strong> Send test messages to verify setup</li>
                            <li><strong>Go Live:</strong> Move from sandbox/test mode to production</li>
                        </ol>
                    </div>
                </div>

                <!-- Required Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-clipboard-list mr-2"></i>Information You'll Need</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>For Account Setup:</h6>
                                <ul>
                                    <li>Business name and address</li>
                                    <li>Phone number for verification</li>
                                    <li>Email address</li>
                                    <li>Payment method (credit card)</li>
                                    <li>Use case description</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>For SMS Configuration:</h6>
                                <ul>
                                    <li>API Key/Account ID</li>
                                    <li>API Secret/Auth Token</li>
                                    <li>Sender ID or Phone Number</li>
                                    <li>Target countries/regions</li>
                                    <li>Expected message volume</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Best Practices -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-star mr-2"></i>Best Practices</h6>
                    </div>
                    <div class="card-body">
                        <h6>Security:</h6>
                        <ul>
                            <li>Never share API secrets publicly</li>
                            <li>Use environment variables for credentials</li>
                            <li>Regularly rotate API keys</li>
                            <li>Monitor usage for unusual activity</li>
                        </ul>

                        <h6>Compliance:</h6>
                        <ul>
                            <li>Include opt-out instructions in messages</li>
                            <li>Respect local SMS regulations</li>
                            <li>Obtain proper consent before sending</li>
                            <li>Keep records of consent and opt-outs</li>
                        </ul>

                        <h6>Message Quality:</h6>
                        <ul>
                            <li>Keep messages concise and clear</li>
                            <li>Include your business name</li>
                            <li>Avoid spam trigger words</li>
                            <li>Test messages before going live</li>
                        </ul>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Common Issues & Solutions</h6>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="troubleshootingAccordion">
                            <div class="card">
                                <div class="card-header" id="headingOne">
                                    <h6 class="mb-0">
                                        <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseOne">
                                            Messages not being delivered
                                        </button>
                                    </h6>
                                </div>
                                <div id="collapseOne" class="collapse" data-parent="#troubleshootingAccordion">
                                    <div class="card-body">
                                        <ul>
                                            <li>Check API credentials are correct</li>
                                            <li>Verify sender ID format and permissions</li>
                                            <li>Ensure recipient numbers are in correct format</li>
                                            <li>Check provider dashboard for error logs</li>
                                            <li>Verify account has sufficient credits</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header" id="headingTwo">
                                    <h6 class="mb-0">
                                        <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo">
                                            Authentication errors
                                        </button>
                                    </h6>
                                </div>
                                <div id="collapseTwo" class="collapse" data-parent="#troubleshootingAccordion">
                                    <div class="card-body">
                                        <ul>
                                            <li>Double-check API key and secret</li>
                                            <li>Ensure no extra spaces in credentials</li>
                                            <li>Verify account is active and verified</li>
                                            <li>Check if credentials have expired</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header" id="headingThree">
                                    <h6 class="mb-0">
                                        <button class="btn btn-link collapsed" type="button" data-toggle="collapse" data-target="#collapseThree">
                                            High costs or unexpected charges
                                        </button>
                                    </h6>
                                </div>
                                <div id="collapseThree" class="collapse" data-parent="#troubleshootingAccordion">
                                    <div class="card-body">
                                        <ul>
                                            <li>Set up usage alerts and spending limits</li>
                                            <li>Monitor message volume and costs</li>
                                            <li>Review pricing for different countries</li>
                                            <li>Consider message optimization strategies</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support Resources -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-life-ring mr-2"></i>Support Resources</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>Twilio</h6>
                                <ul class="list-unstyled">
                                    <li><a href="https://support.twilio.com" target="_blank">Support Center</a></li>
                                    <li><a href="https://www.twilio.com/docs" target="_blank">Documentation</a></li>
                                    <li><a href="https://www.twilio.com/console" target="_blank">Console</a></li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>Nexmo (Vonage)</h6>
                                <ul class="list-unstyled">
                                    <li><a href="https://help.nexmo.com" target="_blank">Help Center</a></li>
                                    <li><a href="https://developer.nexmo.com" target="_blank">Developer Docs</a></li>
                                    <li><a href="https://dashboard.nexmo.com" target="_blank">Dashboard</a></li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>AWS SNS</h6>
                                <ul class="list-unstyled">
                                    <li><a href="https://aws.amazon.com/support" target="_blank">AWS Support</a></li>
                                    <li><a href="https://docs.aws.amazon.com/sns" target="_blank">SNS Documentation</a></li>
                                    <li><a href="https://console.aws.amazon.com/sns" target="_blank">SNS Console</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="$('#twilioTutorialModal').modal('show'); $('#generalHelpModal').modal('hide');">
                        <i class="fab fa-twilio mr-1"></i>Twilio Setup
                    </button>
                    <button type="button" class="btn btn-success" onclick="$('#nexmoTutorialModal').modal('show'); $('#generalHelpModal').modal('hide');">
                        <i class="fas fa-sms mr-1"></i>Nexmo Setup
                    </button>
                    <button type="button" class="btn btn-warning" onclick="$('#awsTutorialModal').modal('show'); $('#generalHelpModal').modal('hide');">
                        <i class="fab fa-aws mr-1"></i>AWS Setup
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
