<!-- Twilio Setup Tutorial Modal -->
<div class="modal fade" id="twilioTutorialModal" tabindex="-1" role="dialog" aria-labelledby="twilioTutorialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="twilioTutorialModalLabel">
                    <i class="fab fa-twilio mr-2"></i>
                    Twilio SMS Setup Tutorial
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Introduction -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>About Twilio</h6>
                    <p class="mb-0">Twilio is a leading cloud communications platform that provides reliable SMS services worldwide. This tutorial will guide you through setting up Twilio for your booking system.</p>
                </div>

                <!-- Step 1: Account Creation -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-plus mr-2"></i>Step 1: Create Twilio Account</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Visit <a href="https://www.twilio.com/try-twilio" target="_blank" class="text-primary">https://www.twilio.com/try-twilio</a></li>
                            <li>Click "Start your free trial" button</li>
                            <li>Fill out the registration form with your details</li>
                            <li>Verify your email address and phone number</li>
                            <li>Complete the account verification process</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-gift mr-1"></i><strong>Free Trial:</strong> Twilio provides $15 in free credits to test their services.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Account SID -->
                <div class="card mb-3" id="twilio-account-sid-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key mr-2"></i>Step 2: Find Your Account SID (API Key)</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Log in to your Twilio Console at <a href="https://console.twilio.com" target="_blank" class="text-primary">console.twilio.com</a></li>
                            <li>On the main dashboard, you'll see your <strong>Account Info</strong> section</li>
                            <li>Your <strong>Account SID</strong> is displayed prominently (starts with "AC")</li>
                            <li>Click the copy icon next to it to copy the Account SID</li>
                            <li>Paste this value into the <strong>API Key</strong> field in your SMS settings</li>
                        </ol>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-exclamation-triangle mr-1"></i><strong>Note:</strong> Account SID is safe to share and is used to identify your account.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Auth Token -->
                <div class="card mb-3" id="twilio-auth-token-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lock mr-2"></i>Step 3: Find Your Auth Token (API Secret)</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>In the same <strong>Account Info</strong> section on your Twilio Console</li>
                            <li>Look for <strong>Auth Token</strong> (it's hidden by default)</li>
                            <li>Click the "Show" link or eye icon to reveal the Auth Token</li>
                            <li>Click the copy icon to copy the Auth Token</li>
                            <li>Paste this value into the <strong>API Secret</strong> field in your SMS settings</li>
                        </ol>
                        <div class="alert alert-danger">
                            <small><i class="fas fa-shield-alt mr-1"></i><strong>Security:</strong> Keep your Auth Token secret! Never share it publicly or commit it to version control.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Phone Number -->
                <div class="card mb-3" id="twilio-phone-number-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-phone mr-2"></i>Step 4: Get a Twilio Phone Number</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>In your Twilio Console, navigate to <strong>Phone Numbers</strong> → <strong>Manage</strong> → <strong>Buy a number</strong></li>
                            <li>Choose your country and select SMS capabilities</li>
                            <li>Browse available numbers and select one you like</li>
                            <li>Click "Buy" to purchase the number (uses your account credits)</li>
                            <li>Copy the purchased number (format: +**********)</li>
                            <li>Paste this into the <strong>From Number</strong> field in your SMS settings</li>
                        </ol>
                        <div class="alert alert-info">
                            <small><i class="fas fa-dollar-sign mr-1"></i><strong>Pricing:</strong> Phone numbers typically cost $1/month. SMS messages cost around $0.0075 per message.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Testing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-vial mr-2"></i>Step 5: Test Your Configuration</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Save your SMS settings in the booking system</li>
                            <li>Create a test booking to trigger an SMS notification</li>
                            <li>Check your Twilio Console logs for message delivery status</li>
                            <li>Verify the SMS was received on the test phone number</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-check-circle mr-1"></i><strong>Success:</strong> If you receive the test SMS, your Twilio integration is working correctly!</small>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Troubleshooting</h6>
                    </div>
                    <div class="card-body">
                        <h6>Common Issues:</h6>
                        <ul>
                            <li><strong>Authentication Error:</strong> Double-check your Account SID and Auth Token</li>
                            <li><strong>Invalid Phone Number:</strong> Ensure the number includes country code (e.g., +1 for US)</li>
                            <li><strong>SMS Not Delivered:</strong> Check Twilio Console logs for error details</li>
                            <li><strong>Trial Account Limitations:</strong> Trial accounts can only send to verified numbers</li>
                        </ul>
                        <div class="mt-3">
                            <a href="https://support.twilio.com" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>Twilio Support
                            </a>
                            <a href="https://www.twilio.com/docs/sms" target="_blank" class="btn btn-outline-info btn-sm ml-2">
                                <i class="fas fa-book mr-1"></i>SMS Documentation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a href="https://console.twilio.com" target="_blank" class="btn btn-primary">
                    <i class="fas fa-external-link-alt mr-2"></i>Open Twilio Console
                </a>
            </div>
        </div>
    </div>
</div>
