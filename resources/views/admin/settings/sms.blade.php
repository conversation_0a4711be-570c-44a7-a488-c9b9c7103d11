@extends('admin.layouts.app')

@section('title', 'SMS Settings')

@section('header')
    <h1 class="m-0">SMS Settings</h1>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sms mr-2"></i>
                        SMS Configuration
                    </h3>
                </div>

                <form action="{{ route('admin.settings.sms.update') }}" method="POST">
                    @csrf
                    <div class="card-body">
                        @if($businesses->count() > 1)
                            <div class="form-group">
                                <label for="business_id">Select Business</label>
                                <select name="business_id" id="business_id" class="form-control">
                                    <option value="">All Businesses</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}"
                                                {{ $business && $business->id == $businessOption->id ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <hr>
                        @endif

                        <!-- SMS Enable/Disable -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="hidden" name="sms_enabled" value="0">
                                <input type="checkbox" class="custom-control-input" id="sms_enabled" name="sms_enabled" value="1"
                                       {{ old('sms_enabled', $settings['sms_enabled']) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="sms_enabled">Enable SMS Notifications</label>
                            </div>
                            <small class="form-text text-muted">Turn on/off SMS notifications for the entire system</small>
                        </div>

                        <div id="sms_configuration" style="{{ old('sms_enabled', $settings['sms_enabled']) ? '' : 'display: none;' }}">
                            <!-- SMS Provider -->
                            <div class="form-group">
                                <label for="sms_provider">
                                    SMS Provider
                                    <button type="button" class="btn btn-sm btn-outline-info ml-2" id="provider-help-btn" title="View setup tutorial">
                                        <i class="fas fa-question-circle"></i> Setup Tutorial
                                    </button>
                                </label>
                                <select name="sms_provider" id="sms_provider" class="form-control @error('sms_provider') is-invalid @enderror">
                                    <option value="twilio" {{ old('sms_provider', $settings['sms_provider']) == 'twilio' ? 'selected' : '' }}>
                                        Twilio
                                    </option>
                                    <option value="nexmo" {{ old('sms_provider', $settings['sms_provider']) == 'nexmo' ? 'selected' : '' }}>
                                        Nexmo (Vonage)
                                    </option>
                                    <option value="aws_sns" {{ old('sms_provider', $settings['sms_provider']) == 'aws_sns' ? 'selected' : '' }}>
                                        AWS SNS
                                    </option>
                                </select>
                                @error('sms_provider')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- API Credentials -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sms_api_key">
                                            <span id="********label">API Key</span>
                                            <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="********help-btn" title="How to find API Key">
                                                <i class="fas fa-question-circle"></i>
                                            </button>
                                        </label>
                                        <input type="text" name="sms_api_key" id="sms_api_key"
                                               class="form-control @error('sms_api_key') is-invalid @enderror"
                                               value="{{ old('sms_api_key', $settings['sms_api_key']) }}"
                                               placeholder="Enter your SMS provider API key">
                                        @error('sms_api_key')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sms_api_secret">
                                            <span id="api********label">API Secret</span>
                                            <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="api********help-btn" title="How to find API Secret">
                                                <i class="fas fa-question-circle"></i>
                                            </button>
                                        </label>
                                        <input type="password" name="sms_api_secret" id="sms_api_secret"
                                               class="form-control @error('sms_api_secret') is-invalid @enderror"
                                               value="{{ old('sms_api_secret', $settings['sms_api_secret']) }}"
                                               placeholder="Enter your SMS provider API secret">
                                        @error('sms_api_secret')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- From Number -->
                            <div class="form-group">
                                <label for="sms_from_number">
                                    <span id="from-number-label">From Number</span>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="from-number-help-btn" title="How to get phone number">
                                        <i class="fas fa-question-circle"></i>
                                    </button>
                                </label>
                                <input type="text" name="sms_from_number" id="sms_from_number"
                                       class="form-control @error('sms_from_number') is-invalid @enderror"
                                       value="{{ old('sms_from_number', $settings['sms_from_number']) }}"
                                       placeholder="e.g., +1234567890">
                                @error('sms_from_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">The phone number that SMS messages will be sent from</small>
                            </div>

                            <!-- AWS Region (only for AWS SNS) -->
                            <div class="form-group" id="aws-region-group" style="display: none;">
                                <label for="aws_region">AWS Region</label>
                                <select name="aws_region" id="aws_region" class="form-control @error('aws_region') is-invalid @enderror">
                                    <option value="us-east-1" {{ old('aws_region', $settings['aws_region']) == 'us-east-1' ? 'selected' : '' }}>US East (N. Virginia) - us-east-1</option>
                                    <option value="us-west-2" {{ old('aws_region', $settings['aws_region']) == 'us-west-2' ? 'selected' : '' }}>US West (Oregon) - us-west-2</option>
                                    <option value="eu-west-1" {{ old('aws_region', $settings['aws_region']) == 'eu-west-1' ? 'selected' : '' }}>Europe (Ireland) - eu-west-1</option>
                                    <option value="ap-southeast-1" {{ old('aws_region', $settings['aws_region']) == 'ap-southeast-1' ? 'selected' : '' }}>Asia Pacific (Singapore) - ap-southeast-1</option>
                                    <option value="ap-northeast-1" {{ old('aws_region', $settings['aws_region']) == 'ap-northeast-1' ? 'selected' : '' }}>Asia Pacific (Tokyo) - ap-northeast-1</option>
                                </select>
                                @error('aws_region')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Select the AWS region for your SNS service</small>
                            </div>

                            <hr>

                            <!-- SMS Notification Types -->
                            <h5>SMS Notification Types</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="hidden" name="sms_booking_confirmation" value="0">
                                            <input type="checkbox" class="custom-control-input" id="sms_booking_confirmation"
                                                   name="sms_booking_confirmation" value="1"
                                                   {{ old('sms_booking_confirmation', $settings['sms_booking_confirmation']) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="sms_booking_confirmation">
                                                Booking Confirmation
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="hidden" name="sms_booking_reminder" value="0">
                                            <input type="checkbox" class="custom-control-input" id="sms_booking_reminder"
                                                   name="sms_booking_reminder" value="1"
                                                   {{ old('sms_booking_reminder', $settings['sms_booking_reminder']) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="sms_booking_reminder">
                                                Booking Reminder
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="hidden" name="sms_waiting_list_notification" value="0">
                                            <input type="checkbox" class="custom-control-input" id="sms_waiting_list_notification"
                                                   name="sms_waiting_list_notification" value="1"
                                                   {{ old('sms_waiting_list_notification', $settings['sms_waiting_list_notification']) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="sms_waiting_list_notification">
                                                Waiting List Alerts
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save SMS Settings
                        </button>
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Settings
                        </a>

                        @if($settings['sms_enabled'])
                            <button type="button" class="btn btn-info ml-2" id="test-sms-btn">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Test SMS
                            </button>
                        @endif
                    </div>
                </form>
            </div>
        </div>

        <!-- Settings Summary -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Current SMS Settings
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <span class="info-box-icon bg-{{ $settings['sms_enabled'] ? 'success' : 'secondary' }}">
                            <i class="fas fa-{{ $settings['sms_enabled'] ? 'check' : 'times' }}"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">SMS Status</span>
                            <span class="info-box-number">
                                {{ $settings['sms_enabled'] ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                    </div>

                    @if($settings['sms_enabled'])
                        <div class="info-box">
                            <span class="info-box-icon bg-info">
                                <i class="fas fa-cog"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Provider</span>
                                <span class="info-box-number">
                                    {{ ucfirst(str_replace('_', ' ', $settings['sms_provider'])) }}
                                </span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-warning">
                                <i class="fas fa-phone"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">From Number</span>
                                <span class="info-box-number">
                                    {{ $settings['sms_from_number'] ?: 'Not Set' }}
                                </span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Help -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-question-circle mr-2"></i>
                        Quick Help
                    </h3>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Click the <i class="fas fa-question-circle text-info"></i> buttons next to each field for detailed setup instructions.</p>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb mr-2"></i>Need Help?</h6>
                        <ul class="mb-0 small">
                            <li>Use the "Setup Tutorial" button to see complete provider setup guides</li>
                            <li>Click help icons next to credential fields for specific instructions</li>
                            <li>All tutorials include step-by-step screenshots and links</li>
                        </ul>
                    </div>

                    <div class="text-center">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="general-help-btn">
                            <i class="fas fa-book mr-2"></i>
                            View Complete Setup Guide
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SMS Provider Tutorial Modals -->
@include('admin.settings.sms.modals.twilio-tutorial')
@include('admin.settings.sms.modals.nexmo-tutorial')
@include('admin.settings.sms.modals.aws-tutorial')
@include('admin.settings.sms.modals.general-help')

<!-- Test SMS Modal -->
<div class="modal fade" id="testSmsModal" tabindex="-1" role="dialog" aria-labelledby="testSmsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="testSmsModalLabel">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Test SMS
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="test-sms-form">
                    @csrf
                    <div class="form-group">
                        <label for="test_phone">Phone Number</label>
                        <input type="text" class="form-control" id="test_phone" name="test_phone"
                               placeholder="e.g., +1234567890" required>
                        <small class="form-text text-muted">
                            Enter the phone number where you want to receive the test SMS. Include country code.
                        </small>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle mr-2"></i>Test Message</h6>
                        <p class="mb-0 small">
                            A test message will be sent to verify your SMS configuration is working correctly.
                            This will use your configured SMS provider and count towards your SMS usage.
                        </p>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-info" id="send-test-sms-btn">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Test SMS
                </button>
            </div>
        </div>
    </div>
</div>

@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle SMS configuration visibility
    $('#sms_enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#sms_configuration').show();
        } else {
            $('#sms_configuration').hide();
        }
    });

    // Update field labels and help based on provider
    function updateProviderLabels() {
        var provider = $('#sms_provider').val();

        if (provider === 'twilio') {
            $('#********label').text('Account SID');
            $('#api********label').text('Auth Token');
            $('#from-number-label').text('Twilio Phone Number');
            $('#aws-region-group').hide();
        } else if (provider === 'nexmo') {
            $('#********label').text('API Key');
            $('#api********label').text('API Secret');
            $('#from-number-label').text('Sender ID / Phone Number');
            $('#aws-region-group').hide();
        } else if (provider === 'aws_sns') {
            $('#********label').text('Access Key ID');
            $('#api********label').text('Secret Access Key');
            $('#from-number-label').text('Sender ID (Optional)');
            $('#aws-region-group').show();
        }
    }

    // Provider change handler
    $('#sms_provider').change(function() {
        updateProviderLabels();
    });

    // Initialize provider labels
    updateProviderLabels();

    // Business selection change
    $('#business_id').change(function() {
        if ($(this).val()) {
            window.location.href = '{{ route("admin.settings.sms") }}?business_id=' + $(this).val();
        } else {
            window.location.href = '{{ route("admin.settings.sms") }}';
        }
    });

    // Modal handlers
    $('#provider-help-btn').click(function() {
        var provider = $('#sms_provider').val();
        if (provider === 'twilio') {
            $('#twilioTutorialModal').modal('show');
        } else if (provider === 'nexmo') {
            $('#nexmoTutorialModal').modal('show');
        } else if (provider === 'aws_sns') {
            $('#awsTutorialModal').modal('show');
        }
    });

    $('#general-help-btn').click(function() {
        $('#generalHelpModal').modal('show');
    });

    // Field-specific help handlers
    $('#********help-btn').click(function() {
        var provider = $('#sms_provider').val();
        if (provider === 'twilio') {
            $('#twilioTutorialModal').modal('show');
            setTimeout(function() {
                $('#twilioTutorialModal .modal-body').scrollTop($('#twilio-account-sid-section').offset().top - 100);
            }, 500);
        } else if (provider === 'nexmo') {
            $('#nexmoTutorialModal').modal('show');
            setTimeout(function() {
                $('#nexmoTutorialModal .modal-body').scrollTop($('#nexmo-********section').offset().top - 100);
            }, 500);
        } else if (provider === 'aws_sns') {
            $('#awsTutorialModal').modal('show');
            setTimeout(function() {
                $('#awsTutorialModal .modal-body').scrollTop($('#aws-access-key-section').offset().top - 100);
            }, 500);
        }
    });

    $('#api********help-btn').click(function() {
        var provider = $('#sms_provider').val();
        if (provider === 'twilio') {
            $('#twilioTutorialModal').modal('show');
            setTimeout(function() {
                $('#twilioTutorialModal .modal-body').scrollTop($('#twilio-auth-token-section').offset().top - 100);
            }, 500);
        } else if (provider === 'nexmo') {
            $('#nexmoTutorialModal').modal('show');
            setTimeout(function() {
                $('#nexmoTutorialModal .modal-body').scrollTop($('#nexmo-api********section').offset().top - 100);
            }, 500);
        } else if (provider === 'aws_sns') {
            $('#awsTutorialModal').modal('show');
            setTimeout(function() {
                $('#awsTutorialModal .modal-body').scrollTop($('#aws********key-section').offset().top - 100);
            }, 500);
        }
    });

    $('#from-number-help-btn').click(function() {
        var provider = $('#sms_provider').val();
        if (provider === 'twilio') {
            $('#twilioTutorialModal').modal('show');
            setTimeout(function() {
                $('#twilioTutorialModal .modal-body').scrollTop($('#twilio-phone-number-section').offset().top - 100);
            }, 500);
        } else if (provider === 'nexmo') {
            $('#nexmoTutorialModal').modal('show');
            setTimeout(function() {
                $('#nexmoTutorialModal .modal-body').scrollTop($('#nexmo********id-section').offset().top - 100);
            }, 500);
        } else if (provider === 'aws_sns') {
            $('#awsTutorialModal').modal('show');
            setTimeout(function() {
                $('#awsTutorialModal .modal-body').scrollTop($('#aws********id-section').offset().top - 100);
            }, 500);
        }
    });

    // Test SMS functionality
    $('#test-sms-btn').click(function() {
        $('#testSmsModal').modal('show');
    });

    $('#send-test-sms-btn').click(function() {
        var phone = $('#test_phone').val();
        if (!phone) {
            alert('Please enter a phone number');
            return;
        }

        var $btn = $(this);
        var originalText = $btn.html();

        // Show loading state
        $btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Sending...');
        $btn.prop('disabled', true);

        $.ajax({
            url: '{{ route("admin.settings.sms.test") }}',
            method: 'POST',
            data: {
                test_phone: phone,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#testSmsModal').modal('hide');
                    alert('Success: ' + response.message);
                    $('#test_phone').val(''); // Clear the form
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                var errorMessage = 'Failed to send test SMS';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert('Error: ' + errorMessage);
            },
            complete: function() {
                // Restore button state
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }
        });
    });
});
</script>
@stop
