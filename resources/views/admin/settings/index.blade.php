@extends('admin.layouts.app')

@section('title', 'System Settings')

@section('header')
    <h1 class="m-0">System Settings</h1>
@stop

@section('main-content')
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                @foreach($groups as $groupName)
                    <li class="nav-item">
                        <a class="nav-link {{ $group === $groupName ? 'active' : '' }}"
                           href="{{ route('admin.settings.index', $groupName) }}">
                            {{ ucfirst($groupName) }}
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-end mb-3">
                <a href="{{ route('admin.settings.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Setting
                </a>
            </div>

            <form action="{{ route('admin.settings.update', $group) }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="row">
                    @foreach($settings as $setting)
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <h5 class="card-title">{{ $setting->display_name }}</h5>
                                        <div>
                                            <a href="{{ route('admin.settings.edit', $setting) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    onclick="confirmDelete('{{ $setting->id }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>

                                    @if($setting->description)
                                        <p class="text-muted">{{ $setting->description }}</p>
                                    @endif

                                    <div class="form-group">
                                        @if($setting->type === 'text')
                                            <input type="text" class="form-control" name="{{ $setting->key }}"
                                                   value="{{ old($setting->key, $setting->value) }}">
                                        @elseif($setting->type === 'textarea')
                                            <textarea class="form-control" name="{{ $setting->key }}" rows="3">{{ old($setting->key, $setting->value) }}</textarea>
                                        @elseif($setting->type === 'checkbox')
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input"
                                                       id="{{ $setting->key }}" name="{{ $setting->key }}"
                                                       {{ old($setting->key, $setting->value) == '1' ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="{{ $setting->key }}">Enabled</label>
                                            </div>
                                        @elseif($setting->type === 'select')
                                            <select class="form-control" name="{{ $setting->key }}">
                                                @foreach($setting->getOptionsArrayAttribute() as $optionValue => $optionLabel)
                                                    <option value="{{ $optionValue }}"
                                                            {{ old($setting->key, $setting->value) == $optionValue ? 'selected' : '' }}>
                                                        {{ $optionLabel }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        @elseif($setting->type === 'file')
                                            <div class="mb-2">
                                                @if($setting->value)
                                                    <img src="{{ asset('storage/' . $setting->value) }}"
                                                         alt="{{ $setting->display_name }}"
                                                         class="img-thumbnail" style="max-height: 100px;">
                                                @endif
                                            </div>
                                            <input type="file" class="form-control-file" name="{{ $setting->key }}">
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="form-group mt-3">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                </div>
            </form>

            <!-- Delete Setting Form -->
            <form id="delete-form" method="POST" style="display: none;">
                @csrf
                @method('DELETE')
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    function confirmDelete(settingId) {
        if (confirm('Are you sure you want to delete this setting? This action cannot be undone.')) {
            const form = document.getElementById('delete-form');
            form.action = "{{ route('admin.settings.destroy', '__id__') }}".replace('__id__', settingId);
            form.submit();
        }
    }
</script>
@endsection
