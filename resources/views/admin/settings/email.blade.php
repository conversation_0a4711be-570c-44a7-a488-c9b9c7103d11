@extends('admin.layouts.app')

@section('title', 'Email Settings')

@section('header')
    <h1 class="m-0">Email Settings</h1>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-envelope-open mr-2"></i>
                        SMTP Configuration
                    </h3>
                </div>

                <form action="{{ route('admin.settings.email.update') }}" method="POST" id="email-settings-form">
                    @csrf
                    <div class="card-body">
                        @if($businesses->count() > 1)
                            <div class="form-group">
                                <label for="business_id">Select Business</label>
                                <select name="business_id" id="business_id" class="form-control">
                                    <option value="">All Businesses</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}"
                                                {{ $business && $business->id == $businessOption->id ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <hr>
                        @endif

                        <!-- Email Enable/Disable -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="hidden" name="mail_enabled" value="0">
                                <input type="checkbox" class="custom-control-input" id="mail_enabled" name="mail_enabled" value="1"
                                       {{ old('mail_enabled', $settings['mail_enabled']) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="mail_enabled">Enable Email Notifications</label>
                            </div>
                            <small class="form-text text-muted">Turn on/off email notifications for the entire system</small>
                        </div>

                        <div id="email_configuration" style="{{ old('mail_enabled', $settings['mail_enabled']) ? '' : 'display: none;' }}">
                            <!-- Mail Driver -->
                            <div class="form-group">
                                <label for="mail_mailer">
                                    Mail Driver
                                    <button type="button" class="btn btn-sm btn-outline-info ml-2" id="provider-help-btn" title="View setup tutorial">
                                        <i class="fas fa-question-circle"></i> Setup Tutorial
                                    </button>
                                </label>
                                <select name="mail_mailer" id="mail_mailer" class="form-control @error('mail_mailer') is-invalid @enderror">
                                    <option value="smtp" {{ old('mail_mailer', $settings['mail_mailer']) == 'smtp' ? 'selected' : '' }}>
                                        SMTP
                                    </option>
                                    <option value="sendmail" {{ old('mail_mailer', $settings['mail_mailer']) == 'sendmail' ? 'selected' : '' }}>
                                        Sendmail
                                    </option>
                                    <option value="mailgun" {{ old('mail_mailer', $settings['mail_mailer']) == 'mailgun' ? 'selected' : '' }}>
                                        Mailgun
                                    </option>
                                    <option value="ses" {{ old('mail_mailer', $settings['mail_mailer']) == 'ses' ? 'selected' : '' }}>
                                        Amazon SES
                                    </option>
                                    <option value="postmark" {{ old('mail_mailer', $settings['mail_mailer']) == 'postmark' ? 'selected' : '' }}>
                                        Postmark
                                    </option>
                                    <option value="log" {{ old('mail_mailer', $settings['mail_mailer']) == 'log' ? 'selected' : '' }}>
                                        Log (Testing)
                                    </option>
                                </select>
                                @error('mail_mailer')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- SMTP Settings -->
                            <div id="smtp_settings" style="{{ old('mail_mailer', $settings['mail_mailer']) == 'smtp' ? '' : 'display: none;' }}">
                                <!-- SMTP Server Settings -->
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="mail_host">
                                                SMTP Server (Host) <span class="text-danger">*</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="smtp-host-help-btn" title="How to find SMTP host">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="text" name="mail_host" id="mail_host"
                                                   class="form-control @error('mail_host') is-invalid @enderror"
                                                   value="{{ old('mail_host', $settings['mail_host']) }}"
                                                   placeholder="e.g., smtp.gmail.com">
                                            @error('mail_host')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="mail_port">
                                                Port <span class="text-danger">*</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="smtp-port-help-btn" title="Common SMTP ports">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="number" name="mail_port" id="mail_port"
                                                   class="form-control @error('mail_port') is-invalid @enderror"
                                                   value="{{ old('mail_port', $settings['mail_port']) }}"
                                                   min="1" max="65535" placeholder="587">
                                            @error('mail_port')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Encryption -->
                                <div class="form-group">
                                    <label for="mail_encryption">Encryption Type</label>
                                    <select name="mail_encryption" id="mail_encryption" class="form-control @error('mail_encryption') is-invalid @enderror">
                                        <option value="tls" {{ old('mail_encryption', $settings['mail_encryption']) == 'tls' ? 'selected' : '' }}>
                                            TLS (Recommended)
                                        </option>
                                        <option value="ssl" {{ old('mail_encryption', $settings['mail_encryption']) == 'ssl' ? 'selected' : '' }}>
                                            SSL
                                        </option>
                                        <option value="null" {{ old('mail_encryption', $settings['mail_encryption']) == 'null' ? 'selected' : '' }}>
                                            None
                                        </option>
                                    </select>
                                    @error('mail_encryption')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">TLS is recommended for most providers</small>
                                </div>

                                <!-- Authentication -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_username">
                                                Username
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="smtp-username-help-btn" title="SMTP username help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="text" name="mail_username" id="mail_username"
                                                   class="form-control @error('mail_username') is-invalid @enderror"
                                                   value="{{ old('mail_username', $settings['mail_username']) }}"
                                                   placeholder="<EMAIL>">
                                            @error('mail_username')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_password">
                                                Password
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="smtp-password-help-btn" title="SMTP password help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="password" name="mail_password" id="mail_password"
                                                   class="form-control @error('mail_password') is-invalid @enderror"
                                                   value="{{ old('mail_password', $settings['mail_password']) }}"
                                                   placeholder="Enter SMTP password">
                                            @error('mail_password')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Mailgun Settings -->
                            <div id="mailgun_settings" style="{{ old('mail_mailer', $settings['mail_mailer']) == 'mailgun' ? '' : 'display: none;' }}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mailgun_domain">
                                                Mailgun Domain <span class="text-danger">*</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="mailgun-domain-help-btn" title="Mailgun domain help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="text" name="mailgun_domain" id="mailgun_domain"
                                                   class="form-control @error('mailgun_domain') is-invalid @enderror"
                                                   value="{{ old('mailgun_domain', $settings['mailgun_domain'] ?? '') }}"
                                                   placeholder="mg.yourdomain.com">
                                            @error('mailgun_domain')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Your verified Mailgun domain</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mailgun_secret">
                                                API Key <span class="text-danger">*</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="mailgun-secret-help-btn" title="Mailgun API key help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="password" name="mailgun_secret" id="mailgun_secret"
                                                   class="form-control @error('mailgun_secret') is-invalid @enderror"
                                                   value="{{ old('mailgun_secret', $settings['mailgun_secret'] ?? '') }}"
                                                   placeholder="key-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
                                            @error('mailgun_secret')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Your private API key from Mailgun</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="mailgun_endpoint">Mailgun Endpoint</label>
                                    <select name="mailgun_endpoint" id="mailgun_endpoint" class="form-control @error('mailgun_endpoint') is-invalid @enderror">
                                        <option value="api.mailgun.net" {{ old('mailgun_endpoint', $settings['mailgun_endpoint'] ?? 'api.mailgun.net') == 'api.mailgun.net' ? 'selected' : '' }}>
                                            US Region (api.mailgun.net)
                                        </option>
                                        <option value="api.eu.mailgun.net" {{ old('mailgun_endpoint', $settings['mailgun_endpoint'] ?? '') == 'api.eu.mailgun.net' ? 'selected' : '' }}>
                                            EU Region (api.eu.mailgun.net)
                                        </option>
                                    </select>
                                    @error('mailgun_endpoint')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Choose the region where your Mailgun domain is hosted</small>
                                </div>
                            </div>

                            <!-- Amazon SES Settings -->
                            <div id="ses_settings" style="{{ old('mail_mailer', $settings['mail_mailer']) == 'ses' ? '' : 'display: none;' }}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="aws_access_key_id">
                                                AWS Access Key ID <span class="text-danger">*</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="ses-access-key-help-btn" title="AWS access key help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="text" name="aws_access_key_id" id="aws_access_key_id"
                                                   class="form-control @error('aws_access_key_id') is-invalid @enderror"
                                                   value="{{ old('aws_access_key_id', $settings['aws_access_key_id'] ?? '') }}"
                                                   placeholder="AKIAIOSFODNN7EXAMPLE">
                                            @error('aws_access_key_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Your AWS IAM access key ID</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="aws_secret_access_key">
                                                AWS Secret Access Key <span class="text-danger">*</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="ses-secret-key-help-btn" title="AWS secret key help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="password" name="aws_secret_access_key" id="aws_secret_access_key"
                                                   class="form-control @error('aws_secret_access_key') is-invalid @enderror"
                                                   value="{{ old('aws_secret_access_key', $settings['aws_secret_access_key'] ?? '') }}"
                                                   placeholder="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY">
                                            @error('aws_secret_access_key')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Your AWS IAM secret access key</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="aws_default_region">
                                                AWS Region <span class="text-danger">*</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="ses-region-help-btn" title="AWS region help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <select name="aws_default_region" id="aws_default_region" class="form-control @error('aws_default_region') is-invalid @enderror">
                                                <option value="us-east-1" {{ old('aws_default_region', $settings['aws_default_region'] ?? 'us-east-1') == 'us-east-1' ? 'selected' : '' }}>US East (N. Virginia) - us-east-1</option>
                                                <option value="us-west-2" {{ old('aws_default_region', $settings['aws_default_region'] ?? '') == 'us-west-2' ? 'selected' : '' }}>US West (Oregon) - us-west-2</option>
                                                <option value="eu-west-1" {{ old('aws_default_region', $settings['aws_default_region'] ?? '') == 'eu-west-1' ? 'selected' : '' }}>Europe (Ireland) - eu-west-1</option>
                                                <option value="eu-central-1" {{ old('aws_default_region', $settings['aws_default_region'] ?? '') == 'eu-central-1' ? 'selected' : '' }}>Europe (Frankfurt) - eu-central-1</option>
                                                <option value="ap-southeast-1" {{ old('aws_default_region', $settings['aws_default_region'] ?? '') == 'ap-southeast-1' ? 'selected' : '' }}>Asia Pacific (Singapore) - ap-southeast-1</option>
                                                <option value="ap-southeast-2" {{ old('aws_default_region', $settings['aws_default_region'] ?? '') == 'ap-southeast-2' ? 'selected' : '' }}>Asia Pacific (Sydney) - ap-southeast-2</option>
                                                <option value="ap-northeast-1" {{ old('aws_default_region', $settings['aws_default_region'] ?? '') == 'ap-northeast-1' ? 'selected' : '' }}>Asia Pacific (Tokyo) - ap-northeast-1</option>
                                            </select>
                                            @error('aws_default_region')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">AWS region where your SES is configured</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="ses_configuration_set">
                                                Configuration Set (Optional)
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="ses-config-set-help-btn" title="SES configuration set help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="text" name="ses_configuration_set" id="ses_configuration_set"
                                                   class="form-control @error('ses_configuration_set') is-invalid @enderror"
                                                   value="{{ old('ses_configuration_set', $settings['ses_configuration_set'] ?? '') }}"
                                                   placeholder="my-configuration-set">
                                            @error('ses_configuration_set')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Optional SES configuration set for tracking</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Postmark Settings -->
                            <div id="postmark_settings" style="{{ old('mail_mailer', $settings['mail_mailer']) == 'postmark' ? '' : 'display: none;' }}">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="postmark_token">
                                                Server API Token <span class="text-danger">*</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="postmark-token-help-btn" title="Postmark token help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="password" name="postmark_token" id="postmark_token"
                                                   class="form-control @error('postmark_token') is-invalid @enderror"
                                                   value="{{ old('postmark_token', $settings['postmark_token'] ?? '') }}"
                                                   placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                                            @error('postmark_token')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Your Postmark server API token</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="postmark_message_stream_id">
                                                Message Stream ID (Optional)
                                                <button type="button" class="btn btn-sm btn-outline-secondary ml-1" id="postmark-stream-help-btn" title="Postmark message stream help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </label>
                                            <input type="text" name="postmark_message_stream_id" id="postmark_message_stream_id"
                                                   class="form-control @error('postmark_message_stream_id') is-invalid @enderror"
                                                   value="{{ old('postmark_message_stream_id', $settings['postmark_message_stream_id'] ?? '') }}"
                                                   placeholder="outbound">
                                            @error('postmark_message_stream_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Default: outbound</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- From Address Settings -->
                            <h5>From Address Settings</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="mail_from_address">From Email Address <span class="text-danger">*</span></label>
                                        <input type="email" name="mail_from_address" id="mail_from_address"
                                               class="form-control @error('mail_from_address') is-invalid @enderror"
                                               value="{{ old('mail_from_address', $settings['mail_from_address']) }}"
                                               placeholder="<EMAIL>">
                                        @error('mail_from_address')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Email address that emails will be sent from</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="mail_from_name">From Name <span class="text-danger">*</span></label>
                                        <input type="text" name="mail_from_name" id="mail_from_name"
                                               class="form-control @error('mail_from_name') is-invalid @enderror"
                                               value="{{ old('mail_from_name', $settings['mail_from_name']) }}"
                                               placeholder="Your Business Name">
                                        @error('mail_from_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Name that will appear as the sender</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Email Settings
                        </button>
                        <button type="button" class="btn btn-info" id="test-email-btn"
                                {{ !old('mail_enabled', $settings['mail_enabled']) ? 'disabled' : '' }}>
                            <i class="fas fa-paper-plane mr-2"></i>
                            Send Test Email
                        </button>
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Settings
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Settings Summary -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Current Email Settings
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <span class="info-box-icon bg-{{ $settings['mail_enabled'] ? 'success' : 'secondary' }}">
                            <i class="fas fa-{{ $settings['mail_enabled'] ? 'check' : 'times' }}"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Email Status</span>
                            <span class="info-box-number">
                                {{ $settings['mail_enabled'] ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                    </div>

                    @if($settings['mail_enabled'])
                        <div class="info-box">
                            <span class="info-box-icon bg-info">
                                <i class="fas fa-cog"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Mail Driver</span>
                                <span class="info-box-number">
                                    {{ strtoupper($settings['mail_mailer']) }}
                                </span>
                            </div>
                        </div>

                        @if($settings['mail_mailer'] == 'smtp')
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-server"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">SMTP Server</span>
                                    <span class="info-box-number">
                                        {{ $settings['mail_host'] ?: 'Not Set' }}:{{ $settings['mail_port'] }}
                                    </span>
                                </div>
                            </div>
                        @elseif($settings['mail_mailer'] == 'mailgun')
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Mailgun Domain</span>
                                    <span class="info-box-number">
                                        {{ $settings['mailgun_domain'] ?: 'Not Set' }}
                                    </span>
                                </div>
                            </div>
                            <div class="info-box">
                                <span class="info-box-icon bg-secondary">
                                    <i class="fas fa-globe"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Region</span>
                                    <span class="info-box-number">
                                        {{ $settings['mailgun_endpoint'] == 'api.eu.mailgun.net' ? 'EU' : 'US' }}
                                    </span>
                                </div>
                            </div>
                        @elseif($settings['mail_mailer'] == 'ses')
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fab fa-aws"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">AWS Region</span>
                                    <span class="info-box-number">
                                        {{ $settings['aws_default_region'] ?: 'Not Set' }}
                                    </span>
                                </div>
                            </div>
                            @if($settings['ses_configuration_set'])
                                <div class="info-box">
                                    <span class="info-box-icon bg-secondary">
                                        <i class="fas fa-cogs"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Configuration Set</span>
                                        <span class="info-box-number">
                                            {{ $settings['ses_configuration_set'] }}
                                        </span>
                                    </div>
                                </div>
                            @endif
                        @elseif($settings['mail_mailer'] == 'postmark')
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-mail-bulk"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Postmark Token</span>
                                    <span class="info-box-number">
                                        {{ $settings['postmark_token'] ? 'Configured' : 'Not Set' }}
                                    </span>
                                </div>
                            </div>
                            @if($settings['postmark_message_stream_id'])
                                <div class="info-box">
                                    <span class="info-box-icon bg-secondary">
                                        <i class="fas fa-stream"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Message Stream</span>
                                        <span class="info-box-number">
                                            {{ $settings['postmark_message_stream_id'] }}
                                        </span>
                                    </div>
                                </div>
                            @endif
                        @endif

                        <div class="info-box">
                            <span class="info-box-icon bg-primary">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">From Address</span>
                                <span class="info-box-number">
                                    {{ $settings['mail_from_address'] ?: 'Not Set' }}
                                </span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Help -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-question-circle mr-2"></i>
                        Quick Help
                    </h3>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Click the <i class="fas fa-question-circle text-info"></i> buttons next to each field for detailed setup instructions.</p>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb mr-2"></i>Need Help?</h6>
                        <ul class="mb-0 small">
                            <li>Use the "Setup Tutorial" button to see complete email provider setup guides</li>
                            <li>Click help icons next to fields for specific instructions</li>
                            <li>All tutorials include step-by-step screenshots and links</li>
                        </ul>
                    </div>

                    <div class="text-center">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="general-email-help-btn">
                            <i class="fas fa-book mr-2"></i>
                            View Complete Setup Guide
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Provider Tutorial Modals -->
@include('admin.settings.email.modals.smtp-tutorial')
@include('admin.settings.email.modals.gmail-tutorial')
@include('admin.settings.email.modals.outlook-tutorial')
@include('admin.settings.email.modals.mailgun-tutorial')
@include('admin.settings.email.modals.ses-tutorial')
@include('admin.settings.email.modals.postmark-tutorial')
@include('admin.settings.email.modals.general-help')

<!-- Test Email Modal -->
<div class="modal fade" id="testEmailModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Test Email</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="test-email-form">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="test_email">Test Email Address</label>
                        <input type="email" name="test_email" id="test_email"
                               class="form-control" required
                               placeholder="Enter email address to send test to">
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        This will send a test email to verify your SMTP configuration is working correctly.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Test Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle email configuration visibility
    $('#mail_enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#email_configuration').show();
            $('#test-email-btn').prop('disabled', false);
        } else {
            $('#email_configuration').hide();
            $('#test-email-btn').prop('disabled', true);
        }
    });

    // Show/hide provider settings based on mail driver
    $('#mail_mailer').change(function() {
        var provider = $(this).val();

        // Hide all provider settings
        $('#smtp_settings, #mailgun_settings, #ses_settings, #postmark_settings').hide();

        // Show appropriate settings
        if (provider === 'smtp') {
            $('#smtp_settings').show();
        } else if (provider === 'mailgun') {
            $('#mailgun_settings').show();
        } else if (provider === 'ses') {
            $('#ses_settings').show();
        } else if (provider === 'postmark') {
            $('#postmark_settings').show();
        }
    });

    // Business selection change
    $('#business_id').change(function() {
        if ($(this).val()) {
            window.location.href = '{{ route("admin.settings.email") }}?business_id=' + $(this).val();
        } else {
            window.location.href = '{{ route("admin.settings.email") }}';
        }
    });

    // Modal handlers
    $('#provider-help-btn').click(function() {
        var provider = $('#mail_mailer').val();
        if (provider === 'smtp') {
            $('#smtpTutorialModal').modal('show');
        } else if (provider === 'mailgun') {
            $('#mailgunTutorialModal').modal('show');
        } else if (provider === 'ses') {
            $('#sesTutorialModal').modal('show');
        } else if (provider === 'postmark') {
            $('#postmarkTutorialModal').modal('show');
        } else {
            $('#generalEmailHelpModal').modal('show');
        }
    });

    $('#general-email-help-btn').click(function() {
        $('#generalEmailHelpModal').modal('show');
    });

    // Field-specific help handlers
    $('#smtp-host-help-btn').click(function() {
        var host = $('#mail_host').val().toLowerCase();
        if (host.includes('gmail')) {
            $('#gmailTutorialModal').modal('show');
        } else if (host.includes('outlook') || host.includes('hotmail')) {
            $('#outlookTutorialModal').modal('show');
        } else {
            $('#smtpTutorialModal').modal('show');
        }
    });

    $('#smtp-port-help-btn, #smtp-username-help-btn, #smtp-password-help-btn').click(function() {
        var host = $('#mail_host').val().toLowerCase();
        if (host.includes('gmail')) {
            $('#gmailTutorialModal').modal('show');
        } else if (host.includes('outlook') || host.includes('hotmail')) {
            $('#outlookTutorialModal').modal('show');
        } else {
            $('#smtpTutorialModal').modal('show');
        }
    });

    // Provider-specific help handlers
    $('#mailgun-domain-help-btn, #mailgun-secret-help-btn').click(function() {
        $('#mailgunTutorialModal').modal('show');
    });

    $('#ses-access-key-help-btn, #ses-secret-key-help-btn, #ses-region-help-btn, #ses-config-set-help-btn').click(function() {
        $('#sesTutorialModal').modal('show');
    });

    $('#postmark-token-help-btn, #postmark-stream-help-btn').click(function() {
        $('#postmarkTutorialModal').modal('show');
    });

    // Test email button
    $('#test-email-btn').click(function() {
        $('#testEmailModal').modal('show');
    });

    // Test email form submission
    $('#test-email-form').submit(function(e) {
        e.preventDefault();

        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.html();

        // Disable submit button and show loading
        $submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Sending...');

        $.ajax({
            url: '{{ route("admin.settings.email.test") }}',
            method: 'POST',
            data: $form.serialize(),
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    $('#testEmailModal').modal('hide');
                    $form[0].reset();
                } else {
                    toastr.error(response.message || 'Failed to send test email');
                }
            },
            error: function(xhr) {
                var message = 'Failed to send test email';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                toastr.error(message);
            },
            complete: function() {
                // Re-enable submit button
                $submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Auto-fill common SMTP settings
    $('#mail_host').on('input', function() {
        var host = $(this).val().toLowerCase();

        if (host.includes('gmail')) {
            $('#mail_port').val(587);
            $('#mail_encryption').val('tls');
        } else if (host.includes('outlook') || host.includes('hotmail')) {
            $('#mail_port').val(587);
            $('#mail_encryption').val('tls');
        } else if (host.includes('yahoo')) {
            $('#mail_port').val(587);
            $('#mail_encryption').val('tls');
        }
    });

    // Form validation
    $('#email-settings-form').on('submit', function(e) {
        var mailEnabled = $('#mail_enabled').is(':checked');
        var mailDriver = $('#mail_mailer').val();
        var fromAddress = $('#mail_from_address').val();
        var fromName = $('#mail_from_name').val();

        // Always require from address and name
        if (!fromAddress || !fromName) {
            e.preventDefault();
            toastr.error('From Address and From Name are required');
            return false;
        }

        if (mailEnabled) {
            // Additional validation for SMTP
            if (mailDriver === 'smtp') {
                var host = $('#mail_host').val();
                var port = $('#mail_port').val();

                if (!host || !port) {
                    e.preventDefault();
                    toastr.error('SMTP Host and Port are required when using SMTP driver');
                    return false;
                }
            }
            // Validation for Mailgun
            else if (mailDriver === 'mailgun') {
                var domain = $('#mailgun_domain').val();
                var secret = $('#mailgun_secret').val();

                if (!domain || !secret) {
                    e.preventDefault();
                    toastr.error('Mailgun Domain and API Key are required when using Mailgun driver');
                    return false;
                }
            }
            // Validation for Amazon SES
            else if (mailDriver === 'ses') {
                var accessKey = $('#aws_access_key_id').val();
                var secretKey = $('#aws_secret_access_key').val();
                var region = $('#aws_default_region').val();

                if (!accessKey || !secretKey || !region) {
                    e.preventDefault();
                    toastr.error('AWS Access Key ID, Secret Access Key, and Region are required when using Amazon SES driver');
                    return false;
                }
            }
            // Validation for Postmark
            else if (mailDriver === 'postmark') {
                var token = $('#postmark_token').val();

                if (!token) {
                    e.preventDefault();
                    toastr.error('Server API Token is required when using Postmark driver');
                    return false;
                }
            }
        }
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});

// Add toastr notifications if not already included
if (typeof toastr === 'undefined') {
    window.toastr = {
        success: function(message) { alert('Success: ' + message); },
        error: function(message) { alert('Error: ' + message); }
    };
}
</script>
@stop
