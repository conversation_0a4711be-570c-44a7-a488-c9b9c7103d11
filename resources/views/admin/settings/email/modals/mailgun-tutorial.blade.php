<!-- Mailgun Setup Tutorial Modal -->
<div class="modal fade" id="mailgunTutorialModal" tabindex="-1" role="dialog" aria-labelledby="mailgunTutorialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="mailgunTutorialModalLabel">
                    <i class="fas fa-envelope mr-2"></i>
                    Mailgun Setup Tutorial
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Introduction -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>About Mailgun</h6>
                    <p class="mb-0">Mailgun is a powerful email service designed for developers. It offers excellent deliverability, detailed analytics, and robust APIs. Perfect for applications that need reliable transactional email delivery.</p>
                </div>

                <!-- Step 1: Create Account -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-plus mr-2"></i>Step 1: Create Mailgun Account</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Visit <a href="https://signup.mailgun.com/new/signup" target="_blank" class="text-primary">Mailgun Signup</a></li>
                            <li>Fill out the registration form</li>
                            <li>Verify your email address</li>
                            <li>Complete phone verification</li>
                            <li>Add payment method (required even for free tier)</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-gift mr-1"></i><strong>Free Tier:</strong> 5,000 emails per month for the first 3 months, then pay-as-you-go.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Add Domain -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-globe mr-2"></i>Step 2: Add and Verify Your Domain</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>In your Mailgun dashboard, go to "Domains"</li>
                            <li>Click "Add New Domain"</li>
                            <li>Enter your domain (e.g., yourdomain.com)</li>
                            <li>Choose your region (US or EU)</li>
                            <li>Add the required DNS records to your domain:</li>
                            <ul>
                                <li><strong>TXT record:</strong> For domain verification</li>
                                <li><strong>MX records:</strong> For receiving emails</li>
                                <li><strong>CNAME records:</strong> For tracking and unsubscribes</li>
                            </ul>
                            <li>Wait for DNS propagation (can take up to 48 hours)</li>
                            <li>Click "Verify DNS Settings" in Mailgun dashboard</li>
                        </ol>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-clock mr-1"></i><strong>Note:</strong> You can use Mailgun's sandbox domain for testing, but it has limitations.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Get API Credentials -->
                <div class="card mb-3" id="mailgun-api-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key mr-2"></i>Step 3: Get API Credentials</h6>
                    </div>
                    <div class="card-body">
                        <h6>For API Integration (Recommended):</h6>
                        <ol>
                            <li>Go to "Settings" → "API Keys" in your Mailgun dashboard</li>
                            <li>Copy your "Private API Key"</li>
                            <li>Note your domain name from the Domains section</li>
                        </ol>

                        <h6>For SMTP Integration:</h6>
                        <ol>
                            <li>Go to your domain settings in Mailgun</li>
                            <li>Find the "SMTP" section</li>
                            <li>Note the SMTP credentials provided</li>
                        </ol>

                        <div class="table-responsive mt-3">
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>SMTP Host:</strong></td>
                                    <td><code>smtp.mailgun.org</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Port:</strong></td>
                                    <td><code>587</code> (TLS) or <code>465</code> (SSL)</td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td><EMAIL></td>
                                </tr>
                                <tr>
                                    <td><strong>Password:</strong></td>
                                    <td>Generated SMTP password from Mailgun</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Configure Application -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cog mr-2"></i>Step 4: Configure Your Application</h6>
                    </div>
                    <div class="card-body">
                        <p>Choose your integration method:</p>
                        
                        <h6><i class="fas fa-plug mr-2"></i>API Integration (Recommended):</h6>
                        <ul>
                            <li>Select "Mailgun" as your mail driver</li>
                            <li>Enter your Mailgun domain</li>
                            <li>Enter your private API key</li>
                            <li>Set your from address (must be from verified domain)</li>
                        </ul>

                        <h6><i class="fas fa-server mr-2"></i>SMTP Integration:</h6>
                        <ul>
                            <li>Select "SMTP" as your mail driver</li>
                            <li>Use the SMTP settings from Step 3</li>
                            <li>Set encryption to TLS</li>
                        </ul>

                        <div class="alert alert-info">
                            <small><i class="fas fa-lightbulb mr-1"></i><strong>Recommendation:</strong> Use API integration for better performance and features.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Testing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-vial mr-2"></i>Step 5: Test Your Setup</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Save your email settings in the booking system</li>
                            <li>Send a test email using the "Send Test Email" button</li>
                            <li>Check the Mailgun dashboard for delivery logs</li>
                            <li>Verify the email arrives in your inbox</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-check-circle mr-1"></i><strong>Success:</strong> Check Mailgun's logs for detailed delivery information!</small>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-star mr-2"></i>Mailgun Features</h6>
                    </div>
                    <div class="card-body">
                        <h6>Analytics & Tracking:</h6>
                        <ul>
                            <li>Detailed delivery reports</li>
                            <li>Open and click tracking</li>
                            <li>Bounce and complaint handling</li>
                            <li>Real-time event webhooks</li>
                        </ul>

                        <h6>Deliverability Tools:</h6>
                        <ul>
                            <li>Automatic IP warming</li>
                            <li>Reputation monitoring</li>
                            <li>Suppression list management</li>
                            <li>A/B testing capabilities</li>
                        </ul>

                        <h6>Developer Features:</h6>
                        <ul>
                            <li>RESTful API</li>
                            <li>Email validation API</li>
                            <li>Inbound email processing</li>
                            <li>Multiple programming language SDKs</li>
                        </ul>
                    </div>
                </div>

                <!-- Pricing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-dollar-sign mr-2"></i>Pricing Information</h6>
                    </div>
                    <div class="card-body">
                        <h6>Free Tier:</h6>
                        <ul>
                            <li>5,000 emails per month for first 3 months</li>
                            <li>All core features included</li>
                            <li>Email validation: 100 validations/month</li>
                        </ul>

                        <h6>Pay-as-you-go:</h6>
                        <ul>
                            <li>$0.80 per 1,000 emails</li>
                            <li>No monthly minimums</li>
                            <li>Volume discounts available</li>
                        </ul>

                        <div class="alert alert-info">
                            <small><i class="fas fa-calculator mr-1"></i><strong>Cost Example:</strong> 10,000 emails/month = ~$8/month</small>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Troubleshooting</h6>
                    </div>
                    <div class="card-body">
                        <h6>Common Issues:</h6>
                        <ul>
                            <li><strong>Domain not verified:</strong> Check DNS records and wait for propagation</li>
                            <li><strong>API authentication failed:</strong> Verify your API key and domain</li>
                            <li><strong>Emails not delivered:</strong> Check Mailgun logs for detailed error messages</li>
                            <li><strong>High bounce rate:</strong> Verify email addresses and list quality</li>
                        </ul>

                        <h6>Best Practices:</h6>
                        <ul>
                            <li>Always verify your sending domain</li>
                            <li>Monitor your sender reputation</li>
                            <li>Use suppression lists to avoid bounces</li>
                            <li>Implement proper unsubscribe handling</li>
                        </ul>

                        <div class="mt-3">
                            <a href="https://help.mailgun.com" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>Mailgun Help Center
                            </a>
                            <a href="https://documentation.mailgun.com" target="_blank" class="btn btn-outline-info btn-sm ml-2">
                                <i class="fas fa-book mr-1"></i>API Documentation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a href="https://app.mailgun.com" target="_blank" class="btn btn-warning">
                    <i class="fas fa-external-link-alt mr-2"></i>Open Mailgun Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
