<!-- General SMTP Setup Tutorial Modal -->
<div class="modal fade" id="smtpTutorialModal" tabindex="-1" role="dialog" aria-labelledby="smtpTutorialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="smtpTutorialModalLabel">
                    <i class="fas fa-server mr-2"></i>
                    General SMTP Setup Tutorial
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Introduction -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>About SMTP</h6>
                    <p class="mb-0">SMTP (Simple Mail Transfer Protocol) is the standard protocol for sending emails. This guide covers general SMTP setup for any email provider not specifically covered in other tutorials.</p>
                </div>

                <!-- Step 1: Gather Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info mr-2"></i>Step 1: Gather SMTP Information</h6>
                    </div>
                    <div class="card-body">
                        <p>You'll need the following information from your email provider:</p>
                        <ul>
                            <li><strong>SMTP Server/Host:</strong> The server address (e.g., mail.yourdomain.com)</li>
                            <li><strong>Port:</strong> Usually 587 (TLS), 465 (SSL), or 25 (unencrypted)</li>
                            <li><strong>Encryption:</strong> TLS, SSL, or None</li>
                            <li><strong>Username:</strong> Usually your full email address</li>
                            <li><strong>Password:</strong> Your email account password</li>
                        </ul>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-search mr-1"></i><strong>Where to find:</strong> Check your email provider's documentation or contact their support.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Common SMTP Settings -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list mr-2"></i>Step 2: Common SMTP Settings by Provider</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        <th>Provider</th>
                                        <th>SMTP Host</th>
                                        <th>Port</th>
                                        <th>Encryption</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>Gmail</strong></td>
                                        <td>smtp.gmail.com</td>
                                        <td>587</td>
                                        <td>TLS</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Outlook</strong></td>
                                        <td>smtp-mail.outlook.com</td>
                                        <td>587</td>
                                        <td>TLS</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Yahoo</strong></td>
                                        <td>smtp.mail.yahoo.com</td>
                                        <td>587</td>
                                        <td>TLS</td>
                                    </tr>
                                    <tr>
                                        <td><strong>iCloud</strong></td>
                                        <td>smtp.mail.me.com</td>
                                        <td>587</td>
                                        <td>TLS</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Zoho</strong></td>
                                        <td>smtp.zoho.com</td>
                                        <td>587</td>
                                        <td>TLS</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Configure Settings -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cog mr-2"></i>Step 3: Configure SMTP Settings</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>SMTP Host:</strong> Enter the server address provided by your email provider</li>
                            <li><strong>Port:</strong> Use 587 for TLS (recommended) or 465 for SSL</li>
                            <li><strong>Encryption:</strong> Select TLS (recommended) for better security</li>
                            <li><strong>Username:</strong> Enter your full email address</li>
                            <li><strong>Password:</strong> Enter your email password (or app password if 2FA is enabled)</li>
                        </ol>
                        
                        <div class="alert alert-success">
                            <h6><i class="fas fa-lightbulb mr-2"></i>Pro Tips:</h6>
                            <ul class="mb-0">
                                <li>TLS on port 587 is the most widely supported configuration</li>
                                <li>Avoid port 25 as it's often blocked by ISPs</li>
                                <li>Always use encryption (TLS/SSL) for security</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Authentication -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key mr-2"></i>Step 4: Handle Authentication</h6>
                    </div>
                    <div class="card-body">
                        <h6>Standard Authentication:</h6>
                        <ul>
                            <li>Use your email address as the username</li>
                            <li>Use your regular email password</li>
                        </ul>

                        <h6>Two-Factor Authentication (2FA):</h6>
                        <ul>
                            <li>Create an app-specific password</li>
                            <li>Use the app password instead of your regular password</li>
                            <li>Keep your username as your email address</li>
                        </ul>

                        <h6>Custom Domain/Hosting:</h6>
                        <ul>
                            <li>Username might be just the local part (before @)</li>
                            <li>Check with your hosting provider for specific requirements</li>
                            <li>Some providers use different authentication methods</li>
                        </ul>
                    </div>
                </div>

                <!-- Step 5: Testing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-vial mr-2"></i>Step 5: Test Your Configuration</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Save your SMTP settings in the booking system</li>
                            <li>Use the "Send Test Email" button</li>
                            <li>Check your inbox for the test email</li>
                            <li>Verify the email appears correctly</li>
                        </ol>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-clock mr-2"></i>If test fails:</h6>
                            <ul class="mb-0">
                                <li>Double-check all settings</li>
                                <li>Try different ports (587, 465, 25)</li>
                                <li>Verify your email provider allows SMTP</li>
                                <li>Check firewall and network restrictions</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Security Considerations -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt mr-2"></i>Security Considerations</h6>
                    </div>
                    <div class="card-body">
                        <h6>Encryption:</h6>
                        <ul>
                            <li><strong>TLS (STARTTLS):</strong> Starts unencrypted, then upgrades to encrypted</li>
                            <li><strong>SSL:</strong> Encrypted from the beginning</li>
                            <li><strong>None:</strong> No encryption (not recommended)</li>
                        </ul>

                        <h6>Password Security:</h6>
                        <ul>
                            <li>Use strong, unique passwords</li>
                            <li>Enable 2FA when available</li>
                            <li>Use app passwords for 2FA-enabled accounts</li>
                            <li>Store credentials securely</li>
                        </ul>

                        <div class="alert alert-danger">
                            <small><i class="fas fa-exclamation-triangle mr-1"></i><strong>Warning:</strong> Never use unencrypted SMTP in production environments.</small>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Troubleshooting Common Issues</h6>
                    </div>
                    <div class="card-body">
                        <h6>Connection Issues:</h6>
                        <ul>
                            <li><strong>Connection timeout:</strong> Check host, port, and firewall settings</li>
                            <li><strong>Connection refused:</strong> Verify the SMTP server is correct</li>
                            <li><strong>SSL/TLS errors:</strong> Try different encryption settings</li>
                        </ul>

                        <h6>Authentication Issues:</h6>
                        <ul>
                            <li><strong>Login failed:</strong> Verify username and password</li>
                            <li><strong>Authentication required:</strong> Ensure SMTP auth is enabled</li>
                            <li><strong>App password needed:</strong> Create app-specific password for 2FA accounts</li>
                        </ul>

                        <h6>Delivery Issues:</h6>
                        <ul>
                            <li><strong>Emails not arriving:</strong> Check spam folders and DNS settings</li>
                            <li><strong>Rejected by server:</strong> Verify sender address and domain</li>
                            <li><strong>Rate limiting:</strong> Check sending limits with your provider</li>
                        </ul>

                        <div class="mt-3">
                            <a href="https://www.smtper.net" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>SMTP Tester Tool
                            </a>
                            <a href="https://mxtoolbox.com/diagnostic.aspx" target="_blank" class="btn btn-outline-info btn-sm ml-2">
                                <i class="fas fa-tools mr-1"></i>Email Diagnostics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="$('#generalEmailHelpModal').modal('show'); $('#smtpTutorialModal').modal('hide');">
                    <i class="fas fa-book mr-2"></i>View General Email Guide
                </button>
            </div>
        </div>
    </div>
</div>
