<!-- Amazon SES Setup Tutorial Modal -->
<div class="modal fade" id="sesTutorialModal" tabindex="-1" role="dialog" aria-labelledby="sesTutorialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="sesTutorialModalLabel">
                    <i class="fab fa-aws mr-2"></i>
                    Amazon SES Setup Tutorial
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Introduction -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>About Amazon SES</h6>
                    <p class="mb-0">Amazon Simple Email Service (SES) is a cost-effective, flexible email service. It's perfect for high-volume sending with excellent deliverability and detailed analytics.</p>
                </div>

                <!-- Step 1: AWS Account -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-plus mr-2"></i>Step 1: Create AWS Account</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Visit <a href="https://aws.amazon.com/free" target="_blank" class="text-primary">AWS Free Tier</a></li>
                            <li>Click "Create a Free Account"</li>
                            <li>Fill out the registration form</li>
                            <li>Provide payment method (required even for free tier)</li>
                            <li>Complete phone verification</li>
                            <li>Choose Basic support plan (free)</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-gift mr-1"></i><strong>Free Tier:</strong> 62,000 emails per month when sent from EC2, or $0.10 per 1,000 emails.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Verify Email/Domain -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-check-circle mr-2"></i>Step 2: Verify Email Address or Domain</h6>
                    </div>
                    <div class="card-body">
                        <h6>Option 1: Verify Email Address (Quick Start)</h6>
                        <ol>
                            <li>Go to AWS Console → SES → Verified identities</li>
                            <li>Click "Create identity"</li>
                            <li>Select "Email address"</li>
                            <li>Enter your email address</li>
                            <li>Click "Create identity"</li>
                            <li>Check your email and click the verification link</li>
                        </ol>

                        <h6>Option 2: Verify Domain (Recommended for Production)</h6>
                        <ol>
                            <li>Go to AWS Console → SES → Verified identities</li>
                            <li>Click "Create identity"</li>
                            <li>Select "Domain"</li>
                            <li>Enter your domain name</li>
                            <li>Add the provided DNS records to your domain</li>
                            <li>Wait for verification (can take up to 72 hours)</li>
                        </ol>
                    </div>
                </div>

                <!-- Step 3: Request Production Access -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-unlock mr-2"></i>Step 3: Request Production Access</h6>
                    </div>
                    <div class="card-body">
                        <p>New SES accounts start in sandbox mode with limitations:</p>
                        <ul>
                            <li>Can only send to verified email addresses</li>
                            <li>Limited to 200 emails per 24 hours</li>
                            <li>Maximum 1 email per second</li>
                        </ul>

                        <h6>To request production access:</h6>
                        <ol>
                            <li>Go to SES → Account dashboard</li>
                            <li>Click "Request production access"</li>
                            <li>Fill out the request form:</li>
                            <ul>
                                <li>Mail type: Transactional</li>
                                <li>Website URL: Your booking system URL</li>
                                <li>Use case description: Booking confirmations and notifications</li>
                                <li>Expected sending volume</li>
                            </ul>
                            <li>Submit the request</li>
                            <li>Wait for approval (usually 24-48 hours)</li>
                        </ol>
                    </div>
                </div>

                <!-- Step 4: Create IAM User -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-users-cog mr-2"></i>Step 4: Create IAM User for SES</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Go to AWS Console → IAM → Users</li>
                            <li>Click "Add user"</li>
                            <li>Enter username (e.g., "ses-smtp-user")</li>
                            <li>Select "Programmatic access"</li>
                            <li>Click "Next: Permissions"</li>
                            <li>Click "Attach existing policies directly"</li>
                            <li>Search and select "AmazonSESFullAccess"</li>
                            <li>Complete user creation</li>
                            <li>Save the Access Key ID and Secret Access Key</li>
                        </ol>
                        <div class="alert alert-danger">
                            <small><i class="fas fa-shield-alt mr-1"></i><strong>Security:</strong> Never use your root AWS credentials for applications!</small>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Get SMTP Credentials -->
                <div class="card mb-3" id="ses-smtp-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key mr-2"></i>Step 5: Get SMTP Credentials</h6>
                    </div>
                    <div class="card-body">
                        <h6>Option 1: Generate SMTP Credentials</h6>
                        <ol>
                            <li>Go to SES → Account dashboard → SMTP settings</li>
                            <li>Click "Create SMTP credentials"</li>
                            <li>Enter IAM user name (e.g., "ses-smtp-user")</li>
                            <li>Click "Create"</li>
                            <li>Download and save the SMTP username and password</li>
                        </ol>

                        <h6>Option 2: Convert IAM Credentials</h6>
                        <p>You can convert your IAM Access Key to SMTP credentials using AWS documentation.</p>

                        <div class="table-responsive mt-3">
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>SMTP Host:</strong></td>
                                    <td><code>email-smtp.us-east-1.amazonaws.com</code> (adjust region)</td>
                                </tr>
                                <tr>
                                    <td><strong>Port:</strong></td>
                                    <td><code>587</code> (TLS) or <code>465</code> (SSL)</td>
                                </tr>
                                <tr>
                                    <td><strong>Encryption:</strong></td>
                                    <td><code>TLS</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td>SMTP username from SES</td>
                                </tr>
                                <tr>
                                    <td><strong>Password:</strong></td>
                                    <td>SMTP password from SES</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Step 6: Configure Application -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cog mr-2"></i>Step 6: Configure Your Application</h6>
                    </div>
                    <div class="card-body">
                        <h6>For SES API Integration:</h6>
                        <ul>
                            <li>Select "Amazon SES" as mail driver</li>
                            <li>Enter your AWS Access Key ID</li>
                            <li>Enter your AWS Secret Access Key</li>
                            <li>Set the correct AWS region</li>
                        </ul>

                        <h6>For SMTP Integration:</h6>
                        <ul>
                            <li>Select "SMTP" as mail driver</li>
                            <li>Use the SMTP settings from Step 5</li>
                            <li>Ensure the region matches your SES setup</li>
                        </ul>

                        <div class="alert alert-info">
                            <small><i class="fas fa-map-marker-alt mr-1"></i><strong>Regions:</strong> Choose a region close to your users for better performance.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 7: Testing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-vial mr-2"></i>Step 7: Test Your Setup</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Save your email settings</li>
                            <li>Send a test email</li>
                            <li>Check SES dashboard for sending statistics</li>
                            <li>Verify email delivery</li>
                        </ol>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-exclamation-triangle mr-1"></i><strong>Sandbox Mode:</strong> You can only send to verified email addresses until production access is approved.</small>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-star mr-2"></i>SES Features</h6>
                    </div>
                    <div class="card-body">
                        <h6>Analytics & Monitoring:</h6>
                        <ul>
                            <li>Detailed sending statistics</li>
                            <li>Bounce and complaint tracking</li>
                            <li>CloudWatch integration</li>
                            <li>Real-time event publishing</li>
                        </ul>

                        <h6>Deliverability Tools:</h6>
                        <ul>
                            <li>Dedicated IP addresses</li>
                            <li>Reputation tracking</li>
                            <li>Suppression list management</li>
                            <li>Configuration sets</li>
                        </ul>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Troubleshooting</h6>
                    </div>
                    <div class="card-body">
                        <h6>Common Issues:</h6>
                        <ul>
                            <li><strong>Access denied:</strong> Check IAM permissions and credentials</li>
                            <li><strong>Email not verified:</strong> Verify sender email/domain in SES</li>
                            <li><strong>Sandbox limitations:</strong> Request production access</li>
                            <li><strong>Region mismatch:</strong> Ensure all services use the same region</li>
                        </ul>

                        <h6>Best Practices:</h6>
                        <ul>
                            <li>Always verify your sending domain</li>
                            <li>Monitor bounce and complaint rates</li>
                            <li>Use configuration sets for tracking</li>
                            <li>Implement proper error handling</li>
                        </ul>

                        <div class="mt-3">
                            <a href="https://docs.aws.amazon.com/ses/" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>SES Documentation
                            </a>
                            <a href="https://aws.amazon.com/support/" target="_blank" class="btn btn-outline-info btn-sm ml-2">
                                <i class="fas fa-life-ring mr-1"></i>AWS Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a href="https://console.aws.amazon.com/ses" target="_blank" class="btn btn-warning">
                    <i class="fas fa-external-link-alt mr-2"></i>Open SES Console
                </a>
            </div>
        </div>
    </div>
</div>
