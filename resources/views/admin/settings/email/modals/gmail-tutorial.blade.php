<!-- Gmail SMTP Setup Tutorial Modal -->
<div class="modal fade" id="gmailTutorialModal" tabindex="-1" role="dialog" aria-labelledby="gmailTutorialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="gmailTutorialModalLabel">
                    <i class="fab fa-google mr-2"></i>
                    Gmail SMTP Setup Tutorial
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Introduction -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>About Gmail SMTP</h6>
                    <p class="mb-0">Gmail SMTP is perfect for small businesses and testing. It's free, reliable, and easy to set up. However, it has daily sending limits and requires app passwords for security.</p>
                </div>

                <!-- Step 1: Enable 2FA -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt mr-2"></i>Step 1: Enable 2-Factor Authentication</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Go to your <a href="https://myaccount.google.com" target="_blank" class="text-primary">Google Account</a></li>
                            <li>Click on "Security" in the left sidebar</li>
                            <li>Under "Signing in to Google", click "2-Step Verification"</li>
                            <li>Follow the setup process to enable 2FA</li>
                            <li>You can use SMS, authenticator app, or backup codes</li>
                        </ol>
                        <div class="alert alert-warning">
                            <small><i class="fas fa-exclamation-triangle mr-1"></i><strong>Required:</strong> 2FA must be enabled to create app passwords for SMTP access.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Create App Password -->
                <div class="card mb-3" id="gmail-app-password-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key mr-2"></i>Step 2: Create App Password</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>In your Google Account Security page, find "App passwords"</li>
                            <li>Click "App passwords" (you may need to sign in again)</li>
                            <li>Select "Mail" from the app dropdown</li>
                            <li>Select "Other (custom name)" from the device dropdown</li>
                            <li>Enter a name like "Booking System" or "SMTP"</li>
                            <li>Click "Generate"</li>
                            <li>Copy the 16-character password that appears</li>
                        </ol>
                        <div class="alert alert-danger">
                            <small><i class="fas fa-shield-alt mr-1"></i><strong>Important:</strong> Save this password immediately! You won't be able to see it again.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 3: SMTP Settings -->
                <div class="card mb-3" id="gmail-smtp-settings-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cog mr-2"></i>Step 3: Configure SMTP Settings</h6>
                    </div>
                    <div class="card-body">
                        <p>Use these settings in your email configuration:</p>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>SMTP Host:</strong></td>
                                    <td><code>smtp.gmail.com</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Port:</strong></td>
                                    <td><code>587</code> (TLS) or <code>465</code> (SSL)</td>
                                </tr>
                                <tr>
                                    <td><strong>Encryption:</strong></td>
                                    <td><code>TLS</code> (recommended)</td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td>Your full Gmail address (e.g., <EMAIL>)</td>
                                </tr>
                                <tr>
                                    <td><strong>Password:</strong></td>
                                    <td>The 16-character app password you generated</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Step 4: From Address -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-envelope mr-2"></i>Step 4: Configure From Address</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>From Email Address:</strong> Use your Gmail address or a custom domain email</li>
                            <li><strong>From Name:</strong> Your business name (e.g., "BookingApp Notifications")</li>
                        </ol>
                        <div class="alert alert-info">
                            <small><i class="fas fa-info-circle mr-1"></i><strong>Custom Domain:</strong> You can send from a custom domain if you've verified it in Gmail settings.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Testing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-vial mr-2"></i>Step 5: Test Your Configuration</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Save your email settings in the booking system</li>
                            <li>Use the "Send Test Email" button to verify configuration</li>
                            <li>Check that the test email arrives in your inbox</li>
                            <li>Verify the sender name and address are correct</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-check-circle mr-1"></i><strong>Success:</strong> If you receive the test email, your Gmail SMTP is working correctly!</small>
                        </div>
                    </div>
                </div>

                <!-- Limitations -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle mr-2"></i>Gmail SMTP Limitations</h6>
                    </div>
                    <div class="card-body">
                        <h6>Sending Limits:</h6>
                        <ul>
                            <li><strong>Daily Limit:</strong> 500 emails per day for free accounts</li>
                            <li><strong>Rate Limit:</strong> Maximum 100 recipients per message</li>
                            <li><strong>Bulk Sending:</strong> Not recommended for marketing emails</li>
                        </ul>

                        <h6>Best Use Cases:</h6>
                        <ul>
                            <li>Transactional emails (booking confirmations, reminders)</li>
                            <li>Small businesses with low email volume</li>
                            <li>Testing and development environments</li>
                        </ul>

                        <div class="alert alert-warning">
                            <small><i class="fas fa-lightbulb mr-1"></i><strong>Tip:</strong> For higher volumes, consider upgrading to Google Workspace or using a dedicated email service.</small>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Troubleshooting</h6>
                    </div>
                    <div class="card-body">
                        <h6>Common Issues:</h6>
                        <ul>
                            <li><strong>"Username and Password not accepted":</strong> Make sure you're using an app password, not your regular Gmail password</li>
                            <li><strong>"Less secure app access":</strong> This is disabled by default. Use app passwords instead</li>
                            <li><strong>"Connection timeout":</strong> Check your firewall and ensure port 587 or 465 is open</li>
                            <li><strong>"Daily sending quota exceeded":</strong> Wait 24 hours or upgrade to Google Workspace</li>
                        </ul>

                        <h6>Security Tips:</h6>
                        <ul>
                            <li>Regularly review and revoke unused app passwords</li>
                            <li>Use unique app passwords for each application</li>
                            <li>Monitor your Google Account activity</li>
                            <li>Enable security alerts for suspicious activity</li>
                        </ul>

                        <div class="mt-3">
                            <a href="https://support.google.com/accounts/answer/185833" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>App Passwords Help
                            </a>
                            <a href="https://support.google.com/mail/answer/7126229" target="_blank" class="btn btn-outline-info btn-sm ml-2">
                                <i class="fas fa-book mr-1"></i>SMTP Documentation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a href="https://myaccount.google.com/security" target="_blank" class="btn btn-danger">
                    <i class="fas fa-external-link-alt mr-2"></i>Open Google Account Security
                </a>
            </div>
        </div>
    </div>
</div>
