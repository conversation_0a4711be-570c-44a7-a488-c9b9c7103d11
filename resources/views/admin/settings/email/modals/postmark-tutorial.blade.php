<!-- Postmark Setup Tutorial Modal -->
<div class="modal fade" id="postmarkTutorialModal" tabindex="-1" role="dialog" aria-labelledby="postmarkTutorialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="postmarkTutorialModalLabel">
                    <i class="fas fa-mail-bulk mr-2"></i>
                    Postmark Setup Tutorial
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <!-- Introduction -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>About Postmark</h6>
                    <p class="mb-0">Postmark specializes in transactional email delivery with excellent deliverability rates, fast delivery times, and detailed analytics. Perfect for booking confirmations, receipts, and notifications.</p>
                </div>

                <!-- Step 1: Create Account -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-plus mr-2"></i>Step 1: Create Postmark Account</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Visit <a href="https://postmarkapp.com/sign_up" target="_blank" class="text-primary">Postmark Signup</a></li>
                            <li>Fill out the registration form</li>
                            <li>Verify your email address</li>
                            <li>Complete the onboarding process</li>
                            <li>Add payment method (required for sending)</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-gift mr-1"></i><strong>Free Trial:</strong> 100 emails per month forever, plus 30-day trial with higher limits.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Create Server -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-server mr-2"></i>Step 2: Create a Server</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>In your Postmark dashboard, click "Create a Server"</li>
                            <li>Choose "Transactional" as the server type</li>
                            <li>Enter a server name (e.g., "Booking System")</li>
                            <li>Add a description</li>
                            <li>Click "Create Server"</li>
                        </ol>
                        <div class="alert alert-info">
                            <small><i class="fas fa-info-circle mr-1"></i><strong>Servers:</strong> Postmark uses servers to organize different types of email sending.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Add Sender Signature -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-signature mr-2"></i>Step 3: Add Sender Signature</h6>
                    </div>
                    <div class="card-body">
                        <h6>Option 1: Single Email Address</h6>
                        <ol>
                            <li>Go to "Sender Signatures" in your server</li>
                            <li>Click "Add Sender Signature"</li>
                            <li>Enter your email address</li>
                            <li>Click "Add Sender Signature"</li>
                            <li>Check your email and click the confirmation link</li>
                        </ol>

                        <h6>Option 2: Entire Domain (Recommended)</h6>
                        <ol>
                            <li>Go to "Sender Signatures" in your server</li>
                            <li>Click "Add Domain"</li>
                            <li>Enter your domain name</li>
                            <li>Add the required DNS records:</li>
                            <ul>
                                <li><strong>DKIM record:</strong> For authentication</li>
                                <li><strong>Return-Path record:</strong> For bounce handling</li>
                            </ul>
                            <li>Wait for DNS propagation and verification</li>
                        </ol>
                    </div>
                </div>

                <!-- Step 4: Get API Token -->
                <div class="card mb-3" id="postmark-api-section">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-key mr-2"></i>Step 4: Get Server API Token</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>In your server dashboard, go to "API Tokens"</li>
                            <li>Copy the "Server API token"</li>
                            <li>Keep this token secure - you'll need it for configuration</li>
                        </ol>

                        <div class="alert alert-warning">
                            <small><i class="fas fa-shield-alt mr-1"></i><strong>Security:</strong> Never share your API token publicly or commit it to version control.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Configure Application -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cog mr-2"></i>Step 5: Configure Your Application</h6>
                    </div>
                    <div class="card-body">
                        <h6>For Postmark API Integration (Recommended):</h6>
                        <ul>
                            <li>Select "Postmark" as your mail driver</li>
                            <li>Enter your Server API token</li>
                            <li>Set your from address (must be verified)</li>
                            <li>Set your from name</li>
                        </ul>

                        <h6>For SMTP Integration:</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>SMTP Host:</strong></td>
                                    <td><code>smtp.postmarkapp.com</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Port:</strong></td>
                                    <td><code>587</code> (TLS) or <code>25</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td>Your Server API token</td>
                                </tr>
                                <tr>
                                    <td><strong>Password:</strong></td>
                                    <td>Your Server API token</td>
                                </tr>
                                <tr>
                                    <td><strong>Encryption:</strong></td>
                                    <td><code>TLS</code></td>
                                </tr>
                            </table>
                        </div>

                        <div class="alert alert-info">
                            <small><i class="fas fa-lightbulb mr-1"></i><strong>Note:</strong> For SMTP, use your API token as both username and password.</small>
                        </div>
                    </div>
                </div>

                <!-- Step 6: Testing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-vial mr-2"></i>Step 6: Test Your Setup</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Save your email settings in the booking system</li>
                            <li>Send a test email using the "Send Test Email" button</li>
                            <li>Check the Postmark dashboard for delivery statistics</li>
                            <li>Verify the email arrives in your inbox</li>
                        </ol>
                        <div class="alert alert-success">
                            <small><i class="fas fa-check-circle mr-1"></i><strong>Success:</strong> Postmark provides detailed delivery tracking and analytics!</small>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-star mr-2"></i>Postmark Features</h6>
                    </div>
                    <div class="card-body">
                        <h6>Deliverability:</h6>
                        <ul>
                            <li>Industry-leading delivery rates (99%+)</li>
                            <li>Fast delivery times (typically under 10 seconds)</li>
                            <li>Dedicated IP addresses available</li>
                            <li>Automatic bounce and spam complaint handling</li>
                        </ul>

                        <h6>Analytics & Tracking:</h6>
                        <ul>
                            <li>Real-time delivery tracking</li>
                            <li>Open and click tracking</li>
                            <li>Detailed bounce analysis</li>
                            <li>Email activity timeline</li>
                        </ul>

                        <h6>Developer Tools:</h6>
                        <ul>
                            <li>RESTful API</li>
                            <li>Webhooks for real-time events</li>
                            <li>Email templates</li>
                            <li>Inbound email processing</li>
                        </ul>
                    </div>
                </div>

                <!-- Pricing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-dollar-sign mr-2"></i>Pricing Information</h6>
                    </div>
                    <div class="card-body">
                        <h6>Free Tier:</h6>
                        <ul>
                            <li>100 emails per month forever</li>
                            <li>All core features included</li>
                            <li>Email support</li>
                        </ul>

                        <h6>Paid Plans:</h6>
                        <ul>
                            <li><strong>Starter:</strong> $15/month for 10,000 emails</li>
                            <li><strong>Growth:</strong> $50/month for 50,000 emails</li>
                            <li><strong>Scale:</strong> Custom pricing for higher volumes</li>
                        </ul>

                        <div class="alert alert-info">
                            <small><i class="fas fa-calculator mr-1"></i><strong>Cost:</strong> $1.50 per 1,000 emails (very competitive for transactional email)</small>
                        </div>
                    </div>
                </div>

                <!-- Best Practices -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-thumbs-up mr-2"></i>Best Practices</h6>
                    </div>
                    <div class="card-body">
                        <h6>Email Content:</h6>
                        <ul>
                            <li>Use clear, descriptive subject lines</li>
                            <li>Include both HTML and text versions</li>
                            <li>Keep content relevant and transactional</li>
                            <li>Include unsubscribe links where appropriate</li>
                        </ul>

                        <h6>Sender Reputation:</h6>
                        <ul>
                            <li>Always verify your sending domain</li>
                            <li>Monitor bounce and complaint rates</li>
                            <li>Use consistent from addresses</li>
                            <li>Implement proper list hygiene</li>
                        </ul>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools mr-2"></i>Troubleshooting</h6>
                    </div>
                    <div class="card-body">
                        <h6>Common Issues:</h6>
                        <ul>
                            <li><strong>Sender signature not verified:</strong> Check email and click confirmation link</li>
                            <li><strong>API authentication failed:</strong> Verify your server API token</li>
                            <li><strong>Domain not verified:</strong> Check DNS records and wait for propagation</li>
                            <li><strong>Emails rejected:</strong> Ensure from address matches verified signature</li>
                        </ul>

                        <h6>Getting Help:</h6>
                        <ul>
                            <li>Postmark has excellent customer support</li>
                            <li>Comprehensive documentation available</li>
                            <li>Active community and resources</li>
                            <li>Email support included in all plans</li>
                        </ul>

                        <div class="mt-3">
                            <a href="https://postmarkapp.com/support" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>Postmark Support
                            </a>
                            <a href="https://postmarkapp.com/developer" target="_blank" class="btn btn-outline-info btn-sm ml-2">
                                <i class="fas fa-book mr-1"></i>Developer Docs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a href="https://account.postmarkapp.com" target="_blank" class="btn btn-success">
                    <i class="fas fa-external-link-alt mr-2"></i>Open Postmark Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
