@extends('admin.layouts.app')

@section('title', 'Email Templates')

@section('header')
    <h1 class="m-0">Email Templates</h1>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-envelope mr-2"></i>
                        Email Template Configuration
                    </h3>
                </div>
                
                <form action="{{ route('admin.settings.email-templates.update') }}" method="POST">
                    @csrf
                    <div class="card-body">
                        @if($businesses->count() > 1)
                            <div class="form-group">
                                <label for="business_id">Select Business</label>
                                <select name="business_id" id="business_id" class="form-control">
                                    <option value="">All Businesses</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}" 
                                                {{ $business && $business->id == $businessOption->id ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <hr>
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_confirmation">Booking Confirmation Email</label>
                                    <textarea name="booking_confirmation" id="booking_confirmation" 
                                              class="form-control" rows="6" 
                                              placeholder="Enter booking confirmation email template...">{{ old('booking_confirmation', $templates['booking_confirmation']) }}</textarea>
                                    <small class="form-text text-muted">
                                        Available variables: {customer_name}, {booking_date}, {booking_time}, {service_name}, {business_name}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_reminder">Booking Reminder Email</label>
                                    <textarea name="booking_reminder" id="booking_reminder" 
                                              class="form-control" rows="6" 
                                              placeholder="Enter booking reminder email template...">{{ old('booking_reminder', $templates['booking_reminder']) }}</textarea>
                                    <small class="form-text text-muted">
                                        Available variables: {customer_name}, {booking_date}, {booking_time}, {service_name}, {business_name}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="booking_cancellation">Booking Cancellation Email</label>
                                    <textarea name="booking_cancellation" id="booking_cancellation" 
                                              class="form-control" rows="6" 
                                              placeholder="Enter booking cancellation email template...">{{ old('booking_cancellation', $templates['booking_cancellation']) }}</textarea>
                                    <small class="form-text text-muted">
                                        Available variables: {customer_name}, {booking_date}, {booking_time}, {service_name}, {business_name}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="waiting_list_notification">Waiting List Notification Email</label>
                                    <textarea name="waiting_list_notification" id="waiting_list_notification" 
                                              class="form-control" rows="6" 
                                              placeholder="Enter waiting list notification email template...">{{ old('waiting_list_notification', $templates['waiting_list_notification']) }}</textarea>
                                    <small class="form-text text-muted">
                                        Available variables: {customer_name}, {service_name}, {business_name}, {available_slot}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="check_in_confirmation">Check-in Confirmation Email</label>
                                    <textarea name="check_in_confirmation" id="check_in_confirmation" 
                                              class="form-control" rows="6" 
                                              placeholder="Enter check-in confirmation email template...">{{ old('check_in_confirmation', $templates['check_in_confirmation']) }}</textarea>
                                    <small class="form-text text-muted">
                                        Available variables: {customer_name}, {booking_date}, {booking_time}, {service_name}, {business_name}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="no_show_follow_up">No-Show Follow-up Email</label>
                                    <textarea name="no_show_follow_up" id="no_show_follow_up" 
                                              class="form-control" rows="6" 
                                              placeholder="Enter no-show follow-up email template...">{{ old('no_show_follow_up', $templates['no_show_follow_up']) }}</textarea>
                                    <small class="form-text text-muted">
                                        Available variables: {customer_name}, {booking_date}, {booking_time}, {service_name}, {business_name}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Email Templates
                        </button>
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Settings
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Template Variables Guide
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Common Variables</h5>
                            <ul class="list-unstyled">
                                <li><code>{customer_name}</code> - Customer's full name</li>
                                <li><code>{business_name}</code> - Business name</li>
                                <li><code>{service_name}</code> - Service name</li>
                                <li><code>{booking_date}</code> - Booking date</li>
                                <li><code>{booking_time}</code> - Booking time</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Special Variables</h5>
                            <ul class="list-unstyled">
                                <li><code>{available_slot}</code> - Available time slot (waiting list)</li>
                                <li><code>{cancellation_reason}</code> - Reason for cancellation</li>
                                <li><code>{check_in_time}</code> - Time of check-in</li>
                                <li><code>{business_address}</code> - Business address</li>
                                <li><code>{business_phone}</code> - Business phone number</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Business selection change
    $('#business_id').change(function() {
        if ($(this).val()) {
            window.location.href = '{{ route("admin.settings.email-templates") }}?business_id=' + $(this).val();
        } else {
            window.location.href = '{{ route("admin.settings.email-templates") }}';
        }
    });
    
    // Auto-resize textareas
    $('textarea').each(function() {
        this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
    }).on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
@stop
