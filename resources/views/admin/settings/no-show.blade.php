@extends('admin.layouts.app')

@section('title', 'No-Show Management Settings')

@section('header')
    <h1 class="m-0">No-Show Management Settings</h1>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-times mr-2"></i>
                    No-Show Management Configuration
                </h3>
            </div>
            <form action="{{ route('admin.settings.no-show.update') }}" method="POST">
                @csrf
                <div class="card-body">
                    <!-- Business Selection -->
                    @if($businesses->count() > 1)
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select name="business_id" id="business_id" class="form-control">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ $business && $business->id == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <hr>
                    @endif

                    <!-- No-Show Fee Settings -->
                    <div class="form-group">
                        <h5><i class="fas fa-dollar-sign mr-2"></i>No-Show Fee Settings</h5>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="no_show_fee_enabled" 
                                   name="no_show_fee_enabled" value="1"
                                   {{ old('no_show_fee_enabled', $settings['no_show_fee_enabled']) ? 'checked' : '' }}>
                            <label class="custom-control-label" for="no_show_fee_enabled">
                                Enable No-Show Fees
                            </label>
                        </div>
                        <small class="form-text text-muted">Charge customers a fee when they don't show up for appointments</small>
                    </div>

                    <div id="fee_options" style="{{ old('no_show_fee_enabled', $settings['no_show_fee_enabled']) ? '' : 'display: none;' }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="no_show_fee_amount">Fixed Fee Amount ($)</label>
                                    <input type="number" name="no_show_fee_amount" id="no_show_fee_amount"
                                           class="form-control @error('no_show_fee_amount') is-invalid @enderror"
                                           value="{{ old('no_show_fee_amount', $settings['no_show_fee_amount']) }}"
                                           min="0" max="1000" step="0.01">
                                    @error('no_show_fee_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Fixed dollar amount to charge</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="no_show_fee_percentage">Percentage Fee (%)</label>
                                    <input type="number" name="no_show_fee_percentage" id="no_show_fee_percentage"
                                           class="form-control @error('no_show_fee_percentage') is-invalid @enderror"
                                           value="{{ old('no_show_fee_percentage', $settings['no_show_fee_percentage']) }}"
                                           min="0" max="100">
                                    @error('no_show_fee_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Percentage of service cost to charge</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Blacklist Settings -->
                    <div class="form-group">
                        <h5><i class="fas fa-ban mr-2"></i>Blacklist Settings</h5>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="no_show_blacklist_enabled" 
                                   name="no_show_blacklist_enabled" value="1"
                                   {{ old('no_show_blacklist_enabled', $settings['no_show_blacklist_enabled']) ? 'checked' : '' }}>
                            <label class="custom-control-label" for="no_show_blacklist_enabled">
                                Enable Automatic Blacklisting
                            </label>
                        </div>
                        <small class="form-text text-muted">Automatically blacklist customers after repeated no-shows</small>
                    </div>

                    <div id="blacklist_options" style="{{ old('no_show_blacklist_enabled', $settings['no_show_blacklist_enabled']) ? '' : 'display: none;' }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="no_show_blacklist_threshold">No-Show Threshold</label>
                                    <input type="number" name="no_show_blacklist_threshold" id="no_show_blacklist_threshold"
                                           class="form-control @error('no_show_blacklist_threshold') is-invalid @enderror"
                                           value="{{ old('no_show_blacklist_threshold', $settings['no_show_blacklist_threshold']) }}"
                                           min="1" max="10">
                                    @error('no_show_blacklist_threshold')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Number of no-shows before blacklisting</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="no_show_blacklist_period_days">Period (days)</label>
                                    <input type="number" name="no_show_blacklist_period_days" id="no_show_blacklist_period_days"
                                           class="form-control @error('no_show_blacklist_period_days') is-invalid @enderror"
                                           value="{{ old('no_show_blacklist_period_days', $settings['no_show_blacklist_period_days']) }}"
                                           min="1" max="365">
                                    @error('no_show_blacklist_period_days')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Time period to count no-shows</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Notification Settings -->
                    <div class="form-group">
                        <h5><i class="fas fa-bell mr-2"></i>Notification Settings</h5>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="send_no_show_notifications" 
                                   name="send_no_show_notifications" value="1"
                                   {{ old('send_no_show_notifications', $settings['send_no_show_notifications']) ? 'checked' : '' }}>
                            <label class="custom-control-label" for="send_no_show_notifications">
                                Send No-Show Notifications
                            </label>
                        </div>
                        <small class="form-text text-muted">Notify staff when customers don't show up</small>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="no_show_follow_up_enabled" 
                                   name="no_show_follow_up_enabled" value="1"
                                   {{ old('no_show_follow_up_enabled', $settings['no_show_follow_up_enabled']) ? 'checked' : '' }}>
                            <label class="custom-control-label" for="no_show_follow_up_enabled">
                                Enable Follow-Up Messages
                            </label>
                        </div>
                        <small class="form-text text-muted">Send follow-up messages to customers who didn't show</small>
                    </div>

                    <div id="follow_up_options" style="{{ old('no_show_follow_up_enabled', $settings['no_show_follow_up_enabled']) ? '' : 'display: none;' }}">
                        <div class="form-group">
                            <label for="no_show_follow_up_delay_hours">Follow-Up Delay (hours)</label>
                            <input type="number" name="no_show_follow_up_delay_hours" id="no_show_follow_up_delay_hours"
                                   class="form-control @error('no_show_follow_up_delay_hours') is-invalid @enderror"
                                   value="{{ old('no_show_follow_up_delay_hours', $settings['no_show_follow_up_delay_hours']) }}"
                                   min="1" max="168">
                            @error('no_show_follow_up_delay_hours')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Hours to wait before sending follow-up message</small>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i>
                        Save Settings
                    </button>
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Settings Summary -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle mr-2"></i>
                    Current Settings Summary
                </h3>
            </div>
            <div class="card-body">
                <div class="info-box">
                    <span class="info-box-icon bg-{{ $settings['no_show_fee_enabled'] ? 'success' : 'secondary' }}">
                        <i class="fas fa-dollar-sign"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">No-Show Fees</span>
                        <span class="info-box-number">
                            {{ $settings['no_show_fee_enabled'] ? 'Enabled' : 'Disabled' }}
                        </span>
                    </div>
                </div>

                <div class="info-box">
                    <span class="info-box-icon bg-{{ $settings['no_show_blacklist_enabled'] ? 'warning' : 'secondary' }}">
                        <i class="fas fa-ban"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Auto Blacklist</span>
                        <span class="info-box-number">
                            {{ $settings['no_show_blacklist_enabled'] ? 'Enabled' : 'Disabled' }}
                        </span>
                    </div>
                </div>

                <div class="info-box">
                    <span class="info-box-icon bg-{{ $settings['send_no_show_notifications'] ? 'info' : 'secondary' }}">
                        <i class="fas fa-bell"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Notifications</span>
                        <span class="info-box-number">
                            {{ $settings['send_no_show_notifications'] ? 'Enabled' : 'Disabled' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle fee options
    $('#no_show_fee_enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#fee_options').show();
        } else {
            $('#fee_options').hide();
        }
    });

    // Toggle blacklist options
    $('#no_show_blacklist_enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#blacklist_options').show();
        } else {
            $('#blacklist_options').hide();
        }
    });

    // Toggle follow-up options
    $('#no_show_follow_up_enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#follow_up_options').show();
        } else {
            $('#follow_up_options').hide();
        }
    });

    // Business selection change
    $('#business_id').change(function() {
        if ($(this).val()) {
            window.location.href = '{{ route("admin.settings.no-show") }}?business_id=' + $(this).val();
        }
    });
});
</script>
@stop
