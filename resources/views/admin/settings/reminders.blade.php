@extends('admin.layouts.app')

@section('title', 'Reminder Rules')

@section('header')
    <h1 class="m-0">Reminder Rules</h1>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2"></i>
                        Reminder Configuration
                    </h3>
                </div>
                
                <form action="{{ route('admin.settings.reminders.update') }}" method="POST">
                    @csrf
                    <div class="card-body">
                        @if($businesses->count() > 1)
                            <div class="form-group">
                                <label for="business_id">Select Business</label>
                                <select name="business_id" id="business_id" class="form-control">
                                    <option value="">All Businesses</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}" 
                                                {{ $business && $business->id == $businessOption->id ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <hr>
                        @endif

                        <!-- Global Reminder Settings -->
                        <h5>Global Reminder Settings</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="reminder_enabled" 
                                               name="reminder_enabled" 
                                               {{ old('reminder_enabled', $settings['reminder_enabled']) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="reminder_enabled">
                                            Enable Reminders
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Master switch for all reminders</small>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="reminder_email_enabled" 
                                               name="reminder_email_enabled" 
                                               {{ old('reminder_email_enabled', $settings['reminder_email_enabled']) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="reminder_email_enabled">
                                            Email Reminders
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Send reminders via email</small>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="reminder_sms_enabled" 
                                               name="reminder_sms_enabled" 
                                               {{ old('reminder_sms_enabled', $settings['reminder_sms_enabled']) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="reminder_sms_enabled">
                                            SMS Reminders
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Send reminders via SMS</small>
                                </div>
                            </div>
                        </div>

                        <div id="reminder_configuration" style="{{ old('reminder_enabled', $settings['reminder_enabled']) ? '' : 'display: none;' }}">
                            <hr>
                            
                            <!-- Standard Reminder Times -->
                            <h5>Standard Reminder Times</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="reminder_24h_enabled" 
                                                   name="reminder_24h_enabled" 
                                                   {{ old('reminder_24h_enabled', $settings['reminder_24h_enabled']) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="reminder_24h_enabled">
                                                24 Hours Before
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">Send reminder 1 day before appointment</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="reminder_2h_enabled" 
                                                   name="reminder_2h_enabled" 
                                                   {{ old('reminder_2h_enabled', $settings['reminder_2h_enabled']) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="reminder_2h_enabled">
                                                2 Hours Before
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">Send reminder 2 hours before appointment</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="reminder_30m_enabled" 
                                                   name="reminder_30m_enabled" 
                                                   {{ old('reminder_30m_enabled', $settings['reminder_30m_enabled']) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="reminder_30m_enabled">
                                                30 Minutes Before
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">Send reminder 30 minutes before appointment</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Custom Reminder Times -->
                            <h5>Custom Reminder Times</h5>
                            <div class="form-group">
                                <label for="custom_reminder_times">Custom Times (in minutes before appointment)</label>
                                <div id="custom-times-container">
                                    @if(old('custom_reminder_times', $settings['reminder_custom_times']))
                                        @foreach(old('custom_reminder_times', $settings['reminder_custom_times']) as $index => $time)
                                            <div class="input-group mb-2 custom-time-input">
                                                <input type="number" name="custom_reminder_times[]" 
                                                       class="form-control" value="{{ $time }}" 
                                                       min="1" max="10080" placeholder="Minutes before appointment">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-danger remove-custom-time">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                                <button type="button" id="add-custom-time" class="btn btn-sm btn-secondary">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Custom Time
                                </button>
                                <small class="form-text text-muted">
                                    Add custom reminder times in minutes (1 minute to 1 week = 10080 minutes)
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Reminder Rules
                        </button>
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Settings
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Settings Summary -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Current Reminder Settings
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <span class="info-box-icon bg-{{ $settings['reminder_enabled'] ? 'success' : 'secondary' }}">
                            <i class="fas fa-{{ $settings['reminder_enabled'] ? 'check' : 'times' }}"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Reminders</span>
                            <span class="info-box-number">
                                {{ $settings['reminder_enabled'] ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                    </div>

                    @if($settings['reminder_enabled'])
                        <div class="info-box">
                            <span class="info-box-icon bg-info">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Email</span>
                                <span class="info-box-number">
                                    {{ $settings['reminder_email_enabled'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-warning">
                                <i class="fas fa-sms"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">SMS</span>
                                <span class="info-box-number">
                                    {{ $settings['reminder_sms_enabled'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </div>
                        </div>

                        <div class="info-box">
                            <span class="info-box-icon bg-primary">
                                <i class="fas fa-clock"></i>
                            </span>
                            <div class="info-box-content">
                                <span class="info-box-text">Active Times</span>
                                <span class="info-box-number">
                                    {{ collect([$settings['reminder_24h_enabled'], $settings['reminder_2h_enabled'], $settings['reminder_30m_enabled']])->filter()->count() + count($settings['reminder_custom_times']) }}
                                </span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Reference -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-question-circle mr-2"></i>
                        Quick Reference
                    </h3>
                </div>
                <div class="card-body">
                    <h6>Common Reminder Times:</h6>
                    <ul class="small">
                        <li><strong>1440 minutes</strong> = 24 hours</li>
                        <li><strong>720 minutes</strong> = 12 hours</li>
                        <li><strong>360 minutes</strong> = 6 hours</li>
                        <li><strong>120 minutes</strong> = 2 hours</li>
                        <li><strong>60 minutes</strong> = 1 hour</li>
                        <li><strong>30 minutes</strong> = 30 minutes</li>
                        <li><strong>15 minutes</strong> = 15 minutes</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle reminder configuration visibility
    $('#reminder_enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#reminder_configuration').show();
        } else {
            $('#reminder_configuration').hide();
        }
    });

    // Add custom reminder time
    $('#add-custom-time').click(function() {
        var html = `
            <div class="input-group mb-2 custom-time-input">
                <input type="number" name="custom_reminder_times[]" 
                       class="form-control" min="1" max="10080" 
                       placeholder="Minutes before appointment">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-custom-time">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        $('#custom-times-container').append(html);
    });

    // Remove custom reminder time
    $(document).on('click', '.remove-custom-time', function() {
        $(this).closest('.custom-time-input').remove();
    });

    // Business selection change
    $('#business_id').change(function() {
        if ($(this).val()) {
            window.location.href = '{{ route("admin.settings.reminders") }}?business_id=' + $(this).val();
        } else {
            window.location.href = '{{ route("admin.settings.reminders") }}';
        }
    });
});
</script>
@stop
