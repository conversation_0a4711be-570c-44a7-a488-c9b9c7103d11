@extends('admin.layouts.app')

@section('title', 'Create New Setting')

@section('header')
    <h1 class="m-0">Create New Setting</h1>
@stop

@section('main-content')
    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.settings.store') }}" method="POST">
                @csrf

                <div class="form-group">
                    <label for="key">Key <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('key') is-invalid @enderror" id="key" name="key" value="{{ old('key') }}" required>
                    <small class="form-text text-muted">Unique identifier for this setting (e.g., app_name, mail_driver)</small>
                    @error('key')
                        <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="group">Group <span class="text-danger">*</span></label>
                    <select class="form-control @error('group') is-invalid @enderror" id="group" name="group">
                        @foreach($groups as $group)
                            <option value="{{ $group }}" {{ old('group') == $group ? 'selected' : '' }}>{{ ucfirst($group) }}</option>
                        @endforeach
                        <option value="new">Add New Group</option>
                    </select>
                    @error('group')
                        <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group" id="new-group-container" style="display: none;">
                    <label for="new_group">New Group Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="new_group" name="new_group" value="{{ old('new_group') }}">
                </div>

                <div class="form-group">
                    <label for="display_name">Display Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('display_name') is-invalid @enderror" id="display_name" name="display_name" value="{{ old('display_name') }}" required>
                    <small class="form-text text-muted">The human-readable name for this setting</small>
                    @error('display_name')
                        <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="type">Type <span class="text-danger">*</span></label>
                    <select class="form-control @error('type') is-invalid @enderror" id="type" name="type" required>
                        <option value="text" {{ old('type') == 'text' ? 'selected' : '' }}>Text</option>
                        <option value="textarea" {{ old('type') == 'textarea' ? 'selected' : '' }}>Textarea</option>
                        <option value="checkbox" {{ old('type') == 'checkbox' ? 'selected' : '' }}>Checkbox</option>
                        <option value="select" {{ old('type') == 'select' ? 'selected' : '' }}>Select</option>
                        <option value="file" {{ old('type') == 'file' ? 'selected' : '' }}>File</option>
                    </select>
                    @error('type')
                        <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group" id="options-container" style="display: none;">
                    <label for="options">Options <span class="text-danger">*</span></label>
                    <textarea class="form-control @error('options') is-invalid @enderror" id="options" name="options" rows="3" placeholder="key1:value1&#10;key2:value2">{{ old('options') }}</textarea>
                    <small class="form-text text-muted">Enter options in key:value format, one per line</small>
                    @error('options')
                        <span class="invalid-feedback">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="value">Default Value</label>
                    <input type="text" class="form-control" id="value" name="value" value="{{ old('value') }}">
                    <small class="form-text text-muted">The default value for this setting</small>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="2">{{ old('description') }}</textarea>
                    <small class="form-text text-muted">A helpful description of what this setting does</small>
                </div>

                <div class="form-group">
                    <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" id="is_public" name="is_public" {{ old('is_public') ? 'checked' : '' }}>
                        <label class="custom-control-label" for="is_public">Public</label>
                    </div>
                    <small class="form-text text-muted">If enabled, this setting can be accessed publicly</small>
                </div>

                <div class="form-group">
                    <label for="order">Order</label>
                    <input type="number" class="form-control" id="order" name="order" value="{{ old('order', 0) }}">
                    <small class="form-text text-muted">Display order (lower numbers appear first)</small>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Create Setting
                    </button>
                    <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const groupSelect = document.getElementById('group');
        const newGroupContainer = document.getElementById('new-group-container');
        const typeSelect = document.getElementById('type');
        const optionsContainer = document.getElementById('options-container');

        // Toggle new group input visibility
        groupSelect.addEventListener('change', function() {
            if (this.value === 'new') {
                newGroupContainer.style.display = 'block';
            } else {
                newGroupContainer.style.display = 'none';
            }
        });

        // Toggle options textarea visibility for select type
        typeSelect.addEventListener('change', function() {
            if (this.value === 'select') {
                optionsContainer.style.display = 'block';
            } else {
                optionsContainer.style.display = 'none';
            }
        });

        // Initialize visibility
        if (groupSelect.value === 'new') {
            newGroupContainer.style.display = 'block';
        }

        if (typeSelect.value === 'select') {
            optionsContainer.style.display = 'block';
        }
    });
</script>
@endsection
