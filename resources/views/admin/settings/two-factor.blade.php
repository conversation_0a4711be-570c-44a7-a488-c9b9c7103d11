@extends('admin.layouts.app')

@section('title', 'Two-Factor Authentication Settings')

@section('header')
    <h1>
        <i class="fas fa-shield-alt"></i> Two-Factor Authentication Settings
        <small class="text-muted">Configure 2FA security options</small>
    </h1>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-8">
        <!-- Main Settings Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cog"></i> 2FA Configuration
                </h3>
                <div class="card-tools">
                    <span class="badge badge-{{ $settings['enabled'] ? 'success' : 'secondary' }}">
                        {{ $settings['enabled'] ? 'ENABLED' : 'DISABLED' }}
                    </span>
                </div>
            </div>
            <form action="{{ route('admin.settings.two-factor.update') }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="card-body">
                    <!-- Global 2FA Toggle -->
                    <div class="form-group">
                        <div class="custom-control custom-switch custom-switch-lg">
                            <input type="checkbox" 
                                   class="custom-control-input" 
                                   id="2fa_enabled" 
                                   name="2fa_enabled" 
                                   value="1"
                                   {{ $settings['enabled'] ? 'checked' : '' }}>
                            <label class="custom-control-label" for="2fa_enabled">
                                <strong>Enable Two-Factor Authentication</strong>
                            </label>
                        </div>
                        <small class="form-text text-muted">
                            Master switch for all 2FA functionality. When disabled, 2FA will not be required for any operations (except Super Admin if configured).
                        </small>
                    </div>

                    <hr>

                    <!-- Role-Specific Settings -->
                    <h5 class="text-primary">Role-Specific Settings</h5>
                    
                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" 
                                   class="custom-control-input" 
                                   id="2fa_required_for_roles" 
                                   name="2fa_required_for_roles" 
                                   value="1"
                                   {{ $settings['required_for_roles'] ? 'checked' : '' }}>
                            <label class="custom-control-label" for="2fa_required_for_roles">
                                Require 2FA for Role Management
                            </label>
                        </div>
                        <small class="form-text text-muted">
                            When enabled, 2FA will be required for creating, editing, or deleting roles.
                        </small>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" 
                                   class="custom-control-input" 
                                   id="2fa_required_for_super_admin" 
                                   name="2fa_required_for_super_admin" 
                                   value="1"
                                   {{ $settings['required_for_super_admin'] ? 'checked' : '' }}>
                            <label class="custom-control-label" for="2fa_required_for_super_admin">
                                Always Require 2FA for Super Admin
                            </label>
                        </div>
                        <small class="form-text text-muted">
                            When enabled, Super Admin operations will always require 2FA, even if global 2FA is disabled.
                        </small>
                    </div>

                    <hr>

                    <!-- Timing Settings -->
                    <h5 class="text-primary">Timing Configuration</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="2fa_session_duration">Session Duration (minutes)</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="2fa_session_duration" 
                                       name="2fa_session_duration" 
                                       value="{{ $settings['session_duration'] }}"
                                       min="5" 
                                       max="480">
                                <small class="form-text text-muted">
                                    How long 2FA verification remains valid for similar operations.
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="2fa_code_expiry">Code Expiry (minutes)</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="2fa_code_expiry" 
                                       name="2fa_code_expiry" 
                                       value="{{ $settings['code_expiry'] }}"
                                       min="1" 
                                       max="60">
                                <small class="form-text text-muted">
                                    How long verification codes remain valid.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="testBtn">
                        <i class="fas fa-vial"></i> Test 2FA
                    </button>
                </div>
            </form>
        </div>

        <!-- Quick Actions Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button type="button" 
                                class="btn btn-block {{ $settings['enabled'] ? 'btn-danger' : 'btn-success' }} quick-toggle"
                                data-action="{{ $settings['enabled'] ? 'disable' : 'enable' }}">
                            <i class="fas fa-{{ $settings['enabled'] ? 'times' : 'check' }}"></i>
                            {{ $settings['enabled'] ? 'Disable' : 'Enable' }} 2FA Globally
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" 
                                class="btn btn-block {{ $settings['required_for_roles'] ? 'btn-warning' : 'btn-info' }} quick-toggle"
                                data-action="{{ $settings['required_for_roles'] ? 'disable_roles' : 'enable_roles' }}">
                            <i class="fas fa-{{ $settings['required_for_roles'] ? 'unlock' : 'lock' }}"></i>
                            {{ $settings['required_for_roles'] ? 'Disable' : 'Enable' }} 2FA for Roles
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Sidebar -->
    <div class="col-md-4">
        <!-- Current Status -->
        <div class="card">
            <div class="card-header bg-info">
                <h3 class="card-title text-white">
                    <i class="fas fa-info-circle"></i> Current Status
                </h3>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-{{ $settings['enabled'] ? 'check text-success' : 'times text-danger' }}"></i>
                        <strong>Global 2FA:</strong> {{ $settings['enabled'] ? 'Enabled' : 'Disabled' }}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-{{ $settings['required_for_roles'] ? 'check text-success' : 'times text-secondary' }}"></i>
                        <strong>Role Management:</strong> {{ $settings['required_for_roles'] ? 'Protected' : 'Not Protected' }}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-{{ $settings['required_for_super_admin'] ? 'shield-alt text-warning' : 'times text-secondary' }}"></i>
                        <strong>Super Admin:</strong> {{ $settings['required_for_super_admin'] ? 'Always Protected' : 'Not Protected' }}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-clock text-info"></i>
                        <strong>Session Duration:</strong> {{ $settings['session_duration'] }} minutes
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-hourglass-half text-warning"></i>
                        <strong>Code Expiry:</strong> {{ $settings['code_expiry'] }} minutes
                    </li>
                </ul>
            </div>
        </div>

        <!-- Security Guidelines -->
        <div class="card mt-3">
            <div class="card-header bg-warning">
                <h3 class="card-title text-dark">
                    <i class="fas fa-exclamation-triangle"></i> Security Guidelines
                </h3>
            </div>
            <div class="card-body">
                <h6>Recommended Settings:</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> Enable 2FA for production environments</li>
                    <li><i class="fas fa-check text-success"></i> Always protect Super Admin operations</li>
                    <li><i class="fas fa-check text-success"></i> Use 30-minute session duration</li>
                    <li><i class="fas fa-check text-success"></i> Set 10-minute code expiry</li>
                </ul>

                <h6 class="mt-3">Important Notes:</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-info text-info"></i> Changes take effect immediately</li>
                    <li><i class="fas fa-info text-info"></i> All changes are logged for audit</li>
                    <li><i class="fas fa-info text-info"></i> Test 2FA before enabling globally</li>
                </ul>
            </div>
        </div>

        <!-- Current User Info -->
        <div class="card mt-3">
            <div class="card-header bg-secondary">
                <h3 class="card-title text-white">
                    <i class="fas fa-user"></i> Your Account
                </h3>
            </div>
            <div class="card-body">
                <p><strong>Role:</strong> {{ $currentUser->getRoleNames()->first() ?? 'No Role' }}</p>
                <p><strong>Email:</strong> {{ $currentUser->email }}</p>
                <p class="mb-0">
                    <strong>2FA Status:</strong> 
                    @if($settings['enabled'])
                        @if($currentUser->hasRole('Super Admin') && $settings['required_for_super_admin'])
                            <span class="badge badge-warning">Always Required</span>
                        @elseif($settings['required_for_roles'])
                            <span class="badge badge-info">Required for Roles</span>
                        @else
                            <span class="badge badge-success">Enabled</span>
                        @endif
                    @else
                        <span class="badge badge-secondary">Disabled</span>
                    @endif
                </p>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Quick toggle functionality
    $('.quick-toggle').click(function() {
        const action = $(this).data('action');
        const btn = $(this);
        
        btn.prop('disabled', true);
        
        $.ajax({
            url: '{{ route("admin.settings.two-factor.toggle") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                action: action
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message);
                    btn.prop('disabled', false);
                }
            },
            error: function() {
                toastr.error('Failed to update 2FA settings.');
                btn.prop('disabled', false);
            }
        });
    });
    
    // Test 2FA functionality
    $('#testBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true);
        
        $.ajax({
            url: '{{ route("admin.settings.two-factor.test") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    if (response.test_code) {
                        toastr.info('Test code: ' + response.test_code, 'Debug Mode', {timeOut: 10000});
                    }
                } else {
                    toastr.error(response.message);
                }
                btn.prop('disabled', false);
            },
            error: function() {
                toastr.error('Failed to test 2FA functionality.');
                btn.prop('disabled', false);
            }
        });
    });
    
    // Form validation
    $('form').submit(function() {
        const sessionDuration = parseInt($('#2fa_session_duration').val());
        const codeExpiry = parseInt($('#2fa_code_expiry').val());
        
        if (sessionDuration < 5 || sessionDuration > 480) {
            toastr.error('Session duration must be between 5 and 480 minutes.');
            return false;
        }
        
        if (codeExpiry < 1 || codeExpiry > 60) {
            toastr.error('Code expiry must be between 1 and 60 minutes.');
            return false;
        }
        
        return true;
    });
});
</script>
@stop
