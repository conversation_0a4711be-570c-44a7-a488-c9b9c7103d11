@extends('admin.layouts.app')

@section('title', 'Branding Settings')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Branding Settings</h1>
            <p class="text-muted">Customize your business appearance</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Settings
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-palette mr-2"></i>
                        Business Branding
                    </h3>
                </div>
                
                <form action="{{ route('admin.settings.branding.update') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="card-body">
                        <!-- Business Selection -->
                        <div class="form-group">
                            <label for="business_id">Business <span class="text-danger">*</span></label>
                            <select name="business_id" id="business_id" class="form-control @error('business_id') is-invalid @enderror" required>
                                <option value="">Select Business</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ old('business_id', $business?->id) == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('business_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        @if($business)
                            <!-- Color Settings -->
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="primary_color">Primary Color</label>
                                        <div class="input-group">
                                            <input type="color" name="primary_color" id="primary_color" 
                                                   class="form-control @error('primary_color') is-invalid @enderror"
                                                   value="{{ old('primary_color', $business->branding['primary_color'] ?? '#007bff') }}">
                                            <div class="input-group-append">
                                                <span class="input-group-text">
                                                    <i class="fas fa-palette"></i>
                                                </span>
                                            </div>
                                        </div>
                                        @error('primary_color')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="secondary_color">Secondary Color</label>
                                        <div class="input-group">
                                            <input type="color" name="secondary_color" id="secondary_color" 
                                                   class="form-control @error('secondary_color') is-invalid @enderror"
                                                   value="{{ old('secondary_color', $business->branding['secondary_color'] ?? '#6c757d') }}">
                                            <div class="input-group-append">
                                                <span class="input-group-text">
                                                    <i class="fas fa-palette"></i>
                                                </span>
                                            </div>
                                        </div>
                                        @error('secondary_color')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="accent_color">Accent Color</label>
                                        <div class="input-group">
                                            <input type="color" name="accent_color" id="accent_color" 
                                                   class="form-control @error('accent_color') is-invalid @enderror"
                                                   value="{{ old('accent_color', $business->branding['accent_color'] ?? '#28a745') }}">
                                            <div class="input-group-append">
                                                <span class="input-group-text">
                                                    <i class="fas fa-palette"></i>
                                                </span>
                                            </div>
                                        </div>
                                        @error('accent_color')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Logo and Favicon -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="logo">Business Logo</label>
                                        <div class="custom-file">
                                            <input type="file" name="logo" id="logo" 
                                                   class="custom-file-input @error('logo') is-invalid @enderror"
                                                   accept="image/jpeg,image/png,image/jpg,image/gif">
                                            <label class="custom-file-label" for="logo">Choose logo file...</label>
                                        </div>
                                        @if($business->branding && isset($business->branding['logo']))
                                            <div class="mt-2">
                                                <img src="{{ Storage::url($business->branding['logo']) }}" 
                                                     alt="Current Logo" class="img-thumbnail" style="max-height: 100px;">
                                            </div>
                                        @endif
                                        @error('logo')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="favicon">Favicon</label>
                                        <div class="custom-file">
                                            <input type="file" name="favicon" id="favicon" 
                                                   class="custom-file-input @error('favicon') is-invalid @enderror"
                                                   accept="image/x-icon,image/png">
                                            <label class="custom-file-label" for="favicon">Choose favicon file...</label>
                                        </div>
                                        @if($business->branding && isset($business->branding['favicon']))
                                            <div class="mt-2">
                                                <img src="{{ Storage::url($business->branding['favicon']) }}" 
                                                     alt="Current Favicon" class="img-thumbnail" style="max-height: 32px;">
                                            </div>
                                        @endif
                                        @error('favicon')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Max size: 512KB. Formats: ICO, PNG</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Typography -->
                            <div class="form-group">
                                <label for="font_family">Font Family</label>
                                <select name="font_family" id="font_family" class="form-control @error('font_family') is-invalid @enderror">
                                    <option value="">Default Font</option>
                                    <option value="Arial, sans-serif" {{ old('font_family', $business->branding['font_family'] ?? '') === 'Arial, sans-serif' ? 'selected' : '' }}>Arial</option>
                                    <option value="Helvetica, sans-serif" {{ old('font_family', $business->branding['font_family'] ?? '') === 'Helvetica, sans-serif' ? 'selected' : '' }}>Helvetica</option>
                                    <option value="Georgia, serif" {{ old('font_family', $business->branding['font_family'] ?? '') === 'Georgia, serif' ? 'selected' : '' }}>Georgia</option>
                                    <option value="Times New Roman, serif" {{ old('font_family', $business->branding['font_family'] ?? '') === 'Times New Roman, serif' ? 'selected' : '' }}>Times New Roman</option>
                                    <option value="Roboto, sans-serif" {{ old('font_family', $business->branding['font_family'] ?? '') === 'Roboto, sans-serif' ? 'selected' : '' }}>Roboto</option>
                                    <option value="Open Sans, sans-serif" {{ old('font_family', $business->branding['font_family'] ?? '') === 'Open Sans, sans-serif' ? 'selected' : '' }}>Open Sans</option>
                                </select>
                                @error('font_family')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Booking Page Customization -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="booking_page_header">Booking Page Header</label>
                                        <input type="text" name="booking_page_header" id="booking_page_header" 
                                               class="form-control @error('booking_page_header') is-invalid @enderror"
                                               value="{{ old('booking_page_header', $business->branding['booking_page_header'] ?? '') }}"
                                               placeholder="Welcome to our booking system">
                                        @error('booking_page_header')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="booking_page_footer">Booking Page Footer</label>
                                        <input type="text" name="booking_page_footer" id="booking_page_footer" 
                                               class="form-control @error('booking_page_footer') is-invalid @enderror"
                                               value="{{ old('booking_page_footer', $business->branding['booking_page_footer'] ?? '') }}"
                                               placeholder="Thank you for choosing us">
                                        @error('booking_page_footer')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Email Customization -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email_header_color">Email Header Color</label>
                                        <div class="input-group">
                                            <input type="color" name="email_header_color" id="email_header_color" 
                                                   class="form-control @error('email_header_color') is-invalid @enderror"
                                                   value="{{ old('email_header_color', $business->branding['email_header_color'] ?? '#007bff') }}">
                                            <div class="input-group-append">
                                                <span class="input-group-text">
                                                    <i class="fas fa-envelope"></i>
                                                </span>
                                            </div>
                                        </div>
                                        @error('email_header_color')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email_footer_text">Email Footer Text</label>
                                        <textarea name="email_footer_text" id="email_footer_text" rows="3"
                                                  class="form-control @error('email_footer_text') is-invalid @enderror"
                                                  placeholder="© 2024 Your Business Name. All rights reserved.">{{ old('email_footer_text', $business->branding['email_footer_text'] ?? '') }}</textarea>
                                        @error('email_footer_text')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Custom CSS -->
                            <div class="form-group">
                                <label for="custom_css">Custom CSS</label>
                                <textarea name="custom_css" id="custom_css" rows="6"
                                          class="form-control @error('custom_css') is-invalid @enderror"
                                          placeholder="/* Add your custom CSS here */">{{ old('custom_css', $business->branding['custom_css'] ?? '') }}</textarea>
                                @error('custom_css')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Advanced: Add custom CSS to further customize the appearance</small>
                            </div>
                        @endif
                    </div>
                    
                    @if($business)
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Save Branding Settings
                            </button>
                            <a href="{{ route('admin.businesses.show', $business) }}" class="btn btn-secondary">
                                Cancel
                            </a>
                        </div>
                    @endif
                </form>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Branding Guide
                    </h3>
                </div>
                <div class="card-body">
                    <h6>Color Guidelines:</h6>
                    <ul class="text-muted">
                        <li><strong>Primary:</strong> Main brand color for buttons and highlights</li>
                        <li><strong>Secondary:</strong> Supporting color for backgrounds</li>
                        <li><strong>Accent:</strong> Color for success states and CTAs</li>
                    </ul>
                    
                    <h6>Logo Requirements:</h6>
                    <ul class="text-muted">
                        <li>Recommended size: 200x80px</li>
                        <li>Transparent background preferred</li>
                        <li>High resolution for crisp display</li>
                    </ul>
                    
                    <h6>Typography:</h6>
                    <p class="text-muted">Choose fonts that reflect your brand personality and ensure good readability across all devices.</p>
                </div>
            </div>

            @if($business)
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-eye mr-2"></i>
                            Preview
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="preview-container" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px;">
                            <div class="preview-header" style="background-color: {{ $business->branding['primary_color'] ?? '#007bff' }}; color: white; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 4px 4px 0 0;">
                                <h6 class="mb-0">{{ $business->name }}</h6>
                            </div>
                            <p style="color: {{ $business->branding['secondary_color'] ?? '#6c757d' }};">
                                This is how your branding will appear to customers.
                            </p>
                            <button class="btn btn-sm" style="background-color: {{ $business->branding['accent_color'] ?? '#28a745' }}; color: white;">
                                Book Now
                            </button>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Update file input labels
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });
    
    // Live preview updates
    $('#primary_color, #secondary_color, #accent_color').on('change', function() {
        updatePreview();
    });
    
    function updatePreview() {
        var primaryColor = $('#primary_color').val();
        var secondaryColor = $('#secondary_color').val();
        var accentColor = $('#accent_color').val();
        
        $('.preview-header').css('background-color', primaryColor);
        $('.preview-container p').css('color', secondaryColor);
        $('.preview-container button').css('background-color', accentColor);
    }
    
    // Business selection change
    $('#business_id').change(function() {
        if ($(this).val()) {
            window.location.href = '{{ route("admin.settings.branding") }}?business_id=' + $(this).val();
        }
    });
});
</script>
@stop
