@extends('admin.layouts.app')

@section('title', 'Change Password')

@section('header')
    <h1>Change Password</h1>
@stop

@section('main-content')
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Change Your Password</h3>
                <div class="card-tools">
                    <a href="{{ route('admin.profile.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to Profile
                    </a>
                </div>
            </div>
            <form action="{{ route('admin.profile.update-password') }}" method="POST">
                @csrf
                @method('PUT')
                <div class="card-body">
                    <div class="form-group">
                        <label for="current_password">Current Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                               id="current_password" name="current_password" required>
                        @error('current_password')
                            <span class="invalid-feedback">{{ $message }}</span>
                        @enderror
                        <small class="form-text text-muted">Enter your current password to confirm your identity.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">New Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control @error('password') is-invalid @enderror" 
                               id="password" name="password" required>
                        @error('password')
                            <span class="invalid-feedback">{{ $message }}</span>
                        @enderror
                        <small class="form-text text-muted">Choose a strong password with at least 8 characters.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="password_confirmation">Confirm New Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" 
                               id="password_confirmation" name="password_confirmation" required>
                        <small class="form-text text-muted">Re-enter your new password to confirm.</small>
                    </div>
                    
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> Password Requirements</h5>
                        <ul class="mb-0">
                            <li>At least 8 characters long</li>
                            <li>Mix of uppercase and lowercase letters</li>
                            <li>Include numbers and special characters</li>
                            <li>Avoid common words or personal information</li>
                        </ul>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key"></i> Change Password
                    </button>
                    <a href="{{ route('admin.profile.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Security Tips</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h5><i class="icon fas fa-exclamation-triangle"></i> Security Recommendations</h5>
                    <ul class="mb-0">
                        <li><strong>Change passwords regularly:</strong> Update your password every 3-6 months</li>
                        <li><strong>Use unique passwords:</strong> Don't reuse passwords from other accounts</li>
                        <li><strong>Enable two-factor authentication:</strong> Add an extra layer of security</li>
                        <li><strong>Log out when done:</strong> Always log out from shared computers</li>
                    </ul>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <h6>Account Information:</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <th width="40%">Last Login:</th>
                                <td>{{ auth()->user()->last_login_at ? auth()->user()->last_login_at->format('M d, Y H:i') : 'Never' }}</td>
                            </tr>
                            <tr>
                                <th>Account Created:</th>
                                <td>{{ auth()->user()->created_at->format('M d, Y') }}</td>
                            </tr>
                            <tr>
                                <th>Email Verified:</th>
                                <td>
                                    @if(auth()->user()->email_verified_at)
                                        <span class="badge badge-success">Yes</span>
                                    @else
                                        <span class="badge badge-warning">No</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Password strength indicator
    $('#password').on('input', function() {
        var password = $(this).val();
        var strength = 0;
        
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        
        var strengthText = '';
        var strengthClass = '';
        
        switch(strength) {
            case 0:
            case 1:
                strengthText = 'Very Weak';
                strengthClass = 'text-danger';
                break;
            case 2:
                strengthText = 'Weak';
                strengthClass = 'text-warning';
                break;
            case 3:
                strengthText = 'Fair';
                strengthClass = 'text-info';
                break;
            case 4:
                strengthText = 'Good';
                strengthClass = 'text-primary';
                break;
            case 5:
                strengthText = 'Strong';
                strengthClass = 'text-success';
                break;
        }
        
        // Remove existing strength indicator
        $('#password').next('.password-strength').remove();
        
        if (password.length > 0) {
            $('#password').after('<small class="password-strength ' + strengthClass + '">Password Strength: ' + strengthText + '</small>');
        }
    });
    
    // Password confirmation validation
    $('#password_confirmation').on('input', function() {
        var password = $('#password').val();
        var confirmation = $(this).val();
        
        // Remove existing match indicator
        $(this).next('.password-match').remove();
        
        if (confirmation.length > 0) {
            if (password === confirmation) {
                $(this).after('<small class="password-match text-success">Passwords match</small>');
            } else {
                $(this).after('<small class="password-match text-danger">Passwords do not match</small>');
            }
        }
    });
});
</script>
@stop
