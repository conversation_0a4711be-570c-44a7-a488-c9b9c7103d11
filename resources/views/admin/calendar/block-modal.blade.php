<div class="block-details">
    <div class="row">
        <div class="col-md-6">
            <h5>
                <i class="fas fa-ban mr-2"></i>
                Block Details
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Title:</strong></td>
                    <td>{{ $block->title }}</td>
                </tr>
                <tr>
                    <td><strong>Type:</strong></td>
                    <td>
                        <span class="badge badge-{{ $block->type_color }}">
                            {{ ucfirst(str_replace('_', ' ', $block->block_type)) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Start:</strong></td>
                    <td>{{ $block->start_datetime->format('M d, Y g:i A') }}</td>
                </tr>
                <tr>
                    <td><strong>End:</strong></td>
                    <td>{{ $block->end_datetime->format('M d, Y g:i A') }}</td>
                </tr>
                <tr>
                    <td><strong>Duration:</strong></td>
                    <td>{{ $block->formatted_duration }}</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h5>
                <i class="fas fa-building mr-2"></i>
                Business Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Business:</strong></td>
                    <td>{{ $block->business->name }}</td>
                </tr>
                @if($block->branch)
                <tr>
                    <td><strong>Branch:</strong></td>
                    <td>{{ $block->branch->name }}</td>
                </tr>
                @endif
                @if($block->resource)
                <tr>
                    <td><strong>Resource:</strong></td>
                    <td>{{ $block->resource->name }}</td>
                </tr>
                @else
                <tr>
                    <td><strong>Scope:</strong></td>
                    <td>
                        @if($block->affects_all_resources)
                            <span class="badge badge-warning">All Resources</span>
                        @else
                            <span class="badge badge-info">Specific Services</span>
                        @endif
                    </td>
                </tr>
                @endif
            </table>
        </div>
    </div>

    @if($block->description)
    <div class="row">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-info-circle mr-2"></i>
                Description
            </h5>
            <p>{{ $block->description }}</p>
        </div>
    </div>
    @endif

    @if($block->affected_services && !$block->affects_all_resources)
    <div class="row">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-concierge-bell mr-2"></i>
                Affected Services
            </h5>
            <div class="d-flex flex-wrap">
                @foreach($block->affected_services as $serviceId)
                    @php
                        $service = \App\Models\Service::find($serviceId);
                    @endphp
                    @if($service)
                        <span class="badge badge-secondary mr-2 mb-2">{{ $service->name }}</span>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="btn-group" role="group">
                @can('edit calendar events')
                    <button type="button" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Block
                    </button>
                @endcan
                @can('delete calendar events')
                    <button type="button" class="btn btn-danger">
                        <i class="fas fa-trash mr-2"></i>
                        Delete Block
                    </button>
                @endcan
            </div>
        </div>
    </div>
</div>
