@extends('admin.layouts.app')

@section('title', 'Calendar')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Calendar</h1>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('create bookings')
                    <button type="button" class="btn btn-success" id="btn-create-booking">
                        <i class="fas fa-plus mr-2"></i>
                        Quick Booking
                    </button>
                @endcan
                @can('create calendar events')
                    <button type="button" class="btn btn-warning" id="btn-create-block">
                        <i class="fas fa-ban mr-2"></i>
                        Block Time
                    </button>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="business-filter">Business</label>
                        <select class="form-control" id="business-filter">
                            <option value="">All Businesses</option>
                            @foreach($businesses as $businessOption)
                                <option value="{{ $businessOption->id }}"
                                        {{ $business && $business->id == $businessOption->id ? 'selected' : '' }}>
                                    {{ $businessOption->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="view-filter">View Type</label>
                        <select class="form-control" id="view-filter">
                            <option value="all">All Events</option>
                            <option value="bookings">Bookings Only</option>
                            <option value="blocks">Blocks Only</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="calendar-view">Calendar View</label>
                        <select class="form-control" id="calendar-view">
                            <option value="dayGridMonth">Month</option>
                            <option value="timeGridWeek">Week</option>
                            <option value="timeGridDay">Day</option>
                            <option value="listWeek">Agenda</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Legend -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-info-circle mr-2"></i>
                Legend
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6><strong>Booking Status</strong></h6>
                    <div class="d-flex flex-wrap">
                        <span class="badge mr-2 mb-1" style="background-color: #ffc107; color: #000;">Pending</span>
                        <span class="badge mr-2 mb-1" style="background-color: #17a2b8; color: #fff;">Confirmed</span>
                        <span class="badge mr-2 mb-1" style="background-color: #007bff; color: #fff;">In Progress</span>
                        <span class="badge mr-2 mb-1" style="background-color: #28a745; color: #fff;">Completed</span>
                        <span class="badge mr-2 mb-1" style="background-color: #dc3545; color: #fff;">Cancelled</span>
                        <span class="badge mr-2 mb-1" style="background-color: #6c757d; color: #fff;">No Show</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6><strong>Availability Blocks</strong></h6>
                    <div class="d-flex flex-wrap">
                        <span class="badge mr-2 mb-1" style="background-color: #ffc107; color: #000;">Maintenance</span>
                        <span class="badge mr-2 mb-1" style="background-color: #17a2b8; color: #fff;">Holiday</span>
                        <span class="badge mr-2 mb-1" style="background-color: #007bff; color: #fff;">Private Event</span>
                        <span class="badge mr-2 mb-1" style="background-color: #6c757d; color: #fff;">Staff Break</span>
                        <span class="badge mr-2 mb-1" style="background-color: #343a40; color: #fff;">Other</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar -->
    <div class="card">
        <div class="card-body">
            <div id="calendar"></div>
        </div>
    </div>
</div>

<!-- Event Details Modal -->
<div class="modal fade" id="event-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Event Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="event-modal-body">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Quick Booking Modal -->
<div class="modal fade" id="quick-booking-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Quick Booking</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="quick-booking-modal-body">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Block Time Modal -->
<div class="modal fade" id="block-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Block Time Slot</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="block-form">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="block-business">Business <span class="text-danger">*</span></label>
                        <select class="form-control" id="block-business" name="business_id" required>
                            <option value="">Select Business</option>
                            @foreach($businesses as $businessOption)
                                <option value="{{ $businessOption->id }}">{{ $businessOption->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="block-title">Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="block-title" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="block-description">Description</label>
                        <textarea class="form-control" id="block-description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="block-start">Start Date & Time <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="block-start" name="start_datetime" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="block-end">End Date & Time <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="block-end" name="end_datetime" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="block-type">Block Type <span class="text-danger">*</span></label>
                        <select class="form-control" id="block-type" name="block_type" required>
                            <option value="">Select Type</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="holiday">Holiday</option>
                            <option value="private_event">Private Event</option>
                            <option value="staff_break">Staff Break</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="block-affects-all" name="affects_all_resources">
                        <label class="form-check-label" for="block-affects-all">
                            Affects all resources
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-ban mr-2"></i>
                        Block Time
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('styles')
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
<style>
.fc-event {
    cursor: pointer;
}
.fc-event-title {
    font-weight: bold;
}
.fc-daygrid-event {
    white-space: normal;
}
</style>
@stop

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const calendarEl = document.getElementById('calendar');
    const businessFilter = document.getElementById('business-filter');
    const viewFilter = document.getElementById('view-filter');
    const calendarViewSelect = document.getElementById('calendar-view');

    const calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: '{{ $view === "day" ? "timeGridDay" : ($view === "week" ? "timeGridWeek" : "dayGridMonth") }}',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
        },
        events: function(info, successCallback, failureCallback) {
            fetch(`{{ route('admin.calendar.events') }}?start=${info.startStr}&end=${info.endStr}&business_id=${businessFilter.value}&view=${viewFilter.value}`)
                .then(response => response.json())
                .then(data => successCallback(data))
                .catch(error => failureCallback(error));
        },
        eventClick: function(info) {
            const eventType = info.event.extendedProps.type;

            if (eventType === 'booking') {
                showBookingDetails(info.event.extendedProps.booking_id);
            } else if (eventType === 'block') {
                showBlockDetails(info.event.extendedProps.block_id);
            }
        },
        selectable: true,
        select: function(info) {
            if (businessFilter.value) {
                showQuickBookingForm(businessFilter.value, info.startStr);
            } else {
                alert('Please select a business first');
            }
        },
        height: 'auto'
    });

    calendar.render();

    // Filter change handlers
    businessFilter.addEventListener('change', function() {
        calendar.refetchEvents();
    });

    viewFilter.addEventListener('change', function() {
        calendar.refetchEvents();
    });

    calendarViewSelect.addEventListener('change', function() {
        calendar.changeView(this.value);
    });

    // Quick booking button
    document.getElementById('btn-create-booking').addEventListener('click', function() {
        if (businessFilter.value) {
            showQuickBookingForm(businessFilter.value, new Date().toISOString());
        } else {
            alert('Please select a business first');
        }
    });

    // Block time button
    document.getElementById('btn-create-block').addEventListener('click', function() {
        $('#block-modal').modal('show');
    });

    // Block form submission
    document.getElementById('block-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch('{{ route('admin.calendar.create-block') }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#block-modal').modal('hide');
                calendar.refetchEvents();
                alert(data.message);
            } else {
                alert('Error creating block: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the block');
        });
    });

    function showBookingDetails(bookingId) {
        fetch(`{{ url('admin/calendar/booking') }}/${bookingId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('event-modal-body').innerHTML = data.html;
                $('#event-modal').modal('show');
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error loading booking details');
            });
    }

    function showBlockDetails(blockId) {
        fetch(`{{ url('admin/calendar/block') }}/${blockId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('event-modal-body').innerHTML = data.html;
                $('#event-modal').modal('show');
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error loading block details');
            });
    }

    function showQuickBookingForm(businessId, startDateTime) {
        fetch('{{ route('admin.calendar.create-booking') }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                business_id: businessId,
                start_datetime: startDateTime
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('quick-booking-modal-body').innerHTML = data.html;
                $('#quick-booking-modal').modal('show');
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading booking form');
        });
    }
});
</script>
@stop
