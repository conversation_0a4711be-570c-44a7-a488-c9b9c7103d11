<div class="booking-details">
    <div class="row">
        <div class="col-md-6">
            <h5>
                <i class="fas fa-calendar-check mr-2"></i>
                Booking Details
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Booking #:</strong></td>
                    <td>{{ $booking->booking_number }}</td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        <span class="badge badge-{{ $booking->status_color }}">
                            {{ ucfirst($booking->status) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>Date & Time:</strong></td>
                    <td>
                        {{ $booking->start_datetime->format('M d, Y') }}<br>
                        {{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}
                    </td>
                </tr>
                <tr>
                    <td><strong>Duration:</strong></td>
                    <td>{{ $booking->formatted_duration }}</td>
                </tr>
                <tr>
                    <td><strong>Participants:</strong></td>
                    <td>{{ $booking->participant_count }}</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h5>
                <i class="fas fa-user mr-2"></i>
                Customer Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td>{{ $booking->customer_name }}</td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td>{{ $booking->customer_email }}</td>
                </tr>
                @if($booking->customer_phone)
                <tr>
                    <td><strong>Phone:</strong></td>
                    <td>{{ $booking->customer_phone }}</td>
                </tr>
                @endif
                @if($booking->notes)
                <tr>
                    <td><strong>Notes:</strong></td>
                    <td>{{ $booking->notes }}</td>
                </tr>
                @endif
            </table>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <h5>
                <i class="fas fa-building mr-2"></i>
                Business Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Business:</strong></td>
                    <td>{{ $booking->business->name }}</td>
                </tr>
                @if($booking->branch)
                <tr>
                    <td><strong>Branch:</strong></td>
                    <td>{{ $booking->branch->name }}</td>
                </tr>
                @endif
            </table>
        </div>
        <div class="col-md-6">
            <h5>
                <i class="fas fa-dollar-sign mr-2"></i>
                Payment Information
            </h5>
            <table class="table table-sm">
                <tr>
                    <td><strong>Total Amount:</strong></td>
                    <td>${{ number_format($booking->total_amount, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Paid Amount:</strong></td>
                    <td>${{ number_format($booking->paid_amount, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Remaining:</strong></td>
                    <td>${{ number_format($booking->remaining_amount, 2) }}</td>
                </tr>
                <tr>
                    <td><strong>Payment Status:</strong></td>
                    <td>
                        <span class="badge badge-{{ $booking->payment_status_color }}">
                            {{ ucfirst(str_replace('_', ' ', $booking->payment_status)) }}
                        </span>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h5>
                <i class="fas fa-concierge-bell mr-2"></i>
                Services
            </h5>
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>Quantity</th>
                            <th>Duration</th>
                            <th>Unit Price</th>
                            <th>Total Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($booking->bookingServices as $bookingService)
                        <tr>
                            <td>{{ $bookingService->service->name }}</td>
                            <td>{{ $bookingService->quantity }}</td>
                            <td>{{ $bookingService->formatted_duration }}</td>
                            <td>${{ number_format($bookingService->unit_price, 2) }}</td>
                            <td>${{ number_format($bookingService->total_price, 2) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="btn-group" role="group">
                <a href="{{ route('admin.bookings.show', $booking) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Full Details
                </a>
                @if($booking->can_be_checked_in)
                    <form action="{{ route('admin.bookings.check-in', $booking) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Check In
                        </button>
                    </form>
                @endif
                @if($booking->can_be_checked_out)
                    <form action="{{ route('admin.bookings.check-out', $booking) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Check Out
                        </button>
                    </form>
                @endif
                @can('edit bookings')
                    <a href="{{ route('admin.bookings.edit', $booking) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit
                    </a>
                @endcan
            </div>
        </div>
    </div>
</div>
