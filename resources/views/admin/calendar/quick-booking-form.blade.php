<form id="quick-booking-form">
    @csrf
    <input type="hidden" name="business_id" value="{{ $business->id }}">
    <input type="hidden" name="service_id" value="{{ $service->id }}">
    <input type="hidden" name="start_datetime" value="{{ $startDateTime->toISOString() }}">
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="customer_name" name="customer_name" required>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="customer_email">Customer Email <span class="text-danger">*</span></label>
                <input type="email" class="form-control" id="customer_email" name="customer_email" required>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="customer_phone">Customer Phone</label>
                <input type="tel" class="form-control" id="customer_phone" name="customer_phone">
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="participant_count">Number of Participants <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="participant_count" name="participant_count" min="1" value="1" required>
            </div>
        </div>
    </div>
    
    <div class="form-group">
        <label for="notes">Notes</label>
        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any special requests or notes..."></textarea>
    </div>
    
    <div class="alert alert-info">
        <h6><i class="fas fa-info-circle mr-2"></i>Booking Summary</h6>
        <p class="mb-1"><strong>Business:</strong> {{ $business->name }}</p>
        <p class="mb-1"><strong>Service:</strong> {{ $service->name }}</p>
        <p class="mb-1"><strong>Date & Time:</strong> {{ $startDateTime->format('M d, Y g:i A') }}</p>
        <p class="mb-1"><strong>Duration:</strong> {{ $service->formatted_duration }}</p>
        <p class="mb-0"><strong>Price:</strong> ${{ $service->formatted_price }}</p>
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-success">
            <i class="fas fa-calendar-check mr-2"></i>
            Create Booking
        </button>
    </div>
</form>

<script>
document.getElementById('quick-booking-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{{ route('admin.calendar.store-quick-booking') }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#quick-booking-modal').modal('hide');
            // Refresh calendar events
            if (typeof calendar !== 'undefined') {
                calendar.refetchEvents();
            }
            alert(data.message);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the booking');
    });
});
</script>
