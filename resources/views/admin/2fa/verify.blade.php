@extends('admin.layouts.app')

@section('title', 'Two-Factor Authentication Required')

@section('header')
    <h1>
        <i class="fas fa-shield-alt text-warning"></i> Security Verification Required
        <small class="text-muted">Two-Factor Authentication</small>
    </h1>
@stop

@section('main-content')
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card border-warning">
            <div class="card-header bg-warning">
                <h3 class="card-title text-dark">
                    <i class="fas fa-exclamation-triangle"></i> Critical Operation Detected
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Action:</strong> {{ $actionDescription ?? 'Critical Security Operation' }}
                    <br>
                    <strong>User:</strong> {{ $user->name }} ({{ $user->email }})
                    <br>
                    <strong>Time:</strong> {{ now()->format('Y-m-d H:i:s') }}
                </div>

                <p class="text-muted">
                    For security reasons, this operation requires two-factor authentication.
                    A verification code has been sent to your registered email address.
                </p>

                @if(config('app.debug'))
                    <div class="alert alert-info">
                        <strong>Debug Info:</strong><br>
                        Action: {{ $action ?? 'NULL' }}<br>
                        Session Action: {{ session('2fa_intended_action', 'NULL') }}<br>
                        Intended URL: {{ session('2fa_intended_url', 'NULL') }}
                    </div>
                @endif

                <form action="{{ route('admin.2fa.verify') }}" method="POST" id="verificationForm">
                    @csrf
                    <input type="hidden" name="action" value="{{ $action ?? session('2fa_intended_action', 'critical_operation') }}">

                    <div class="form-group">
                        <label for="verification_code">
                            <i class="fas fa-key"></i> Verification Code
                        </label>
                        <input type="text"
                               class="form-control form-control-lg text-center @error('verification_code') is-invalid @enderror"
                               id="verification_code"
                               name="verification_code"
                               placeholder="000000"
                               maxlength="6"
                               pattern="[0-9]{6}"
                               required
                               autofocus>
                        @error('verification_code')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">
                            Enter the 6-digit code sent to {{ substr($user->email, 0, 3) }}***{{ substr($user->email, strpos($user->email, '@')) }}
                        </small>
                    </div>

                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-check"></i> Verify & Continue
                        </button>
                        <a href="{{ route('admin.2fa.cancel') }}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>

                <hr>

                <div class="text-center">
                    <form action="{{ route('admin.2fa.resend') }}" method="POST" style="display: inline;">
                        @csrf
                        <input type="hidden" name="action" value="{{ $action ?? session('2fa_intended_action', 'critical_operation') }}">
                        <button type="submit" class="btn btn-outline-primary btn-sm" id="resendBtn">
                            <i class="fas fa-redo"></i> Resend Code
                        </button>
                    </form>

                    <div class="mt-2">
                        <small class="text-muted">
                            Code expires in <span id="countdown">10:00</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Information -->
        <div class="card mt-3">
            <div class="card-header bg-info">
                <h5 class="card-title text-white mb-0">
                    <i class="fas fa-info-circle"></i> Security Information
                </h5>
            </div>
            <div class="card-body">
                <h6>Why is 2FA required?</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Protects against unauthorized access</li>
                    <li><i class="fas fa-check text-success"></i> Prevents privilege escalation attacks</li>
                    <li><i class="fas fa-check text-success"></i> Ensures audit trail integrity</li>
                    <li><i class="fas fa-check text-success"></i> Complies with security standards</li>
                </ul>

                <h6 class="mt-3">What happens next?</h6>
                <ol class="list-unstyled">
                    <li><i class="fas fa-arrow-right text-primary"></i> Enter the verification code</li>
                    <li><i class="fas fa-arrow-right text-primary"></i> Complete your intended action</li>
                    <li><i class="fas fa-arrow-right text-primary"></i> All changes will be logged</li>
                </ol>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Important:</strong> This verification is valid for 30 minutes for similar operations.
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Auto-format verification code input
    $('#verification_code').on('input', function() {
        let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
        if (value.length > 6) {
            value = value.substring(0, 6);
        }
        $(this).val(value);

        // Auto-submit when 6 digits are entered
        if (value.length === 6) {
            $('#verificationForm').submit();
        }
    });

    // Countdown timer
    let timeLeft = 600; // 10 minutes in seconds
    const countdownElement = $('#countdown');

    function updateCountdown() {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        countdownElement.text(
            minutes.toString().padStart(2, '0') + ':' +
            seconds.toString().padStart(2, '0')
        );

        if (timeLeft <= 0) {
            countdownElement.text('EXPIRED');
            countdownElement.addClass('text-danger');
            $('#verificationForm button[type="submit"]').prop('disabled', true);

            // Show expiration message
            if (!$('.expiration-alert').length) {
                $('<div class="alert alert-danger expiration-alert mt-3">' +
                  '<i class="fas fa-exclamation-triangle"></i> ' +
                  'Verification code has expired. Please request a new code.' +
                  '</div>').insertAfter('#verificationForm');
            }
        } else {
            timeLeft--;
        }
    }

    // Update countdown every second
    const countdownInterval = setInterval(updateCountdown, 1000);
    updateCountdown(); // Initial call

    // Resend button rate limiting
    $('#resendBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true);

        // Re-enable after 60 seconds
        setTimeout(function() {
            btn.prop('disabled', false);
        }, 60000);
    });

    // Focus on input when page loads
    $('#verification_code').focus();

    // Prevent form submission with invalid code
    $('#verificationForm').submit(function(e) {
        const code = $('#verification_code').val();
        if (!/^\d{6}$/.test(code)) {
            e.preventDefault();
            toastr.error('Please enter a valid 6-digit verification code.');
            return false;
        }
    });

    // Auto-refresh page if user has been idle for too long
    let idleTime = 0;
    const idleInterval = setInterval(function() {
        idleTime++;
        if (idleTime >= 20) { // 20 minutes
            clearInterval(idleInterval);
            clearInterval(countdownInterval);
            window.location.href = '{{ route("admin.2fa.cancel") }}';
        }
    }, 60000); // Check every minute

    // Reset idle time on user activity
    $(document).on('mousemove keypress', function() {
        idleTime = 0;
    });
});
</script>

@if(session('success'))
<script>
$(document).ready(function() {
    toastr.success('{{ session('success') }}');
});
</script>
@endif

@if(session('error'))
<script>
$(document).ready(function() {
    toastr.error('{{ session('error') }}');
});
</script>
@endif
@stop
