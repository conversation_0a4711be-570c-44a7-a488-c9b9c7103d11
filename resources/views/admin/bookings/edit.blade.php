@extends('admin.layouts.app')

@section('title', 'Edit Booking')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Edit Booking</h1>
            <p class="text-muted">{{ $booking->booking_number }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.bookings.show', $booking) }}" class="btn btn-info">
                    <i class="fas fa-eye mr-2"></i>
                    View Booking
                </a>
                <a href="{{ route('admin.bookings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Bookings
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-12">
        <form action="{{ route('admin.bookings.update', $booking) }}" method="POST" id="booking-form">
            @csrf
            @method('PUT')
            
            <!-- Customer Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user mr-2"></i>
                        Customer Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('customer_name') is-invalid @enderror" 
                                       id="customer_name" name="customer_name" 
                                       value="{{ old('customer_name', $booking->customer_name) }}" required>
                                @error('customer_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="customer_email">Customer Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('customer_email') is-invalid @enderror" 
                                       id="customer_email" name="customer_email" 
                                       value="{{ old('customer_email', $booking->customer_email) }}" required>
                                @error('customer_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="customer_phone">Customer Phone</label>
                                <input type="text" class="form-control @error('customer_phone') is-invalid @enderror" 
                                       id="customer_phone" name="customer_phone" 
                                       value="{{ old('customer_phone', $booking->customer_phone) }}">
                                @error('customer_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Details -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-check mr-2"></i>
                        Booking Details
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_datetime">Date & Time <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control @error('start_datetime') is-invalid @enderror" 
                                       id="start_datetime" name="start_datetime" 
                                       value="{{ old('start_datetime', $booking->start_datetime->format('Y-m-d\TH:i')) }}" required>
                                @error('start_datetime')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="participant_count">Number of Participants <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('participant_count') is-invalid @enderror" 
                                       id="participant_count" name="participant_count" 
                                       value="{{ old('participant_count', $booking->participant_count) }}" min="1" max="100" required>
                                @error('participant_count')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Status Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">Booking Status <span class="text-danger">*</span></label>
                                <select class="form-control @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="pending" {{ old('status', $booking->status) == 'pending' ? 'selected' : '' }}>
                                        Pending
                                    </option>
                                    <option value="confirmed" {{ old('status', $booking->status) == 'confirmed' ? 'selected' : '' }}>
                                        Confirmed
                                    </option>
                                    <option value="in_progress" {{ old('status', $booking->status) == 'in_progress' ? 'selected' : '' }}>
                                        In Progress
                                    </option>
                                    <option value="completed" {{ old('status', $booking->status) == 'completed' ? 'selected' : '' }}>
                                        Completed
                                    </option>
                                    <option value="cancelled" {{ old('status', $booking->status) == 'cancelled' ? 'selected' : '' }}>
                                        Cancelled
                                    </option>
                                    <option value="no_show" {{ old('status', $booking->status) == 'no_show' ? 'selected' : '' }}>
                                        No Show
                                    </option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="payment_status">Payment Status <span class="text-danger">*</span></label>
                                <select class="form-control @error('payment_status') is-invalid @enderror" 
                                        id="payment_status" name="payment_status" required>
                                    <option value="pending" {{ old('payment_status', $booking->payment_status) == 'pending' ? 'selected' : '' }}>
                                        Pending
                                    </option>
                                    <option value="partial" {{ old('payment_status', $booking->payment_status) == 'partial' ? 'selected' : '' }}>
                                        Partial
                                    </option>
                                    <option value="paid" {{ old('payment_status', $booking->payment_status) == 'paid' ? 'selected' : '' }}>
                                        Paid
                                    </option>
                                    <option value="refunded" {{ old('payment_status', $booking->payment_status) == 'refunded' ? 'selected' : '' }}>
                                        Refunded
                                    </option>
                                </select>
                                @error('payment_status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-concierge-bell mr-2"></i>
                        Services
                    </h3>
                </div>
                <div class="card-body">
                    @if($booking->bookingServices->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th>Duration</th>
                                        <th>Quantity</th>
                                        <th>Unit Price</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($booking->bookingServices as $bookingService)
                                        <tr>
                                            <td>
                                                <strong>{{ $bookingService->service->name }}</strong>
                                                @if($bookingService->service->description)
                                                    <br><small class="text-muted">{{ $bookingService->service->description }}</small>
                                                @endif
                                            </td>
                                            <td>{{ $bookingService->service->formatted_duration }}</td>
                                            <td>{{ $bookingService->quantity }}</td>
                                            <td>${{ number_format($bookingService->unit_price, 2) }}</td>
                                            <td>${{ number_format($bookingService->total_price, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            <strong>Note:</strong> To modify services, please create a new booking or contact system administrator.
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            No services found for this booking.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Notes -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sticky-note mr-2"></i>
                        Notes
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="notes">Customer Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="4" 
                                          placeholder="Any special requests or notes from the customer">{{ old('notes', $booking->notes) }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="internal_notes">Internal Notes</label>
                                <textarea class="form-control @error('internal_notes') is-invalid @enderror" 
                                          id="internal_notes" name="internal_notes" rows="4" 
                                          placeholder="Internal notes for staff (not visible to customer)">{{ old('internal_notes', $booking->internal_notes) }}</textarea>
                                @error('internal_notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Information (Read-only) -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building mr-2"></i>
                        Business Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Business:</strong></td>
                                    <td>{{ $booking->business->name }}</td>
                                </tr>
                                @if($booking->branch)
                                <tr>
                                    <td><strong>Branch:</strong></td>
                                    <td>{{ $booking->branch->name }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Booking Number:</strong></td>
                                    <td>{{ $booking->booking_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $booking->created_at->format('M d, Y g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Note:</strong> Business and service information cannot be changed after booking creation.
                    </div>
                </div>
            </div>

            <!-- Payment Summary (Read-only) -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        Payment Summary
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Subtotal:</strong></td>
                                    <td class="text-right">${{ number_format($booking->subtotal, 2) }}</td>
                                </tr>
                                @if($booking->discount_amount > 0)
                                <tr>
                                    <td><strong>Discount:</strong></td>
                                    <td class="text-right text-success">-${{ number_format($booking->discount_amount, 2) }}</td>
                                </tr>
                                @endif
                                @if($booking->tax_amount > 0)
                                <tr>
                                    <td><strong>Tax:</strong></td>
                                    <td class="text-right">${{ number_format($booking->tax_amount, 2) }}</td>
                                </tr>
                                @endif
                                <tr class="border-top">
                                    <td><strong>Total Amount:</strong></td>
                                    <td class="text-right"><strong>${{ number_format($booking->total_amount, 2) }}</strong></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Paid Amount:</strong></td>
                                    <td class="text-right text-success">${{ number_format($booking->paid_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Remaining:</strong></td>
                                    <td class="text-right {{ $booking->remaining_amount > 0 ? 'text-danger' : 'text-success' }}">
                                        ${{ number_format($booking->remaining_amount, 2) }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Note:</strong> To modify payments, please use the "Add Payment" feature in the booking details view.
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="card">
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save mr-2"></i>
                                Update Booking
                            </button>
                            <a href="{{ route('admin.bookings.show', $booking) }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                        <div class="col-md-6">
                            <div class="text-right">
                                <small class="text-muted">
                                    Last updated: {{ $booking->updated_at->format('M d, Y g:i A') }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // Add any JavaScript functionality here if needed
    
    // Form validation
    $('#booking-form').on('submit', function(e) {
        // Add any custom validation logic here
    });
    
    // Status change warnings
    $('#status').change(function() {
        const status = $(this).val();
        if (status === 'cancelled') {
            if (!confirm('Are you sure you want to mark this booking as cancelled? This action may trigger refund processes.')) {
                $(this).val('{{ $booking->status }}');
            }
        }
    });
});
</script>
@stop
