@extends('admin.layouts.app')

@section('title', 'Booking Details')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Booking Details</h1>
            <p class="text-muted">{{ $booking->booking_number }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('edit bookings')
                    <a href="{{ route('admin.bookings.edit', $booking) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Booking
                    </a>
                @endcan
                <a href="{{ route('admin.bookings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Bookings
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <!-- Booking Information -->
    <div class="col-md-8">
        <!-- Basic Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-calendar-check mr-2"></i>
                    Booking Information
                </h3>
                <div class="card-tools">
                    <span class="badge badge-{{ $booking->status_color }} badge-lg">
                        {{ ucfirst($booking->status) }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Booking Number:</strong></td>
                                <td>{{ $booking->booking_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date & Time:</strong></td>
                                <td>
                                    {{ $booking->start_datetime->format('M d, Y') }}<br>
                                    <small class="text-muted">
                                        {{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Duration:</strong></td>
                                <td>{{ $booking->formatted_duration }}</td>
                            </tr>
                            <tr>
                                <td><strong>Participants:</strong></td>
                                <td>{{ $booking->participant_count }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge badge-{{ $booking->status_color }}">
                                        {{ ucfirst($booking->status) }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Payment Status:</strong></td>
                                <td>
                                    <span class="badge badge-{{ $booking->payment_status_color }}">
                                        {{ ucfirst(str_replace('_', ' ', $booking->payment_status)) }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $booking->created_at->format('M d, Y g:i A') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Last Updated:</strong></td>
                                <td>{{ $booking->updated_at->format('M d, Y g:i A') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user mr-2"></i>
                    Customer Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $booking->customer_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>
                                    <a href="mailto:{{ $booking->customer_email }}">
                                        {{ $booking->customer_email }}
                                    </a>
                                </td>
                            </tr>
                            @if($booking->customer_phone)
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>
                                    <a href="tel:{{ $booking->customer_phone }}">
                                        {{ $booking->customer_phone }}
                                    </a>
                                </td>
                            </tr>
                            @endif
                        </table>
                    </div>
                    <div class="col-md-6">
                        @if($booking->customer)
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Customer Account:</strong></td>
                                    <td>
                                        <a href="{{ route('admin.users.show', $booking->customer) }}">
                                            View Profile
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Total Bookings:</strong></td>
                                    <td>{{ $booking->customer->bookings()->count() }}</td>
                                </tr>
                            </table>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                This customer doesn't have an account yet.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-building mr-2"></i>
                    Business Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Business:</strong></td>
                                <td>
                                    <a href="{{ route('admin.businesses.show', $booking->business) }}">
                                        {{ $booking->business->name }}
                                    </a>
                                </td>
                            </tr>
                            @if($booking->branch)
                            <tr>
                                <td><strong>Branch:</strong></td>
                                <td>{{ $booking->branch->name }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Address:</strong></td>
                                <td>
                                    @if($booking->branch && $booking->branch->address)
                                        {{ $booking->branch->address }}
                                    @else
                                        {{ $booking->business->address }}
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Services -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-concierge-bell mr-2"></i>
                    Services
                </h3>
            </div>
            <div class="card-body">
                @if($booking->bookingServices->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Duration</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($booking->bookingServices as $bookingService)
                                    <tr>
                                        <td>
                                            <strong>{{ $bookingService->service->name }}</strong>
                                            @if($bookingService->service->description)
                                                <br><small class="text-muted">{{ $bookingService->service->description }}</small>
                                            @endif
                                        </td>
                                        <td>{{ $bookingService->service->formatted_duration }}</td>
                                        <td>{{ $bookingService->quantity }}</td>
                                        <td>${{ number_format($bookingService->unit_price, 2) }}</td>
                                        <td>${{ number_format($bookingService->total_price, 2) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        No services found for this booking.
                    </div>
                @endif
            </div>
        </div>

        <!-- Notes -->
        @if($booking->notes || $booking->internal_notes)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-sticky-note mr-2"></i>
                    Notes
                </h3>
            </div>
            <div class="card-body">
                @if($booking->notes)
                    <div class="mb-3">
                        <h6><i class="fas fa-comment mr-2"></i>Customer Notes:</h6>
                        <p class="text-muted">{{ $booking->notes }}</p>
                    </div>
                @endif
                @if($booking->internal_notes)
                    <div>
                        <h6><i class="fas fa-lock mr-2"></i>Internal Notes:</h6>
                        <p class="text-muted">{{ $booking->internal_notes }}</p>
                    </div>
                @endif
            </div>
        </div>
        @endif
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bolt mr-2"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="card-body">
                <div class="btn-group-vertical w-100" role="group">
                    @if($booking->can_be_checked_in)
                        <form action="{{ route('admin.bookings.check-in', $booking) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-success btn-block">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                Check In Customer
                            </button>
                        </form>
                    @endif
                    
                    @if($booking->can_be_checked_out)
                        <form action="{{ route('admin.bookings.check-out', $booking) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-sign-out-alt mr-2"></i>
                                Check Out Customer
                            </button>
                        </form>
                    @endif
                    
                    @if($booking->status !== 'cancelled')
                        <form action="{{ route('admin.bookings.cancel', $booking) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-danger btn-block" 
                                    onclick="return confirm('Are you sure you want to cancel this booking?')">
                                <i class="fas fa-times mr-2"></i>
                                Cancel Booking
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-dollar-sign mr-2"></i>
                    Payment Summary
                </h3>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Subtotal:</strong></td>
                        <td class="text-right">${{ number_format($booking->subtotal, 2) }}</td>
                    </tr>
                    @if($booking->discount_amount > 0)
                    <tr>
                        <td><strong>Discount:</strong></td>
                        <td class="text-right text-success">-${{ number_format($booking->discount_amount, 2) }}</td>
                    </tr>
                    @endif
                    @if($booking->tax_amount > 0)
                    <tr>
                        <td><strong>Tax:</strong></td>
                        <td class="text-right">${{ number_format($booking->tax_amount, 2) }}</td>
                    </tr>
                    @endif
                    <tr class="border-top">
                        <td><strong>Total Amount:</strong></td>
                        <td class="text-right"><strong>${{ number_format($booking->total_amount, 2) }}</strong></td>
                    </tr>
                    <tr>
                        <td><strong>Paid Amount:</strong></td>
                        <td class="text-right text-success">${{ number_format($booking->paid_amount, 2) }}</td>
                    </tr>
                    <tr>
                        <td><strong>Remaining:</strong></td>
                        <td class="text-right {{ $booking->remaining_amount > 0 ? 'text-danger' : 'text-success' }}">
                            ${{ number_format($booking->remaining_amount, 2) }}
                        </td>
                    </tr>
                </table>
                
                @if($booking->remaining_amount > 0)
                    <button type="button" class="btn btn-success btn-block" data-toggle="modal" data-target="#payment-modal">
                        <i class="fas fa-credit-card mr-2"></i>
                        Add Payment
                    </button>
                @endif
            </div>
        </div>

        <!-- Payment History -->
        @if($booking->payments->count() > 0)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history mr-2"></i>
                    Payment History
                </h3>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Method</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($booking->payments as $payment)
                                <tr>
                                    <td>{{ $payment->created_at->format('M d, Y') }}</td>
                                    <td>${{ number_format($payment->amount, 2) }}</td>
                                    <td>{{ ucfirst($payment->payment_method) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="payment-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form action="{{ route('admin.bookings.add-payment', $booking) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add Payment</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="payment_amount">Amount <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="payment_amount" name="amount" 
                               step="0.01" max="{{ $booking->remaining_amount }}" 
                               value="{{ $booking->remaining_amount }}" required>
                    </div>
                    <div class="form-group">
                        <label for="payment_method">Payment Method <span class="text-danger">*</span></label>
                        <select class="form-control" id="payment_method" name="payment_method" required>
                            <option value="cash">Cash</option>
                            <option value="card">Credit/Debit Card</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="payment_notes">Notes</label>
                        <textarea class="form-control" id="payment_notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Add Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop
