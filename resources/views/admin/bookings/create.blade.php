@extends('admin.layouts.app')

@section('title', 'Create Booking')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Create New Booking</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.bookings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Bookings
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-12">
        <form action="{{ route('admin.bookings.store') }}" method="POST" id="booking-form">
            @csrf

            <!-- Business Selection -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building mr-2"></i>
                        Business Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="business_id">Business <span class="text-danger">*</span></label>
                                <select class="form-control @error('business_id') is-invalid @enderror"
                                        id="business_id" name="business_id" required>
                                    <option value="">Select a business</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}"
                                                {{ (old('business_id', $business?->id) == $businessOption->id) ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('business_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="business_branch_id">Branch (Optional)</label>
                                <select class="form-control @error('business_branch_id') is-invalid @enderror"
                                        id="business_branch_id" name="business_branch_id">
                                    <option value="">Main Location</option>
                                </select>
                                @error('business_branch_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user mr-2"></i>
                        Customer Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="customer_id">Existing Customer (Optional)</label>
                                <select class="form-control @error('customer_id') is-invalid @enderror"
                                        id="customer_id" name="customer_id">
                                    <option value="">Select existing customer or enter new</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}"
                                                data-name="{{ $customer->name }}"
                                                data-email="{{ $customer->email }}"
                                                data-phone="{{ $customer->phone }}"
                                                {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }} ({{ $customer->email }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('customer_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('customer_name') is-invalid @enderror"
                                       id="customer_name" name="customer_name"
                                       value="{{ old('customer_name') }}" required>
                                @error('customer_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="customer_email">Customer Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('customer_email') is-invalid @enderror"
                                       id="customer_email" name="customer_email"
                                       value="{{ old('customer_email') }}" required>
                                @error('customer_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="customer_phone">Customer Phone</label>
                                <input type="text" class="form-control @error('customer_phone') is-invalid @enderror"
                                       id="customer_phone" name="customer_phone"
                                       value="{{ old('customer_phone') }}">
                                @error('customer_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Details -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-check mr-2"></i>
                        Booking Details
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="start_datetime">Date & Time <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control @error('start_datetime') is-invalid @enderror"
                                       id="start_datetime" name="start_datetime"
                                       value="{{ old('start_datetime') }}" required>
                                @error('start_datetime')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="participant_count">Number of Participants <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('participant_count') is-invalid @enderror"
                                       id="participant_count" name="participant_count"
                                       value="{{ old('participant_count', 1) }}" min="1" max="100" required>
                                @error('participant_count')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services Selection -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-concierge-bell mr-2"></i>
                        Services
                    </h3>
                </div>
                <div class="card-body">
                    <div id="services-container">
                        @if($services->count() > 0)
                            @foreach($services as $service)
                                <div class="form-check mb-3">
                                    <input class="form-check-input service-checkbox" type="checkbox"
                                           name="services[{{ $loop->index }}][service_id]"
                                           value="{{ $service->id }}"
                                           id="service_{{ $service->id }}"
                                           data-price="{{ $service->base_price }}"
                                           data-duration="{{ $service->duration_minutes }}">
                                    <label class="form-check-label" for="service_{{ $service->id }}">
                                        <strong>{{ $service->name }}</strong>
                                        <br><small class="text-muted">
                                            Duration: {{ $service->formatted_duration }} |
                                            Price: ${{ $service->formatted_price }}
                                        </small>
                                        @if($service->description)
                                            <br><small class="text-muted">{{ $service->description }}</small>
                                        @endif
                                    </label>
                                    <input type="hidden" name="services[{{ $loop->index }}][quantity]" value="1">
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                Please select a business first to see available services.
                            </div>
                        @endif
                    </div>
                    @error('services')
                        <div class="text-danger">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Notes -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sticky-note mr-2"></i>
                        Additional Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="notes">Customer Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror"
                                          id="notes" name="notes" rows="3"
                                          placeholder="Any special requests or notes from the customer">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="internal_notes">Internal Notes</label>
                                <textarea class="form-control @error('internal_notes') is-invalid @enderror"
                                          id="internal_notes" name="internal_notes" rows="3"
                                          placeholder="Internal notes for staff (not visible to customer)">{{ old('internal_notes') }}</textarea>
                                @error('internal_notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="card">
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save mr-2"></i>
                                Create Booking
                            </button>
                            <a href="{{ route('admin.bookings.index') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                        <div class="col-md-6">
                            <div class="text-right">
                                <div id="booking-summary" class="text-muted">
                                    <small>Select services to see booking summary</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // Handle business selection change
    $('#business_id').change(function() {
        const businessId = $(this).val();
        if (businessId) {
            // Reload page with business_id parameter
            window.location.href = '{{ route("admin.bookings.create") }}?business_id=' + businessId;
        }
    });

    // Handle customer selection
    $('#customer_id').change(function() {
        const selectedOption = $(this).find('option:selected');
        if (selectedOption.val()) {
            $('#customer_name').val(selectedOption.data('name'));
            $('#customer_email').val(selectedOption.data('email'));
            $('#customer_phone').val(selectedOption.data('phone'));
        } else {
            $('#customer_name').val('');
            $('#customer_email').val('');
            $('#customer_phone').val('');
        }
    });

    // Handle service selection and calculate totals
    $('.service-checkbox').change(function() {
        updateBookingSummary();
    });

    function updateBookingSummary() {
        let totalPrice = 0;
        let totalDuration = 0;
        let selectedServices = [];

        $('.service-checkbox:checked').each(function() {
            const price = parseFloat($(this).data('price')) || 0;
            const duration = parseInt($(this).data('duration')) || 0;
            const serviceName = $(this).next('label').find('strong').text();

            totalPrice += price;
            totalDuration += duration;
            selectedServices.push(serviceName);
        });

        if (selectedServices.length > 0) {
            const hours = Math.floor(totalDuration / 60);
            const minutes = totalDuration % 60;
            const durationText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;

            $('#booking-summary').html(`
                <strong>Selected Services:</strong> ${selectedServices.join(', ')}<br>
                <strong>Total Duration:</strong> ${durationText}<br>
                <strong>Total Price:</strong> $${totalPrice.toFixed(2)}
            `);
        } else {
            $('#booking-summary').html('<small>Select services to see booking summary</small>');
        }
    }

    // Initialize summary
    updateBookingSummary();
});
</script>
@stop
