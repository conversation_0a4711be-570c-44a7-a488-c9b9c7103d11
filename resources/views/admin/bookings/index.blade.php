@extends('admin.layouts.app')

@section('title', 'Bookings')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Bookings</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.calendar.index') }}" class="btn btn-info">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Calendar View
                </a>
                @can('create bookings')
                    <a href="{{ route('admin.bookings.create', ['business_id' => $business?->id]) }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        New Booking
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <!-- Quick Stats -->
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $bookings->where('status', 'confirmed')->count() }}</h3>
                    <p>Confirmed Today</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $bookings->where('status', 'completed')->count() }}</h3>
                    <p>Completed</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $bookings->where('status', 'pending')->count() }}</h3>
                    <p>Pending</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $bookings->where('status', 'cancelled')->count() }}</h3>
                    <p>Cancelled</p>
                </div>
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.bookings.index') }}">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select class="form-control" id="business_id" name="business_id" onchange="this.form.submit()">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Booking # or customer">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                @foreach($statuses as $status)
                                    <option value="{{ $status }}" 
                                            {{ request('status') == $status ? 'selected' : '' }}>
                                        {{ ucfirst($status) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="payment_status">Payment</label>
                            <select class="form-control" id="payment_status" name="payment_status">
                                <option value="">All Payment Status</option>
                                @foreach($paymentStatuses as $paymentStatus)
                                    <option value="{{ $paymentStatus }}" 
                                            {{ request('payment_status') == $paymentStatus ? 'selected' : '' }}>
                                        {{ ucfirst($paymentStatus) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="{{ request('date_to') }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search mr-2"></i>
                            Filter
                        </button>
                        <a href="{{ route('admin.bookings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times mr-2"></i>
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bookings List -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-calendar-check mr-2"></i>
                Bookings ({{ $bookings->total() }})
            </h3>
        </div>
        <div class="card-body p-0">
            @if($bookings->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Booking #</th>
                                <th>Customer</th>
                                <th>Business</th>
                                <th>Services</th>
                                <th>Date & Time</th>
                                <th>Duration</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($bookings as $booking)
                                <tr>
                                    <td>
                                        <strong>{{ $booking->booking_number }}</strong>
                                        <br><small class="text-muted">{{ $booking->created_at->format('M d, Y') }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ $booking->customer_name }}</strong>
                                        <br><small class="text-muted">{{ $booking->customer_email }}</small>
                                        @if($booking->customer_phone)
                                            <br><small class="text-muted">{{ $booking->customer_phone }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $booking->business->name }}</strong>
                                        @if($booking->branch)
                                            <br><small class="text-muted">{{ $booking->branch->name }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @foreach($booking->bookingServices as $bookingService)
                                            <span class="badge badge-info">
                                                {{ $bookingService->service->name }}
                                                @if($bookingService->quantity > 1)
                                                    ({{ $bookingService->quantity }})
                                                @endif
                                            </span>
                                            @if(!$loop->last)<br>@endif
                                        @endforeach
                                    </td>
                                    <td>
                                        <strong>{{ $booking->start_datetime->format('M d, Y') }}</strong>
                                        <br>{{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}
                                        @if($booking->start_datetime->isToday())
                                            <br><span class="badge badge-primary">Today</span>
                                        @elseif($booking->start_datetime->isTomorrow())
                                            <br><span class="badge badge-info">Tomorrow</span>
                                        @elseif($booking->start_datetime->isPast())
                                            <br><span class="badge badge-secondary">Past</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">
                                            {{ $booking->formatted_duration }}
                                        </span>
                                        @if($booking->participant_count > 1)
                                            <br><small class="text-muted">{{ $booking->participant_count }} people</small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>${{ number_format($booking->total_amount, 2) }}</strong>
                                        @if($booking->paid_amount > 0)
                                            <br><small class="text-success">
                                                Paid: ${{ number_format($booking->paid_amount, 2) }}
                                            </small>
                                        @endif
                                        @if($booking->remaining_amount > 0)
                                            <br><small class="text-danger">
                                                Due: ${{ number_format($booking->remaining_amount, 2) }}
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $booking->status_color }}">
                                            {{ ucfirst($booking->status) }}
                                        </span>
                                        <br><span class="badge badge-{{ $booking->payment_status_color }}">
                                            {{ ucfirst(str_replace('_', ' ', $booking->payment_status)) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical" role="group">
                                            <a href="{{ route('admin.bookings.show', $booking) }}" 
                                               class="btn btn-sm btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($booking->can_be_checked_in)
                                                <form action="{{ route('admin.bookings.check-in', $booking) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-success" title="Check In">
                                                        <i class="fas fa-sign-in-alt"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            @if($booking->can_be_checked_out)
                                                <form action="{{ route('admin.bookings.check-out', $booking) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-primary" title="Check Out">
                                                        <i class="fas fa-sign-out-alt"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            @can('edit bookings')
                                                <a href="{{ route('admin.bookings.edit', $booking) }}" 
                                                   class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                    <h4>No bookings found</h4>
                    @if($business)
                        <p class="text-muted">No bookings found for the selected criteria.</p>
                        <a href="{{ route('admin.bookings.create', ['business_id' => $business->id]) }}" class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Create Booking
                        </a>
                    @else
                        <p class="text-muted">Select a business to view bookings or adjust your filters.</p>
                    @endif
                </div>
            @endif
        </div>
        @if($bookings->hasPages())
            <div class="card-footer">
                {{ $bookings->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</div>
@stop
