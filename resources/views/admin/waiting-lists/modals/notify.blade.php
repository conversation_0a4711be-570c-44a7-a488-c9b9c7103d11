<!-- Notify Customer Modal -->
<div class="modal fade" id="notifyModal" tabindex="-1" role="dialog" aria-labelledby="notifyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notifyModalLabel">
                    <i class="fas fa-bell mr-2"></i>
                    Notify Customer
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.waiting-lists.notify', $waitingList) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Customer:</strong> {{ $waitingList->customer_name }} ({{ $waitingList->customer_email }})
                    </div>

                    <div class="form-group">
                        <label for="notification_type">Notification Type</label>
                        <select name="notification_type" id="notification_type" class="form-control" required>
                            <option value="">Select notification type</option>
                            <option value="availability">Slot Available</option>
                            <option value="reminder">Waiting List Reminder</option>
                            <option value="custom">Custom Message</option>
                        </select>
                    </div>

                    <div class="form-group" id="available_slots_group" style="display: none;">
                        <label for="available_slots">Available Time Slots</label>
                        <div id="available_slots_list">
                            <!-- Available slots will be loaded here -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notification_method">Send Via</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="send_email" id="send_email" value="1" checked>
                            <label class="form-check-label" for="send_email">
                                Email ({{ $waitingList->customer_email }})
                            </label>
                        </div>
                        @if($waitingList->customer_phone)
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="send_sms" id="send_sms" value="1">
                                <label class="form-check-label" for="send_sms">
                                    SMS ({{ $waitingList->customer_phone }})
                                </label>
                            </div>
                        @endif
                    </div>

                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" name="subject" id="subject" class="form-control" 
                               placeholder="Notification subject">
                    </div>

                    <div class="form-group">
                        <label for="message">Message</label>
                        <textarea name="message" id="message" class="form-control" rows="4" 
                                  placeholder="Enter your message to the customer"></textarea>
                        <small class="form-text text-muted">
                            Available variables: {customer_name}, {business_name}, {service_name}
                        </small>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="update_status" id="update_status" value="1" checked>
                            <label class="form-check-label" for="update_status">
                                Update waiting list status to "Notified"
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Notification
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#notification_type').change(function() {
        var type = $(this).val();
        
        if (type === 'availability') {
            $('#available_slots_group').show();
            loadAvailableSlots();
            $('#subject').val('Available Time Slot - {{ $waitingList->business->name }}');
            $('#message').val('Hi {customer_name},\n\nGood news! We have available time slots that match your preferences.\n\nPlease review the available options and book your appointment.\n\nBest regards,\n{{ $waitingList->business->name }}');
        } else if (type === 'reminder') {
            $('#available_slots_group').hide();
            $('#subject').val('Waiting List Update - {{ $waitingList->business->name }}');
            $('#message').val('Hi {customer_name},\n\nThis is a reminder that you are on our waiting list for {service_name}.\n\nWe will notify you as soon as a suitable time slot becomes available.\n\nBest regards,\n{{ $waitingList->business->name }}');
        } else if (type === 'custom') {
            $('#available_slots_group').hide();
            $('#subject').val('');
            $('#message').val('');
        } else {
            $('#available_slots_group').hide();
        }
    });
});

function loadAvailableSlots() {
    $('#available_slots_list').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading available slots...</div>');
    
    $.post('{{ route("admin.waiting-lists.find-matches") }}', {
        _token: '{{ csrf_token() }}',
        waiting_list_id: {{ $waitingList->id }}
    })
    .done(function(data) {
        if (data.slots && data.slots.length > 0) {
            let html = '<div class="list-group">';
            data.slots.forEach(function(slot, index) {
                html += '<div class="list-group-item">';
                html += '<div class="form-check">';
                html += '<input class="form-check-input" type="checkbox" name="selected_slots[]" value="' + slot.id + '" id="slot_' + index + '">';
                html += '<label class="form-check-label" for="slot_' + index + '">';
                html += '<strong>' + slot.date + '</strong> at ' + slot.time + ' (' + slot.duration + ' minutes)';
                html += '</label>';
                html += '</div>';
                html += '</div>';
            });
            html += '</div>';
            $('#available_slots_list').html(html);
        } else {
            $('#available_slots_list').html('<div class="alert alert-warning">No matching slots found at this time.</div>');
        }
    })
    .fail(function() {
        $('#available_slots_list').html('<div class="alert alert-danger">Error loading available slots.</div>');
    });
}
</script>
