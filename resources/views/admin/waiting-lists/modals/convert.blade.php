<!-- Convert to Booking Modal -->
<div class="modal fade" id="convertModal" tabindex="-1" role="dialog" aria-labelledby="convertModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="convertModalLabel">
                    <i class="fas fa-calendar-plus mr-2"></i>
                    Convert to Booking
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.waiting-lists.convert-to-booking', $waitingList) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Customer:</strong> {{ $waitingList->customer_name }} ({{ $waitingList->customer_email }})
                        @if($waitingList->service)
                            <br><strong>Service:</strong> {{ $waitingList->service->name }}
                        @endif
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="booking_date">Booking Date <span class="text-danger">*</span></label>
                                <input type="date" name="booking_date" id="booking_date" class="form-control" 
                                       min="{{ date('Y-m-d') }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="booking_time">Booking Time <span class="text-danger">*</span></label>
                                <input type="time" name="booking_time" id="booking_time" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    @if(!$waitingList->service)
                        <div class="form-group">
                            <label for="service_id">Service <span class="text-danger">*</span></label>
                            <select name="service_id" id="service_id" class="form-control" required>
                                <option value="">Select Service</option>
                                @foreach($waitingList->business->services()->active()->get() as $service)
                                    <option value="{{ $service->id }}" 
                                            data-duration="{{ $service->duration_minutes }}"
                                            data-price="{{ $service->base_price }}">
                                        {{ $service->name }} ({{ $service->duration_minutes }}min - ${{ $service->base_price }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @else
                        <input type="hidden" name="service_id" value="{{ $waitingList->service->id }}">
                    @endif

                    <div class="form-group">
                        <label for="participant_count">Number of Participants</label>
                        <input type="number" name="participant_count" id="participant_count" 
                               class="form-control" value="1" min="1" 
                               max="{{ $waitingList->service ? $waitingList->service->max_participants : 10 }}">
                    </div>

                    <div class="form-group">
                        <label for="notes">Booking Notes</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="Any special notes for this booking">{{ $waitingList->notes }}</textarea>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="send_confirmation" id="send_confirmation" value="1" checked>
                            <label class="form-check-label" for="send_confirmation">
                                Send booking confirmation to customer
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="remove_from_waiting_list" id="remove_from_waiting_list" value="1" checked>
                            <label class="form-check-label" for="remove_from_waiting_list">
                                Remove from waiting list after booking
                            </label>
                        </div>
                    </div>

                    <!-- Booking Summary -->
                    <div class="card bg-light" id="booking_summary" style="display: none;">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-receipt mr-2"></i>
                                Booking Summary
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Service:</strong> <span id="summary_service">-</span><br>
                                    <strong>Duration:</strong> <span id="summary_duration">-</span><br>
                                    <strong>Date & Time:</strong> <span id="summary_datetime">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Customer:</strong> {{ $waitingList->customer_name }}<br>
                                    <strong>Participants:</strong> <span id="summary_participants">1</span><br>
                                    <strong>Total Price:</strong> $<span id="summary_price">0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calendar-check mr-2"></i>
                        Create Booking
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Update summary when form fields change
    $('#booking_date, #booking_time, #service_id, #participant_count').on('change', updateBookingSummary);
    
    function updateBookingSummary() {
        var date = $('#booking_date').val();
        var time = $('#booking_time').val();
        var serviceSelect = $('#service_id');
        var selectedService = serviceSelect.find('option:selected');
        var participants = $('#participant_count').val() || 1;
        
        if (date && time && (selectedService.val() || {{ $waitingList->service ? $waitingList->service->id : 'null' }})) {
            var serviceName, duration, price;
            
            @if($waitingList->service)
                serviceName = '{{ $waitingList->service->name }}';
                duration = '{{ $waitingList->service->duration_minutes }}';
                price = {{ $waitingList->service->base_price }};
            @else
                serviceName = selectedService.text().split(' (')[0];
                duration = selectedService.data('duration');
                price = selectedService.data('price');
            @endif
            
            var totalPrice = price * participants;
            var datetime = new Date(date + 'T' + time).toLocaleString();
            
            $('#summary_service').text(serviceName);
            $('#summary_duration').text(duration + ' minutes');
            $('#summary_datetime').text(datetime);
            $('#summary_participants').text(participants);
            $('#summary_price').text(totalPrice.toFixed(2));
            
            $('#booking_summary').show();
        } else {
            $('#booking_summary').hide();
        }
    }
    
    // Check availability when date/time changes
    $('#booking_date, #booking_time').on('change', function() {
        var date = $('#booking_date').val();
        var time = $('#booking_time').val();
        
        if (date && time) {
            checkAvailability(date, time);
        }
    });
    
    function checkAvailability(date, time) {
        // This would make an AJAX call to check if the slot is available
        // For now, we'll just show a placeholder
        console.log('Checking availability for', date, time);
    }
});
</script>
