<!-- Delete Waiting List Entry Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle mr-2 text-danger"></i>
                    Confirm Deletion
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
                
                <p>Are you sure you want to remove <strong>{{ $waitingList->customer_name }}</strong> from the waiting list?</p>
                
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Entry Details:</h6>
                        <ul class="list-unstyled mb-0">
                            <li><strong>Customer:</strong> {{ $waitingList->customer_name }}</li>
                            <li><strong>Email:</strong> {{ $waitingList->customer_email }}</li>
                            <li><strong>Business:</strong> {{ $waitingList->business->name }}</li>
                            @if($waitingList->service)
                                <li><strong>Service:</strong> {{ $waitingList->service->name }}</li>
                            @endif
                            <li><strong>Priority:</strong> {{ $waitingList->priority }}/10</li>
                            <li><strong>Created:</strong> {{ $waitingList->created_at->format('M d, Y g:i A') }}</li>
                        </ul>
                    </div>
                </div>

                <div class="form-group mt-3">
                    <label for="deletion_reason">Reason for Removal (Optional)</label>
                    <select name="deletion_reason" id="deletion_reason" class="form-control">
                        <option value="">Select a reason</option>
                        <option value="customer_request">Customer requested removal</option>
                        <option value="no_longer_interested">Customer no longer interested</option>
                        <option value="converted_to_booking">Successfully converted to booking</option>
                        <option value="duplicate_entry">Duplicate entry</option>
                        <option value="invalid_information">Invalid customer information</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div class="form-group" id="other_reason_group" style="display: none;">
                    <label for="other_reason">Please specify:</label>
                    <textarea name="other_reason" id="other_reason" class="form-control" rows="2" 
                              placeholder="Please provide more details..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="notify_customer" id="notify_customer" value="1">
                        <label class="form-check-label" for="notify_customer">
                            Notify customer about removal from waiting list
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.waiting-lists.destroy', $waitingList) }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <input type="hidden" name="deletion_reason" id="hidden_deletion_reason">
                    <input type="hidden" name="other_reason" id="hidden_other_reason">
                    <input type="hidden" name="notify_customer" id="hidden_notify_customer">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash mr-2"></i>
                        Remove from Waiting List
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Show/hide other reason field
    $('#deletion_reason').change(function() {
        if ($(this).val() === 'other') {
            $('#other_reason_group').show();
        } else {
            $('#other_reason_group').hide();
        }
    });
    
    // Update hidden fields before form submission
    $('#deleteModal form').on('submit', function() {
        $('#hidden_deletion_reason').val($('#deletion_reason').val());
        $('#hidden_other_reason').val($('#other_reason').val());
        $('#hidden_notify_customer').val($('#notify_customer').is(':checked') ? '1' : '0');
    });
});
</script>
