@extends('admin.layouts.app')

@section('title', 'Add to Waiting List')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Add to Waiting List</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.waiting-lists.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Waiting Lists
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2"></i>
                        Waiting List Entry
                    </h3>
                </div>
                
                <form action="{{ route('admin.waiting-lists.store') }}" method="POST">
                    @csrf
                    
                    <div class="card-body">
                        <!-- Business Selection -->
                        <div class="form-group">
                            <label for="business_id">Business <span class="text-danger">*</span></label>
                            <select name="business_id" id="business_id" class="form-control @error('business_id') is-invalid @enderror" required>
                                <option value="">Select Business</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ old('business_id', $business?->id) == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('business_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Service Selection -->
                        <div class="form-group">
                            <label for="service_id">Service</label>
                            <select name="service_id" id="service_id" class="form-control @error('service_id') is-invalid @enderror">
                                <option value="">Any Service</option>
                                @foreach($services as $serviceOption)
                                    <option value="{{ $serviceOption->id }}" 
                                            {{ old('service_id', $service?->id) == $serviceOption->id ? 'selected' : '' }}>
                                        {{ $serviceOption->name }} ({{ $serviceOption->duration_minutes }}min - ${{ $serviceOption->base_price }})
                                    </option>
                                @endforeach
                            </select>
                            @error('service_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Customer Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                                    <input type="text" name="customer_name" id="customer_name" 
                                           class="form-control @error('customer_name') is-invalid @enderror"
                                           value="{{ old('customer_name') }}" required>
                                    @error('customer_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_email">Customer Email <span class="text-danger">*</span></label>
                                    <input type="email" name="customer_email" id="customer_email" 
                                           class="form-control @error('customer_email') is-invalid @enderror"
                                           value="{{ old('customer_email') }}" required>
                                    @error('customer_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_phone">Customer Phone</label>
                                    <input type="text" name="customer_phone" id="customer_phone" 
                                           class="form-control @error('customer_phone') is-invalid @enderror"
                                           value="{{ old('customer_phone') }}">
                                    @error('customer_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">Priority (0-10)</label>
                                    <input type="number" name="priority" id="priority" 
                                           class="form-control @error('priority') is-invalid @enderror"
                                           value="{{ old('priority', 0) }}" min="0" max="10">
                                    @error('priority')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Higher numbers = higher priority</small>
                                </div>
                            </div>
                        </div>

                        <!-- Preferred Date Range -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="preferred_date_from">Preferred Date From</label>
                                    <input type="date" name="preferred_date_from" id="preferred_date_from" 
                                           class="form-control @error('preferred_date_from') is-invalid @enderror"
                                           value="{{ old('preferred_date_from') }}">
                                    @error('preferred_date_from')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="preferred_date_to">Preferred Date To</label>
                                    <input type="date" name="preferred_date_to" id="preferred_date_to" 
                                           class="form-control @error('preferred_date_to') is-invalid @enderror"
                                           value="{{ old('preferred_date_to') }}">
                                    @error('preferred_date_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea name="notes" id="notes" rows="3" 
                                      class="form-control @error('notes') is-invalid @enderror">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Add to Waiting List
                        </button>
                        <a href="{{ route('admin.waiting-lists.index') }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Information
                    </h3>
                </div>
                <div class="card-body">
                    <p><strong>Waiting List Purpose:</strong></p>
                    <p class="text-muted">Add customers to the waiting list when their preferred time slots are not available. They will be automatically notified when matching slots become available.</p>
                    
                    <p><strong>Priority System:</strong></p>
                    <ul class="text-muted">
                        <li>0-3: Low priority</li>
                        <li>4-6: Normal priority</li>
                        <li>7-10: High priority</li>
                    </ul>
                    
                    <p><strong>Automatic Matching:</strong></p>
                    <p class="text-muted">The system will automatically check for available slots and notify customers based on their preferences and priority level.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Load services when business changes
    $('#business_id').change(function() {
        var businessId = $(this).val();
        var serviceSelect = $('#service_id');
        
        serviceSelect.html('<option value="">Loading...</option>');
        
        if (businessId) {
            $.get('/admin/businesses/' + businessId + '/services', function(services) {
                serviceSelect.html('<option value="">Any Service</option>');
                $.each(services, function(index, service) {
                    serviceSelect.append('<option value="' + service.id + '">' + 
                                       service.name + ' (' + service.duration_minutes + 'min - $' + service.base_price + ')</option>');
                });
            });
        } else {
            serviceSelect.html('<option value="">Any Service</option>');
        }
    });
});
</script>
@stop
