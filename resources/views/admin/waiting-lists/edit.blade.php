@extends('admin.layouts.app')

@section('title', 'Edit Waiting List Entry')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Edit Waiting List Entry</h1>
            <p class="text-muted">{{ $waitingList->customer_name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.waiting-lists.show', $waitingList) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Entry
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Waiting List Entry
                    </h3>
                </div>
                
                <form action="{{ route('admin.waiting-lists.update', $waitingList) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="card-body">
                        <!-- Business Selection -->
                        <div class="form-group">
                            <label for="business_id">Business <span class="text-danger">*</span></label>
                            <select name="business_id" id="business_id" class="form-control @error('business_id') is-invalid @enderror" required>
                                <option value="">Select Business</option>
                                @foreach($businesses as $business)
                                    <option value="{{ $business->id }}" 
                                            {{ old('business_id', $waitingList->business_id) == $business->id ? 'selected' : '' }}>
                                        {{ $business->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('business_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Service Selection -->
                        <div class="form-group">
                            <label for="service_id">Service</label>
                            <select name="service_id" id="service_id" class="form-control @error('service_id') is-invalid @enderror">
                                <option value="">Any Service</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}" 
                                            {{ old('service_id', $waitingList->service_id) == $service->id ? 'selected' : '' }}>
                                        {{ $service->name }} ({{ $service->duration_minutes }}min - ${{ $service->base_price }})
                                    </option>
                                @endforeach
                            </select>
                            @error('service_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Customer Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                                    <input type="text" name="customer_name" id="customer_name" 
                                           class="form-control @error('customer_name') is-invalid @enderror"
                                           value="{{ old('customer_name', $waitingList->customer_name) }}" required>
                                    @error('customer_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_email">Customer Email <span class="text-danger">*</span></label>
                                    <input type="email" name="customer_email" id="customer_email" 
                                           class="form-control @error('customer_email') is-invalid @enderror"
                                           value="{{ old('customer_email', $waitingList->customer_email) }}" required>
                                    @error('customer_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer_phone">Customer Phone</label>
                                    <input type="text" name="customer_phone" id="customer_phone" 
                                           class="form-control @error('customer_phone') is-invalid @enderror"
                                           value="{{ old('customer_phone', $waitingList->customer_phone) }}">
                                    @error('customer_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">Priority (0-10)</label>
                                    <input type="number" name="priority" id="priority" 
                                           class="form-control @error('priority') is-invalid @enderror"
                                           value="{{ old('priority', $waitingList->priority) }}" min="0" max="10">
                                    @error('priority')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Higher numbers = higher priority</small>
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control @error('status') is-invalid @enderror">
                                <option value="active" {{ old('status', $waitingList->status) === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="notified" {{ old('status', $waitingList->status) === 'notified' ? 'selected' : '' }}>Notified</option>
                                <option value="converted" {{ old('status', $waitingList->status) === 'converted' ? 'selected' : '' }}>Converted</option>
                                <option value="cancelled" {{ old('status', $waitingList->status) === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Preferred Date Range -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="preferred_date_from">Preferred Date From</label>
                                    <input type="date" name="preferred_date_from" id="preferred_date_from" 
                                           class="form-control @error('preferred_date_from') is-invalid @enderror"
                                           value="{{ old('preferred_date_from', $waitingList->preferred_date_from ? $waitingList->preferred_date_from->format('Y-m-d') : '') }}">
                                    @error('preferred_date_from')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="preferred_date_to">Preferred Date To</label>
                                    <input type="date" name="preferred_date_to" id="preferred_date_to" 
                                           class="form-control @error('preferred_date_to') is-invalid @enderror"
                                           value="{{ old('preferred_date_to', $waitingList->preferred_date_to ? $waitingList->preferred_date_to->format('Y-m-d') : '') }}">
                                    @error('preferred_date_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea name="notes" id="notes" rows="3" 
                                      class="form-control @error('notes') is-invalid @enderror">{{ old('notes', $waitingList->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Update Entry
                        </button>
                        <a href="{{ route('admin.waiting-lists.show', $waitingList) }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Entry Information
                    </h3>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Created:</th>
                            <td>{{ $waitingList->created_at->format('M d, Y g:i A') }}</td>
                        </tr>
                        <tr>
                            <th>Last Updated:</th>
                            <td>{{ $waitingList->updated_at->format('M d, Y g:i A') }}</td>
                        </tr>
                        <tr>
                            <th>Days Waiting:</th>
                            <td>{{ $waitingList->created_at->diffInDays() }} days</td>
                        </tr>
                        <tr>
                            <th>Notifications Sent:</th>
                            <td>{{ $waitingList->notifications ? $waitingList->notifications->count() : 0 }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Status Guide
                    </h3>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><span class="badge badge-success">Active</span> - Actively looking for slots</li>
                        <li><span class="badge badge-warning">Notified</span> - Customer has been notified of available slots</li>
                        <li><span class="badge badge-info">Converted</span> - Successfully converted to booking</li>
                        <li><span class="badge badge-secondary">Cancelled</span> - No longer interested</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Load services when business changes
    $('#business_id').change(function() {
        var businessId = $(this).val();
        var serviceSelect = $('#service_id');
        
        serviceSelect.html('<option value="">Loading...</option>');
        
        if (businessId) {
            $.get('/admin/businesses/' + businessId + '/services', function(services) {
                serviceSelect.html('<option value="">Any Service</option>');
                $.each(services, function(index, service) {
                    serviceSelect.append('<option value="' + service.id + '">' + 
                                       service.name + ' (' + service.duration_minutes + 'min - $' + service.base_price + ')</option>');
                });
            });
        } else {
            serviceSelect.html('<option value="">Any Service</option>');
        }
    });
});
</script>
@stop
