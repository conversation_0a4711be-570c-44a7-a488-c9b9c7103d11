@extends('admin.layouts.app')

@section('title', 'Waiting Lists')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Waiting Lists</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('create waiting lists')
                    <a href="{{ route('admin.waiting-lists.create', ['business_id' => $business?->id]) }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add to Waiting List
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.waiting-lists.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select class="form-control" id="business_id" name="business_id">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="service">Service</label>
                            <select class="form-control" id="service" name="service">
                                <option value="">All Services</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}" 
                                            {{ request('service') == $service->id ? 'selected' : '' }}>
                                        {{ $service->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Statuses</option>
                                @foreach($statuses as $status)
                                    <option value="{{ $status }}" 
                                            {{ request('status') === $status ? 'selected' : '' }}>
                                        {{ ucfirst($status) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Search customers...">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_from">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="{{ request('date_from') }}">
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_to">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="{{ request('date_to') }}">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-block">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search mr-2"></i>
                                    Search
                                </button>
                                <a href="{{ route('admin.waiting-lists.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times mr-2"></i>
                                    Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Waiting Lists -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-clock mr-2"></i>
                Waiting Lists ({{ $waitingLists->total() }})
            </h3>
        </div>
        <div class="card-body p-0">
            @if($waitingLists->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Service</th>
                                <th>Business</th>
                                <th>Preferred Date</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($waitingLists as $waitingList)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $waitingList->customer_name }}</strong>
                                            <br><small class="text-muted">{{ $waitingList->customer_email }}</small>
                                            @if($waitingList->customer_phone)
                                                <br><small class="text-muted">{{ $waitingList->customer_phone }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $waitingList->service->name }}</strong>
                                            <br><small class="text-muted">{{ $waitingList->participant_count }} {{ Str::plural('participant', $waitingList->participant_count) }}</small>
                                        </div>
                                    </td>
                                    <td>{{ $waitingList->business->name }}</td>
                                    <td>
                                        <div>
                                            <strong>{{ $waitingList->preferred_date->format('M d, Y') }}</strong>
                                            @if($waitingList->preferred_time_start && $waitingList->preferred_time_end)
                                                <br><small class="text-muted">
                                                    {{ $waitingList->preferred_time_start->format('H:i') }} - 
                                                    {{ $waitingList->preferred_time_end->format('H:i') }}
                                                </small>
                                            @endif
                                            @if($waitingList->preferred_days_of_week)
                                                <br><small class="text-muted">
                                                    {{ implode(', ', $waitingList->preferred_days_names) }}
                                                </small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $waitingList->priority > 5 ? 'danger' : ($waitingList->priority > 2 ? 'warning' : 'secondary') }}">
                                            {{ $waitingList->priority }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $waitingList->status_color }}">
                                            {{ ucfirst($waitingList->status) }}
                                        </span>
                                        @if($waitingList->notified_at)
                                            <br><small class="text-muted">
                                                Notified: {{ $waitingList->notified_at->format('M d, H:i') }}
                                            </small>
                                        @endif
                                        @if($waitingList->expires_at && $waitingList->status === 'notified')
                                            <br><small class="text-{{ $waitingList->isExpired() ? 'danger' : 'warning' }}">
                                                Expires: {{ $waitingList->expires_at->format('M d, H:i') }}
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $waitingList->created_at->format('M d, Y') }}
                                        <br><small class="text-muted">{{ $waitingList->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.waiting-lists.show', $waitingList) }}"
                                               class="btn btn-sm btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @can('edit waiting lists')
                                                <a href="{{ route('admin.waiting-lists.edit', $waitingList) }}"
                                                   class="btn btn-sm btn-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                            @if($waitingList->status === 'active')
                                                @can('manage notifications')
                                                    <button type="button" class="btn btn-sm btn-warning" 
                                                            title="Notify Customer" 
                                                            onclick="showNotifyModal({{ $waitingList->id }})">
                                                        <i class="fas fa-bell"></i>
                                                    </button>
                                                @endcan
                                                @can('create bookings')
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            title="Convert to Booking"
                                                            onclick="showConvertModal({{ $waitingList->id }})">
                                                        <i class="fas fa-calendar-plus"></i>
                                                    </button>
                                                @endcan
                                            @endif
                                            @can('delete waiting lists')
                                                <form action="{{ route('admin.waiting-lists.destroy', $waitingList) }}" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this waiting list entry?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h4>No Waiting List Entries Found</h4>
                    <p class="text-muted">
                        @if(request()->hasAny(['search', 'business_id', 'service', 'status', 'date_from', 'date_to']))
                            No waiting list entries match your current filters.
                        @else
                            No customers are currently on the waiting list.
                        @endif
                    </p>
                    @can('create waiting lists')
                        <a href="{{ route('admin.waiting-lists.create', ['business_id' => $business?->id]) }}" 
                           class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Add Customer to Waiting List
                        </a>
                    @endcan
                </div>
            @endif
        </div>
        
        @if($waitingLists->hasPages())
            <div class="card-footer">
                {{ $waitingLists->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
@stop

@section('scripts')
<script>
function showNotifyModal(waitingListId) {
    // Implementation for notify modal
    alert('Notify functionality would be implemented here');
}

function showConvertModal(waitingListId) {
    // Implementation for convert to booking modal
    alert('Convert to booking functionality would be implemented here');
}
</script>
@stop
