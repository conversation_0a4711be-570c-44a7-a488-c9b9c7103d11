@extends('admin.layouts.app')

@section('title', 'Waiting List Entry')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Waiting List Entry</h1>
            <p class="text-muted">{{ $waitingList->customer_name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.waiting-lists.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Waiting Lists
                </a>
                @can('edit waiting lists')
                    <a href="{{ route('admin.waiting-lists.edit', $waitingList) }}" class="btn btn-primary">
                        <i class="fas fa-edit mr-2"></i>
                        Edit
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <!-- Waiting List Details -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock mr-2"></i>
                        Waiting List Details
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-{{ $waitingList->status === 'active' ? 'success' : ($waitingList->status === 'notified' ? 'warning' : 'secondary') }}">
                            {{ ucfirst($waitingList->status) }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Business:</th>
                                    <td>{{ $waitingList->business->name }}</td>
                                </tr>
                                <tr>
                                    <th>Service:</th>
                                    <td>
                                        @if($waitingList->service)
                                            {{ $waitingList->service->name }}
                                            <br><small class="text-muted">
                                                {{ $waitingList->service->duration_minutes }}min - ${{ $waitingList->service->base_price }}
                                            </small>
                                        @else
                                            <span class="text-muted">Any Service</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Priority:</th>
                                    <td>
                                        <span class="badge badge-{{ $waitingList->priority >= 7 ? 'danger' : ($waitingList->priority >= 4 ? 'warning' : 'secondary') }}">
                                            {{ $waitingList->priority }}/10
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created:</th>
                                    <td>
                                        {{ $waitingList->created_at->format('M d, Y g:i A') }}
                                        <br><small class="text-muted">{{ $waitingList->created_at->diffForHumans() }}</small>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Customer Name:</th>
                                    <td>{{ $waitingList->customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Email:</th>
                                    <td>
                                        <a href="mailto:{{ $waitingList->customer_email }}">
                                            {{ $waitingList->customer_email }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Phone:</th>
                                    <td>
                                        @if($waitingList->customer_phone)
                                            <a href="tel:{{ $waitingList->customer_phone }}">
                                                {{ $waitingList->customer_phone }}
                                            </a>
                                        @else
                                            <span class="text-muted">Not provided</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Preferred Dates:</th>
                                    <td>
                                        @if($waitingList->preferred_date_from || $waitingList->preferred_date_to)
                                            @if($waitingList->preferred_date_from)
                                                From: {{ \Carbon\Carbon::parse($waitingList->preferred_date_from)->format('M d, Y') }}
                                            @endif
                                            @if($waitingList->preferred_date_to)
                                                <br>To: {{ \Carbon\Carbon::parse($waitingList->preferred_date_to)->format('M d, Y') }}
                                            @endif
                                        @else
                                            <span class="text-muted">Any date</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    @if($waitingList->notes)
                        <div class="row mt-3">
                            <div class="col-12">
                                <h5>Notes:</h5>
                                <div class="alert alert-info">
                                    {{ $waitingList->notes }}
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Notification History -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bell mr-2"></i>
                        Notification History
                    </h3>
                </div>
                <div class="card-body">
                    @if($waitingList->notifications && $waitingList->notifications->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Message</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($waitingList->notifications as $notification)
                                        <tr>
                                            <td>{{ $notification->created_at->format('M d, Y g:i A') }}</td>
                                            <td>
                                                <span class="badge badge-info">{{ ucfirst($notification->type) }}</span>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $notification->status === 'sent' ? 'success' : ($notification->status === 'failed' ? 'danger' : 'warning') }}">
                                                    {{ ucfirst($notification->status) }}
                                                </span>
                                            </td>
                                            <td>{{ $notification->message }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No notifications sent yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bolt mr-2"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body">
                    @if($waitingList->status === 'active')
                        <button type="button" class="btn btn-success btn-block mb-2" onclick="showNotifyModal()">
                            <i class="fas fa-bell mr-2"></i>
                            Notify Customer
                        </button>
                        
                        <button type="button" class="btn btn-primary btn-block mb-2" onclick="showConvertModal()">
                            <i class="fas fa-calendar-plus mr-2"></i>
                            Convert to Booking
                        </button>
                    @endif
                    
                    <a href="{{ route('admin.waiting-lists.edit', $waitingList) }}" class="btn btn-warning btn-block mb-2">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Entry
                    </a>
                    
                    <button type="button" class="btn btn-danger btn-block" onclick="showDeleteModal()">
                        <i class="fas fa-trash mr-2"></i>
                        Remove from List
                    </button>
                </div>
            </div>

            <!-- Matching Availability -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-search mr-2"></i>
                        Available Slots
                    </h3>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-info btn-block" onclick="findMatches()">
                        <i class="fas fa-sync mr-2"></i>
                        Find Matching Slots
                    </button>
                    
                    <div id="matching-slots" class="mt-3">
                        <!-- Matching slots will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Statistics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="description-block">
                                <h5 class="description-header">{{ $waitingList->notifications ? $waitingList->notifications->count() : 0 }}</h5>
                                <span class="description-text">Notifications</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="description-block">
                                <h5 class="description-header">{{ $waitingList->created_at->diffInDays() }}</h5>
                                <span class="description-text">Days Waiting</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
@include('admin.waiting-lists.modals.notify')
@include('admin.waiting-lists.modals.convert')
@include('admin.waiting-lists.modals.delete')
@stop

@section('scripts')
<script>
function showNotifyModal() {
    $('#notifyModal').modal('show');
}

function showConvertModal() {
    $('#convertModal').modal('show');
}

function showDeleteModal() {
    $('#deleteModal').modal('show');
}

function findMatches() {
    $('#matching-slots').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Searching...</div>');
    
    $.post('{{ route("admin.waiting-lists.find-matches") }}', {
        _token: '{{ csrf_token() }}',
        waiting_list_id: {{ $waitingList->id }}
    })
    .done(function(data) {
        if (data.slots && data.slots.length > 0) {
            let html = '<div class="list-group">';
            data.slots.forEach(function(slot) {
                html += '<div class="list-group-item">';
                html += '<strong>' + slot.date + '</strong><br>';
                html += '<small class="text-muted">' + slot.time + ' (' + slot.duration + 'min)</small>';
                html += '</div>';
            });
            html += '</div>';
            $('#matching-slots').html(html);
        } else {
            $('#matching-slots').html('<div class="alert alert-info">No matching slots found at this time.</div>');
        }
    })
    .fail(function() {
        $('#matching-slots').html('<div class="alert alert-danger">Error searching for slots.</div>');
    });
}
</script>
@stop
