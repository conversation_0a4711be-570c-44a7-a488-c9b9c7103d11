@extends('admin.layouts.app')

@section('title', 'Revenue Reports')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Revenue Reports</h1>
            <p class="text-muted">Track revenue performance and trends</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.reports.export', 'revenue') }}?business_id={{ request('business_id') }}&date_from={{ $dateFrom }}&date_to={{ $dateTo }}" 
                   class="btn btn-success">
                    <i class="fas fa-download mr-2"></i>
                    Export CSV
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports.revenue-reports') }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select name="business_id" id="business_id" class="form-control">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="{{ $dateFrom }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" 
                                   value="{{ $dateTo }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search mr-2"></i>
                                Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Revenue Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>${{ number_format($stats['total_revenue'], 2) }}</h3>
                    <p>Total Revenue</p>
                </div>
                <div class="icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>${{ number_format($stats['paid_amount'], 2) }}</h3>
                    <p>Paid Amount</p>
                </div>
                <div class="icon">
                    <i class="fas fa-credit-card"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>${{ number_format($stats['pending_amount'], 2) }}</h3>
                    <p>Pending Amount</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3>${{ number_format($stats['average_booking_value'], 2) }}</h3>
                    <p>Avg Booking Value</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Revenue by Day Chart -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line mr-2"></i>
                        Revenue Trend
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="revenueByDayChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Revenue by Service Chart -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Revenue by Service
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="revenueByServiceChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Details Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list mr-2"></i>
                Revenue Details
            </h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="revenueTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Customer</th>
                            <th>Business</th>
                            <th>Service</th>
                            <th>Total Amount</th>
                            <th>Paid Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($bookings->take(50) as $booking)
                            <tr>
                                <td>{{ $booking->start_datetime->format('M d, Y H:i') }}</td>
                                <td>{{ $booking->customer_name }}</td>
                                <td>{{ $booking->business->name }}</td>
                                <td>
                                    @foreach($booking->bookingServices as $service)
                                        <span class="badge badge-info">{{ $service->service->name }}</span>
                                    @endforeach
                                </td>
                                <td>${{ number_format($booking->total_amount, 2) }}</td>
                                <td>${{ number_format($booking->paid_amount, 2) }}</td>
                                <td>
                                    @if($booking->paid_amount >= $booking->total_amount)
                                        <span class="badge badge-success">Paid</span>
                                    @elseif($booking->paid_amount > 0)
                                        <span class="badge badge-warning">Partial</span>
                                    @else
                                        <span class="badge badge-danger">Unpaid</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.bookings.show', $booking) }}" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@stop

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Revenue by Day Chart
    const revenueByDayCtx = document.getElementById('revenueByDayChart').getContext('2d');
    new Chart(revenueByDayCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($revenueByDay->keys()) !!},
            datasets: [{
                label: 'Revenue ($)',
                data: {!! json_encode($revenueByDay->values()) !!},
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Revenue: $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Revenue by Service Chart
    const revenueByServiceCtx = document.getElementById('revenueByServiceChart').getContext('2d');
    new Chart(revenueByServiceCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode($revenueByService->keys()) !!},
            datasets: [{
                data: {!! json_encode($revenueByService->values()) !!},
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40',
                    '#FF6384',
                    '#C9CBCF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': $' + context.parsed.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Initialize DataTable
    $('#revenueTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[0, 'desc']],
        columnDefs: [
            {
                targets: [4, 5], // Total Amount and Paid Amount columns
                render: function(data, type, row) {
                    if (type === 'display') {
                        return '$' + parseFloat(data).toLocaleString('en-US', {minimumFractionDigits: 2});
                    }
                    return data;
                }
            }
        ]
    });
</script>
@endsection
