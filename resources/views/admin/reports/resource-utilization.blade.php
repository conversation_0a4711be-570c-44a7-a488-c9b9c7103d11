@extends('admin.layouts.app')

@section('title', 'Resource Utilization Reports')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Resource Utilization</h1>
            <p class="text-muted">Monitor resource efficiency and usage patterns</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.reports.export', 'resource') }}?business_id={{ request('business_id') }}&date_from={{ $dateFrom }}&date_to={{ $dateTo }}" 
                   class="btn btn-success">
                    <i class="fas fa-download mr-2"></i>
                    Export CSV
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports.resource-utilization') }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select name="business_id" id="business_id" class="form-control">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="{{ $dateFrom }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" 
                                   value="{{ $dateTo }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search mr-2"></i>
                                Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Resource Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_resources'] }}</h3>
                    <p>Total Resources</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tools"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ number_format($stats['average_utilization'], 1) }}%</h3>
                    <p>Average Utilization</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['most_utilized']['resource']->name ?? 'N/A' }}</h3>
                    <p>Most Utilized</p>
                </div>
                <div class="icon">
                    <i class="fas fa-trophy"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $stats['least_utilized']['resource']->name ?? 'N/A' }}</h3>
                    <p>Least Utilized</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Utilization Chart -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Resource Utilization Rates
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="utilizationChart" style="height: 400px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Utilization Distribution -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Utilization Distribution
                    </h3>
                </div>
                <div class="card-body">
                    <div class="progress-group">
                        <span class="progress-text">High Utilization (>80%)</span>
                        <span class="float-right"><b>{{ $utilizationData->where('utilization_rate', '>', 80)->count() }}</b>/{{ $utilizationData->count() }}</span>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-success" style="width: {{ $utilizationData->count() > 0 ? ($utilizationData->where('utilization_rate', '>', 80)->count() / $utilizationData->count()) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                    <div class="progress-group">
                        <span class="progress-text">Medium Utilization (50-80%)</span>
                        <span class="float-right"><b>{{ $utilizationData->whereBetween('utilization_rate', [50, 80])->count() }}</b>/{{ $utilizationData->count() }}</span>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-warning" style="width: {{ $utilizationData->count() > 0 ? ($utilizationData->whereBetween('utilization_rate', [50, 80])->count() / $utilizationData->count()) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                    <div class="progress-group">
                        <span class="progress-text">Low Utilization (<50%)</span>
                        <span class="float-right"><b>{{ $utilizationData->where('utilization_rate', '<', 50)->count() }}</b>/{{ $utilizationData->count() }}</span>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-danger" style="width: {{ $utilizationData->count() > 0 ? ($utilizationData->where('utilization_rate', '<', 50)->count() / $utilizationData->count()) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Resource Details Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list mr-2"></i>
                Resource Utilization Details
            </h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="resourcesTable">
                    <thead>
                        <tr>
                            <th>Resource Name</th>
                            <th>Type</th>
                            <th>Business</th>
                            <th>Total Bookings</th>
                            <th>Booked Hours</th>
                            <th>Utilization Rate</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($utilizationData as $data)
                            <tr>
                                <td>{{ $data['resource']->name }}</td>
                                <td>{{ $data['resource']->resourceType->name ?? 'N/A' }}</td>
                                <td>{{ $data['resource']->business->name }}</td>
                                <td>
                                    <span class="badge badge-primary">{{ $data['total_bookings'] }}</span>
                                </td>
                                <td>{{ number_format($data['booked_hours'], 1) }}h</td>
                                <td>
                                    <div class="progress progress-sm">
                                        <div class="progress-bar 
                                            @if($data['utilization_rate'] > 80) bg-success
                                            @elseif($data['utilization_rate'] > 50) bg-warning
                                            @else bg-danger
                                            @endif" 
                                            style="width: {{ $data['utilization_rate'] }}%">
                                        </div>
                                    </div>
                                    <small>{{ number_format($data['utilization_rate'], 1) }}%</small>
                                </td>
                                <td>
                                    @if($data['utilization_rate'] > 80)
                                        <span class="badge badge-success">High</span>
                                    @elseif($data['utilization_rate'] > 50)
                                        <span class="badge badge-warning">Medium</span>
                                    @else
                                        <span class="badge badge-danger">Low</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.resources.show', $data['resource']) }}" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.resources.edit', $data['resource']) }}" 
                                       class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@stop

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Resource Utilization Chart
    const utilizationCtx = document.getElementById('utilizationChart').getContext('2d');
    new Chart(utilizationCtx, {
        type: 'bar',
        data: {
            labels: {!! json_encode($utilizationData->pluck('resource.name')) !!},
            datasets: [{
                label: 'Utilization Rate (%)',
                data: {!! json_encode($utilizationData->pluck('utilization_rate')) !!},
                backgroundColor: function(context) {
                    const value = context.parsed.y;
                    if (value > 80) return 'rgba(34, 197, 94, 0.8)';
                    if (value > 50) return 'rgba(251, 191, 36, 0.8)';
                    return 'rgba(239, 68, 68, 0.8)';
                },
                borderColor: function(context) {
                    const value = context.parsed.y;
                    if (value > 80) return 'rgb(34, 197, 94)';
                    if (value > 50) return 'rgb(251, 191, 36)';
                    return 'rgb(239, 68, 68)';
                },
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Utilization: ' + context.parsed.y.toFixed(1) + '%';
                        }
                    }
                }
            }
        }
    });

    // Initialize DataTable
    $('#resourcesTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[5, 'desc']], // Sort by utilization rate
        columnDefs: [
            {
                targets: [5], // Utilization Rate column
                type: 'num',
                render: function(data, type, row) {
                    if (type === 'display') {
                        return data; // Keep the HTML progress bar
                    }
                    // For sorting, extract the numeric value
                    const match = data.match(/(\d+\.?\d*)%/);
                    return match ? parseFloat(match[1]) : 0;
                }
            }
        ]
    });
</script>
@endsection
