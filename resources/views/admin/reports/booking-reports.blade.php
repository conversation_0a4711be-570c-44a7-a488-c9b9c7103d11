@extends('admin.layouts.app')

@section('title', 'Booking Reports')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Booking Reports</h1>
            <p class="text-muted">Analyze booking patterns and performance</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.reports.export', 'booking') }}?business_id={{ request('business_id') }}&date_from={{ $dateFrom }}&date_to={{ $dateTo }}" 
                   class="btn btn-success">
                    <i class="fas fa-download mr-2"></i>
                    Export CSV
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports.booking-reports') }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select name="business_id" id="business_id" class="form-control">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="{{ $dateFrom }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" 
                                   value="{{ $dateTo }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search mr-2"></i>
                                Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-2 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_bookings'] }}</h3>
                    <p>Total Bookings</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['confirmed'] }}</h3>
                    <p>Confirmed</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3>{{ $stats['completed'] }}</h3>
                    <p>Completed</p>
                </div>
                <div class="icon">
                    <i class="fas fa-flag-checkered"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['pending'] }}</h3>
                    <p>Pending</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $stats['cancelled'] }}</h3>
                    <p>Cancelled</p>
                </div>
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-secondary">
                <div class="inner">
                    <h3>{{ $stats['no_show'] }}</h3>
                    <p>No Show</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-times"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Bookings by Day Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line mr-2"></i>
                        Bookings by Day
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="bookingsByDayChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Bookings by Service Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Bookings by Service
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="bookingsByServiceChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Bookings Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list mr-2"></i>
                Recent Bookings
            </h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="bookingsTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Customer</th>
                            <th>Business</th>
                            <th>Service</th>
                            <th>Status</th>
                            <th>Amount</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($bookings->take(50) as $booking)
                            <tr>
                                <td>{{ $booking->start_datetime->format('M d, Y H:i') }}</td>
                                <td>{{ $booking->customer_name }}</td>
                                <td>{{ $booking->business->name }}</td>
                                <td>
                                    @foreach($booking->bookingServices as $service)
                                        <span class="badge badge-info">{{ $service->service->name }}</span>
                                    @endforeach
                                </td>
                                <td>
                                    <span class="badge badge-{{ $booking->status_color }}">
                                        {{ ucfirst($booking->status) }}
                                    </span>
                                </td>
                                <td>${{ number_format($booking->total_amount, 2) }}</td>
                                <td>
                                    <a href="{{ route('admin.bookings.show', $booking) }}" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@stop

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Bookings by Day Chart
    const bookingsByDayCtx = document.getElementById('bookingsByDayChart').getContext('2d');
    new Chart(bookingsByDayCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($bookingsByDay->keys()) !!},
            datasets: [{
                label: 'Bookings',
                data: {!! json_encode($bookingsByDay->values()) !!},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Bookings by Service Chart
    const bookingsByServiceCtx = document.getElementById('bookingsByServiceChart').getContext('2d');
    new Chart(bookingsByServiceCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode($bookingsByService->keys()) !!},
            datasets: [{
                data: {!! json_encode($bookingsByService->values()) !!},
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Initialize DataTable
    $('#bookingsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[0, 'desc']]
    });
</script>
@endsection
