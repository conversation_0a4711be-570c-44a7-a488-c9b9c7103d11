@extends('admin.layouts.app')

@section('title', 'Customer Reports')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Customer Reports</h1>
            <p class="text-muted">Analyze customer behavior and engagement</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.reports.export', 'customer') }}?business_id={{ request('business_id') }}&date_from={{ $dateFrom }}&date_to={{ $dateTo }}" 
                   class="btn btn-success">
                    <i class="fas fa-download mr-2"></i>
                    Export CSV
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports.customer-reports') }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select name="business_id" id="business_id" class="form-control">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="{{ $dateFrom }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" 
                                   value="{{ $dateTo }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search mr-2"></i>
                                Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Customer Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_customers'] }}</h3>
                    <p>Total Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['new_customers'] }}</h3>
                    <p>New Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-plus"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3>{{ $stats['returning_customers'] }}</h3>
                    <p>Returning Customers</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ number_format($stats['average_bookings_per_customer'], 1) }}</h3>
                    <p>Avg Bookings/Customer</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Customer Type Distribution -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Customer Type Distribution
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="customerTypeChart" style="height: 300px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Customer Acquisition Trend -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line mr-2"></i>
                        Customer Acquisition
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="description-block border-right">
                                <span class="description-percentage text-success">
                                    <i class="fas fa-caret-up"></i> 
                                    {{ $stats['new_customers'] > 0 ? round(($stats['new_customers'] / $stats['total_customers']) * 100, 1) : 0 }}%
                                </span>
                                <h5 class="description-header">{{ $stats['new_customers'] }}</h5>
                                <span class="description-text">NEW CUSTOMERS</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="description-block">
                                <span class="description-percentage text-info">
                                    <i class="fas fa-caret-up"></i> 
                                    {{ $stats['returning_customers'] > 0 ? round(($stats['returning_customers'] / $stats['total_customers']) * 100, 1) : 0 }}%
                                </span>
                                <h5 class="description-header">{{ $stats['returning_customers'] }}</h5>
                                <span class="description-text">RETURNING</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers Table -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-star mr-2"></i>
                Top Customers
            </h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="customersTable">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Customer Name</th>
                            <th>Email</th>
                            <th>Total Bookings</th>
                            <th>Total Spent</th>
                            <th>Avg per Booking</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($topCustomers as $index => $customer)
                            <tr>
                                <td>
                                    @if($index == 0)
                                        <i class="fas fa-trophy text-warning"></i> #{{ $index + 1 }}
                                    @elseif($index == 1)
                                        <i class="fas fa-medal text-secondary"></i> #{{ $index + 1 }}
                                    @elseif($index == 2)
                                        <i class="fas fa-award text-warning"></i> #{{ $index + 1 }}
                                    @else
                                        #{{ $index + 1 }}
                                    @endif
                                </td>
                                <td>{{ $customer['name'] }}</td>
                                <td>{{ $customer['email'] }}</td>
                                <td>
                                    <span class="badge badge-primary">{{ $customer['booking_count'] }}</span>
                                </td>
                                <td>${{ number_format($customer['total_spent'], 2) }}</td>
                                <td>${{ number_format($customer['total_spent'] / $customer['booking_count'], 2) }}</td>
                                <td>
                                    <a href="mailto:{{ $customer['email'] }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-envelope"></i>
                                    </a>
                                    <button class="btn btn-sm btn-primary" onclick="viewCustomerDetails('{{ $customer['email'] }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@stop

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Customer Type Distribution Chart
    const customerTypeCtx = document.getElementById('customerTypeChart').getContext('2d');
    new Chart(customerTypeCtx, {
        type: 'doughnut',
        data: {
            labels: ['New Customers', 'Returning Customers'],
            datasets: [{
                data: [{{ $stats['new_customers'] }}, {{ $stats['returning_customers'] }}],
                backgroundColor: [
                    '#28a745',
                    '#007bff'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = {{ $stats['total_customers'] }};
                            const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });

    // Initialize DataTable
    $('#customersTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[3, 'desc']], // Sort by booking count
        columnDefs: [
            {
                targets: [4, 5], // Total Spent and Avg per Booking columns
                render: function(data, type, row) {
                    if (type === 'display') {
                        return '$' + parseFloat(data).toLocaleString('en-US', {minimumFractionDigits: 2});
                    }
                    return data;
                }
            }
        ]
    });

    function viewCustomerDetails(email) {
        // This could open a modal or redirect to a customer detail page
        alert('Customer details for: ' + email + '\n\nThis would typically open a detailed customer view.');
    }
</script>
@endsection
