@extends('admin.layouts.app')

@section('title', 'Service Categories')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Service Categories</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('create services')
                    <a href="{{ route('admin.service-categories.create', ['business_id' => $business?->id]) }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add Category
                    </a>
                @endcan
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <!-- Filters -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.service-categories.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select name="business_id" id="business_id" class="form-control">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search categories..." value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All Statuses</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search mr-2"></i>
                                    Filter
                                </button>
                                <a href="{{ route('admin.service-categories.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times mr-2"></i>
                                    Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Service Categories List -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-tags mr-2"></i>
                Service Categories ({{ $categories->total() }})
            </h3>
        </div>
        <div class="card-body p-0">
            @if($categories->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Business</th>
                                <th>Services Count</th>
                                <th>Sort Order</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($categories as $category)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($category->icon)
                                                <i class="{{ $category->icon }} mr-2" 
                                                   style="color: {{ $category->color ?? '#6c757d' }}"></i>
                                            @endif
                                            <div>
                                                <strong>{{ $category->name }}</strong>
                                                @if($category->description)
                                                    <br><small class="text-muted">{{ Str::limit($category->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ $category->business->name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ $category->services_count ?? 0 }}</span>
                                    </td>
                                    <td>{{ $category->sort_order }}</td>
                                    <td>
                                        <form method="POST" action="{{ route('admin.service-categories.toggle-status', $category) }}" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-sm {{ $category->is_active ? 'btn-success' : 'btn-secondary' }}">
                                                {{ $category->is_active ? 'Active' : 'Inactive' }}
                                            </button>
                                        </form>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.service-categories.show', $category) }}" 
                                               class="btn btn-sm btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @can('edit services')
                                                <a href="{{ route('admin.service-categories.edit', $category) }}" 
                                                   class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                            @can('manage services')
                                                <form method="POST" action="{{ route('admin.service-categories.duplicate', $category) }}" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-secondary" title="Duplicate">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </form>
                                            @endcan
                                            @can('delete services')
                                                @if($category->services_count == 0)
                                                    <form method="POST" action="{{ route('admin.service-categories.destroy', $category) }}" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('Are you sure you want to delete this category?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                @else
                                                    <button class="btn btn-sm btn-danger" disabled title="Cannot delete category with services">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h4>No Service Categories Found</h4>
                    <p class="text-muted">
                        @if(request()->hasAny(['search', 'business_id', 'status']))
                            No service categories match your current filters.
                        @else
                            Get started by creating your first service category.
                        @endif
                    </p>
                    @can('create services')
                        <a href="{{ route('admin.service-categories.create', ['business_id' => $business?->id]) }}" class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Add First Category
                        </a>
                    @endcan
                </div>
            @endif
        </div>
        @if($categories->hasPages())
            <div class="card-footer">
                {{ $categories->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Auto-submit form when business filter changes
    $('#business_id').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
@stop
