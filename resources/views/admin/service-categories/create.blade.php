@extends('admin.layouts.app')

@section('title', 'Create Service Category')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Create New Service Category</h1>
            @if($business)
                <p class="text-muted">{{ $business->name }}</p>
            @endif
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="{{ route('admin.service-categories.index', ['business_id' => $business?->id]) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Categories
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-8">
        <form action="{{ route('admin.service-categories.store') }}" method="POST" id="category-form">
            @csrf
            
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        Basic Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="business_id">Business <span class="text-danger">*</span></label>
                                <select name="business_id" id="business_id" class="form-control @error('business_id') is-invalid @enderror" required>
                                    <option value="">Select Business</option>
                                    @foreach($businesses as $businessOption)
                                        <option value="{{ $businessOption->id }}" 
                                                {{ (old('business_id', $business?->id) == $businessOption->id) ? 'selected' : '' }}>
                                            {{ $businessOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('business_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Category Name <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" 
                                       value="{{ old('name') }}" required maxlength="255">
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                  rows="3" maxlength="1000">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Optional description for this service category.</small>
                    </div>
                </div>
            </div>

            <!-- Appearance -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-palette mr-2"></i>
                        Appearance
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="icon">Icon</label>
                                <input type="text" name="icon" id="icon" class="form-control @error('icon') is-invalid @enderror" 
                                       value="{{ old('icon') }}" placeholder="e.g., fas fa-cut" maxlength="100">
                                @error('icon')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">
                                    FontAwesome icon class (e.g., "fas fa-cut", "fas fa-spa"). 
                                    <a href="https://fontawesome.com/icons" target="_blank">Browse icons</a>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="color">Color</label>
                                <input type="color" name="color" id="color" class="form-control @error('color') is-invalid @enderror" 
                                       value="{{ old('color', '#6c757d') }}">
                                @error('color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Color for the category icon and theme.</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sort_order">Sort Order</label>
                                <input type="number" name="sort_order" id="sort_order" class="form-control @error('sort_order') is-invalid @enderror" 
                                       value="{{ old('sort_order', 0) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Lower numbers appear first in lists.</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check mt-4">
                                    <input type="checkbox" name="is_active" id="is_active" class="form-check-input" 
                                           value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                                <small class="form-text text-muted">Only active categories are available for selection.</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-eye mr-2"></i>
                        Preview
                    </h3>
                </div>
                <div class="card-body">
                    <div id="category-preview" class="d-flex align-items-center">
                        <i id="preview-icon" class="fas fa-tag mr-2" style="color: #6c757d;"></i>
                        <div>
                            <strong id="preview-name">Category Name</strong>
                            <br><small id="preview-description" class="text-muted">Category description will appear here</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Create Category
                            </button>
                            <a href="{{ route('admin.service-categories.index', ['business_id' => $business?->id]) }}" class="btn btn-secondary">
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="col-md-4">
        <!-- Help -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-question-circle mr-2"></i>
                    Help
                </h3>
            </div>
            <div class="card-body">
                <h6>Service Categories</h6>
                <p class="text-sm">Service categories help organize your services and make them easier for customers to find.</p>
                
                <h6>Tips:</h6>
                <ul class="text-sm">
                    <li>Use clear, descriptive names</li>
                    <li>Choose appropriate icons and colors</li>
                    <li>Set sort order to control display order</li>
                    <li>Categories are business-specific</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Update preview when form fields change
    function updatePreview() {
        var name = $('#name').val() || 'Category Name';
        var description = $('#description').val() || 'Category description will appear here';
        var icon = $('#icon').val() || 'fas fa-tag';
        var color = $('#color').val() || '#6c757d';
        
        $('#preview-name').text(name);
        $('#preview-description').text(description);
        $('#preview-icon').attr('class', icon + ' mr-2').css('color', color);
    }
    
    // Bind events
    $('#name, #description, #icon, #color').on('input change', updatePreview);
    
    // Initial preview update
    updatePreview();
});
</script>
@stop
