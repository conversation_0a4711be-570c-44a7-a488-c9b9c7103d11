@extends('admin.layouts.app')

@section('title', 'Service Category: ' . $serviceCategory->name)

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>
                @if($serviceCategory->icon)
                    <i class="{{ $serviceCategory->icon }}" style="color: {{ $serviceCategory->color ?? '#6c757d' }}"></i>
                @endif
                {{ $serviceCategory->name }}
            </h1>
            <p class="text-muted">{{ $serviceCategory->business->name }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                @can('edit services')
                    <a href="{{ route('admin.service-categories.edit', $serviceCategory) }}" class="btn btn-warning">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Category
                    </a>
                @endcan
                <form method="POST" action="{{ route('admin.service-categories.duplicate', $serviceCategory) }}" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-copy mr-2"></i>
                        Duplicate
                    </button>
                </form>
                <a href="{{ route('admin.service-categories.index', ['business_id' => $serviceCategory->business_id]) }}" class="btn btn-info">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Categories
                </a>
            </div>
        </div>
    </div>
@stop

@section('main-content')
    <div class="row">
        <div class="col-md-8">
            <!-- Category Details -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Category Details</h3>
                    <div class="card-tools">
                        <form method="POST" action="{{ route('admin.service-categories.toggle-status', $serviceCategory) }}" style="display: inline;">
                            @csrf
                            <button type="submit" class="btn btn-sm {{ $serviceCategory->is_active ? 'btn-success' : 'btn-secondary' }}">
                                {{ $serviceCategory->is_active ? 'Active' : 'Inactive' }}
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Name:</strong>
                            <p>{{ $serviceCategory->name }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>Slug:</strong>
                            <p><code>{{ $serviceCategory->slug }}</code></p>
                        </div>
                    </div>

                    @if($serviceCategory->description)
                        <div class="row">
                            <div class="col-md-12">
                                <strong>Description:</strong>
                                <p>{{ $serviceCategory->description }}</p>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <strong>Business:</strong>
                            <p>
                                <a href="{{ route('admin.businesses.show', $serviceCategory->business) }}">
                                    {{ $serviceCategory->business->name }}
                                </a>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <strong>Sort Order:</strong>
                            <p>{{ $serviceCategory->sort_order }}</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <strong>Icon:</strong>
                            <p>
                                @if($serviceCategory->icon)
                                    <i class="{{ $serviceCategory->icon }}" style="color: {{ $serviceCategory->color ?? '#6c757d' }}"></i>
                                    <code class="ml-2">{{ $serviceCategory->icon }}</code>
                                @else
                                    <span class="text-muted">No icon set</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-6">
                            <strong>Color:</strong>
                            <p>
                                @if($serviceCategory->color)
                                    <span class="d-inline-block" style="width: 20px; height: 20px; background-color: {{ $serviceCategory->color }}; border: 1px solid #ddd; border-radius: 3px;"></span>
                                    <code class="ml-2">{{ $serviceCategory->color }}</code>
                                @else
                                    <span class="text-muted">No color set</span>
                                @endif
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <strong>Created:</strong>
                            <p>{{ $serviceCategory->created_at->format('M j, Y g:i A') }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>Last Updated:</strong>
                            <p>{{ $serviceCategory->updated_at->format('M j, Y g:i A') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Associated Services -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-concierge-bell mr-2"></i>
                        Associated Services ({{ $serviceCategory->services->count() }})
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.services.create', ['business_id' => $serviceCategory->business_id, 'category_id' => $serviceCategory->id]) }}" 
                           class="btn btn-sm btn-primary">
                            <i class="fas fa-plus mr-1"></i>
                            Add Service
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($serviceCategory->services->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Service Name</th>
                                        <th>Duration</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($serviceCategory->services as $service)
                                        <tr>
                                            <td>
                                                <strong>{{ $service->name }}</strong>
                                                @if($service->short_description)
                                                    <br><small class="text-muted">{{ Str::limit($service->short_description, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>{{ $service->duration_minutes }} min</td>
                                            <td>${{ number_format($service->base_price, 2) }}</td>
                                            <td>
                                                <span class="badge badge-{{ $service->is_active ? 'success' : 'secondary' }}">
                                                    {{ $service->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.services.show', $service) }}" 
                                                       class="btn btn-sm btn-info" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @can('edit services')
                                                        <a href="{{ route('admin.services.edit', $service) }}" 
                                                           class="btn btn-sm btn-warning" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    @endcan
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                            <h5>No Services in this Category</h5>
                            <p class="text-muted">This category doesn't have any services yet.</p>
                            <a href="{{ route('admin.services.create', ['business_id' => $serviceCategory->business_id, 'category_id' => $serviceCategory->id]) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-plus mr-2"></i>
                                Add First Service
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Quick Stats -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Quick Stats
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="description-block border-right">
                                <span class="description-percentage text-success">
                                    <i class="fas fa-concierge-bell"></i>
                                </span>
                                <h5 class="description-header">{{ $serviceCategory->services->count() }}</h5>
                                <span class="description-text">SERVICES</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="description-block">
                                <span class="description-percentage text-info">
                                    <i class="fas fa-check-circle"></i>
                                </span>
                                <h5 class="description-header">{{ $serviceCategory->services->where('is_active', true)->count() }}</h5>
                                <span class="description-text">ACTIVE</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs mr-2"></i>
                        Actions
                    </h3>
                </div>
                <div class="card-body">
                    @can('edit services')
                        <a href="{{ route('admin.service-categories.edit', $serviceCategory) }}" class="btn btn-warning btn-block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Category
                        </a>
                    @endcan

                    <form method="POST" action="{{ route('admin.service-categories.duplicate', $serviceCategory) }}" class="mt-2">
                        @csrf
                        <button type="submit" class="btn btn-secondary btn-block">
                            <i class="fas fa-copy mr-2"></i>
                            Duplicate Category
                        </button>
                    </form>

                    @can('delete services')
                        @if($serviceCategory->services->count() === 0)
                            <form method="POST" action="{{ route('admin.service-categories.destroy', $serviceCategory) }}"
                                  class="mt-2" onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-block">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Category
                                </button>
                            </form>
                        @else
                            <button class="btn btn-danger btn-block mt-2" disabled title="Cannot delete category with associated services">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Category
                            </button>
                            <small class="text-muted">Remove all associated services first</small>
                        @endif
                    @endcan
                </div>
            </div>
        </div>
    </div>
@stop
