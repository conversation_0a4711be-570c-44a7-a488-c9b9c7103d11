@extends('admin.layouts.app')

@section('title', 'Check-In Dashboard')

@section('header')
    <div class="row">
        <div class="col-sm-6">
            <h1>Check-In Dashboard</h1>
            <p class="text-muted">{{ $date ? \Carbon\Carbon::parse($date)->format('l, F j, Y') : 'Today' }}</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <button type="button" class="btn btn-info" onclick="refreshData()">
                    <i class="fas fa-sync mr-2"></i>
                    Refresh
                </button>
                <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#exportModal">
                    <i class="fas fa-download mr-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>
@stop

@section('main-content')
<div class="container-fluid">
    <!-- Filters -->
    <div class="card">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.check-in.index') }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="business_id">Business</label>
                            <select name="business_id" id="business_id" class="form-control">
                                <option value="">All Businesses</option>
                                @foreach($businesses as $businessOption)
                                    <option value="{{ $businessOption->id }}" 
                                            {{ request('business_id') == $businessOption->id ? 'selected' : '' }}>
                                        {{ $businessOption->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="date">Date</label>
                            <input type="date" name="date" id="date" class="form-control" 
                                   value="{{ $date }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter mr-2"></i>
                                    Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row">
        <div class="col-lg-2 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total'] }}</h3>
                    <p>Total Bookings</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['pending'] }}</h3>
                    <p>Pending Check-In</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3>{{ $stats['checked_in'] }}</h3>
                    <p>Checked In</p>
                </div>
                <div class="icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['checked_out'] }}</h3>
                    <p>Completed</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $stats['no_show'] }}</h3>
                    <p>No Shows</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-times"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-6">
            <div class="small-box bg-secondary">
                <div class="inner">
                    <h3 id="completion-rate">-</h3>
                    <p>Completion Rate</p>
                </div>
                <div class="icon">
                    <i class="fas fa-percentage"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Check-In Tabs -->
    <div class="card">
        <div class="card-header p-0 pt-1">
            <ul class="nav nav-tabs" id="checkInTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="pending-tab" data-toggle="tab" href="#pending" role="tab">
                        <i class="fas fa-clock mr-2"></i>
                        Pending Check-In ({{ $pendingCheckIn->count() }})
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="checked-in-tab" data-toggle="tab" href="#checked-in" role="tab">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Checked In ({{ $checkedIn->count() }})
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="completed-tab" data-toggle="tab" href="#completed" role="tab">
                        <i class="fas fa-check-circle mr-2"></i>
                        Completed ({{ $checkedOut->count() }})
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="checkInTabsContent">
                <!-- Pending Check-In -->
                <div class="tab-pane fade show active" id="pending" role="tabpanel">
                    @if($pendingCheckIn->count() > 0)
                        <div class="row">
                            @foreach($pendingCheckIn as $booking)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card booking-card" data-booking-id="{{ $booking->id }}">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">{{ $booking->start_datetime->format('g:i A') }}</h6>
                                                <span class="badge badge-warning">Pending</span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <h5 class="card-title">{{ $booking->customer_name }}</h5>
                                            <p class="card-text">
                                                <strong>Service:</strong> {{ $booking->bookingServices->pluck('service.name')->join(', ') }}<br>
                                                <strong>Duration:</strong> {{ $booking->total_duration_minutes }}min<br>
                                                @if($business)
                                                    <strong>Phone:</strong> {{ $booking->customer_phone ?: 'Not provided' }}
                                                @else
                                                    <strong>Business:</strong> {{ $booking->business->name }}
                                                @endif
                                            </p>
                                            <div class="btn-group btn-group-sm w-100">
                                                <button type="button" class="btn btn-success" onclick="checkIn({{ $booking->id }})">
                                                    <i class="fas fa-sign-in-alt mr-1"></i>
                                                    Check In
                                                </button>
                                                <button type="button" class="btn btn-danger" onclick="markNoShow({{ $booking->id }})">
                                                    <i class="fas fa-user-times mr-1"></i>
                                                    No Show
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h4>All caught up!</h4>
                            <p class="text-muted">No customers pending check-in.</p>
                        </div>
                    @endif
                </div>

                <!-- Checked In -->
                <div class="tab-pane fade" id="checked-in" role="tabpanel">
                    @if($checkedIn->count() > 0)
                        <div class="row">
                            @foreach($checkedIn as $booking)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card booking-card" data-booking-id="{{ $booking->id }}">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">{{ $booking->start_datetime->format('g:i A') }}</h6>
                                                <span class="badge badge-primary">In Progress</span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <h5 class="card-title">{{ $booking->customer_name }}</h5>
                                            <p class="card-text">
                                                <strong>Service:</strong> {{ $booking->bookingServices->pluck('service.name')->join(', ') }}<br>
                                                <strong>Checked In:</strong> {{ $booking->checked_in_at->format('g:i A') }}<br>
                                                <strong>Duration:</strong> {{ $booking->checked_in_at->diffInMinutes() }}min elapsed
                                            </p>
                                            <div class="btn-group btn-group-sm w-100">
                                                <button type="button" class="btn btn-success" onclick="checkOut({{ $booking->id }})">
                                                    <i class="fas fa-sign-out-alt mr-1"></i>
                                                    Check Out
                                                </button>
                                                <button type="button" class="btn btn-warning" onclick="undoCheckIn({{ $booking->id }})">
                                                    <i class="fas fa-undo mr-1"></i>
                                                    Undo
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h4>No customers checked in</h4>
                            <p class="text-muted">Customers will appear here once they check in.</p>
                        </div>
                    @endif
                </div>

                <!-- Completed -->
                <div class="tab-pane fade" id="completed" role="tabpanel">
                    @if($checkedOut->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Customer</th>
                                        <th>Service</th>
                                        <th>Check-In</th>
                                        <th>Check-Out</th>
                                        <th>Duration</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($checkedOut as $booking)
                                        <tr>
                                            <td>{{ $booking->start_datetime->format('g:i A') }}</td>
                                            <td>{{ $booking->customer_name }}</td>
                                            <td>{{ $booking->bookingServices->pluck('service.name')->join(', ') }}</td>
                                            <td>{{ $booking->checked_in_at->format('g:i A') }}</td>
                                            <td>{{ $booking->checked_out_at->format('g:i A') }}</td>
                                            <td>{{ $booking->checked_in_at->diffInMinutes($booking->checked_out_at) }}min</td>
                                            <td>
                                                <span class="badge badge-success">Completed</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                            <h4>No completed appointments</h4>
                            <p class="text-muted">Completed appointments will appear here.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Check-Out Modal -->
<div class="modal fade" id="checkOutModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Check Out Customer</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="checkOutForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="checkout_notes">Notes (Optional)</label>
                        <textarea name="notes" id="checkout_notes" class="form-control" rows="3" 
                                  placeholder="Any notes about the service..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="checkout_rating">Service Rating (Optional)</label>
                        <select name="rating" id="checkout_rating" class="form-control">
                            <option value="">No rating</option>
                            <option value="5">5 - Excellent</option>
                            <option value="4">4 - Good</option>
                            <option value="3">3 - Average</option>
                            <option value="2">2 - Poor</option>
                            <option value="1">1 - Very Poor</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Check Out
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export Check-In Data</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.check-in.export') }}" method="GET">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="export_business_id">Business</label>
                        <select name="business_id" id="export_business_id" class="form-control">
                            <option value="">All Businesses</option>
                            @foreach($businesses as $businessOption)
                                <option value="{{ $businessOption->id }}">{{ $businessOption->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_from">From Date</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" 
                                       value="{{ now()->subDays(30)->format('Y-m-d') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_to">To Date</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" 
                                       value="{{ now()->format('Y-m-d') }}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download mr-2"></i>
                        Export CSV
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
let currentCheckOutBookingId = null;

// Calculate completion rate
$(document).ready(function() {
    const total = {{ $stats['total'] }};
    const completed = {{ $stats['checked_out'] }};
    const rate = total > 0 ? Math.round((completed / total) * 100) : 0;
    $('#completion-rate').text(rate + '%');
});

function checkIn(bookingId) {
    if (confirm('Check in this customer?')) {
        $.post(`/admin/bookings/${bookingId}/check-in`, {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                showAlert('success', response.message);
                refreshData();
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function(xhr) {
            const response = xhr.responseJSON;
            showAlert('error', response.message || 'An error occurred');
        });
    }
}

function checkOut(bookingId) {
    currentCheckOutBookingId = bookingId;
    $('#checkOutModal').modal('show');
}

$('#checkOutForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        _token: '{{ csrf_token() }}',
        notes: $('#checkout_notes').val(),
        rating: $('#checkout_rating').val()
    };
    
    $.post(`/admin/bookings/${currentCheckOutBookingId}/check-out`, formData)
    .done(function(response) {
        if (response.success) {
            $('#checkOutModal').modal('hide');
            showAlert('success', response.message);
            refreshData();
        } else {
            showAlert('error', response.message);
        }
    })
    .fail(function(xhr) {
        const response = xhr.responseJSON;
        showAlert('error', response.message || 'An error occurred');
    });
});

function markNoShow(bookingId) {
    const notes = prompt('Reason for no-show (optional):');
    if (notes !== null) { // User didn't cancel
        $.post(`/admin/bookings/${bookingId}/mark-no-show`, {
            _token: '{{ csrf_token() }}',
            notes: notes
        })
        .done(function(response) {
            if (response.success) {
                showAlert('success', response.message);
                refreshData();
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function(xhr) {
            const response = xhr.responseJSON;
            showAlert('error', response.message || 'An error occurred');
        });
    }
}

function undoCheckIn(bookingId) {
    if (confirm('Undo check-in for this customer?')) {
        $.post(`/admin/bookings/${bookingId}/undo-check-in`, {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                showAlert('success', response.message);
                refreshData();
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function(xhr) {
            const response = xhr.responseJSON;
            showAlert('error', response.message || 'An error occurred');
        });
    }
}

function refreshData() {
    location.reload();
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top of the content
    $('.container-fluid').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Auto-refresh every 30 seconds
setInterval(function() {
    refreshData();
}, 30000);
</script>
@stop
