@extends('admin.layouts.app')

@section('title', 'Create New Role')

@section('header')
    <h1>
        <i class="fas fa-plus-circle"></i> Create New Role
        <small class="text-muted">Add a new role with specific permissions</small>
    </h1>
@stop

@section('main-content')
<div class="row">
    <div class="col-md-8">
        <form action="{{ route('admin.roles.store') }}" method="POST" id="createRoleForm">
            @csrf

            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i> Basic Information
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Roles
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Role Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="hierarchy_level">Hierarchy Level <span class="text-danger">*</span></label>
                                <select class="form-control @error('hierarchy_level') is-invalid @enderror"
                                        id="hierarchy_level" name="hierarchy_level" required>
                                    <option value="">Select Hierarchy Level</option>
                                    @if(isset($availableHierarchyLevels))
                                        @foreach($availableHierarchyLevels as $level => $name)
                                            <option value="{{ $level }}" {{ old('hierarchy_level') == $level ? 'selected' : '' }}>
                                                Level {{ $level }} - {{ $name }}
                                            </option>
                                        @endforeach
                                    @else
                                        <option value="5">Level 5 - Customer</option>
                                        <option value="4">Level 4 - Staff</option>
                                        <option value="3">Level 3 - Manager</option>
                                        <option value="2">Level 2 - Business Owner</option>
                                        <option value="1">Level 1 - Admin</option>
                                        <option value="0">Level 0 - Super Admin</option>
                                    @endif
                                </select>
                                @error('hierarchy_level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="security_level">Security Level <span class="text-danger">*</span></label>
                                <select class="form-control @error('security_level') is-invalid @enderror"
                                        id="security_level" name="security_level" required>
                                    @if(isset($securityLevels))
                                        @foreach($securityLevels as $level => $name)
                                            <option value="{{ $level }}" {{ old('security_level') == $level ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    @else
                                        <option value="1">Low</option>
                                        <option value="2" selected>Medium</option>
                                        <option value="3">High</option>
                                        <option value="4">Critical</option>
                                        <option value="5">Maximum</option>
                                    @endif
                                </select>
                                @error('security_level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max_users">Maximum Users (Optional)</label>
                                <input type="number" class="form-control @error('max_users') is-invalid @enderror"
                                       id="max_users" name="max_users" value="{{ old('max_users') }}" min="1">
                                @error('max_users')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Leave empty for unlimited users</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description" name="description" rows="3"
                                  placeholder="Describe the role's purpose and responsibilities">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            <!-- Permissions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-key"></i> Permissions Assignment
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllPermissions">
                            Select All
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllPermissions">
                            Deselect All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if(isset($permissionCategories))
                        @foreach($permissionCategories as $category => $permissions)
                            <div class="permission-category mb-4">
                                <h5 class="text-primary border-bottom pb-2">
                                    <input type="checkbox" class="category-checkbox mr-2" data-category="{{ $category }}">
                                    {{ $category }}
                                    <small class="text-muted">({{ count($permissions) }} permissions)</small>
                                </h5>
                                <div class="row">
                                    @foreach($permissions as $permission)
                                        <div class="col-md-4 col-sm-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox"
                                                       class="custom-control-input permission-checkbox"
                                                       id="permission_{{ $permission->id }}"
                                                       name="permissions[]"
                                                       value="{{ $permission->id }}"
                                                       data-category="{{ $category }}"
                                                       {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="permission_{{ $permission->id }}">
                                                    {{ $permission->name }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    @else
                        <!-- Fallback for when permissionCategories is not available -->
                        <div class="row">
                            @if(isset($permissions))
                                @foreach($permissions as $permission)
                                    <div class="col-md-4 col-sm-6 mb-2">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox"
                                                   class="custom-control-input permission-checkbox"
                                                   id="permission_{{ $permission->id }}"
                                                   name="permissions[]"
                                                   value="{{ $permission->id }}"
                                                   {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="permission_{{ $permission->id }}">
                                                {{ $permission->name }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                        </div>
                    @endif

                    @error('permissions')
                        <div class="alert alert-danger">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Role
                            </button>
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                All changes will be logged for security audit
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Security Information Sidebar -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info">
                <h3 class="card-title text-white">
                    <i class="fas fa-shield-alt"></i> Security Guidelines
                </h3>
            </div>
            <div class="card-body">
                <h6>Hierarchy Levels</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge badge-danger">0</span>
                        <strong>Super Admin</strong>
                    </li>
                    <li class="mb-2">
                        <span class="badge badge-warning">1</span>
                        <strong>Admin</strong>
                    </li>
                    <li class="mb-2">
                        <span class="badge badge-warning">2</span>
                        <strong>Business Owner</strong>
                    </li>
                    <li class="mb-2">
                        <span class="badge badge-info">3</span>
                        <strong>Manager</strong>
                    </li>
                    <li class="mb-2">
                        <span class="badge badge-info">4</span>
                        <strong>Staff</strong>
                    </li>
                    <li class="mb-2">
                        <span class="badge badge-info">5</span>
                        <strong>Customer</strong>
                    </li>
                </ul>

                <hr>

                <h6>Security Levels</h6>
                <ul class="list-unstyled">
                    <li class="mb-1">
                        <span class="badge badge-success">Low</span>
                    </li>
                    <li class="mb-1">
                        <span class="badge badge-info">Medium</span>
                    </li>
                    <li class="mb-1">
                        <span class="badge badge-warning">High</span>
                    </li>
                    <li class="mb-1">
                        <span class="badge badge-danger">Critical</span>
                    </li>
                    <li class="mb-1">
                        <span class="badge badge-dark">Maximum</span>
                    </li>
                </ul>

                <hr>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Important:</strong> You can only create roles at your hierarchy level or lower.
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Category checkbox functionality
    $('.category-checkbox').change(function() {
        const category = $(this).data('category');
        const isChecked = $(this).is(':checked');

        $('input[data-category="' + category + '"]').prop('checked', isChecked);
    });

    // Individual permission checkbox functionality
    $('.permission-checkbox').change(function() {
        updateCategoryCheckboxes();
    });

    // Select/Deselect all functionality
    $('#selectAllPermissions').click(function() {
        $('.permission-checkbox').prop('checked', true);
        $('.category-checkbox').prop('checked', true);
    });

    $('#deselectAllPermissions').click(function() {
        $('.permission-checkbox').prop('checked', false);
        $('.category-checkbox').prop('checked', false);
    });

    function updateCategoryCheckboxes() {
        $('.category-checkbox').each(function() {
            const category = $(this).data('category');
            const categoryPermissions = $('input[data-category="' + category + '"]');
            const checkedPermissions = categoryPermissions.filter(':checked');

            if (checkedPermissions.length === 0) {
                $(this).prop('indeterminate', false).prop('checked', false);
            } else if (checkedPermissions.length === categoryPermissions.length) {
                $(this).prop('indeterminate', false).prop('checked', true);
            } else {
                $(this).prop('indeterminate', true);
            }
        });
    }

    // Form validation
    $('#createRoleForm').submit(function(e) {
        const selectedPermissions = $('.permission-checkbox:checked').length;

        if (selectedPermissions === 0) {
            e.preventDefault();
            toastr.error('Please select at least one permission for this role.');
            return false;
        }

        return true;
    });

    // Initialize category checkboxes
    updateCategoryCheckboxes();

    // Fallback for old select-all buttons
    $('#select-all').click(function() {
        $('input[name="permissions[]"]').prop('checked', true);
        updateCategoryCheckboxes();
    });

    $('#deselect-all').click(function() {
        $('input[name="permissions[]"]').prop('checked', false);
        updateCategoryCheckboxes();
    });
});
</script>
@stop
