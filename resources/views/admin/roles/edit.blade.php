@extends('admin.layouts.app')

@section('title', 'Edit Role')

@section('header')
    <h1>Edit Role</h1>
@stop

@section('main-content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Edit Role: {{ $role->name }}</h3>
        <div class="card-tools">
            <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-info btn-sm">
                <i class="fas fa-eye"></i> View Role
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Roles
            </a>
        </div>
    </div>
    <form action="{{ route('admin.roles.update', $role) }}" method="POST">
        @csrf
        @method('PUT')
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name">Role Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', $role->name) }}" required>
                        @error('name')
                            <span class="invalid-feedback">{{ $message }}</span>
                        @enderror
                        <small class="form-text text-muted">Enter a unique name for this role.</small>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="permissions">Permissions <span class="text-danger">*</span></label>
                        <div class="row">
                            @foreach($permissions as $permission)
                                <div class="col-md-4 col-sm-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="permissions[]" value="{{ $permission->id }}" 
                                               id="permission_{{ $permission->id }}"
                                               {{ in_array($permission->id, old('permissions', $rolePermissions)) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="permission_{{ $permission->id }}">
                                            {{ $permission->name }}
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @error('permissions')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                        <small class="form-text text-muted">Select the permissions that users with this role should have.</small>
                    </div>
                </div>
            </div>
            
            @if($permissions->count() > 6)
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="select-all">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="deselect-all">
                            <i class="fas fa-square"></i> Deselect All
                        </button>
                    </div>
                </div>
            </div>
            @endif
            
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> Role Information</h5>
                        <ul class="mb-0">
                            <li><strong>Created:</strong> {{ $role->created_at->format('M d, Y H:i') }}</li>
                            <li><strong>Last Updated:</strong> {{ $role->updated_at->format('M d, Y H:i') }}</li>
                            <li><strong>Users with this role:</strong> {{ $role->users->count() }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Role
            </button>
            <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> View Role
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    $('#select-all').click(function() {
        $('input[name="permissions[]"]').prop('checked', true);
    });
    
    $('#deselect-all').click(function() {
        $('input[name="permissions[]"]').prop('checked', false);
    });
});
</script>
@stop
