@extends('admin.layouts.app')

@section('title', 'View Role')

@section('header')
    <h1>Role Details</h1>
@stop

@section('main-content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">{{ $role->name }}</h3>
        <div class="card-tools">
            @can('manage roles')
            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-warning btn-sm">
                <i class="fas fa-edit"></i> Edit Role
            </a>
            @endcan
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Roles
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <th width="30%">ID:</th>
                        <td>{{ $role->id }}</td>
                    </tr>
                    <tr>
                        <th>Name:</th>
                        <td>{{ $role->name }}</td>
                    </tr>
                    <tr>
                        <th>Guard:</th>
                        <td>{{ $role->guard_name }}</td>
                    </tr>
                    <tr>
                        <th>Created At:</th>
                        <td>{{ $role->created_at->format('M d, Y H:i') }}</td>
                    </tr>
                    <tr>
                        <th>Updated At:</th>
                        <td>{{ $role->updated_at->format('M d, Y H:i') }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>Users with this Role:</h5>
                @if($role->users->count() > 0)
                    <div class="list-group">
                        @foreach($role->users as $user)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ $user->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $user->email }}</small>
                                </div>
                                @can('manage users')
                                <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                @endcan
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted">No users assigned to this role.</p>
                @endif
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <h5>Permissions:</h5>
                @if($role->permissions->count() > 0)
                    <div class="row">
                        @foreach($role->permissions as $permission)
                            <div class="col-md-3 col-sm-4 col-6 mb-2">
                                <span class="badge badge-info">{{ $permission->name }}</span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted">No permissions assigned to this role.</p>
                @endif
            </div>
        </div>
    </div>
    <div class="card-footer">
        @can('manage roles')
        <div class="btn-group">
            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Role
            </a>
            @if($role->users->count() == 0)
            <form action="{{ route('admin.roles.destroy', $role) }}" method="POST" style="display: inline;" 
                  onsubmit="return confirm('Are you sure you want to delete this role? This action cannot be undone.')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Delete Role
                </button>
            </form>
            @else
            <button type="button" class="btn btn-danger" disabled title="Cannot delete role with assigned users">
                <i class="fas fa-trash"></i> Delete Role
            </button>
            @endif
        </div>
        @endcan
    </div>
</div>
@stop
