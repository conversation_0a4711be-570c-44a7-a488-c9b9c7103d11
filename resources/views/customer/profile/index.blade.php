@extends('customer.layouts.app')

@section('title', 'My Profile')

@section('content_header')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">My Profile</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Profile</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <!-- Profile Header -->
        <div class="row">
            <div class="col-12">
                <div class="card card-primary card-outline">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="text-primary mb-1">
                                    <i class="fas fa-user mr-2"></i>
                                    Welcome, {{ $customer->name }}!
                                </h2>
                                <p class="text-muted mb-0">Manage your profile information, preferences, and account settings.</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="{{ route('customer.profile.edit') }}" class="btn btn-primary">
                                    <i class="fas fa-edit mr-2"></i>
                                    Edit Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Information -->
        <div class="row">
            <div class="col-md-4">
                <!-- Profile Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user mr-2"></i>
                            Profile Information
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            @if($customer->profile_image)
                                <img src="{{ asset('storage/' . $customer->profile_image) }}"
                                     alt="Profile Image"
                                     class="img-circle img-fluid"
                                     style="width: 100px; height: 100px; object-fit: cover;">
                            @else
                                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center"
                                     style="width: 100px; height: 100px; font-size: 2rem;">
                                    {{ strtoupper(substr($customer->name, 0, 1)) }}
                                </div>
                            @endif
                        </div>

                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $customer->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ $customer->email }}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{ $customer->phone ?? 'Not provided' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Member Since:</strong></td>
                                <td>{{ $customer->created_at->format('M j, Y') }}</td>
                            </tr>
                            @if($customer->date_of_birth)
                            <tr>
                                <td><strong>Birthday:</strong></td>
                                <td>{{ \Carbon\Carbon::parse($customer->date_of_birth)->format('M j, Y') }}</td>
                            </tr>
                            @endif
                            @if($customer->gender)
                            <tr>
                                <td><strong>Gender:</strong></td>
                                <td>{{ ucfirst(str_replace('_', ' ', $customer->gender)) }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar mr-2"></i>
                            Quick Stats
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Bookings</span>
                                        <span class="info-box-number">{{ $customer->bookings()->count() }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-heart"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Favorites</span>
                                        <span class="info-box-number">{{ $customer->favoriteServices()->count() }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <!-- Account Management -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cog mr-2"></i>
                            Account Management
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-edit fa-2x text-primary mb-2"></i>
                                        <h5>Edit Profile</h5>
                                        <p class="text-muted">Update your personal information and contact details.</p>
                                        <a href="{{ route('customer.profile.edit') }}" class="btn btn-primary btn-sm">
                                            Edit Profile
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-lock fa-2x text-warning mb-2"></i>
                                        <h5>Change Password</h5>
                                        <p class="text-muted">Update your account password for security.</p>
                                        <a href="{{ route('customer.profile.password') }}" class="btn btn-warning btn-sm">
                                            Change Password
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-bell fa-2x text-info mb-2"></i>
                                        <h5>Preferences</h5>
                                        <p class="text-muted">Manage your notification and booking preferences.</p>
                                        <a href="{{ route('customer.preferences') }}" class="btn btn-info btn-sm">
                                            Preferences
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                        <h5>Privacy Settings</h5>
                                        <p class="text-muted">Control your privacy and data sharing settings.</p>
                                        <a href="{{ route('customer.privacy') }}" class="btn btn-success btn-sm">
                                            Privacy Settings
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history mr-2"></i>
                            Recent Activity
                        </h3>
                    </div>
                    <div class="card-body">
                        @php
                            $recentBookings = $customer->bookings()->latest()->limit(5)->get();
                        @endphp

                        @if($recentBookings->count() > 0)
                            <div class="timeline">
                                @foreach($recentBookings as $booking)
                                    <div class="time-label">
                                        <span class="bg-primary">{{ $booking->created_at->format('M j') }}</span>
                                    </div>
                                    <div>
                                        <i class="fas fa-calendar-check bg-blue"></i>
                                        <div class="timeline-item">
                                            <span class="time">
                                                <i class="fas fa-clock"></i> {{ $booking->created_at->format('H:i') }}
                                            </span>
                                            <h3 class="timeline-header">
                                                Booking Created
                                            </h3>
                                            <div class="timeline-body">
                                                Service: {{ $booking->service->name ?? 'N/A' }}<br>
                                                Status: <span class="badge badge-{{ $booking->status === 'confirmed' ? 'success' : 'warning' }}">
                                                    {{ ucfirst($booking->status) }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5>No Recent Activity</h5>
                                <p class="text-muted">You haven't made any bookings yet.</p>
                                <a href="{{ route('customer.services.index') }}" class="btn btn-primary">
                                    <i class="fas fa-search mr-2"></i>
                                    Browse Services
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Export & Account Deletion -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-database mr-2"></i>
                            Data Management
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Export Your Data</h5>
                                <p class="text-muted">Download a copy of all your data including bookings, preferences, and profile information.</p>
                                <a href="{{ route('customer.profile.export') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-download mr-2"></i>
                                    Export Data
                                </a>
                            </div>
                            <div class="col-md-6">
                                <h5 class="text-danger">Delete Account</h5>
                                <p class="text-muted">Permanently delete your account and all associated data. This action cannot be undone.</p>
                                <button type="button" class="btn btn-outline-danger" data-toggle="modal" data-target="#deleteAccountModal">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Account Modal -->
    <div class="modal fade" id="deleteAccountModal" tabindex="-1" role="dialog" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteAccountModalLabel">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Delete Account
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="POST" action="{{ route('customer.profile.delete') }}">
                    @csrf
                    @method('DELETE')
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <strong>Warning!</strong> This action will permanently delete your account and all associated data. This cannot be undone.
                        </div>

                        <div class="form-group">
                            <label for="password">Enter your password to confirm:</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <div class="form-group">
                            <label for="confirmation">Type "DELETE" to confirm:</label>
                            <input type="text" class="form-control" id="confirmation" name="confirmation" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Account</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('css')
    <style>
        .info-box {
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .info-box:hover {
            transform: translateY(-2px);
        }
        .card {
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .timeline {
            position: relative;
            margin: 0 0 30px 0;
            padding: 0;
            list-style: none;
        }
        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #ddd;
            left: 31px;
            margin: 0;
            border-radius: 2px;
        }
    </style>
@stop

@section('js')
    <script>
        // Auto-hide success messages
        setTimeout(function() {
            $('.alert-success').fadeOut();
        }, 5000);
    </script>
@stop
