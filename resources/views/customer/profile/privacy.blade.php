@extends('customer.layouts.app')

@section('title', 'Privacy Settings')

@section('content_header')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Privacy Settings</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('customer.profile.index') }}">Profile</a></li>
                        <li class="breadcrumb-item active">Privacy Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-shield-alt mr-2"></i>
                            Privacy & Data Settings
                        </h3>
                    </div>
                    
                    <form method="POST" action="{{ route('customer.privacy.update') }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="card-body">
                            <!-- Privacy Notice -->
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                <strong>Your Privacy Matters:</strong> We are committed to protecting your personal information. These settings help you control how your data is used and shared.
                            </div>

                            <!-- Data Sharing Preferences -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-share-alt mr-2"></i>
                                        Data Sharing Preferences
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="share_with_businesses" 
                                                   name="privacy_settings[share_with_businesses]" 
                                                   value="1"
                                                   {{ old('privacy_settings.share_with_businesses', $customer->privacy_settings['share_with_businesses'] ?? true) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="share_with_businesses">
                                                <strong>Share with Service Providers</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Allow businesses to access your contact information for bookings</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="analytics_tracking" 
                                                   name="privacy_settings[analytics_tracking]" 
                                                   value="1"
                                                   {{ old('privacy_settings.analytics_tracking', $customer->privacy_settings['analytics_tracking'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="analytics_tracking">
                                                <strong>Analytics & Tracking</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Help us improve our service by sharing anonymous usage data</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="marketing_communications" 
                                                   name="privacy_settings[marketing_communications]" 
                                                   value="1"
                                                   {{ old('privacy_settings.marketing_communications', $customer->privacy_settings['marketing_communications'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="marketing_communications">
                                                <strong>Marketing Communications</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Receive personalized offers and recommendations</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="third_party_sharing" 
                                                   name="privacy_settings[third_party_sharing]" 
                                                   value="1"
                                                   {{ old('privacy_settings.third_party_sharing', $customer->privacy_settings['third_party_sharing'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="third_party_sharing">
                                                <strong>Third-Party Sharing</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Allow sharing data with trusted partners for enhanced services</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Profile Visibility -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-eye mr-2"></i>
                                        Profile Visibility
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="profile_visibility">Profile Visibility</label>
                                        <select class="form-control" id="profile_visibility" name="privacy_settings[profile_visibility]">
                                            <option value="public" {{ old('privacy_settings.profile_visibility', $customer->privacy_settings['profile_visibility'] ?? 'private') == 'public' ? 'selected' : '' }}>Public</option>
                                            <option value="businesses_only" {{ old('privacy_settings.profile_visibility', $customer->privacy_settings['profile_visibility'] ?? 'private') == 'businesses_only' ? 'selected' : '' }}>Businesses Only</option>
                                            <option value="private" {{ old('privacy_settings.profile_visibility', $customer->privacy_settings['profile_visibility'] ?? 'private') == 'private' ? 'selected' : '' }}>Private</option>
                                        </select>
                                        <small class="text-muted">Control who can see your profile information</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="show_booking_history" 
                                                   name="privacy_settings[show_booking_history]" 
                                                   value="1"
                                                   {{ old('privacy_settings.show_booking_history', $customer->privacy_settings['show_booking_history'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="show_booking_history">
                                                <strong>Show Booking History</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Allow businesses to see your previous bookings with them</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="show_reviews" 
                                                   name="privacy_settings[show_reviews]" 
                                                   value="1"
                                                   {{ old('privacy_settings.show_reviews', $customer->privacy_settings['show_reviews'] ?? true) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="show_reviews">
                                                <strong>Show My Reviews</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Display your reviews publicly to help other customers</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="anonymous_reviews" 
                                                   name="privacy_settings[anonymous_reviews]" 
                                                   value="1"
                                                   {{ old('privacy_settings.anonymous_reviews', $customer->privacy_settings['anonymous_reviews'] ?? true) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="anonymous_reviews">
                                                <strong>Anonymous Reviews</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Show only your first name and last initial in reviews</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Data Retention -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-database mr-2"></i>
                                        Data Retention
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="data_retention_period">Data Retention Period</label>
                                        <select class="form-control" id="data_retention_period" name="privacy_settings[data_retention_period]">
                                            <option value="1_year" {{ old('privacy_settings.data_retention_period', $customer->privacy_settings['data_retention_period'] ?? '3_years') == '1_year' ? 'selected' : '' }}>1 Year</option>
                                            <option value="2_years" {{ old('privacy_settings.data_retention_period', $customer->privacy_settings['data_retention_period'] ?? '3_years') == '2_years' ? 'selected' : '' }}>2 Years</option>
                                            <option value="3_years" {{ old('privacy_settings.data_retention_period', $customer->privacy_settings['data_retention_period'] ?? '3_years') == '3_years' ? 'selected' : '' }}>3 Years</option>
                                            <option value="5_years" {{ old('privacy_settings.data_retention_period', $customer->privacy_settings['data_retention_period'] ?? '3_years') == '5_years' ? 'selected' : '' }}>5 Years</option>
                                            <option value="indefinite" {{ old('privacy_settings.data_retention_period', $customer->privacy_settings['data_retention_period'] ?? '3_years') == 'indefinite' ? 'selected' : '' }}>Keep Indefinitely</option>
                                        </select>
                                        <small class="text-muted">How long to keep your data after account closure</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="auto_delete_old_data" 
                                                   name="privacy_settings[auto_delete_old_data]" 
                                                   value="1"
                                                   {{ old('privacy_settings.auto_delete_old_data', $customer->privacy_settings['auto_delete_old_data'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="auto_delete_old_data">
                                                <strong>Auto-Delete Old Data</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Automatically delete old booking data after retention period</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Communication Preferences -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-envelope mr-2"></i>
                                        Communication Preferences
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="allow_business_contact" 
                                                   name="privacy_settings[allow_business_contact]" 
                                                   value="1"
                                                   {{ old('privacy_settings.allow_business_contact', $customer->privacy_settings['allow_business_contact'] ?? true) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="allow_business_contact">
                                                <strong>Allow Business Contact</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Let businesses contact you directly about bookings</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="allow_survey_requests" 
                                                   name="privacy_settings[allow_survey_requests]" 
                                                   value="1"
                                                   {{ old('privacy_settings.allow_survey_requests', $customer->privacy_settings['allow_survey_requests'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="allow_survey_requests">
                                                <strong>Survey Requests</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Receive requests to participate in customer surveys</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="{{ route('customer.profile.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times mr-2"></i>
                                        Cancel
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save mr-2"></i>
                                        Save Privacy Settings
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <style>
        .card {
            border: none;
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 8px rgba(0,0,0,.3);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .custom-control-input:focus ~ .custom-control-label::before {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .text-primary {
            color: #667eea !important;
        }
        
        .alert-info {
            background-color: #e3f2fd;
            border-color: #bbdefb;
            color: #0d47a1;
        }
    </style>
@stop

@section('js')
    <script>
        function contactSupport(type) {
            alert('This feature will redirect you to our support team to help with your data ' + type + ' request.');
        }

        // Show confirmation for important privacy changes
        $('#share_with_businesses, #analytics_tracking, #third_party_sharing').on('change', function() {
            const isChecked = $(this).is(':checked');
            const setting = $(this).attr('id').replace('_', ' ');
            
            if (isChecked && $(this).attr('id') === 'third_party_sharing') {
                if (!confirm('Are you sure you want to allow third-party data sharing? This will share your information with our trusted partners.')) {
                    $(this).prop('checked', false);
                }
            }
        });

        // Auto-save privacy settings
        $('input, select').on('change', function() {
            const formData = {};
            $('form').serializeArray().forEach(item => {
                formData[item.name] = item.value;
            });
            localStorage.setItem('customer-privacy-settings', JSON.stringify(formData));
        });

        // Form submission
        $('form').on('submit', function() {
            localStorage.removeItem('customer-privacy-settings');
        });
    </script>
@stop
