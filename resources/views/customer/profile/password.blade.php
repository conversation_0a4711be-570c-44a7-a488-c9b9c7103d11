@extends('customer.layouts.app')

@section('title', 'Change Password')

@section('content_header')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Change Password</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('customer.profile.index') }}">Profile</a></li>
                        <li class="breadcrumb-item active">Change Password</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6 offset-md-3">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-lock mr-2"></i>
                            Change Your Password
                        </h3>
                    </div>

                    <form method="POST" action="{{ route('customer.profile.update-password') }}">
                        @csrf
                        @method('PUT')

                        <div class="card-body">
                            <!-- Security Notice -->
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                <strong>Security Tip:</strong> Use a strong password with at least 8 characters, including uppercase, lowercase, numbers, and special characters.
                            </div>

                            <!-- Current Password -->
                            <div class="form-group">
                                <label for="current_password">Current Password <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password"
                                           class="form-control @error('current_password') is-invalid @enderror"
                                           id="current_password"
                                           name="current_password"
                                           required
                                           placeholder="Enter your current password">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye" id="current_password_icon"></i>
                                        </button>
                                    </div>
                                </div>
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- New Password -->
                            <div class="form-group">
                                <label for="password">New Password <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password"
                                           class="form-control @error('password') is-invalid @enderror"
                                           id="password"
                                           name="password"
                                           required
                                           minlength="8"
                                           placeholder="Enter your new password">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password_icon"></i>
                                        </button>
                                    </div>
                                </div>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror

                                <!-- Password Strength Indicator -->
                                <div class="mt-2">
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" id="password-strength" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="form-text text-muted" id="password-strength-text">Password strength: <span id="strength-level">None</span></small>
                                </div>
                            </div>

                            <!-- Confirm New Password -->
                            <div class="form-group">
                                <label for="password_confirmation">Confirm New Password <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password"
                                           class="form-control @error('password_confirmation') is-invalid @enderror"
                                           id="password_confirmation"
                                           name="password_confirmation"
                                           required
                                           placeholder="Confirm your new password">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password_confirmation')">
                                            <i class="fas fa-eye" id="password_confirmation_icon"></i>
                                        </button>
                                    </div>
                                </div>
                                @error('password_confirmation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div id="password-match-feedback" class="mt-1"></div>
                            </div>

                            <!-- Password Requirements -->
                            <div class="card bg-light">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">Password Requirements:</h6>
                                    <ul class="list-unstyled mb-0" id="password-requirements">
                                        <li id="req-length"><i class="fas fa-times text-danger mr-2"></i>At least 8 characters</li>
                                        <li id="req-uppercase"><i class="fas fa-times text-danger mr-2"></i>One uppercase letter</li>
                                        <li id="req-lowercase"><i class="fas fa-times text-danger mr-2"></i>One lowercase letter</li>
                                        <li id="req-number"><i class="fas fa-times text-danger mr-2"></i>One number</li>
                                        <li id="req-special"><i class="fas fa-times text-danger mr-2"></i>One special character</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="{{ route('customer.profile.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times mr-2"></i>
                                        Cancel
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="submit" class="btn btn-primary" id="submit-btn" disabled>
                                        <i class="fas fa-save mr-2"></i>
                                        Update Password
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Security Tips -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-shield-alt mr-2"></i>
                            Security Tips
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-check text-success mr-2"></i>Do:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-arrow-right text-muted mr-2"></i>Use a unique password</li>
                                    <li><i class="fas fa-arrow-right text-muted mr-2"></i>Include mixed characters</li>
                                    <li><i class="fas fa-arrow-right text-muted mr-2"></i>Change regularly</li>
                                    <li><i class="fas fa-arrow-right text-muted mr-2"></i>Use a password manager</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-times text-danger mr-2"></i>Don't:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-arrow-right text-muted mr-2"></i>Use personal information</li>
                                    <li><i class="fas fa-arrow-right text-muted mr-2"></i>Reuse old passwords</li>
                                    <li><i class="fas fa-arrow-right text-muted mr-2"></i>Share with others</li>
                                    <li><i class="fas fa-arrow-right text-muted mr-2"></i>Use common words</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <style>
        .card {
            border: none;
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover:not(:disabled) {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .btn-primary:disabled {
            background: #6c757d;
            opacity: 0.6;
        }

        .progress-bar {
            transition: all 0.3s ease;
        }

        .requirement-met {
            color: #28a745 !important;
        }

        .requirement-not-met {
            color: #dc3545 !important;
        }
    </style>
@stop

@section('js')
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + '_icon');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        function checkPasswordStrength(password) {
            let score = 0;
            let feedback = [];

            // Length check
            if (password.length >= 8) {
                score += 20;
                updateRequirement('req-length', true);
            } else {
                updateRequirement('req-length', false);
            }

            // Uppercase check
            if (/[A-Z]/.test(password)) {
                score += 20;
                updateRequirement('req-uppercase', true);
            } else {
                updateRequirement('req-uppercase', false);
            }

            // Lowercase check
            if (/[a-z]/.test(password)) {
                score += 20;
                updateRequirement('req-lowercase', true);
            } else {
                updateRequirement('req-lowercase', false);
            }

            // Number check
            if (/[0-9]/.test(password)) {
                score += 20;
                updateRequirement('req-number', true);
            } else {
                updateRequirement('req-number', false);
            }

            // Special character check
            if (/[^A-Za-z0-9]/.test(password)) {
                score += 20;
                updateRequirement('req-special', true);
            } else {
                updateRequirement('req-special', false);
            }

            // Update progress bar
            const progressBar = document.getElementById('password-strength');
            const strengthText = document.getElementById('strength-level');

            progressBar.style.width = score + '%';

            if (score < 40) {
                progressBar.className = 'progress-bar bg-danger';
                strengthText.textContent = 'Weak';
            } else if (score < 80) {
                progressBar.className = 'progress-bar bg-warning';
                strengthText.textContent = 'Medium';
            } else {
                progressBar.className = 'progress-bar bg-success';
                strengthText.textContent = 'Strong';
            }

            return score >= 80;
        }

        function updateRequirement(reqId, met) {
            const element = document.getElementById(reqId);
            const icon = element.querySelector('i');

            if (met) {
                icon.className = 'fas fa-check text-success mr-2';
                element.classList.add('requirement-met');
                element.classList.remove('requirement-not-met');
            } else {
                icon.className = 'fas fa-times text-danger mr-2';
                element.classList.add('requirement-not-met');
                element.classList.remove('requirement-met');
            }
        }

        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmation = document.getElementById('password_confirmation').value;
            const feedback = document.getElementById('password-match-feedback');

            if (confirmation.length > 0) {
                if (password === confirmation) {
                    feedback.innerHTML = '<small class="text-success"><i class="fas fa-check mr-1"></i>Passwords match</small>';
                    return true;
                } else {
                    feedback.innerHTML = '<small class="text-danger"><i class="fas fa-times mr-1"></i>Passwords do not match</small>';
                    return false;
                }
            } else {
                feedback.innerHTML = '';
                return false;
            }
        }

        function updateSubmitButton() {
            const currentPassword = document.getElementById('current_password').value;
            const password = document.getElementById('password').value;
            const confirmation = document.getElementById('password_confirmation').value;
            const submitBtn = document.getElementById('submit-btn');

            const isStrong = checkPasswordStrength(password);
            const isMatching = checkPasswordMatch();
            const hasCurrentPassword = currentPassword.length > 0;

            if (hasCurrentPassword && isStrong && isMatching) {
                submitBtn.disabled = false;
            } else {
                submitBtn.disabled = true;
            }
        }

        // Event listeners
        document.getElementById('password').addEventListener('input', updateSubmitButton);
        document.getElementById('password_confirmation').addEventListener('input', updateSubmitButton);
        document.getElementById('current_password').addEventListener('input', updateSubmitButton);

        // Form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmation = document.getElementById('password_confirmation').value;

            if (password !== confirmation) {
                e.preventDefault();
                showNotification('Passwords do not match!', 'danger');
                return false;
            }

            if (!checkPasswordStrength(password)) {
                e.preventDefault();
                showNotification('Password does not meet security requirements!', 'danger');
                return false;
            }
        });
    </script>
@stop
