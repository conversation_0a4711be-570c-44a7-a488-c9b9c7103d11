@extends('customer.layouts.app')

@section('title', 'Preferences')

@section('content_header')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Preferences</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('customer.profile.index') }}">Profile</a></li>
                        <li class="breadcrumb-item active">Preferences</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cog mr-2"></i>
                            Manage Your Preferences
                        </h3>
                    </div>
                    
                    <form method="POST" action="{{ route('customer.preferences.update') }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="card-body">
                            <!-- Notification Preferences -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-bell mr-2"></i>
                                        Notification Preferences
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="email_notifications" 
                                                   name="preferences[email_notifications]" 
                                                   value="1"
                                                   {{ old('preferences.email_notifications', $customer->preferences['email_notifications'] ?? true) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="email_notifications">
                                                <strong>Email Notifications</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Receive booking confirmations and updates via email</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="sms_notifications" 
                                                   name="preferences[sms_notifications]" 
                                                   value="1"
                                                   {{ old('preferences.sms_notifications', $customer->preferences['sms_notifications'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="sms_notifications">
                                                <strong>SMS Notifications</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Receive text message reminders and updates</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="booking_reminders" 
                                                   name="preferences[booking_reminders]" 
                                                   value="1"
                                                   {{ old('preferences.booking_reminders', $customer->preferences['booking_reminders'] ?? true) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="booking_reminders">
                                                <strong>Booking Reminders</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Get reminded about upcoming appointments</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="promotional_emails" 
                                                   name="preferences[promotional_emails]" 
                                                   value="1"
                                                   {{ old('preferences.promotional_emails', $customer->preferences['promotional_emails'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="promotional_emails">
                                                <strong>Promotional Emails</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Receive special offers and promotions</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Booking Preferences -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-calendar-check mr-2"></i>
                                        Booking Preferences
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="reminder_time">Reminder Time</label>
                                        <select class="form-control" id="reminder_time" name="preferences[reminder_time]">
                                            <option value="15" {{ old('preferences.reminder_time', $customer->preferences['reminder_time'] ?? '60') == '15' ? 'selected' : '' }}>15 minutes before</option>
                                            <option value="30" {{ old('preferences.reminder_time', $customer->preferences['reminder_time'] ?? '60') == '30' ? 'selected' : '' }}>30 minutes before</option>
                                            <option value="60" {{ old('preferences.reminder_time', $customer->preferences['reminder_time'] ?? '60') == '60' ? 'selected' : '' }}>1 hour before</option>
                                            <option value="120" {{ old('preferences.reminder_time', $customer->preferences['reminder_time'] ?? '60') == '120' ? 'selected' : '' }}>2 hours before</option>
                                            <option value="1440" {{ old('preferences.reminder_time', $customer->preferences['reminder_time'] ?? '60') == '1440' ? 'selected' : '' }}>1 day before</option>
                                        </select>
                                        <small class="text-muted">When to send appointment reminders</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="default_duration">Default Appointment Duration</label>
                                        <select class="form-control" id="default_duration" name="preferences[default_duration]">
                                            <option value="30" {{ old('preferences.default_duration', $customer->preferences['default_duration'] ?? '60') == '30' ? 'selected' : '' }}>30 minutes</option>
                                            <option value="45" {{ old('preferences.default_duration', $customer->preferences['default_duration'] ?? '60') == '45' ? 'selected' : '' }}>45 minutes</option>
                                            <option value="60" {{ old('preferences.default_duration', $customer->preferences['default_duration'] ?? '60') == '60' ? 'selected' : '' }}>1 hour</option>
                                            <option value="90" {{ old('preferences.default_duration', $customer->preferences['default_duration'] ?? '60') == '90' ? 'selected' : '' }}>1.5 hours</option>
                                            <option value="120" {{ old('preferences.default_duration', $customer->preferences['default_duration'] ?? '60') == '120' ? 'selected' : '' }}>2 hours</option>
                                        </select>
                                        <small class="text-muted">Preferred appointment length</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="auto_confirm" 
                                                   name="preferences[auto_confirm]" 
                                                   value="1"
                                                   {{ old('preferences.auto_confirm', $customer->preferences['auto_confirm'] ?? false) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="auto_confirm">
                                                <strong>Auto-confirm Bookings</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Automatically confirm available time slots</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" 
                                                   class="custom-control-input" 
                                                   id="allow_waitlist" 
                                                   name="preferences[allow_waitlist]" 
                                                   value="1"
                                                   {{ old('preferences.allow_waitlist', $customer->preferences['allow_waitlist'] ?? true) ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="allow_waitlist">
                                                <strong>Join Waitlists</strong>
                                            </label>
                                        </div>
                                        <small class="text-muted">Automatically join waitlists when slots are full</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Display Preferences -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-eye mr-2"></i>
                                        Display Preferences
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="date_format">Date Format</label>
                                        <select class="form-control" id="date_format" name="preferences[date_format]">
                                            <option value="MM/DD/YYYY" {{ old('preferences.date_format', $customer->preferences['date_format'] ?? 'MM/DD/YYYY') == 'MM/DD/YYYY' ? 'selected' : '' }}>MM/DD/YYYY</option>
                                            <option value="DD/MM/YYYY" {{ old('preferences.date_format', $customer->preferences['date_format'] ?? 'MM/DD/YYYY') == 'DD/MM/YYYY' ? 'selected' : '' }}>DD/MM/YYYY</option>
                                            <option value="YYYY-MM-DD" {{ old('preferences.date_format', $customer->preferences['date_format'] ?? 'MM/DD/YYYY') == 'YYYY-MM-DD' ? 'selected' : '' }}>YYYY-MM-DD</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="time_format">Time Format</label>
                                        <select class="form-control" id="time_format" name="preferences[time_format]">
                                            <option value="12" {{ old('preferences.time_format', $customer->preferences['time_format'] ?? '12') == '12' ? 'selected' : '' }}>12-hour (AM/PM)</option>
                                            <option value="24" {{ old('preferences.time_format', $customer->preferences['time_format'] ?? '12') == '24' ? 'selected' : '' }}>24-hour</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="calendar_view">Default Calendar View</label>
                                        <select class="form-control" id="calendar_view" name="preferences[calendar_view]">
                                            <option value="month" {{ old('preferences.calendar_view', $customer->preferences['calendar_view'] ?? 'month') == 'month' ? 'selected' : '' }}>Month View</option>
                                            <option value="week" {{ old('preferences.calendar_view', $customer->preferences['calendar_view'] ?? 'month') == 'week' ? 'selected' : '' }}>Week View</option>
                                            <option value="day" {{ old('preferences.calendar_view', $customer->preferences['calendar_view'] ?? 'month') == 'day' ? 'selected' : '' }}>Day View</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="items_per_page">Items Per Page</label>
                                        <select class="form-control" id="items_per_page" name="preferences[items_per_page]">
                                            <option value="10" {{ old('preferences.items_per_page', $customer->preferences['items_per_page'] ?? '20') == '10' ? 'selected' : '' }}>10 items</option>
                                            <option value="20" {{ old('preferences.items_per_page', $customer->preferences['items_per_page'] ?? '20') == '20' ? 'selected' : '' }}>20 items</option>
                                            <option value="50" {{ old('preferences.items_per_page', $customer->preferences['items_per_page'] ?? '20') == '50' ? 'selected' : '' }}>50 items</option>
                                            <option value="100" {{ old('preferences.items_per_page', $customer->preferences['items_per_page'] ?? '20') == '100' ? 'selected' : '' }}>100 items</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="{{ route('customer.profile.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times mr-2"></i>
                                        Cancel
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save mr-2"></i>
                                        Save Preferences
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Quick Actions -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <h5>Privacy Settings</h5>
                                <p class="text-muted">Control your privacy and data sharing settings.</p>
                                <a href="{{ route('customer.privacy') }}" class="btn btn-success">
                                    Privacy Settings
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-edit fa-2x text-primary mb-2"></i>
                                <h5>Edit Profile</h5>
                                <p class="text-muted">Update your personal information and contact details.</p>
                                <a href="{{ route('customer.profile.edit') }}" class="btn btn-primary">
                                    Edit Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <style>
        .card {
            border: none;
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 8px rgba(0,0,0,.3);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .custom-control-input:focus ~ .custom-control-label::before {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .text-primary {
            color: #667eea !important;
        }
    </style>
@stop

@section('js')
    <script>
        // Auto-save preferences
        $('input, select').on('change', function() {
            const formData = {};
            $('form').serializeArray().forEach(item => {
                formData[item.name] = item.value;
            });
            localStorage.setItem('customer-preferences', JSON.stringify(formData));
        });

        // Form submission
        $('form').on('submit', function() {
            localStorage.removeItem('customer-preferences');
        });

        // Show confirmation for important changes
        $('#email_notifications, #sms_notifications').on('change', function() {
            const isChecked = $(this).is(':checked');
            const type = $(this).attr('id').replace('_notifications', '');
            
            if (!isChecked) {
                if (!confirm(`Are you sure you want to disable ${type.toUpperCase()} notifications? You may miss important updates about your bookings.`)) {
                    $(this).prop('checked', true);
                }
            }
        });
    </script>
@stop
