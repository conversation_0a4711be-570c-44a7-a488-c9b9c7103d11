@extends('customer.layouts.app')

@section('title', 'Edit Profile')

@section('content_header')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Edit Profile</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('customer.profile.index') }}">Profile</a></li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <!-- Display Success/Error Messages -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle mr-2"></i>
                        {{ session('success') }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        {{ session('error') }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                @endif

                @if($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Please correct the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                @endif

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-edit mr-2"></i>
                            Edit Your Profile
                        </h3>
                    </div>

                    <form method="POST" action="{{ route('customer.profile.update') }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="card-body">
                            <!-- Profile Image Section -->
                            <div class="form-group text-center mb-4">
                                <label class="form-label">Profile Picture</label>
                                <div class="mb-3">
                                    @if($customer->profile_image)
                                        <img src="{{ asset('storage/' . $customer->profile_image) }}"
                                             alt="Current Profile Image"
                                             class="img-circle img-fluid"
                                             style="width: 120px; height: 120px; object-fit: cover;"
                                             id="current-image">
                                    @else
                                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center"
                                             style="width: 120px; height: 120px; font-size: 3rem;"
                                             id="current-image">
                                            {{ strtoupper(substr($customer->name, 0, 1)) }}
                                        </div>
                                    @endif
                                </div>
                                <div class="custom-file" style="max-width: 300px; margin: 0 auto;">
                                    <input type="file" class="custom-file-input" id="profile_image" name="profile_image" accept="image/*">
                                    <label class="custom-file-label" for="profile_image">Choose new picture</label>
                                </div>
                                <small class="form-text text-muted">
                                    Accepted formats: JPEG, PNG, JPG, GIF. Max size: 2MB
                                </small>
                                @error('profile_image')
                                    <div class="text-danger mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="row">
                                <!-- Basic Information -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">Full Name <span class="text-danger">*</span></label>
                                        <input type="text"
                                               class="form-control @error('name') is-invalid @enderror"
                                               id="name"
                                               name="name"
                                               value="{{ old('name', $customer->name) }}"
                                               required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email Address <span class="text-danger">*</span></label>
                                        <input type="email"
                                               class="form-control @error('email') is-invalid @enderror"
                                               id="email"
                                               name="email"
                                               value="{{ old('email', $customer->email) }}"
                                               required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="phone">Phone Number</label>
                                        <input type="tel"
                                               class="form-control @error('phone') is-invalid @enderror"
                                               id="phone"
                                               name="phone"
                                               value="{{ old('phone', $customer->phone) }}"
                                               placeholder="+****************">
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="date_of_birth">Date of Birth</label>
                                        <input type="date"
                                               class="form-control @error('date_of_birth') is-invalid @enderror"
                                               id="date_of_birth"
                                               name="date_of_birth"
                                               value="{{ old('date_of_birth', $customer->date_of_birth ? (is_string($customer->date_of_birth) ? $customer->date_of_birth : $customer->date_of_birth->format('Y-m-d')) : '') }}">
                                        @error('date_of_birth')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="gender">Gender</label>
                                        <select class="form-control @error('gender') is-invalid @enderror"
                                                id="gender"
                                                name="gender">
                                            <option value="">Select Gender</option>
                                            <option value="male" {{ old('gender', $customer->gender) === 'male' ? 'selected' : '' }}>Male</option>
                                            <option value="female" {{ old('gender', $customer->gender) === 'female' ? 'selected' : '' }}>Female</option>
                                            <option value="other" {{ old('gender', $customer->gender) === 'other' ? 'selected' : '' }}>Other</option>
                                            <option value="prefer_not_to_say" {{ old('gender', $customer->gender) === 'prefer_not_to_say' ? 'selected' : '' }}>Prefer not to say</option>
                                        </select>
                                        @error('gender')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="timezone">Timezone</label>
                                        <select class="form-control @error('timezone') is-invalid @enderror"
                                                id="timezone"
                                                name="timezone">
                                            <option value="">Select Timezone</option>
                                            <option value="America/New_York" {{ old('timezone', $customer->timezone) === 'America/New_York' ? 'selected' : '' }}>Eastern Time (ET)</option>
                                            <option value="America/Chicago" {{ old('timezone', $customer->timezone) === 'America/Chicago' ? 'selected' : '' }}>Central Time (CT)</option>
                                            <option value="America/Denver" {{ old('timezone', $customer->timezone) === 'America/Denver' ? 'selected' : '' }}>Mountain Time (MT)</option>
                                            <option value="America/Los_Angeles" {{ old('timezone', $customer->timezone) === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time (PT)</option>
                                            <option value="UTC" {{ old('timezone', $customer->timezone) === 'UTC' ? 'selected' : '' }}>UTC</option>
                                        </select>
                                        @error('timezone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="language">Preferred Language</label>
                                        <select class="form-control @error('language') is-invalid @enderror"
                                                id="language"
                                                name="language">
                                            <option value="">Select Language</option>
                                            <option value="en" {{ old('language', $customer->language) === 'en' ? 'selected' : '' }}>English</option>
                                            <option value="es" {{ old('language', $customer->language) === 'es' ? 'selected' : '' }}>Spanish</option>
                                            <option value="fr" {{ old('language', $customer->language) === 'fr' ? 'selected' : '' }}>French</option>
                                            <option value="de" {{ old('language', $customer->language) === 'de' ? 'selected' : '' }}>German</option>
                                            <option value="it" {{ old('language', $customer->language) === 'it' ? 'selected' : '' }}>Italian</option>
                                            <option value="pt" {{ old('language', $customer->language) === 'pt' ? 'selected' : '' }}>Portuguese</option>
                                        </select>
                                        @error('language')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="{{ route('customer.profile.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times mr-2"></i>
                                        Cancel
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save mr-2"></i>
                                        Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Additional Actions -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-lock fa-2x text-warning mb-2"></i>
                                <h5>Change Password</h5>
                                <p class="text-muted">Update your account password for security.</p>
                                <a href="{{ route('customer.profile.password') }}" class="btn btn-warning">
                                    Change Password
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-cog fa-2x text-info mb-2"></i>
                                <h5>Preferences</h5>
                                <p class="text-muted">Manage your notification and booking preferences.</p>
                                <a href="{{ route('customer.preferences') }}" class="btn btn-info">
                                    Manage Preferences
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <style>
        .card {
            border: none;
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 8px rgba(0,0,0,.3);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .custom-file-label::after {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
        }

        #current-image {
            border: 3px solid #e9ecef;
            transition: all 0.3s ease;
        }

        #current-image:hover {
            border-color: #667eea;
        }
    </style>
@stop

@section('js')
    <script>
        // Handle file input change
        $('#profile_image').on('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#current-image').html('<img src="' + e.target.result + '" class="img-circle img-fluid" style="width: 120px; height: 120px; object-fit: cover;">');
                };
                reader.readAsDataURL(file);

                // Update label
                $(this).next('.custom-file-label').text(file.name);
            }
        });

        // Form submission handling
        $('form').on('submit', function(e) {
            // Show loading state
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...');

            // Re-enable button after 10 seconds as fallback
            setTimeout(function() {
                submitBtn.prop('disabled', false).html(originalText);
            }, 10000);
        });

        // Auto-save form data
        $('input, select').on('change', function() {
            const formData = {};
            $('form').serializeArray().forEach(item => {
                formData[item.name] = item.value;
            });
            localStorage.setItem('customer-profile-edit', JSON.stringify(formData));
        });

        // Load saved data on page load
        $(document).ready(function() {
            const savedData = localStorage.getItem('customer-profile-edit');
            if (savedData) {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(key => {
                    const field = $(`[name="${key}"]`);
                    if (field.length && !field.val()) {
                        field.val(data[key]);
                    }
                });
            }
        });

        // Clear saved data on successful submit
        $('form').on('submit', function() {
            localStorage.removeItem('customer-profile-edit');
        });
    </script>
@stop
