@extends('customer.layouts.app')

@section('title', 'Customer Dashboard')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Customer Dashboard</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item active">Dashboard</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info elevation-1"><i class="fas fa-calendar-check"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Bookings</span>
                        <span class="info-box-number">{{ $metrics['total_bookings'] ?? 0 }}</span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-clock"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Upcoming</span>
                        <span class="info-box-number">{{ $metrics['upcoming_bookings'] ?? 0 }}</span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-dollar-sign"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Spent</span>
                        <span class="info-box-number">${{ number_format($metrics['total_spent'] ?? 0, 2) }}</span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-gift"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Loyalty Points</span>
                        <span class="info-box-number">{{ $metrics['loyalty_points'] ?? 0 }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main row -->
        <div class="row">
            <!-- Left col -->
            <section class="col-lg-7 connectedSortable">
                <!-- Booking History Chart -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line mr-1"></i>
                            Booking History
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="bookingChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Bookings -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Bookings</h3>
                        <div class="card-tools">
                            <a href="{{ route('customer.bookings.index') }}" class="btn btn-tool">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <ul class="products-list product-list-in-card pl-2 pr-2">
                            @forelse($metrics['recent_bookings'] ?? [] as $booking)
                                <li class="item">
                                    <div class="product-img">
                                        <i class="fas fa-calendar-alt text-info"></i>
                                    </div>
                                    <div class="product-info">
                                        <a href="{{ route('customer.bookings.show', $booking['id']) }}" class="product-title">
                                            {{ $booking['business_name'] }}
                                            <span class="badge badge-{{ $booking['status'] === 'completed' ? 'success' : ($booking['status'] === 'cancelled' ? 'danger' : 'warning') }} float-right">
                                                {{ ucfirst($booking['status']) }}
                                            </span>
                                        </a>
                                        <span class="product-description">
                                            {{ $booking['service'] }} - {{ $booking['date'] }} at {{ $booking['time'] }}
                                        </span>
                                    </div>
                                </li>
                            @empty
                                <li class="item">
                                    <div class="product-info">
                                        <span class="product-description text-muted">No recent bookings found.</span>
                                    </div>
                                </li>
                            @endforelse
                        </ul>
                    </div>
                    <div class="card-footer text-center">
                        <a href="{{ route('customer.bookings.index') }}" class="uppercase">View All Bookings</a>
                    </div>
                </div>
            </section>

            <!-- Right col -->
            <section class="col-lg-5 connectedSortable">
                <!-- Quick Actions -->
                <div class="card bg-gradient-info">
                    <div class="card-header border-0">
                        <h3 class="card-title">
                            <i class="fas fa-bolt mr-1"></i>
                            Quick Actions
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <a href="{{ route('customer.bookings.create') }}" class="btn btn-light btn-block">
                                    <i class="fas fa-plus"></i> New Booking
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="{{ route('customer.services.index') }}" class="btn btn-light btn-block">
                                    <i class="fas fa-search"></i> Browse Services
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Appointments -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Upcoming Appointments</h3>
                    </div>
                    <div class="card-body p-0">
                        <ul class="todo-list" data-widget="todo-list">
                            @forelse($metrics['upcoming_appointments'] ?? [] as $appointment)
                                <li>
                                    <span class="handle">
                                        <i class="fas fa-ellipsis-v"></i>
                                        <i class="fas fa-ellipsis-v"></i>
                                    </span>
                                    <div class="icheck-primary d-inline ml-2">
                                        <input type="checkbox" value="" name="todo{{ $loop->index }}" id="todoCheck{{ $loop->index }}">
                                        <label for="todoCheck{{ $loop->index }}"></label>
                                    </div>
                                    <span class="text">{{ $appointment['business_name'] }} - {{ $appointment['service'] }}</span>
                                    <small class="badge badge-info">
                                        <i class="far fa-clock"></i> {{ $appointment['date'] }} {{ $appointment['time'] }}
                                    </small>
                                    <div class="tools">
                                        <a href="{{ route('customer.bookings.show', $appointment['id']) }}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($appointment['can_cancel'])
                                            <i class="fas fa-times text-danger"></i>
                                        @endif
                                    </div>
                                </li>
                            @empty
                                <li>
                                    <span class="text text-muted">No upcoming appointments.</span>
                                </li>
                            @endforelse
                        </ul>
                    </div>
                </div>

                <!-- Favorite Businesses -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Favorite Businesses</h3>
                    </div>
                    <div class="card-body p-0">
                        <ul class="users-list clearfix">
                            @forelse($metrics['favorite_businesses'] ?? [] as $business)
                                <li>
                                    <img src="https://via.placeholder.com/128x128" alt="Business Image">
                                    <a class="users-list-name" href="#">{{ $business['name'] }}</a>
                                    <span class="users-list-date">{{ $business['booking_count'] }} bookings</span>
                                </li>
                            @empty
                                <li class="text-center text-muted">
                                    No favorite businesses yet.
                                </li>
                            @endforelse
                        </ul>
                    </div>
                </div>

                <!-- Recent Notifications -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Notifications</h3>
                        <div class="card-tools">
                            <a href="{{ route('customer.notifications.index') }}" class="btn btn-tool">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table m-0">
                                <tbody>
                                    @forelse($metrics['recent_notifications'] ?? [] as $notification)
                                        <tr>
                                            <td>
                                                <span class="badge badge-{{ $notification['read_at'] ? 'secondary' : 'primary' }}">
                                                    {{ $notification['title'] }}
                                                </span>
                                            </td>
                                            <td class="text-right">
                                                <small class="text-muted">{{ $notification['created_at'] }}</small>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="2" class="text-center text-muted">No notifications.</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(function () {
            // Booking History Chart
            var bookingChartCanvas = $('#bookingChart').get(0).getContext('2d');
            var bookingChartData = @json($metrics['booking_history_chart'] ?? ['labels' => [], 'datasets' => []]);

            var bookingChart = new Chart(bookingChartCanvas, {
                type: 'line',
                data: bookingChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Auto-refresh stats every 30 seconds
            setInterval(function() {
                $.get('{{ route("customer.dashboard.quick-stats") }}', function(data) {
                    // Update info boxes with new data
                    $('.info-box-number').each(function(index) {
                        var newValue = Object.values(data)[index] || 0;
                        $(this).text(newValue);
                    });
                });
            }, 30000);
        });
    </script>
@stop
