@extends('customer.layouts.app')

@section('title', 'My Favorites')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">My Favorites</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Favorites</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <!-- Quick Actions -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card bg-gradient-info">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h4 class="text-white mb-1">Your Favorite Services</h4>
                                <p class="text-white-50 mb-0">Quick access to your most loved services and businesses.</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="{{ route('customer.services.index') }}" class="btn btn-light">
                                    <i class="fas fa-search mr-2"></i>
                                    Browse Services
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Favorite Services -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-heart mr-2"></i>
                            Favorite Services
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @forelse($favorites as $service)
                                <div class="col-md-6 col-lg-4 mb-4" id="favorite-{{ $service->id }}">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="service-icon bg-primary text-white mr-3" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                    <i class="fas fa-{{ $service->category->icon ?? 'concierge-bell' }}"></i>
                                                </div>
                                                <div>
                                                    <h5 class="card-title mb-0">{{ $service->name }}</h5>
                                                    <small class="text-muted">{{ $service->category->name ?? 'General' }}</small>
                                                </div>
                                            </div>
                                            <p class="card-text">{{ Str::limit($service->short_description ?? $service->description, 80) }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="h5 text-primary">${{ number_format($service->base_price, 2) }}</span>
                                                    <small class="text-muted">{{ $service->duration_minutes }} min</small>
                                                </div>
                                                <div>
                                                    <button class="btn btn-outline-danger btn-sm" onclick="removeFavorite({{ $service->id }})">
                                                        <i class="fas fa-heart"></i>
                                                    </button>
                                                    <a href="{{ route('customer.bookings.create', ['service' => $service->id]) }}" class="btn btn-primary btn-sm">
                                                        Book Now
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <!-- Empty state when no favorites -->
                                <div class="col-12 text-center" id="empty-state">
                                    <div class="py-5">
                                        <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                                        <h4>No favorites yet</h4>
                                        <p class="text-muted">Start adding services to your favorites to see them here.</p>
                                        <a href="{{ route('customer.services.index') }}" class="btn btn-primary">
                                            <i class="fas fa-search mr-2"></i>
                                            Browse Services
                                        </a>
                                    </div>
                                </div>
                            @endforelse
                        </div>

                        @if($favorites->hasPages())
                            <!-- Pagination -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-center">
                                        {{ $favorites->links() }}
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <a href="{{ route('customer.services.index') }}" class="btn btn-primary btn-block">
                                    <i class="fas fa-search mr-2"></i>
                                    Browse All Services
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ route('customer.bookings.create') }}" class="btn btn-success btn-block">
                                    <i class="fas fa-plus mr-2"></i>
                                    Book Appointment
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary btn-block">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <style>
        .service-icon {
            transition: all 0.3s ease;
        }
        .card:hover .service-icon {
            transform: scale(1.1);
        }
        .card {
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
@stop

@section('js')
    <script>
        function removeFavorite(serviceId) {
            if (confirm('Remove this service from your favorites?')) {
                // Make AJAX call to remove the favorite
                fetch('{{ route("customer.favorites.toggle") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        service_id: serviceId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Hide the card with animation
                        const card = document.getElementById('favorite-' + serviceId);
                        if (card) {
                            card.style.transition = 'all 0.3s ease';
                            card.style.opacity = '0';
                            card.style.transform = 'scale(0.8)';

                            setTimeout(() => {
                                card.style.display = 'none';

                                // Check if there are any favorites left
                                const remainingFavorites = document.querySelectorAll('[id^="favorite-"]:not([style*="display: none"])');
                                if (remainingFavorites.length === 0) {
                                    // Reload the page to show empty state
                                    window.location.reload();
                                }
                            }, 300);
                        }

                        // Show success message
                        showNotification(data.message, 'success');
                    } else {
                        showNotification('Failed to remove from favorites', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('An error occurred', 'danger');
                });
            }
        }
    </script>
@stop
