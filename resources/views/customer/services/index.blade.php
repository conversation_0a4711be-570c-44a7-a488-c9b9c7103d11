@extends('customer.layouts.app')

@section('title', 'Browse Services')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Browse Services</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Services</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <!-- Search and Filters -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="{{ route('customer.services.index') }}">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="search">Search Services</label>
                                        <input type="text" class="form-control" id="search" name="search"
                                               placeholder="Search by name or description..."
                                               value="{{ request('search') }}">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="category">Category</label>
                                        <select class="form-control" id="category" name="category">
                                            <option value="">All Categories</option>
                                            <option value="1" {{ request('category') == '1' ? 'selected' : '' }}>Hair Services</option>
                                            <option value="2" {{ request('category') == '2' ? 'selected' : '' }}>Beauty Services</option>
                                            <option value="3" {{ request('category') == '3' ? 'selected' : '' }}>Wellness</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="price_range">Price Range</label>
                                        <select class="form-control" id="price_range" name="price_range">
                                            <option value="">Any Price</option>
                                            <option value="0-50" {{ request('price_range') == '0-50' ? 'selected' : '' }}>$0 - $50</option>
                                            <option value="50-100" {{ request('price_range') == '50-100' ? 'selected' : '' }}>$50 - $100</option>
                                            <option value="100-200" {{ request('price_range') == '100-200' ? 'selected' : '' }}>$100 - $200</option>
                                            <option value="200+" {{ request('price_range') == '200+' ? 'selected' : '' }}>$200+</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary btn-block">
                                                <i class="fas fa-search mr-1"></i> Search
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Services -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-info">
                    <div class="card-header border-0">
                        <h3 class="card-title text-white">
                            <i class="fas fa-star mr-2"></i>
                            Featured Services
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card bg-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-cut fa-3x text-primary mb-3"></i>
                                        <h5>Premium Haircut</h5>
                                        <p class="text-muted">Professional styling with consultation</p>
                                        <h4 class="text-primary">$75</h4>
                                        <a href="{{ route('customer.services.show', 1) }}" class="btn btn-primary btn-sm">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-spa fa-3x text-success mb-3"></i>
                                        <h5>Relaxing Massage</h5>
                                        <p class="text-muted">60-minute full body massage</p>
                                        <h4 class="text-success">$120</h4>
                                        <a href="{{ route('customer.services.show', 2) }}" class="btn btn-success btn-sm">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-paint-brush fa-3x text-warning mb-3"></i>
                                        <h5>Manicure & Pedicure</h5>
                                        <p class="text-muted">Complete nail care package</p>
                                        <h4 class="text-warning">$65</h4>
                                        <a href="{{ route('customer.services.show', 3) }}" class="btn btn-warning btn-sm">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Services Grid -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-concierge-bell mr-2"></i>
                            All Services
                        </h3>
                        <div class="card-tools">
                            <a href="{{ route('customer.services.categories') }}" class="btn btn-tool">
                                <i class="fas fa-tags"></i> Browse by Category
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Service Card 1 -->
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="service-icon bg-primary text-white mr-3" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-cut"></i>
                                            </div>
                                            <div>
                                                <h5 class="card-title mb-0">Haircut & Style</h5>
                                                <small class="text-muted">Hair Services</small>
                                            </div>
                                        </div>
                                        <p class="card-text">Professional haircut with styling and consultation. Includes wash and blow-dry.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="h5 text-primary">$45</span>
                                                <small class="text-muted">60 min</small>
                                            </div>
                                            <div>
                                                <a href="{{ route('customer.services.show', 1) }}" class="btn btn-outline-primary btn-sm">
                                                    View Details
                                                </a>
                                                <a href="{{ route('customer.bookings.create', ['service' => 1]) }}" class="btn btn-primary btn-sm">
                                                    Book Now
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Service Card 2 -->
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="service-icon bg-success text-white mr-3" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-spa"></i>
                                            </div>
                                            <div>
                                                <h5 class="card-title mb-0">Facial Treatment</h5>
                                                <small class="text-muted">Beauty Services</small>
                                            </div>
                                        </div>
                                        <p class="card-text">Deep cleansing facial with moisturizing treatment. Perfect for all skin types.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="h5 text-success">$85</span>
                                                <small class="text-muted">90 min</small>
                                            </div>
                                            <div>
                                                <a href="{{ route('customer.services.show', 2) }}" class="btn btn-outline-success btn-sm">
                                                    View Details
                                                </a>
                                                <a href="{{ route('customer.bookings.create', ['service' => 2]) }}" class="btn btn-success btn-sm">
                                                    Book Now
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Service Card 3 -->
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="service-icon bg-warning text-white mr-3" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-paint-brush"></i>
                                            </div>
                                            <div>
                                                <h5 class="card-title mb-0">Manicure</h5>
                                                <small class="text-muted">Beauty Services</small>
                                            </div>
                                        </div>
                                        <p class="card-text">Complete nail care with shaping, cuticle care, and polish application.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="h5 text-warning">$35</span>
                                                <small class="text-muted">45 min</small>
                                            </div>
                                            <div>
                                                <a href="{{ route('customer.services.show', 3) }}" class="btn btn-outline-warning btn-sm">
                                                    View Details
                                                </a>
                                                <a href="{{ route('customer.bookings.create', ['service' => 3]) }}" class="btn btn-warning btn-sm">
                                                    Book Now
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Service Card 4 -->
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="service-icon bg-info text-white mr-3" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-tint"></i>
                                            </div>
                                            <div>
                                                <h5 class="card-title mb-0">Hair Coloring</h5>
                                                <small class="text-muted">Hair Services</small>
                                            </div>
                                        </div>
                                        <p class="card-text">Professional hair coloring service with premium products and color consultation.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="h5 text-info">$125</span>
                                                <small class="text-muted">120 min</small>
                                            </div>
                                            <div>
                                                <a href="{{ route('customer.services.show', 4) }}" class="btn btn-outline-info btn-sm">
                                                    View Details
                                                </a>
                                                <a href="{{ route('customer.bookings.create', ['service' => 4]) }}" class="btn btn-info btn-sm">
                                                    Book Now
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Service Card 5 -->
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="service-icon bg-secondary text-white mr-3" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-hands"></i>
                                            </div>
                                            <div>
                                                <h5 class="card-title mb-0">Massage Therapy</h5>
                                                <small class="text-muted">Wellness</small>
                                            </div>
                                        </div>
                                        <p class="card-text">Relaxing full-body massage to relieve stress and muscle tension.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="h5 text-secondary">$95</span>
                                                <small class="text-muted">60 min</small>
                                            </div>
                                            <div>
                                                <a href="{{ route('customer.services.show', 5) }}" class="btn btn-outline-secondary btn-sm">
                                                    View Details
                                                </a>
                                                <a href="{{ route('customer.bookings.create', ['service' => 5]) }}" class="btn btn-secondary btn-sm">
                                                    Book Now
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Service Card 6 -->
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="service-icon bg-danger text-white mr-3" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-eye"></i>
                                            </div>
                                            <div>
                                                <h5 class="card-title mb-0">Eyebrow Shaping</h5>
                                                <small class="text-muted">Beauty Services</small>
                                            </div>
                                        </div>
                                        <p class="card-text">Professional eyebrow shaping and grooming for the perfect arch.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="h5 text-danger">$25</span>
                                                <small class="text-muted">30 min</small>
                                            </div>
                                            <div>
                                                <a href="{{ route('customer.services.show', 6) }}" class="btn btn-outline-danger btn-sm">
                                                    View Details
                                                </a>
                                                <a href="{{ route('customer.bookings.create', ['service' => 6]) }}" class="btn btn-danger btn-sm">
                                                    Book Now
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="row">
                            <div class="col-12">
                                <nav aria-label="Services pagination">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item disabled">
                                            <span class="page-link">Previous</span>
                                        </li>
                                        <li class="page-item active">
                                            <span class="page-link">1</span>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">2</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">3</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">Next</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <a href="{{ route('customer.bookings.create') }}" class="btn btn-primary btn-block">
                                    <i class="fas fa-plus mr-2"></i>
                                    Book Appointment
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ route('customer.favorites.index') }}" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-heart mr-2"></i>
                                    My Favorites
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary btn-block">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <style>
        .service-icon {
            transition: all 0.3s ease;
        }
        .card:hover .service-icon {
            transform: scale(1.1);
        }
        .card {
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
@stop

@section('js')
    <script>
        $(function () {
            // Add to favorites functionality
            $('.btn-favorite').click(function(e) {
                e.preventDefault();
                var serviceId = $(this).data('service-id');
                var button = $(this);

                // Toggle favorite status (placeholder functionality)
                if (button.hasClass('text-danger')) {
                    button.removeClass('text-danger').addClass('text-muted');
                    button.find('i').removeClass('fas').addClass('far');
                } else {
                    button.removeClass('text-muted').addClass('text-danger');
                    button.find('i').removeClass('far').addClass('fas');
                }
            });
        });
    </script>
@stop
