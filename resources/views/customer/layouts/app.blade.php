<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'BookKei Customer')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">

    @yield('adminlte_css_pre')
    @yield('adminlte_css')
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="{{ url('customer/dashboard') }}" class="nav-link">Home</a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <!-- User Account Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="far fa-user"></i>
                        {{ Auth::user()->name ?? 'Customer' }}
                    </a>
                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                        <a href="{{ url('customer/profile') }}" class="dropdown-item">
                            <i class="fas fa-user mr-2"></i> Profile
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{{ route('logout') }}" class="dropdown-item"
                           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="fas fa-sign-out-alt mr-2"></i> Logout
                        </a>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                            @csrf
                        </form>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a href="{{ url('customer/dashboard') }}" class="brand-link">
                <img src="{{ asset('vendor/adminlte/dist/img/AdminLTELogo.png') }}" alt="BookKei Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
                <span class="brand-text font-weight-light">BookKei</span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a href="{{ url('customer/dashboard') }}" class="nav-link {{ request()->is('customer/dashboard') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>Dashboard</p>
                            </a>
                        </li>

                        <!-- Booking Management -->
                        <li class="nav-header">BOOKING MANAGEMENT</li>
                        <li class="nav-item">
                            <a href="{{ url('customer/bookings') }}" class="nav-link {{ request()->is('customer/bookings*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-calendar-check"></i>
                                <p>My Bookings</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url('customer/bookings/create') }}" class="nav-link {{ request()->is('customer/bookings/create') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-plus-circle"></i>
                                <p>New Booking</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url('customer/favorites') }}" class="nav-link {{ request()->is('customer/favorites*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-heart"></i>
                                <p>Favorites</p>
                            </a>
                        </li>

                        <!-- Services -->
                        <li class="nav-header">SERVICES</li>
                        <li class="nav-item">
                            <a href="{{ url('customer/services') }}" class="nav-link {{ request()->is('customer/services') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-list"></i>
                                <p>Browse Services</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url('customer/services/categories') }}" class="nav-link {{ request()->is('customer/services/categories') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-tags"></i>
                                <p>Service Categories</p>
                            </a>
                        </li>

                        <!-- Account -->
                        <li class="nav-header">ACCOUNT</li>
                        <li class="nav-item">
                            <a href="{{ url('customer/profile') }}" class="nav-link {{ request()->is('customer/profile*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-user"></i>
                                <p>My Profile</p>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Content Header -->
            @hasSection('content_header')
                <div class="content-header">
                    @yield('content_header')
                </div>
            @endif

            <!-- Customer Notifications Area -->
            <div id="customer-notifications" class="mb-3" style="display: none;"></div>

            <!-- Main content -->
            <section class="content">
                @yield('content')
            </section>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
            <strong>Copyright &copy; {{ date('Y') }} <a href="#">BookKei</a>.</strong>
            All rights reserved.
            <div class="float-right d-none d-sm-inline-block">
                <b>Version</b> 1.0.0
            </div>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>

    @yield('adminlte_js_pre')

    @yield('css')

    <style>
        /* Customer-specific styling */
        .main-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .nav-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
        }

        .nav-sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .brand-link {
            background-color: rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .brand-text {
            color: white !important;
        }

        .nav-header {
            color: rgba(255, 255, 255, 0.7);
            background-color: rgba(0, 0, 0, 0.1);
        }

        /* Content styling */
        .content-wrapper {
            background-color: #f8f9fa;
        }

        .card {
            border: none;
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 8px rgba(0,0,0,.3);
        }

        .info-box {
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .info-box:hover {
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        /* Service cards */
        .service-icon {
            transition: all 0.3s ease;
        }

        .card:hover .service-icon {
            transform: scale(1.1);
        }

        /* Animation for loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 1.5s infinite;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .info-box {
                margin-bottom: 1rem;
            }

            .card {
                margin-bottom: 1rem;
            }
        }
    </style>

    @yield('js')

    <script>
        // Initialize tooltips
        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
        });

        // Global CSRF token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Global notification system
        function showNotification(message, type = 'info') {
            const notification = $(`
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `);

            $('#customer-notifications').show().html(notification);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                notification.fadeOut();
            }, 5000);
        }

        // Global loading state management
        function setLoadingState(element, loading = true) {
            if (loading) {
                $(element).addClass('loading pulse');
            } else {
                $(element).removeClass('loading pulse');
            }
        }

        // Global error handler for AJAX requests
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status === 401) {
                window.location.href = '/login';
            } else if (xhr.status === 403) {
                showNotification('You do not have permission to perform this action.', 'danger');
            } else if (xhr.status >= 500) {
                showNotification('A server error occurred. Please try again later.', 'danger');
            }
        });

        // Initialize common functionality
        $(document).ready(function() {
            // Add smooth scrolling to anchor links
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if( target.length ) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });

            // Add loading states to buttons
            $('button[type="submit"], .btn-loading').on('click', function() {
                const btn = $(this);
                const originalText = btn.html();
                btn.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
                btn.prop('disabled', true);

                // Re-enable after 10 seconds as fallback
                setTimeout(function() {
                    btn.html(originalText);
                    btn.prop('disabled', false);
                }, 10000);
            });
        });
    </script>
</body>
</html>
