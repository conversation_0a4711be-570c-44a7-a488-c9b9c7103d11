@extends('customer.layouts.app')

@section('title', 'Booking Details')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Booking Details</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('customer.bookings.index') }}">Bookings</a></li>
                    <li class="breadcrumb-item active">Details</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <div class="row">
            <!-- Main Booking Information -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar-check mr-2"></i>
                            Booking Information
                        </h3>
                        <div class="card-tools">
                            <span class="badge" style="background-color: {{ $booking->status_color }}; color: white; font-size: 14px;">
                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Appointment Details</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Booking Number:</strong></td>
                                        <td>{{ $booking->booking_number ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Business:</strong></td>
                                        <td>{{ $booking->business->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Date:</strong></td>
                                        <td>{{ $booking->start_datetime->format('l, F j, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Time:</strong></td>
                                        <td>{{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Duration:</strong></td>
                                        <td>{{ $booking->total_duration_minutes }} minutes</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Amount:</strong></td>
                                        <td><strong>${{ number_format($booking->total_amount, 2) }}</strong></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5>Business Information</h5>
                                <table class="table table-borderless">
                                    @if($booking->business->address)
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>{{ $booking->business->address }}</td>
                                    </tr>
                                    @endif
                                    @if($booking->business->phone)
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>
                                            <a href="tel:{{ $booking->business->phone }}">{{ $booking->business->phone }}</a>
                                        </td>
                                    </tr>
                                    @endif
                                    @if($booking->business->email)
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>
                                            <a href="mailto:{{ $booking->business->email }}">{{ $booking->business->email }}</a>
                                        </td>
                                    </tr>
                                    @endif
                                    @if($booking->business->website)
                                    <tr>
                                        <td><strong>Website:</strong></td>
                                        <td>
                                            <a href="{{ $booking->business->website }}" target="_blank">{{ $booking->business->website }}</a>
                                        </td>
                                    </tr>
                                    @endif
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Details -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-concierge-bell mr-2"></i>
                            Service Details
                        </h3>
                    </div>
                    <div class="card-body">
                        @if($booking->bookingServices && $booking->bookingServices->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>Service</th>
                                            <th class="text-center">Quantity</th>
                                            <th class="text-center">Duration</th>
                                            <th class="text-right">Unit Price</th>
                                            <th class="text-right">Total Price</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($booking->bookingServices as $bookingService)
                                            <tr>
                                                <td>
                                                    @if($bookingService->service)
                                                        <strong>{{ $bookingService->service->name }}</strong>
                                                        @if($bookingService->service->short_description)
                                                            <br><small class="text-muted">{{ $bookingService->service->short_description }}</small>
                                                        @endif
                                                    @else
                                                        <span class="text-muted">Service not found</span>
                                                    @endif
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-secondary">{{ $bookingService->quantity ?? 1 }}</span>
                                                </td>
                                                <td class="text-center">
                                                    @php
                                                        $hours = floor($bookingService->duration_minutes / 60);
                                                        $minutes = $bookingService->duration_minutes % 60;
                                                        $duration = '';
                                                        if ($hours > 0) {
                                                            $duration .= $hours . 'h';
                                                        }
                                                        if ($minutes > 0) {
                                                            $duration .= ($hours > 0 ? ' ' : '') . $minutes . 'm';
                                                        }
                                                    @endphp
                                                    {{ $duration ?: $bookingService->duration_minutes . 'm' }}
                                                </td>
                                                <td class="text-right">
                                                    ${{ number_format($bookingService->unit_price, 2) }}
                                                </td>
                                                <td class="text-right">
                                                    <strong>${{ number_format($bookingService->total_price, 2) }}</strong>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-active">
                                            <td colspan="4" class="text-right"><strong>Total:</strong></td>
                                            <td class="text-right">
                                                <strong>${{ number_format($booking->bookingServices->sum('total_price'), 2) }}</strong>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-light">
                                <i class="fas fa-info-circle mr-2"></i>
                                No services specified for this booking.
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Notes and Special Requests -->
                @if($booking->notes || $booking->special_requests)
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-sticky-note mr-2"></i>
                            Additional Information
                        </h3>
                    </div>
                    <div class="card-body">
                        @if($booking->notes)
                            <div class="mb-3">
                                <h6>Notes:</h6>
                                <p class="text-muted">{{ $booking->notes }}</p>
                            </div>
                        @endif
                        @if($booking->special_requests)
                            <div class="mb-3">
                                <h6>Special Requests:</h6>
                                <p class="text-muted">{{ $booking->special_requests }}</p>
                            </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt mr-2"></i>
                            Quick Actions
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            @if($booking->can_be_edited)
                                <a href="{{ route('customer.bookings.edit', $booking) }}" class="btn btn-warning btn-block mb-2">
                                    <i class="fas fa-edit mr-2"></i>
                                    Edit Booking
                                </a>
                            @endif

                            @if($booking->can_be_cancelled)
                                <button type="button" class="btn btn-danger btn-block mb-2" onclick="cancelBooking()">
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel Booking
                                </button>
                            @endif

                            <a href="{{ route('customer.bookings.index') }}" class="btn btn-secondary btn-block mb-2">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back to Bookings
                            </a>

                            <a href="{{ route('customer.bookings.create') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-plus mr-2"></i>
                                Book Another Appointment
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Booking Timeline -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history mr-2"></i>
                            Booking Timeline
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Booking Created</h6>
                                    <p class="timeline-text">{{ $booking->created_at->format('M j, Y g:i A') }}</p>
                                </div>
                            </div>

                            @if($booking->status !== 'pending')
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Status Updated</h6>
                                    <p class="timeline-text">
                                        Changed to {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                        <br><small class="text-muted">{{ $booking->updated_at->format('M j, Y g:i A') }}</small>
                                    </p>
                                </div>
                            </div>
                            @endif

                            @if($booking->status === 'completed')
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Appointment Completed</h6>
                                    <p class="timeline-text">Service completed successfully</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-phone mr-2"></i>
                            Need Help?
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">If you have any questions about your booking, please contact the business directly:</p>
                        @if($booking->business->phone)
                            <a href="tel:{{ $booking->business->phone }}" class="btn btn-outline-primary btn-block mb-2">
                                <i class="fas fa-phone mr-2"></i>
                                Call {{ $booking->business->name }}
                            </a>
                        @endif
                        @if($booking->business->email)
                            <a href="mailto:{{ $booking->business->email }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-envelope mr-2"></i>
                                Email {{ $booking->business->name }}
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Modal -->
    <div class="modal fade" id="cancelBookingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cancel Booking</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to cancel this booking?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Please note:</strong> Cancellation policies may apply. Contact the business if you have questions about refunds or rescheduling.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Keep Booking</button>
                    <form method="POST" action="{{ route('customer.bookings.cancel', $booking) }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-danger">Yes, Cancel Booking</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}
.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
.timeline-item {
    position: relative;
    margin-bottom: 20px;
}
.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}
.timeline-content {
    padding-left: 10px;
}
.timeline-title {
    margin-bottom: 5px;
    font-weight: 600;
}
.timeline-text {
    margin-bottom: 0;
    color: #6c757d;
    font-size: 0.9em;
}
</style>
@stop

@section('js')
<script>
function cancelBooking() {
    $('#cancelBookingModal').modal('show');
}
</script>
@stop
