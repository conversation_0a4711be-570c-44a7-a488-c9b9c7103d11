@extends('customer.layouts.app')

@section('title', 'My Bookings')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">My Bookings</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Bookings</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <!-- Quick Actions -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card bg-gradient-primary">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h4 class="text-white mb-1">Ready to book your next appointment?</h4>
                                <p class="text-white-50 mb-0">Browse our services and schedule your visit today.</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="{{ route('customer.bookings.create') }}" class="btn btn-light btn-lg">
                                    <i class="fas fa-plus mr-2"></i>
                                    New Booking
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header p-0 pt-1">
                        <ul class="nav nav-tabs" id="booking-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link {{ request('status', 'all') === 'all' ? 'active' : '' }}"
                                   href="{{ route('customer.bookings.index', ['status' => 'all']) }}">
                                    <i class="fas fa-list mr-1"></i>
                                    All Bookings
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request('status') === 'upcoming' ? 'active' : '' }}"
                                   href="{{ route('customer.bookings.index', ['status' => 'upcoming']) }}">
                                    <i class="fas fa-clock mr-1"></i>
                                    Upcoming
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request('status') === 'pending' ? 'active' : '' }}"
                                   href="{{ route('customer.bookings.index', ['status' => 'pending']) }}">
                                    <i class="fas fa-hourglass-half mr-1"></i>
                                    Pending
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request('status') === 'confirmed' ? 'active' : '' }}"
                                   href="{{ route('customer.bookings.index', ['status' => 'confirmed']) }}">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Confirmed
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request('status') === 'completed' ? 'active' : '' }}"
                                   href="{{ route('customer.bookings.index', ['status' => 'completed']) }}">
                                    <i class="fas fa-check-double mr-1"></i>
                                    Completed
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        @if($bookings->count() > 0)
                            <div class="row">
                                @foreach($bookings as $booking)
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100 booking-card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-building mr-1"></i>
                                                    {{ $booking->business->name }}
                                                </h6>
                                                <span class="badge" style="background-color: {{ $booking->status_color }}; color: white;">
                                                    {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                                </span>
                                            </div>
                                            <div class="card-body">
                                                <div class="booking-details">
                                                    <div class="detail-item mb-2">
                                                        <i class="fas fa-calendar text-primary mr-2"></i>
                                                        <strong>{{ $booking->start_datetime->format('M d, Y') }}</strong>
                                                    </div>
                                                    <div class="detail-item mb-2">
                                                        <i class="fas fa-clock text-info mr-2"></i>
                                                        {{ $booking->start_datetime->format('g:i A') }} -
                                                        {{ $booking->end_datetime->format('g:i A') }}
                                                    </div>
                                                    <div class="detail-item mb-2">
                                                        <i class="fas fa-concierge-bell text-success mr-2"></i>
                                                        @if($booking->services->count() > 0)
                                                            {{ $booking->services->pluck('name')->join(', ') }}
                                                        @else
                                                            No services specified
                                                        @endif
                                                    </div>
                                                    <div class="detail-item mb-2">
                                                        <i class="fas fa-dollar-sign text-warning mr-2"></i>
                                                        ${{ number_format($booking->total_amount, 2) }}
                                                    </div>
                                                    @if($booking->notes)
                                                        <div class="detail-item mb-2">
                                                            <i class="fas fa-sticky-note text-secondary mr-2"></i>
                                                            <small>{{ Str::limit($booking->notes, 50) }}</small>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <div class="btn-group w-100" role="group">
                                                    <a href="{{ route('customer.bookings.show', $booking) }}"
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                        View
                                                    </a>
                                                    @if($booking->can_be_edited)
                                                        <a href="{{ route('customer.bookings.edit', $booking) }}"
                                                           class="btn btn-outline-warning btn-sm">
                                                            <i class="fas fa-edit"></i>
                                                            Edit
                                                        </a>
                                                    @endif
                                                    @if($booking->can_be_cancelled)
                                                        <button type="button"
                                                                class="btn btn-outline-danger btn-sm"
                                                                onclick="cancelBooking({{ $booking->id }})">
                                                            <i class="fas fa-times"></i>
                                                            Cancel
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Pagination -->
                            <div class="row">
                                <div class="col-12">
                                    {{ $bookings->appends(request()->query())->links() }}
                                </div>
                            </div>
                        @else
                            <div class="text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-calendar-times fa-4x text-muted"></i>
                                </div>
                                <h4 class="text-muted">No bookings found</h4>
                                <p class="text-muted">
                                    @if(request('status') === 'all')
                                        You haven't made any bookings yet.
                                    @else
                                        No {{ request('status') }} bookings found.
                                    @endif
                                </p>
                                <a href="{{ route('customer.bookings.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus mr-2"></i>
                                    Create Your First Booking
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <a href="{{ route('customer.bookings.history') }}" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-history mr-2"></i>
                                    View Booking History
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ route('customer.services.index') }}" class="btn btn-outline-success btn-block">
                                    <i class="fas fa-search mr-2"></i>
                                    Browse Services
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary btn-block">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Modal -->
    <div class="modal fade" id="cancelBookingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cancel Booking</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to cancel this booking?</p>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Keep Booking</button>
                    <form id="cancelBookingForm" method="POST" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-danger">Yes, Cancel Booking</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
.booking-card {
    transition: transform 0.2s, box-shadow 0.2s;
}
.booking-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.detail-item {
    display: flex;
    align-items: center;
}
.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
}
.nav-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
    border-radius: 0.25rem 0.25rem 0 0;
}
.nav-tabs .nav-link:hover {
    border: none;
    background-color: #e9ecef;
}
.nav-tabs .nav-link.active:hover {
    background-color: #0056b3;
}
</style>
@stop

@section('js')
<script>
function cancelBooking(bookingId) {
    $('#cancelBookingForm').attr('action', '{{ route("customer.bookings.cancel", ":id") }}'.replace(':id', bookingId));
    $('#cancelBookingModal').modal('show');
}

$(document).ready(function() {
    // Auto-refresh page every 30 seconds for real-time updates
    setTimeout(function() {
        location.reload();
    }, 30000);
});
</script>
@stop
