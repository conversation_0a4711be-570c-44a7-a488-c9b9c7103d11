@extends('adminlte::page', ['config' => config('customerlte')])

@section('title', 'Create New Booking')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Create New Booking</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('customer.bookings.index') }}">Bookings</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-plus mr-2"></i>
                    Book a New Appointment
                </h3>
            </div>
            <form method="POST" action="{{ route('customer.bookings.store') }}">
                @csrf
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <!-- Business Selection -->
                            <div class="form-group">
                                <label for="business_id">Select Business <span class="text-danger">*</span></label>
                                <select class="form-control" id="business_id" name="business_id" required>
                                    <option value="">Choose a business...</option>
                                    @foreach($businesses as $business)
                                        <option value="{{ $business->id }}" {{ old('business_id') == $business->id ? 'selected' : '' }}>
                                            {{ $business->name }}
                                            @if($business->address)
                                                - {{ $business->address }}
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Services Selection -->
                            <div class="form-group">
                                <label for="service_ids">Select Services <span class="text-danger">*</span></label>
                                <div id="services-container">
                                    <p class="text-muted">Please select a business first to see available services.</p>
                                </div>
                            </div>

                            <!-- Date and Time -->
                            <div class="form-group">
                                <label for="start_datetime">Preferred Date & Time <span class="text-danger">*</span></label>
                                <input type="datetime-local" 
                                       class="form-control" 
                                       id="start_datetime" 
                                       name="start_datetime" 
                                       value="{{ old('start_datetime') }}" 
                                       min="{{ now()->format('Y-m-d\TH:i') }}"
                                       required>
                                <small class="form-text text-muted">Select your preferred appointment time.</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Available Time Slots -->
                            <div class="form-group">
                                <label>Available Time Slots</label>
                                <div id="available-slots" class="border rounded p-3" style="min-height: 200px;">
                                    <p class="text-muted text-center">Select a business, services, and date to see available time slots.</p>
                                </div>
                            </div>

                            <!-- Booking Summary -->
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Booking Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div id="booking-summary">
                                        <p class="text-muted">No services selected yet.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <!-- Notes -->
                            <div class="form-group">
                                <label for="notes">Notes (Optional)</label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          placeholder="Any additional notes or comments...">{{ old('notes') }}</textarea>
                            </div>

                            <!-- Special Requests -->
                            <div class="form-group">
                                <label for="special_requests">Special Requests (Optional)</label>
                                <textarea class="form-control" 
                                          id="special_requests" 
                                          name="special_requests" 
                                          rows="3" 
                                          placeholder="Any special requests or requirements...">{{ old('special_requests') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calendar-check mr-2"></i>
                        Create Booking
                    </button>
                    <a href="{{ route('customer.bookings.index') }}" class="btn btn-secondary ml-2">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
@stop

@section('css')
<style>
.service-checkbox {
    margin-bottom: 10px;
}
.service-info {
    font-size: 0.9em;
    color: #666;
}
.time-slot {
    cursor: pointer;
    transition: all 0.2s;
}
.time-slot:hover {
    background-color: #e9ecef;
}
.time-slot.selected {
    background-color: #007bff;
    color: white;
}
.booking-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}
</style>
@stop

@section('js')
<script>
$(document).ready(function() {
    let selectedServices = [];
    let selectedTimeSlot = null;
    
    // Handle business selection change
    $('#business_id').change(function() {
        const businessId = $(this).val();
        if (businessId) {
            loadServices(businessId);
        } else {
            $('#services-container').html('<p class="text-muted">Please select a business first to see available services.</p>');
            updateBookingSummary();
        }
    });

    // Handle date/time change
    $('#start_datetime').change(function() {
        loadAvailableSlots();
    });

    function loadServices(businessId) {
        const business = @json($businesses).find(b => b.id == businessId);
        if (business && business.services.length > 0) {
            let html = '<div class="services-list">';
            business.services.forEach(service => {
                html += `
                    <div class="service-checkbox">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input service-check" 
                                   id="service_${service.id}" 
                                   name="service_ids[]" 
                                   value="${service.id}">
                            <label class="custom-control-label" for="service_${service.id}">
                                <strong>${service.name}</strong>
                                <div class="service-info">
                                    Duration: ${service.duration_minutes} minutes | 
                                    Price: $${parseFloat(service.price).toFixed(2)}
                                </div>
                                ${service.description ? `<div class="service-info">${service.description}</div>` : ''}
                            </label>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            $('#services-container').html(html);
            
            // Bind change event to service checkboxes
            $('.service-check').change(function() {
                updateSelectedServices();
                updateBookingSummary();
                loadAvailableSlots();
            });
        } else {
            $('#services-container').html('<p class="text-muted">No services available for this business.</p>');
        }
    }

    function updateSelectedServices() {
        selectedServices = [];
        $('.service-check:checked').each(function() {
            const serviceId = $(this).val();
            const business = @json($businesses).find(b => b.id == $('#business_id').val());
            const service = business.services.find(s => s.id == serviceId);
            if (service) {
                selectedServices.push(service);
            }
        });
    }

    function updateBookingSummary() {
        if (selectedServices.length === 0) {
            $('#booking-summary').html('<p class="text-muted">No services selected yet.</p>');
            return;
        }

        let totalDuration = 0;
        let totalPrice = 0;
        let html = '<div class="booking-summary-items">';
        
        selectedServices.forEach(service => {
            totalDuration += parseInt(service.duration_minutes);
            totalPrice += parseFloat(service.price);
            html += `
                <div class="booking-summary-item">
                    <span>${service.name}</span>
                    <span>$${parseFloat(service.price).toFixed(2)}</span>
                </div>
            `;
        });
        
        html += `
            <hr>
            <div class="booking-summary-item">
                <strong>Total Duration:</strong>
                <strong>${totalDuration} minutes</strong>
            </div>
            <div class="booking-summary-item">
                <strong>Total Price:</strong>
                <strong>$${totalPrice.toFixed(2)}</strong>
            </div>
        `;
        html += '</div>';
        
        $('#booking-summary').html(html);
    }

    function loadAvailableSlots() {
        const businessId = $('#business_id').val();
        const datetime = $('#start_datetime').val();
        
        if (!businessId || !datetime || selectedServices.length === 0) {
            $('#available-slots').html('<p class="text-muted text-center">Select a business, services, and date to see available time slots.</p>');
            return;
        }

        $('#available-slots').html('<p class="text-muted text-center">Loading available slots...</p>');

        // Make AJAX request to get available slots
        $.get('{{ route("customer.bookings.available-slots") }}', {
            business_id: businessId,
            service_ids: selectedServices.map(s => s.id),
            date: datetime.split('T')[0]
        })
        .done(function(data) {
            if (data.slots && data.slots.length > 0) {
                let html = '<div class="row">';
                data.slots.forEach(slot => {
                    html += `
                        <div class="col-6 col-md-4 mb-2">
                            <div class="time-slot btn btn-outline-primary btn-block" data-time="${slot}">
                                ${slot}
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                $('#available-slots').html(html);
                
                // Bind click event to time slots
                $('.time-slot').click(function() {
                    $('.time-slot').removeClass('selected');
                    $(this).addClass('selected');
                    selectedTimeSlot = $(this).data('time');
                    
                    // Update the datetime input
                    const date = $('#start_datetime').val().split('T')[0];
                    $('#start_datetime').val(date + 'T' + selectedTimeSlot);
                });
            } else {
                $('#available-slots').html('<p class="text-muted text-center">No available slots for the selected date and services.</p>');
            }
        })
        .fail(function() {
            $('#available-slots').html('<p class="text-danger text-center">Error loading available slots. Please try again.</p>');
        });
    }

    // Initialize if business is pre-selected
    if ($('#business_id').val()) {
        loadServices($('#business_id').val());
    }
});
</script>
@stop
