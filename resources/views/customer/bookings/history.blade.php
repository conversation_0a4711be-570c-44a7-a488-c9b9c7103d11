@extends('adminlte::page', ['config' => config('customerlte')])

@section('title', 'Booking History')

@section('content_header')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Booking History</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('customer.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('customer.bookings.index') }}">Bookings</a></li>
                    <li class="breadcrumb-item active">History</li>
                </ol>
            </div>
        </div>
    </div>
@stop

@section('content')
    <div class="container-fluid">
        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info elevation-1"><i class="fas fa-calendar-check"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Bookings</span>
                        <span class="info-box-number">{{ $summary['total_bookings'] ?? 0 }}</span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-dollar-sign"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Spent</span>
                        <span class="info-box-number">${{ number_format($summary['total_spent'] ?? 0, 2) }}</span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-building"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Favorite Business</span>
                        <span class="info-box-number" style="font-size: 14px;">
                            {{ $summary['most_booked_business'] ?? 'N/A' }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-heart"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Favorite Service</span>
                        <span class="info-box-number" style="font-size: 14px;">
                            {{ $summary['favorite_service'] ?? 'N/A' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter mr-2"></i>
                    Filter History
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('customer.bookings.history') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date_from">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ $dateFrom }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date_to">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ $dateTo }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="business_id">Business</label>
                                <select class="form-control" id="business_id" name="business_id">
                                    <option value="">All Businesses</option>
                                    @foreach($businesses as $business)
                                        <option value="{{ $business->id }}" {{ $businessId == $business->id ? 'selected' : '' }}>
                                            {{ $business->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search mr-1"></i>
                                        Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Booking History -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history mr-2"></i>
                    Your Booking History
                </h3>
                <div class="card-tools">
                    <a href="{{ route('customer.bookings.index') }}" class="btn btn-tool">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if($bookings->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Business</th>
                                    <th>Services</th>
                                    <th>Duration</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($bookings as $booking)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $booking->start_datetime->format('M j, Y') }}</strong><br>
                                                <small class="text-muted">{{ $booking->start_datetime->format('g:i A') }} - {{ $booking->end_datetime->format('g:i A') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $booking->business->name }}</strong><br>
                                                @if($booking->business->address)
                                                    <small class="text-muted">{{ Str::limit($booking->business->address, 30) }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            @if($booking->services->count() > 0)
                                                @foreach($booking->services as $service)
                                                    <span class="badge badge-secondary mb-1">{{ $service->name }}</span>
                                                    @if(!$loop->last)<br>@endif
                                                @endforeach
                                            @else
                                                <span class="text-muted">No services</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $booking->total_duration_minutes }} min</span>
                                        </td>
                                        <td>
                                            <strong>${{ number_format($booking->total_amount, 2) }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge" style="background-color: {{ $booking->status_color }}; color: white;">
                                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('customer.bookings.show', $booking) }}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($booking->status === 'completed')
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-success" 
                                                            title="Book Again"
                                                            onclick="bookAgain({{ $booking->id }})">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="card-footer">
                        {{ $bookings->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-calendar-times fa-4x text-muted"></i>
                        </div>
                        <h4 class="text-muted">No booking history found</h4>
                        <p class="text-muted">
                            @if(request()->hasAny(['date_from', 'date_to', 'business_id']))
                                No bookings match your filter criteria.
                            @else
                                You haven't made any bookings yet.
                            @endif
                        </p>
                        <div class="mt-3">
                            @if(request()->hasAny(['date_from', 'date_to', 'business_id']))
                                <a href="{{ route('customer.bookings.history') }}" class="btn btn-secondary mr-2">
                                    <i class="fas fa-times mr-1"></i>
                                    Clear Filters
                                </a>
                            @endif
                            <a href="{{ route('customer.bookings.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i>
                                Create Your First Booking
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <a href="{{ route('customer.bookings.create') }}" class="btn btn-primary btn-block">
                                    <i class="fas fa-plus mr-2"></i>
                                    New Booking
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('customer.bookings.index') }}" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-list mr-2"></i>
                                    Current Bookings
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('customer.services.index') }}" class="btn btn-outline-success btn-block">
                                    <i class="fas fa-search mr-2"></i>
                                    Browse Services
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary btn-block">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Book Again Modal -->
    <div class="modal fade" id="bookAgainModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Book Again</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Would you like to book the same services again?</p>
                    <p class="text-muted">This will take you to the booking form with the same business and services pre-selected.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <a href="#" id="bookAgainLink" class="btn btn-primary">Yes, Book Again</a>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
.table td {
    vertical-align: middle;
}
.badge {
    font-size: 0.8em;
}
.info-box-number {
    font-size: 1.5rem;
}
.btn-group .btn {
    margin-right: 2px;
}
.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
@stop

@section('js')
<script>
function bookAgain(bookingId) {
    const bookAgainUrl = '{{ route("customer.bookings.create") }}?booking_id=' + bookingId;
    $('#bookAgainLink').attr('href', bookAgainUrl);
    $('#bookAgainModal').modal('show');
}

$(document).ready(function() {
    // Set max date for date inputs to today
    const today = new Date().toISOString().split('T')[0];
    $('#date_from, #date_to').attr('max', today);
    
    // Auto-submit form when business filter changes
    $('#business_id').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
@stop
