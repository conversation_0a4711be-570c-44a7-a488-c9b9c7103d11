<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Title
    |--------------------------------------------------------------------------
    |
    | Here you can change the default title of your customer panel.
    |
    | For detailed instructions you can look the title section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'title' => 'BookKei Customer',
    'title_prefix' => '',
    'title_postfix' => ' - Customer Portal',

    /*
    |--------------------------------------------------------------------------
    | Favicon
    |--------------------------------------------------------------------------
    |
    | Here you can activate the favicon.
    |
    | For detailed instructions you can look the favicon section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'use_ico_only' => false,
    'use_full_favicon' => false,

    /*
    |--------------------------------------------------------------------------
    | Google Fonts
    |--------------------------------------------------------------------------
    |
    | Here you can allow or not the use of external google fonts. Disabling the
    | google fonts may be useful if your admin panel internet access is
    | restricted somehow.
    |
    | For detailed instructions you can look the google fonts section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'google_fonts' => [
        'allowed' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Customer Panel Logo
    |--------------------------------------------------------------------------
    |
    | Here you can change the logo of your customer panel.
    |
    | For detailed instructions you can look the logo section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'logo' => '<b>Book</b>Kei <span class="text-sm">Portal</span>',
    'logo_img' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
    'logo_img_class' => 'brand-image img-circle elevation-3',
    'logo_img_xl' => null,
    'logo_img_xl_class' => 'brand-image-xs',
    'logo_img_alt' => 'BookKei Customer Logo',

    /*
    |--------------------------------------------------------------------------
    | Authentication Logo
    |--------------------------------------------------------------------------
    |
    | Here you can setup an alternative logo to use on your login and register
    | screens. When disabled, the admin panel logo will be used instead.
    |
    | For detailed instructions you can look the auth logo section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'auth_logo' => [
        'enabled' => false,
        'img' => [
            'path' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
            'alt' => 'Customer Auth Logo',
            'class' => '',
            'width' => 50,
            'height' => 50,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Preloader Animation
    |--------------------------------------------------------------------------
    |
    | Here you can change the preloader animation configuration. Currently, two
    | modes are supported: 'fullscreen' for a fullscreen preloader animation
    | and 'cwrapper' to attach the preloader animation into the content-wrapper
    | element and avoid overlapping it with the sidebars and the top navbar.
    |
    | For detailed instructions you can look the preloader section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'preloader' => [
        'enabled' => true,
        'mode' => 'fullscreen',
        'img' => [
            'path' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
            'alt' => 'BookKei Customer Preloader',
            'effect' => 'animation__shake',
            'width' => 60,
            'height' => 60,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Menu
    |--------------------------------------------------------------------------
    |
    | Here you can activate and change the user menu.
    |
    | For detailed instructions you can look the user menu section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'usermenu_enabled' => true,
    'usermenu_header' => false,
    'usermenu_header_class' => 'bg-info',
    'usermenu_image' => false,
    'usermenu_desc' => false,
    'usermenu_profile_url' => false,

    /*
    |--------------------------------------------------------------------------
    | Layout
    |--------------------------------------------------------------------------
    |
    | Here we change the layout of your customer panel.
    |
    | For detailed instructions you can look the layout section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'layout_topnav' => null,
    'layout_boxed' => null,
    'layout_fixed_sidebar' => null,
    'layout_fixed_navbar' => null,
    'layout_fixed_footer' => null,
    'layout_dark_mode' => null,

    /*
    |--------------------------------------------------------------------------
    | Authentication Views Classes
    |--------------------------------------------------------------------------
    |
    | Here you can change the look and behavior of the authentication views.
    |
    | For detailed instructions you can look the auth classes section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'classes_auth_card' => 'card-outline card-info',
    'classes_auth_header' => '',
    'classes_auth_body' => '',
    'classes_auth_footer' => '',
    'classes_auth_icon' => '',
    'classes_auth_btn' => 'btn-flat btn-info',

    /*
    |--------------------------------------------------------------------------
    | Customer Panel Classes
    |--------------------------------------------------------------------------
    |
    | Here you can change the look and behavior of the customer panel.
    |
    | For detailed instructions you can look the admin panel classes here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'classes_body' => '',
    'classes_brand' => '',
    'classes_brand_text' => '',
    'classes_content_wrapper' => '',
    'classes_content_header' => '',
    'classes_content' => '',
    'classes_sidebar' => 'sidebar-dark-info elevation-4',
    'classes_sidebar_nav' => '',
    'classes_topnav' => 'navbar-white navbar-light',
    'classes_topnav_nav' => 'navbar-expand',
    'classes_topnav_container' => 'container',

    /*
    |--------------------------------------------------------------------------
    | Sidebar
    |--------------------------------------------------------------------------
    |
    | Here we can modify the sidebar of the customer panel.
    |
    | For detailed instructions you can look the sidebar section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'sidebar_mini' => 'lg',
    'sidebar_collapse' => false,
    'sidebar_collapse_auto_size' => false,
    'sidebar_collapse_remember' => false,
    'sidebar_collapse_remember_no_transition' => true,
    'sidebar_scrollbar_theme' => 'os-theme-light',
    'sidebar_scrollbar_auto_hide' => 'l',
    'sidebar_nav_accordion' => true,
    'sidebar_nav_animation_speed' => 300,

    /*
    |--------------------------------------------------------------------------
    | Control Sidebar (Right Sidebar)
    |--------------------------------------------------------------------------
    |
    | Here we can modify the right sidebar aka control sidebar of the customer panel.
    |
    | For detailed instructions you can look the right sidebar section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'right_sidebar' => false,
    'right_sidebar_icon' => 'fas fa-cogs',
    'right_sidebar_theme' => 'dark',
    'right_sidebar_slide' => true,
    'right_sidebar_push' => true,
    'right_sidebar_scrollbar_theme' => 'os-theme-light',
    'right_sidebar_scrollbar_auto_hide' => 'l',

    /*
    |--------------------------------------------------------------------------
    | URLs
    |--------------------------------------------------------------------------
    |
    | Here we can modify the url settings of the customer panel.
    |
    | For detailed instructions you can look the urls section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'use_route_url' => false,
    'dashboard_url' => 'customer/dashboard',
    'logout_url' => 'logout',
    'login_url' => 'login',
    'register_url' => 'register',
    'password_reset_url' => 'password/reset',
    'password_email_url' => 'password/email',
    'profile_url' => 'customer/profile',

    /*
    |--------------------------------------------------------------------------
    | Laravel Mix
    |--------------------------------------------------------------------------
    |
    | Here we can enable the Laravel Mix option for the customer panel.
    |
    | For detailed instructions you can look the laravel mix section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Other-Configuration
    |
    */

    'enabled_laravel_mix' => false,
    'laravel_mix_css_path' => 'css/app.css',
    'laravel_mix_js_path' => 'js/app.js',

    /*
    |--------------------------------------------------------------------------
    | Menu Items
    |--------------------------------------------------------------------------
    |
    | Here we can modify the sidebar/top navigation of the customer panel.
    | Each menu item should have a text and and a URL. You can also specify
    | an icon from Font Awesome. A string instead of an array represents
    | a header in sidebar layout. The 'can' is a filter on Laravel's built
    | in Gate functionality.
    |
    | For detailed instructions you can look the menu section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Menu-Configuration
    |
    */

    'menu' => [
        // Main Navigation
        [
            'text' => 'Dashboard',
            'route' => 'customer.dashboard',
            'icon' => 'fas fa-tachometer-alt',
            'topnav' => false,
        ],

        // Booking Management
        'BOOKING MANAGEMENT',
        [
            'text' => 'My Bookings',
            'route' => 'customer.bookings.index',
            'icon' => 'fas fa-calendar-check',
            'topnav' => false,
        ],
        [
            'text' => 'New Booking',
            'route' => 'customer.bookings.create',
            'icon' => 'fas fa-plus-circle',
            'topnav' => false,
        ],
        [
            'text' => 'Booking History',
            'route' => 'customer.bookings.history',
            'icon' => 'fas fa-history',
            'topnav' => false,
        ],
        [
            'text' => 'Favorites',
            'route' => 'customer.favorites.index',
            'icon' => 'fas fa-heart',
            'topnav' => false,
        ],

        // Services
        'SERVICES',
        [
            'text' => 'Browse Services',
            'route' => 'customer.services.index',
            'icon' => 'fas fa-list',
            'topnav' => false,
        ],
        [
            'text' => 'Service Categories',
            'url' => 'customer/services/categories',
            'icon' => 'fas fa-tags',
            'topnav' => false,
        ],
        [
            'text' => 'Reviews & Ratings',
            'url' => 'customer/reviews',
            'icon' => 'fas fa-star',
            'topnav' => false,
        ],

        // Account Management
        'ACCOUNT',
        [
            'text' => 'My Profile',
            'url' => 'customer/profile',
            'icon' => 'fas fa-user',
            'topnav' => false,
        ],
        [
            'text' => 'Payment Methods',
            'url' => 'customer/payment-methods',
            'icon' => 'fas fa-credit-card',
            'topnav' => false,
        ],
        [
            'text' => 'Billing History',
            'url' => 'customer/billing',
            'icon' => 'fas fa-receipt',
            'topnav' => false,
        ],
        [
            'text' => 'Preferences',
            'url' => 'customer/preferences',
            'icon' => 'fas fa-cog',
            'topnav' => false,
        ],

        // Communication
        'COMMUNICATION',
        [
            'text' => 'Messages',
            'url' => 'customer/messages',
            'icon' => 'fas fa-envelope',
            'topnav' => false,
            'label' => function() {
                return auth()->user() ? auth()->user()->unreadNotifications()->count() : 0;
            },
            'label_color' => 'success',
        ],
        [
            'text' => 'Notifications',
            'url' => 'customer/notifications',
            'icon' => 'fas fa-bell',
            'topnav' => false,
        ],
        [
            'text' => 'Support Center',
            'url' => 'customer/support',
            'icon' => 'fas fa-question-circle',
            'topnav' => false,
        ],

        // Activity & Reports
        'ACTIVITY',
        [
            'text' => 'Activity Log',
            'url' => 'customer/activity',
            'icon' => 'fas fa-list-alt',
            'topnav' => false,
        ],
        [
            'text' => 'Loyalty Points',
            'url' => 'customer/loyalty',
            'icon' => 'fas fa-gift',
            'topnav' => false,
        ],
        [
            'text' => 'Referrals',
            'url' => 'customer/referrals',
            'icon' => 'fas fa-users',
            'topnav' => false,
        ],

        // Help & Support
        'HELP & SUPPORT',
        [
            'text' => 'Help Center',
            'url' => 'customer/help',
            'icon' => 'fas fa-life-ring',
            'topnav' => false,
        ],
        [
            'text' => 'Contact Us',
            'url' => 'customer/contact',
            'icon' => 'fas fa-phone',
            'topnav' => false,
        ],
        [
            'text' => 'Terms & Privacy',
            'url' => 'customer/legal',
            'icon' => 'fas fa-file-contract',
            'topnav' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Menu Filters
    |--------------------------------------------------------------------------
    |
    | Here we can modify the menu filters of the customer panel.
    |
    | For detailed instructions you can look the menu filters section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Menu-Configuration
    |
    */

    'filters' => [
        JeroenNoten\LaravelAdminLte\Menu\Filters\GateFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\HrefFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\SearchFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\ActiveFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\LangFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\DataFilter::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Plugins Initialization
    |--------------------------------------------------------------------------
    |
    | Here we can modify the plugins used inside the customer panel.
    |
    | For detailed instructions you can look the plugins section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Plugins-Configuration
    |
    */

    'plugins' => [
        'Datatables' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/js/dataTables.bootstrap4.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/css/dataTables.bootstrap4.min.css',
                ],
            ],
        ],
        'Select2' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.css',
                ],
            ],
        ],
        'Chartjs' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.0/Chart.bundle.min.js',
                ],
            ],
        ],
        'Sweetalert2' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/sweetalert2@8',
                ],
            ],
        ],
        'Pace' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/themes/blue/pace-theme-center-radar.min.css',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/pace.min.js',
                ],
            ],
        ],
        'TempusDominusBs4' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/moment.js/2.22.2/moment.min.js',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.0.1/js/tempusdominus-bootstrap-4.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.0.1/css/tempusdominus-bootstrap-4.min.css',
                ],
            ],
        ],
        'BsCustomFileInput' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/bs-custom-file-input/dist/bs-custom-file-input.min.js',
                ],
            ],
        ],
        'BootstrapSwitch' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/bootstrap-switch/3.3.4/js/bootstrap-switch.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/bootstrap-switch/3.3.4/css/bootstrap3/bootstrap-switch.min.css',
                ],
            ],
        ],
        'FullCalendar' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/fullcalendar@5.11.0/main.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/fullcalendar@5.11.0/main.min.css',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | IFrame
    |--------------------------------------------------------------------------
    |
    | Here we change the IFrame mode configuration. Note these changes will
    | only apply to the view that extends and enable the IFrame mode.
    |
    | For detailed instructions you can look the iframe mode section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/IFrame-Mode-Configuration
    |
    */

    'iframe' => [
        'default_tab' => [
            'url' => null,
            'title' => null,
        ],
        'buttons' => [
            'close' => true,
            'close_all' => true,
            'close_all_other' => true,
            'scroll_left' => true,
            'scroll_right' => true,
            'fullscreen' => true,
        ],
        'options' => [
            'loading_screen' => 1000,
            'auto_show_new_tab' => true,
            'use_navbar_items' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Livewire
    |--------------------------------------------------------------------------
    |
    | Here we can enable the Livewire support.
    |
    | For detailed instructions you can look the livewire here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Other-Configuration
    |
    */

    'livewire' => false,

    /*
    |--------------------------------------------------------------------------
    | Customer Panel Specific Settings
    |--------------------------------------------------------------------------
    |
    | Here we define customer-specific settings that differentiate this
    | panel from the admin and owner panels.
    |
    */

    'customer_settings' => [
        'theme_color' => 'info',
        'accent_color' => 'cyan',
        'enable_notifications' => true,
        'enable_real_time_updates' => true,
        'max_bookings_per_day' => 5,
        'default_timezone' => 'UTC',
        'date_format' => 'Y-m-d',
        'time_format' => 'H:i',
        'enable_reviews' => true,
        'enable_loyalty_program' => true,
        'enable_referral_system' => true,
        'booking_reminder_hours' => [24, 2],
        'cancellation_policy_hours' => 2,
        'show_pricing' => true,
        'enable_online_payment' => true,
        'default_view' => 'dashboard',
    ],

    /*
    |--------------------------------------------------------------------------
    | Customer Dashboard Widgets
    |--------------------------------------------------------------------------
    |
    | Here we define the widgets that will be displayed on the customer
    | dashboard. Each widget can be enabled/disabled and configured.
    |
    */

    'dashboard_widgets' => [
        'upcoming_bookings' => [
            'enabled' => true,
            'title' => 'Upcoming Bookings',
            'icon' => 'fas fa-calendar-alt',
            'color' => 'primary',
            'limit' => 5,
        ],
        'quick_book' => [
            'enabled' => true,
            'title' => 'Quick Book',
            'icon' => 'fas fa-plus-circle',
            'color' => 'success',
        ],
        'recent_activity' => [
            'enabled' => true,
            'title' => 'Recent Activity',
            'icon' => 'fas fa-history',
            'color' => 'info',
            'limit' => 10,
        ],
        'favorite_services' => [
            'enabled' => true,
            'title' => 'Favorite Services',
            'icon' => 'fas fa-heart',
            'color' => 'danger',
            'limit' => 6,
        ],
        'loyalty_points' => [
            'enabled' => true,
            'title' => 'Loyalty Points',
            'icon' => 'fas fa-gift',
            'color' => 'warning',
        ],
        'notifications' => [
            'enabled' => true,
            'title' => 'Recent Notifications',
            'icon' => 'fas fa-bell',
            'color' => 'secondary',
            'limit' => 5,
        ],
    ],

];