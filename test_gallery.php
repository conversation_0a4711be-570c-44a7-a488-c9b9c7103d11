<?php

require_once 'vendor/autoload.php';

use App\Models\Business;
use App\Models\BusinessGalleryCategory;

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Gallery Functionality\n";
echo "============================\n";

// Test business retrieval
$business = Business::first();
if (!$business) {
    echo "No business found in database\n";
    exit;
}

echo "Business found: {$business->name}\n";

// Test gallery relationships
echo "Gallery categories count: " . $business->galleryCategories()->count() . "\n";
echo "Gallery images count: " . $business->galleryImages()->count() . "\n";

// Test creating a category
try {
    $category = $business->galleryCategories()->create([
        'name' => 'Test Category',
        'slug' => 'test-category-' . time(),
        'description' => 'Test category for gallery functionality',
        'color' => '#007bff',
        'is_active' => true,
        'sort_order' => 0
    ]);
    
    echo "Category created successfully: {$category->name}\n";
    echo "Category ID: {$category->id}\n";
    
    // Clean up
    $category->delete();
    echo "Test category cleaned up\n";
    
} catch (Exception $e) {
    echo "Error creating category: " . $e->getMessage() . "\n";
}

echo "\nGallery functionality test completed!\n";
