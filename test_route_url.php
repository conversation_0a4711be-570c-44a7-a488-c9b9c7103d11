<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing route URL generation...\n\n";

try {
    // Test URL generation
    $bookingId = 3; // Use the booking ID we found earlier
    
    echo "Testing different URL generation methods:\n";
    echo "1. route() helper: " . route('owner.calendar.booking', $bookingId) . "\n";
    echo "2. url() helper: " . url('owner/calendar/booking/' . $bookingId) . "\n";
    echo "3. Direct URL: /owner/calendar/booking/" . $bookingId . "\n";
    
    // Test if the route exists
    echo "\nTesting route existence:\n";
    $routes = app('router')->getRoutes();
    $routeFound = false;
    
    foreach ($routes as $route) {
        if ($route->getName() === 'owner.calendar.booking') {
            $routeFound = true;
            echo "✓ Route 'owner.calendar.booking' found\n";
            echo "  URI: " . $route->uri() . "\n";
            echo "  Methods: " . implode(', ', $route->methods()) . "\n";
            echo "  Action: " . $route->getActionName() . "\n";
            break;
        }
    }
    
    if (!$routeFound) {
        echo "✗ Route 'owner.calendar.booking' not found\n";
    }
    
    // Test middleware
    echo "\nTesting middleware:\n";
    if ($routeFound) {
        $middleware = $route->middleware();
        echo "Middleware: " . implode(', ', $middleware) . "\n";
    }
    
    // Test if we can access the route with authentication
    echo "\nTesting route access:\n";
    
    // Get a business owner user
    $owner = \App\Models\User::role('Business Owner')->first();
    if ($owner) {
        echo "Found owner: " . $owner->name . "\n";
        
        // Simulate authentication
        auth()->login($owner);
        echo "Authenticated as owner\n";
        
        // Test the route
        $request = \Illuminate\Http\Request::create(
            url('owner/calendar/booking/' . $bookingId),
            'GET'
        );
        
        // Set the authenticated user in the request
        $request->setUserResolver(function () use ($owner) {
            return $owner;
        });
        
        echo "Testing route with authenticated request...\n";
        
        try {
            $response = app()->handle($request);
            echo "Response status: " . $response->getStatusCode() . "\n";
            
            if ($response->getStatusCode() === 200) {
                echo "✓ Route accessible and returns 200\n";
                
                // Check if it's JSON
                $content = $response->getContent();
                $json = json_decode($content, true);
                
                if ($json !== null) {
                    echo "✓ Response is valid JSON\n";
                    if (isset($json['success'])) {
                        echo "✓ Response has 'success' field: " . ($json['success'] ? 'true' : 'false') . "\n";
                        if ($json['success'] && isset($json['html'])) {
                            echo "✓ Response contains HTML content\n";
                        }
                    }
                } else {
                    echo "✗ Response is not valid JSON\n";
                    echo "Content preview: " . substr($content, 0, 200) . "...\n";
                }
            } else {
                echo "✗ Route returned status: " . $response->getStatusCode() . "\n";
                echo "Content: " . $response->getContent() . "\n";
            }
        } catch (\Exception $e) {
            echo "✗ Error testing route: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    } else {
        echo "No business owner found for testing\n";
    }

} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\nTest completed.\n";
