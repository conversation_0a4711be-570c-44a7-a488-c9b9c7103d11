<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Business;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Gallery Page Access\n";
echo "===========================\n";

// Get the test user
$user = User::where('email', '<EMAIL>')->first();
if (!$user) {
    echo "Test user not found.\n";
    exit;
}

// Simulate authentication
Auth::login($user);

echo "User authenticated: {$user->name}\n";

// Test the gallery controller index method
use App\Http\Controllers\Owner\GalleryController;

try {
    $controller = new GalleryController(app(\App\Services\GalleryImageService::class));
    
    // Create a mock request
    $request = new Request();
    
    echo "Testing gallery index method...\n";
    $response = $controller->index($request);
    
    if ($response instanceof \Illuminate\View\View) {
        echo "Gallery index returned a view successfully\n";
        echo "View name: {$response->getName()}\n";
        
        $data = $response->getData();
        echo "View data keys: " . implode(', ', array_keys($data)) . "\n";
        
        if (isset($data['images'])) {
            echo "Images count: {$data['images']->count()}\n";
        }
        
        if (isset($data['business'])) {
            echo "Business: {$data['business']->name}\n";
        }
        
    } else {
        echo "Gallery index returned: " . get_class($response) . "\n";
    }
    
} catch (Exception $e) {
    echo "Error testing gallery index: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nTest completed!\n";
