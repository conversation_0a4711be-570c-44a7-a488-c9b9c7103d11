/* Custom Button Colors for BookKei Admin Dashboard */

/* Purple Button */
.btn-purple {
    color: #fff !important;
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
}

.btn-purple:hover {
    color: #fff !important;
    background-color: #5a359a !important;
    border-color: #533085 !important;
}

.btn-purple:focus, .btn-purple.focus {
    color: #fff !important;
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.5) !important;
}

.btn-purple.disabled, .btn-purple:disabled {
    color: #fff !important;
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
}

.btn-purple:not(:disabled):not(.disabled):active,
.btn-purple:not(:disabled):not(.disabled).active,
.btn-purple:not(:disabled):not(.disabled):active:focus {
    color: #fff !important;
    background-color: #533085 !important;
    border-color: #4e2d7d !important;
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.5) !important;
}

/* Orange Button */
.btn-orange {
    color: #fff !important;
    background-color: #fd7e14 !important;
    border-color: #fd7e14 !important;
}

.btn-orange:hover {
    color: #fff !important;
    background-color: #e8690b !important;
    border-color: #dc6002 !important;
}

.btn-orange:focus, .btn-orange.focus {
    color: #fff !important;
    background-color: #fd7e14 !important;
    border-color: #fd7e14 !important;
    box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.5) !important;
}

.btn-orange.disabled, .btn-orange:disabled {
    color: #fff !important;
    background-color: #fd7e14 !important;
    border-color: #fd7e14 !important;
}

.btn-orange:not(:disabled):not(.disabled):active,
.btn-orange:not(:disabled):not(.disabled).active,
.btn-orange:not(:disabled):not(.disabled):active:focus {
    color: #fff !important;
    background-color: #dc6002 !important;
    border-color: #d05a02 !important;
    box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.5) !important;
}

/* Teal Button */
.btn-teal {
    color: #fff !important;
    background-color: #20c997 !important;
    border-color: #20c997 !important;
}

.btn-teal:hover {
    color: #fff !important;
    background-color: #1ca085 !important;
    border-color: #198c7a !important;
}

.btn-teal:focus, .btn-teal.focus {
    color: #fff !important;
    background-color: #20c997 !important;
    border-color: #20c997 !important;
    box-shadow: 0 0 0 0.2rem rgba(32, 201, 151, 0.5) !important;
}

.btn-teal.disabled, .btn-teal:disabled {
    color: #fff !important;
    background-color: #20c997 !important;
    border-color: #20c997 !important;
}

.btn-teal:not(:disabled):not(.disabled):active,
.btn-teal:not(:disabled):not(.disabled).active,
.btn-teal:not(:disabled):not(.disabled):active:focus {
    color: #fff !important;
    background-color: #198c7a !important;
    border-color: #17826f !important;
    box-shadow: 0 0 0 0.2rem rgba(32, 201, 151, 0.5) !important;
}

/* Pink Button */
.btn-pink {
    color: #fff !important;
    background-color: #e83e8c !important;
    border-color: #e83e8c !important;
}

.btn-pink:hover {
    color: #fff !important;
    background-color: #e21b7a !important;
    border-color: #d91a72 !important;
}

.btn-pink:focus, .btn-pink.focus {
    color: #fff !important;
    background-color: #e83e8c !important;
    border-color: #e83e8c !important;
    box-shadow: 0 0 0 0.2rem rgba(232, 62, 140, 0.5) !important;
}

.btn-pink.disabled, .btn-pink:disabled {
    color: #fff !important;
    background-color: #e83e8c !important;
    border-color: #e83e8c !important;
}

.btn-pink:not(:disabled):not(.disabled):active,
.btn-pink:not(:disabled):not(.disabled).active,
.btn-pink:not(:disabled):not(.disabled):active:focus {
    color: #fff !important;
    background-color: #d91a72 !important;
    border-color: #cf186a !important;
    box-shadow: 0 0 0 0.2rem rgba(232, 62, 140, 0.5) !important;
}

/* Indigo Button */
.btn-indigo {
    color: #fff !important;
    background-color: #6610f2 !important;
    border-color: #6610f2 !important;
}

.btn-indigo:hover {
    color: #fff !important;
    background-color: #560bd0 !important;
    border-color: #510bc4 !important;
}

.btn-indigo:focus, .btn-indigo.focus {
    color: #fff !important;
    background-color: #6610f2 !important;
    border-color: #6610f2 !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 16, 242, 0.5) !important;
}

.btn-indigo.disabled, .btn-indigo:disabled {
    color: #fff !important;
    background-color: #6610f2 !important;
    border-color: #6610f2 !important;
}

.btn-indigo:not(:disabled):not(.disabled):active,
.btn-indigo:not(:disabled):not(.disabled).active,
.btn-indigo:not(:disabled):not(.disabled):active:focus {
    color: #fff !important;
    background-color: #510bc4 !important;
    border-color: #4c0ab8 !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 16, 242, 0.5) !important;
}

/* Small Box Custom Colors */
.small-box.bg-purple {
    background-color: #6f42c1 !important;
}

.small-box.bg-orange {
    background-color: #fd7e14 !important;
}

.small-box.bg-teal {
    background-color: #20c997 !important;
}

.small-box.bg-pink {
    background-color: #e83e8c !important;
}

.small-box.bg-indigo {
    background-color: #6610f2 !important;
}

/* Quick Actions Section Styling */
.quick-actions-section {
    margin-bottom: 2rem;
}

.quick-actions-section .btn {
    font-weight: 600 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    border: none !important;
    text-decoration: none !important;
}

.quick-actions-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
    text-decoration: none !important;
}

.quick-actions-section .btn:focus {
    text-decoration: none !important;
}

.quick-actions-section .btn:active {
    text-decoration: none !important;
}

.quick-actions-section .btn i {
    font-size: 1.1em;
}

/* Force button colors in Quick Actions */
.quick-actions-section .btn-purple {
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
    color: #fff !important;
}

.quick-actions-section .btn-orange {
    background-color: #fd7e14 !important;
    border-color: #fd7e14 !important;
    color: #fff !important;
}

.quick-actions-section .btn-teal {
    background-color: #20c997 !important;
    border-color: #20c997 !important;
    color: #fff !important;
}

.quick-actions-section .btn-pink {
    background-color: #e83e8c !important;
    border-color: #e83e8c !important;
    color: #fff !important;
}

.quick-actions-section .btn-indigo {
    background-color: #6610f2 !important;
    border-color: #6610f2 !important;
    color: #fff !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .quick-actions-section .btn {
        margin-bottom: 10px;
        font-size: 0.9rem;
    }
}

/* Dashboard enhancements */
.dashboard-stats {
    margin-bottom: 2rem;
}

.dashboard-stats .small-box {
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.dashboard-stats .small-box:hover {
    transform: translateY(-3px);
}

.dashboard-stats .small-box .icon {
    font-size: 70px;
}

/* Card styling improvements */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Table improvements */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

/* Badge improvements */
.badge {
    font-size: 0.75em;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Info box improvements */
.info-box {
    border-radius: 8px;
    margin-bottom: 1rem;
}

.info-box-icon {
    border-radius: 8px 0 0 8px;
}
