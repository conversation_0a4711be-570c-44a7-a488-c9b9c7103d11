<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>Gallery Buttons Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Toastr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <style>
        .gallery-image-card {
            transition: transform 0.2s;
        }
        
        .gallery-image-card:hover {
            transform: translateY(-2px);
        }
        
        .image-container {
            position: relative;
            overflow: hidden;
        }
        
        .gallery-thumbnail {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .image-container:hover .image-overlay {
            opacity: 1;
        }
        
        .overlay-actions {
            display: flex;
            gap: 5px;
        }
        
        .status-badges {
            position: absolute;
            top: 10px;
            left: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Gallery Buttons Test</h1>
        <p>This page tests the gallery button functionality in isolation.</p>
        
        <div class="row">
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card gallery-image-card">
                    <div class="image-container position-relative">
                        <img src="https://via.placeholder.com/300x200" alt="Test Image" class="card-img-top gallery-thumbnail">
                        
                        <!-- Image Overlay -->
                        <div class="image-overlay">
                            <div class="overlay-actions">
                                <button class="btn btn-sm btn-light" onclick="viewImage(1)" title="View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <a href="#" class="btn btn-sm btn-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-warning toggle-featured" data-id="1" title="Toggle Featured">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button class="btn btn-sm btn-danger delete-image" data-id="1" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Status Badges -->
                        <div class="status-badges">
                            <span class="badge badge-warning">Featured</span>
                        </div>
                    </div>
                    
                    <div class="card-body p-2">
                        <h6 class="card-title mb-1">Test Image 1</h6>
                        <small class="text-muted">
                            <span class="badge badge-info">Test Category</span>
                            <br>
                            1.2 MB • 800x600
                            <br>
                            Jan 21, 2025
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Test Results:</h3>
            <div id="test-results"></div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('Test page loaded');
            $('#test-results').append('<p>✓ jQuery loaded and ready</p>');
            
            // Test toastr
            if (typeof toastr !== 'undefined') {
                $('#test-results').append('<p>✓ Toastr loaded</p>');
            } else {
                $('#test-results').append('<p>✗ Toastr not loaded</p>');
            }
            
            // Global CSRF token setup for AJAX
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            
            // Toggle featured status
            $('.toggle-featured').click(function() {
                const imageId = $(this).data('id');
                const button = $(this);
                console.log('Toggle featured clicked for image:', imageId);
                $('#test-results').append(`<p>✓ Toggle featured clicked for image: ${imageId}</p>`);
                
                // Simulate successful response
                button.find('i').toggleClass('text-warning');
                toastr.success('Image featured status toggled successfully!');
            });
            
            // Delete image handler
            $('.delete-image').click(function() {
                const imageId = $(this).data('id');
                console.log('Delete clicked for image:', imageId);
                $('#test-results').append(`<p>✓ Delete clicked for image: ${imageId}</p>`);
                
                if (confirm('Are you sure you want to delete this image?')) {
                    toastr.success('Image would be deleted (test mode)');
                }
            });
        });
        
        // View image function
        function viewImage(imageId) {
            console.log('View image clicked:', imageId);
            $('#test-results').append(`<p>✓ View image clicked: ${imageId}</p>`);
            toastr.info('Image view modal would open (test mode)');
        }
    </script>
</body>
</html>
