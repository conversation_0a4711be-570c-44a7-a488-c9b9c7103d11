/*
 Copyright (C) Federico <PERSON> 2020
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */function a(a,b){if(1!==a.nodeType)return[];var c=a.ownerDocument.defaultView,d=c.getComputedStyle(a,null);return b?d[b]:d}function b(a){return'HTML'===a.nodeName?a:a.parentNode||a.host}function c(d){if(!d)return document.body;switch(d.nodeName){case'HTML':case'BODY':return d.ownerDocument.body;case'#document':return d.body;}var e=a(d),f=e.overflow,g=e.overflowX,h=e.overflowY;return /(auto|scroll|overlay)/.test(f+h+g)?d:c(b(d))}function d(a){return a&&a.referenceNode?a.referenceNode:a}var e='undefined'!=typeof window&&'undefined'!=typeof document&&'undefined'!=typeof navigator,f=e&&!!(window.MSInputMethodContext&&document.documentMode),g=e&&/MSIE 10/.test(navigator.userAgent);function h(a){return 11===a?f:10===a?g:f||g}function i(b){if(!b)return document.documentElement;for(var c=h(10)?document.body:null,d=b.offsetParent||null;d===c&&b.nextElementSibling;)d=(b=b.nextElementSibling).offsetParent;var e=d&&d.nodeName;return e&&'BODY'!==e&&'HTML'!==e?-1!==['TH','TD','TABLE'].indexOf(d.nodeName)&&'static'===a(d,'position')?i(d):d:b?b.ownerDocument.documentElement:document.documentElement}function j(a){var b=a.nodeName;return'BODY'!==b&&('HTML'===b||i(a.firstElementChild)===a)}function k(a){return null===a.parentNode?a:k(a.parentNode)}function l(a,b){if(!a||!a.nodeType||!b||!b.nodeType)return document.documentElement;var c=a.compareDocumentPosition(b)&Node.DOCUMENT_POSITION_FOLLOWING,d=c?a:b,e=c?b:a,f=document.createRange();f.setStart(d,0),f.setEnd(e,0);var g=f.commonAncestorContainer;if(a!==g&&b!==g||d.contains(e))return j(g)?g:i(g);var h=k(a);return h.host?l(h.host,b):l(a,k(b).host)}function m(a){var b=1<arguments.length&&arguments[1]!==void 0?arguments[1]:'top',c='top'===b?'scrollTop':'scrollLeft',d=a.nodeName;if('BODY'===d||'HTML'===d){var e=a.ownerDocument.documentElement,f=a.ownerDocument.scrollingElement||e;return f[c]}return a[c]}function n(a,b){var c=2<arguments.length&&void 0!==arguments[2]&&arguments[2],d=m(b,'top'),e=m(b,'left'),f=c?-1:1;return a.top+=d*f,a.bottom+=d*f,a.left+=e*f,a.right+=e*f,a}function o(a,b){var c='x'===b?'Left':'Top',d='Left'==c?'Right':'Bottom';return parseFloat(a['border'+c+'Width'])+parseFloat(a['border'+d+'Width'])}function p(a,b,c,d){return Math.max(b['offset'+a],b['scroll'+a],c['client'+a],c['offset'+a],c['scroll'+a],h(10)?parseInt(c['offset'+a])+parseInt(d['margin'+('Height'===a?'Top':'Left')])+parseInt(d['margin'+('Height'===a?'Bottom':'Right')]):0)}function q(a){var b=a.body,c=a.documentElement,d=h(10)&&getComputedStyle(c);return{height:p('Height',b,c,d),width:p('Width',b,c,d)}}var r=Object.assign||function(a){for(var b,c=1;c<arguments.length;c++)for(var d in b=arguments[c],b)Object.prototype.hasOwnProperty.call(b,d)&&(a[d]=b[d]);return a};function s(a){return r({},a,{right:a.left+a.width,bottom:a.top+a.height})}function t(b){var c={};try{if(h(10)){c=b.getBoundingClientRect();var d=m(b,'top'),e=m(b,'left');c.top+=d,c.left+=e,c.bottom+=d,c.right+=e}else c=b.getBoundingClientRect()}catch(a){}var f={left:c.left,top:c.top,width:c.right-c.left,height:c.bottom-c.top},g='HTML'===b.nodeName?q(b.ownerDocument):{},i=g.width||b.clientWidth||f.width,j=g.height||b.clientHeight||f.height,k=b.offsetWidth-i,l=b.offsetHeight-j;if(k||l){var n=a(b);k-=o(n,'x'),l-=o(n,'y'),f.width-=k,f.height-=l}return s(f)}function u(b,d){var e=Math.max,f=2<arguments.length&&void 0!==arguments[2]&&arguments[2],g=h(10),i='HTML'===d.nodeName,j=t(b),k=t(d),l=c(b),m=a(d),o=parseFloat(m.borderTopWidth),p=parseFloat(m.borderLeftWidth);f&&i&&(k.top=e(k.top,0),k.left=e(k.left,0));var q=s({top:j.top-k.top-o,left:j.left-k.left-p,width:j.width,height:j.height});if(q.marginTop=0,q.marginLeft=0,!g&&i){var r=parseFloat(m.marginTop),u=parseFloat(m.marginLeft);q.top-=o-r,q.bottom-=o-r,q.left-=p-u,q.right-=p-u,q.marginTop=r,q.marginLeft=u}return(g&&!f?d.contains(l):d===l&&'BODY'!==l.nodeName)&&(q=n(q,d)),q}function v(a){var b=Math.max,c=1<arguments.length&&void 0!==arguments[1]&&arguments[1],d=a.ownerDocument.documentElement,e=u(a,d),f=b(d.clientWidth,window.innerWidth||0),g=b(d.clientHeight,window.innerHeight||0),h=c?0:m(d),i=c?0:m(d,'left'),j={top:h-e.top+e.marginTop,left:i-e.left+e.marginLeft,width:f,height:g};return s(j)}function w(c){var d=c.nodeName;if('BODY'===d||'HTML'===d)return!1;if('fixed'===a(c,'position'))return!0;var e=b(c);return!!e&&w(e)}function x(b){if(!b||!b.parentElement||h())return document.documentElement;for(var c=b.parentElement;c&&'none'===a(c,'transform');)c=c.parentElement;return c||document.documentElement}function y(a,e,f,g){var h=4<arguments.length&&void 0!==arguments[4]&&arguments[4],i={top:0,left:0},j=h?x(a):l(a,d(e));if('viewport'===g)i=v(j,h);else{var k;'scrollParent'===g?(k=c(b(e)),'BODY'===k.nodeName&&(k=a.ownerDocument.documentElement)):'window'===g?k=a.ownerDocument.documentElement:k=g;var m=u(k,j,h);if('HTML'===k.nodeName&&!w(j)){var n=q(a.ownerDocument),o=n.height,p=n.width;i.top+=m.top-m.marginTop,i.bottom=o+m.top,i.left+=m.left-m.marginLeft,i.right=p+m.left}else i=m}f=f||0;var r='number'==typeof f;return i.left+=r?f:f.left||0,i.top+=r?f:f.top||0,i.right-=r?f:f.right||0,i.bottom-=r?f:f.bottom||0,i}function z(a){var b=a.width,c=a.height;return b*c}function A(a,b,c,d,e){var f=5<arguments.length&&arguments[5]!==void 0?arguments[5]:0;if(-1===a.indexOf('auto'))return a;var g=y(c,d,f,e),h={top:{width:g.width,height:b.top-g.top},right:{width:g.right-b.right,height:g.height},bottom:{width:g.width,height:g.bottom-b.bottom},left:{width:b.left-g.left,height:g.height}},i=Object.keys(h).map(function(a){return r({key:a},h[a],{area:z(h[a])})}).sort(function(c,a){return a.area-c.area}),j=i.filter(function(a){var b=a.width,d=a.height;return b>=c.clientWidth&&d>=c.clientHeight}),k=0<j.length?j[0].key:i[0].key,l=a.split('-')[1];return k+(l?'-'+l:'')}var B=function(){for(var a=['Edge','Trident','Firefox'],b=0;b<a.length;b+=1)if(e&&0<=navigator.userAgent.indexOf(a[b]))return 1;return 0}();function C(a){var b=!1;return function(){b||(b=!0,window.Promise.resolve().then(function(){b=!1,a()}))}}function D(a){var b=!1;return function(){b||(b=!0,setTimeout(function(){b=!1,a()},B))}}var E=e&&window.Promise,F=E?C:D;function G(a,b){return Array.prototype.find?a.find(b):a.filter(b)[0]}function H(a,b,c){if(Array.prototype.findIndex)return a.findIndex(function(a){return a[b]===c});var d=G(a,function(a){return a[b]===c});return a.indexOf(d)}function I(a){var b;if('HTML'===a.nodeName){var c=q(a.ownerDocument),d=c.width,e=c.height;b={width:d,height:e,left:0,top:0}}else b={width:a.offsetWidth,height:a.offsetHeight,left:a.offsetLeft,top:a.offsetTop};return s(b)}function J(a){var b=a.ownerDocument.defaultView,c=b.getComputedStyle(a),d=parseFloat(c.marginTop||0)+parseFloat(c.marginBottom||0),e=parseFloat(c.marginLeft||0)+parseFloat(c.marginRight||0),f={width:a.offsetWidth+e,height:a.offsetHeight+d};return f}function K(a){var b={left:'right',right:'left',bottom:'top',top:'bottom'};return a.replace(/left|right|bottom|top/g,function(a){return b[a]})}function L(a,b,c){c=c.split('-')[0];var d=J(a),e={width:d.width,height:d.height},f=-1!==['right','left'].indexOf(c),g=f?'top':'left',h=f?'left':'top',i=f?'height':'width',j=f?'width':'height';return e[g]=b[g]+b[i]/2-d[i]/2,e[h]=c===h?b[h]-d[j]:b[K(h)],e}function M(a,b,c){var e=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null,f=e?x(b):l(b,d(c));return u(c,f,e)}function N(a){for(var b=[!1,'ms','Webkit','Moz','O'],c=a.charAt(0).toUpperCase()+a.slice(1),d=0;d<b.length;d++){var e=b[d],f=e?''+e+c:a;if('undefined'!=typeof document.body.style[f])return f}return null}function O(a){return a&&'[object Function]'==={}.toString.call(a)}function P(a,b){return a.some(function(a){var c=a.name,d=a.enabled;return d&&c===b})}function Q(a,b,c){var d=G(a,function(a){var c=a.name;return c===b}),e=!!d&&a.some(function(a){return a.name===c&&a.enabled&&a.order<d.order});if(!e){var f='`'+b+'`';console.warn('`'+c+'`'+' modifier is required by '+f+' modifier in order to work, be sure to include it before '+f+'!')}return e}function R(a){return''!==a&&!isNaN(parseFloat(a))&&isFinite(a)}function S(a){var b=a.ownerDocument;return b?b.defaultView:window}function T(a,b){return S(a).removeEventListener('resize',b.updateBound),b.scrollParents.forEach(function(a){a.removeEventListener('scroll',b.updateBound)}),b.updateBound=null,b.scrollParents=[],b.scrollElement=null,b.eventsEnabled=!1,b}function U(a,b,c){var d=void 0===c?a:a.slice(0,H(a,'name',c));return d.forEach(function(a){a['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');var c=a['function']||a.fn;a.enabled&&O(c)&&(b.offsets.popper=s(b.offsets.popper),b.offsets.reference=s(b.offsets.reference),b=c(b,a))}),b}function V(a,b){Object.keys(b).forEach(function(c){var d=b[c];!1===d?a.removeAttribute(c):a.setAttribute(c,b[c])})}function W(a,b){Object.keys(b).forEach(function(c){var d='';-1!==['width','height','top','right','bottom','left'].indexOf(c)&&R(b[c])&&(d='px'),a.style[c]=b[c]+d})}function X(a,b,d,e){var f='BODY'===a.nodeName,g=f?a.ownerDocument.defaultView:a;g.addEventListener(b,d,{passive:!0}),f||X(c(g.parentNode),b,d,e),e.push(g)}function Y(a,b,d,e){d.updateBound=e,S(a).addEventListener('resize',d.updateBound,{passive:!0});var f=c(a);return X(f,'scroll',d.updateBound,d.scrollParents),d.scrollElement=f,d.eventsEnabled=!0,d}var Z={computeAutoPlacement:A,debounce:F,findIndex:H,getBordersSize:o,getBoundaries:y,getBoundingClientRect:t,getClientRect:s,getOffsetParent:i,getOffsetRect:I,getOffsetRectRelativeToArbitraryNode:u,getOuterSizes:J,getParentNode:b,getPopperOffsets:L,getReferenceOffsets:M,getScroll:m,getScrollParent:c,getStyleComputedProperty:a,getSupportedPropertyName:N,getWindowSizes:q,isFixed:w,isFunction:O,isModifierEnabled:P,isModifierRequired:Q,isNumeric:R,removeEventListeners:T,runModifiers:U,setAttributes:V,setStyles:W,setupEventListeners:Y};export{A as computeAutoPlacement,F as debounce,H as findIndex,o as getBordersSize,y as getBoundaries,t as getBoundingClientRect,s as getClientRect,i as getOffsetParent,I as getOffsetRect,u as getOffsetRectRelativeToArbitraryNode,J as getOuterSizes,b as getParentNode,L as getPopperOffsets,M as getReferenceOffsets,m as getScroll,c as getScrollParent,a as getStyleComputedProperty,N as getSupportedPropertyName,q as getWindowSizes,w as isFixed,O as isFunction,P as isModifierEnabled,Q as isModifierRequired,R as isNumeric,T as removeEventListeners,U as runModifiers,V as setAttributes,W as setStyles,Y as setupEventListeners};export default Z;
//# sourceMappingURL=popper-utils.min.js.map
