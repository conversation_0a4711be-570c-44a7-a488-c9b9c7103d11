/**
 * Owner Panel Integration JavaScript
 * Handles cross-section communication and real-time updates
 */

class OwnerIntegration {
    constructor() {
        this.notifications = [];
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startAutoRefresh();
        this.loadNotifications();
    }

    setupEventListeners() {
        // Global CSRF token setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Handle booking status changes
        $(document).on('booking-status-changed', (event, data) => {
            this.handleBookingStatusChange(data);
        });

        // Handle waiting list conversions
        $(document).on('waiting-list-converted', (event, data) => {
            this.handleWaitingListConversion(data);
        });

        // Handle check-in events
        $(document).on('customer-checked-in', (event, data) => {
            this.handleCheckIn(data);
        });

        // Handle check-out events
        $(document).on('customer-checked-out', (event, data) => {
            this.handleCheckOut(data);
        });

        // Cross-section navigation
        $(document).on('click', '[data-cross-navigate]', (event) => {
            this.handleCrossNavigation(event);
        });

        // Notification clicks
        $(document).on('click', '.integration-notification', (event) => {
            this.handleNotificationClick(event);
        });
    }

    startAutoRefresh() {
        // Refresh notifications every 2 minutes
        this.refreshInterval = setInterval(() => {
            this.loadNotifications();
            this.refreshCurrentSection();
        }, 120000);
    }

    loadNotifications() {
        $.get('/owner/integration/notifications')
            .done((response) => {
                this.notifications = response.notifications || [];
                this.updateNotificationDisplay();
            })
            .fail((xhr) => {
                console.error('Failed to load notifications:', xhr);
            });
    }

    updateNotificationDisplay() {
        const container = $('#integration-notifications');
        if (container.length === 0) return;

        container.empty();

        if (this.notifications.length === 0) {
            container.hide();
            return;
        }

        this.notifications.forEach(notification => {
            const badge = this.createNotificationBadge(notification);
            container.append(badge);
        });

        container.show();
    }

    createNotificationBadge(notification) {
        return $(`
            <div class="integration-notification alert alert-${notification.color} alert-dismissible fade show" 
                 data-type="${notification.type}" data-url="${notification.action_url}">
                <i class="${notification.icon} mr-2"></i>
                <strong>${notification.count}</strong> ${notification.message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `);
    }

    handleBookingStatusChange(data) {
        const { booking, oldStatus, newStatus, actions } = data;

        // Show success message
        this.showAlert('success', `Booking ${booking.booking_number} status updated to ${newStatus}`);

        // Handle specific actions
        if (actions && actions.length > 0) {
            actions.forEach(action => {
                this.handleStatusChangeAction(action, booking);
            });
        }

        // Refresh related sections
        this.refreshRelatedSections(['bookings', 'check-in', 'waiting-lists']);
    }

    handleStatusChangeAction(action, booking) {
        switch (action.action) {
            case 'waiting_list_check':
                if (action.matches > 0) {
                    this.showWaitingListOpportunity(booking, action.matches);
                }
                break;
            case 'cancellation':
            case 'no_show':
                if (action.details.waiting_list_matches > 0) {
                    this.showWaitingListOpportunity(booking, action.details.waiting_list_matches);
                }
                break;
        }
    }

    showWaitingListOpportunity(booking, matchCount) {
        const message = `${matchCount} waiting list ${matchCount === 1 ? 'customer' : 'customers'} may be interested in this slot`;
        
        this.showAlert('info', message, {
            action: 'View Waiting Lists',
            url: `/owner/waiting-lists?service=${booking.services[0]?.id}&date=${booking.start_datetime}`
        });
    }

    handleWaitingListConversion(data) {
        const { waitingList, booking } = data;
        
        this.showAlert('success', `Waiting list entry converted to booking ${booking.booking_number}`);
        this.refreshRelatedSections(['bookings', 'waiting-lists']);
    }

    handleCheckIn(data) {
        const { booking } = data;
        
        this.showAlert('success', `${booking.customer_name} checked in successfully`);
        this.refreshRelatedSections(['check-in', 'bookings']);
    }

    handleCheckOut(data) {
        const { booking } = data;
        
        this.showAlert('success', `${booking.customer_name} checked out successfully`);
        this.refreshRelatedSections(['check-in', 'bookings']);
    }

    handleCrossNavigation(event) {
        event.preventDefault();
        const target = $(event.currentTarget);
        const section = target.data('cross-navigate');
        const params = target.data('params') || {};
        
        this.navigateToSection(section, params);
    }

    navigateToSection(section, params = {}) {
        let url;
        const queryString = new URLSearchParams(params).toString();
        const query = queryString ? `?${queryString}` : '';

        switch (section) {
            case 'bookings':
                url = `/owner/bookings${query}`;
                break;
            case 'check-in':
                url = `/owner/check-in${query}`;
                break;
            case 'waiting-lists':
                url = `/owner/waiting-lists${query}`;
                break;
            case 'calendar':
                url = `/owner/calendar${query}`;
                break;
            default:
                console.error('Unknown section:', section);
                return;
        }

        window.location.href = url;
    }

    handleNotificationClick(event) {
        const notification = $(event.currentTarget);
        const url = notification.data('url');
        
        if (url) {
            window.location.href = url;
        }
    }

    refreshCurrentSection() {
        const currentPath = window.location.pathname;
        
        if (currentPath.includes('/owner/bookings')) {
            this.refreshBookingsSection();
        } else if (currentPath.includes('/owner/check-in')) {
            this.refreshCheckInSection();
        } else if (currentPath.includes('/owner/waiting-lists')) {
            this.refreshWaitingListsSection();
        }
    }

    refreshRelatedSections(sections) {
        const currentPath = window.location.pathname;
        
        sections.forEach(section => {
            if (currentPath.includes(`/owner/${section}`)) {
                this.refreshCurrentSection();
            }
        });
    }

    refreshBookingsSection() {
        if (typeof refreshBookingsTable === 'function') {
            refreshBookingsTable();
        }
    }

    refreshCheckInSection() {
        if (typeof refreshStats === 'function') {
            refreshStats();
        }
        // Reload the page for check-in section to get latest data
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    refreshWaitingListsSection() {
        if (typeof refreshStats === 'function') {
            refreshStats();
        }
        // Reload the page for waiting lists to get latest data
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    showAlert(type, message, action = null) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const actionButton = action ? 
            `<a href="${action.url}" class="btn btn-sm btn-outline-primary ml-2">${action.action}</a>` : '';

        const alert = $(`
            <div class="alert ${alertClass} alert-dismissible fade show integration-alert" role="alert">
                ${message}
                ${actionButton}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `);

        // Find or create alert container
        let container = $('#integration-alerts');
        if (container.length === 0) {
            container = $('<div id="integration-alerts" class="fixed-top" style="top: 60px; right: 20px; width: 400px; z-index: 9999;"></div>');
            $('body').append(container);
        }

        container.append(alert);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alert.alert('close');
        }, 5000);
    }

    // Public methods for external use
    triggerBookingStatusChange(booking, oldStatus, newStatus, actions = []) {
        $(document).trigger('booking-status-changed', {
            booking, oldStatus, newStatus, actions
        });
    }

    triggerWaitingListConversion(waitingList, booking) {
        $(document).trigger('waiting-list-converted', {
            waitingList, booking
        });
    }

    triggerCheckIn(booking) {
        $(document).trigger('customer-checked-in', { booking });
    }

    triggerCheckOut(booking) {
        $(document).trigger('customer-checked-out', { booking });
    }

    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        $(document).off('.owner-integration');
    }
}

// Initialize when document is ready
$(document).ready(function() {
    window.ownerIntegration = new OwnerIntegration();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OwnerIntegration;
}
