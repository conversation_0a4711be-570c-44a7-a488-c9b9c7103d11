<?php
require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;

echo "Services table columns:\n";
$columns = Schema::getColumnListing('services');
foreach ($columns as $column) {
    echo "- $column\n";
}

// Check if service display fields exist
$serviceDisplayFields = [
    'is_public',
    'featured_on_landing',
    'landing_display_order',
    'landing_display_config',
    'show_price_on_landing',
    'show_duration_on_landing',
    'show_description_on_landing',
    'show_image_on_landing',
    'landing_page_title',
    'landing_page_description',
    'landing_page_keywords',
    'quick_booking_enabled',
    'booking_button_text',
    'booking_button_color',
    'landing_page_views',
    'landing_page_clicks',
    'last_landing_view'
];

echo "\nService display fields status:\n";
foreach ($serviceDisplayFields as $field) {
    $exists = in_array($field, $columns) ? 'EXISTS' : 'MISSING';
    echo "- $field: $exists\n";
}
