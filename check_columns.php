<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;

echo "Services table columns:\n";
$columns = Schema::getColumnListing('services');
foreach($columns as $column) {
    echo "- $column\n";
}

echo "\nChecking for specific columns:\n";
$checkColumns = ['preparation_instructions', 'aftercare_instructions', 'landing_display_order'];
foreach($checkColumns as $col) {
    $exists = in_array($col, $columns) ? 'EXISTS' : 'MISSING';
    echo "- $col: $exists\n";
}
