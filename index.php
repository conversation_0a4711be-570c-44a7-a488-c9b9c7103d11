<?php

// Uncomment for production debugging
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

/**
 * Laravel - A PHP Framework For Web Artisans
 */

// Performance optimization for cPanel shared hosting
define('LARAVEL_START', microtime(true));

// Define the public path
$publicPath = __DIR__ . '/public';

/**
 * OPcache warming check to speed up requests
 */
$isWarmingRequest = isset($_GET['opcache_warm']) && $_GET['opcache_warm'] === 'true';
if ($isWarmingRequest) {
    // This will force OPcache to compile and cache everything
    function warm_opcache_files($dir) {
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') continue;
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                warm_opcache_files($path);
            } else if (substr($path, -4) === '.php') {
                // We don't need the file contents, just force opcache to compile it
                if (function_exists('opcache_compile_file')) {
                    @opcache_compile_file($path);
                }
            }
        }
    }

    // Cache bootstrap, app/, routes/ folders
    foreach(['bootstrap', 'app', 'routes'] as $folder) {
        if (is_dir(__DIR__ . '/' . $folder)) {
            warm_opcache_files(__DIR__ . '/' . $folder);
        }
    }

    header('Content-Type: application/json');
    echo json_encode(['status' => 'opcache_warmed', 'time' => microtime(true) - LARAVEL_START]);
    exit;
}

// Check for maintenance mode
if (file_exists($maintenance = __DIR__.'/storage/framework/maintenance.php')) {
    require $maintenance;
}

// Register the auto-loader
require __DIR__.'/vendor/autoload.php';

/*
|--------------------------------------------------------------------------
| Turn On The Lights
|--------------------------------------------------------------------------
|
| We need to illuminate PHP development, so let us turn on the lights.
| This bootstraps the framework and gets it ready for use, then it
| will load up this application so that we can run it and send
| the responses back to the browser and delight our users.
|
*/

// Set custom public path for Laravel to use
$_SERVER['DOCUMENT_ROOT'] = $publicPath;

// Create the application instance
$app = require_once __DIR__.'/bootstrap/app.php';

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
|
| Once we have the application, we can handle the incoming request
| through the kernel, and send the associated response back to
| the client's browser allowing them to enjoy the creative
| and wonderful application we have prepared for them.
|
*/

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

$response->send();

$kernel->terminate($request, $response);

// Output execution time as a comment for debug purposes in production
if (defined('LARAVEL_START') && !app()->environment('local') && !$request->ajax() && !$request->expectsJson()) {
    echo '<!-- ' . round((microtime(true) - LARAVEL_START) * 1000, 2) . 'ms -->';
}
