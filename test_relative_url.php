<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing relative URL approach...\n\n";

try {
    $bookingId = 4;
    
    // Get a business owner user
    $owner = \App\Models\User::role('Business Owner')->first();
    auth()->login($owner);
    
    echo "Testing different URL approaches:\n";
    
    // Test 1: Relative URL (what the browser would resolve)
    $relativeUrl = "owner/calendar/booking/{$bookingId}";
    echo "1. Relative URL: {$relativeUrl}\n";
    
    // Test 2: Absolute path without domain
    $absolutePath = "/bookkei/owner/calendar/booking/{$bookingId}";
    echo "2. Absolute path: {$absolutePath}\n";
    
    // Test 3: Full URL
    $fullUrl = "http://localhost/bookkei/owner/calendar/booking/{$bookingId}";
    echo "3. Full URL: {$fullUrl}\n\n";
    
    // Test each approach
    $testUrls = [
        'Relative' => $relativeUrl,
        'Absolute Path' => $absolutePath,
        'Full URL' => $fullUrl,
    ];
    
    foreach ($testUrls as $name => $testUrl) {
        echo "=== Testing {$name}: {$testUrl} ===\n";
        
        try {
            $request = Request::create($testUrl, 'GET');
            $request->setUserResolver(function () use ($owner) {
                return $owner;
            });
            
            // Set up session
            $session = app('session.store');
            $request->setLaravelSession($session);
            
            $kernel = app(\Illuminate\Contracts\Http\Kernel::class);
            $response = $kernel->handle($request);
            
            echo "Status: " . $response->getStatusCode() . "\n";
            
            if ($response->getStatusCode() === 200) {
                echo "✓ SUCCESS!\n";
                
                $content = $response->getContent();
                $json = json_decode($content, true);
                
                if ($json && isset($json['success']) && $json['success']) {
                    echo "✓ JSON response successful\n";
                    echo "HTML length: " . strlen($json['html']) . " characters\n";
                } else {
                    echo "✗ JSON response failed or invalid\n";
                }
            } else {
                echo "✗ Failed with status: " . $response->getStatusCode() . "\n";
            }
            
        } catch (\Exception $e) {
            echo "✗ Exception: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    // Test what the browser would actually send
    echo "=== Testing Browser-like Request ===\n";
    
    // Simulate what a browser would send when making a fetch request from /bookkei/owner/calendar
    $currentPage = '/bookkei/owner/calendar';
    $relativeUrl = 'owner/calendar/booking/' . $bookingId;
    
    // Browser would resolve this to the full path
    $resolvedUrl = '/bookkei/' . $relativeUrl;
    
    echo "Current page: {$currentPage}\n";
    echo "Relative URL: {$relativeUrl}\n";
    echo "Browser resolved URL: {$resolvedUrl}\n";
    
    $request = Request::create($resolvedUrl, 'GET');
    $request->setUserResolver(function () use ($owner) {
        return $owner;
    });
    
    // Set proper headers that a browser would send
    $request->headers->set('Accept', 'application/json');
    $request->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    $session = app('session.store');
    $request->setLaravelSession($session);
    
    try {
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);
        $response = $kernel->handle($request);
        
        echo "Status: " . $response->getStatusCode() . "\n";
        
        if ($response->getStatusCode() === 200) {
            echo "✓ Browser-like request SUCCESS!\n";
            
            $content = $response->getContent();
            $json = json_decode($content, true);
            
            if ($json && isset($json['success']) && $json['success']) {
                echo "✓ This should work in the browser!\n";
            }
        } else {
            echo "✗ Browser-like request failed: " . $response->getStatusCode() . "\n";
        }
        
    } catch (\Exception $e) {
        echo "✗ Browser-like request exception: " . $e->getMessage() . "\n";
    }

} catch (\Exception $e) {
    echo "Setup error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
