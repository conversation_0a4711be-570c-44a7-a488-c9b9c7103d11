<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing booking route issue...\n\n";

try {
    // Check if we have any users with Business Owner role
    $owners = \App\Models\User::role('Business Owner')->get();
    echo "Found " . $owners->count() . " Business Owner users\n";

    if ($owners->count() > 0) {
        $owner = $owners->first();
        echo "Testing with owner: " . $owner->name . " (ID: " . $owner->id . ")\n";

        // Check if owner has businesses
        $businesses = $owner->ownedBusinesses()->get();
        echo "Owner has " . $businesses->count() . " businesses\n";

        if ($businesses->count() > 0) {
            $business = $businesses->first();
            echo "Testing with business: " . $business->name . " (ID: " . $business->id . ")\n";

            // Check if business has bookings
            $bookings = $business->bookings()->get();
            echo "Business has " . $bookings->count() . " bookings\n";

            if ($bookings->count() > 0) {
                $booking = $bookings->first();
                echo "Testing with booking ID: " . $booking->id . "\n";
                echo "Booking customer: " . $booking->customer_name . "\n";
                echo "Booking status: " . $booking->status . "\n";
                echo "Booking start: " . $booking->start_datetime . "\n";

                // Test the controller method directly
                echo "\nTesting controller method directly...\n";

                // Simulate authentication
                auth()->login($owner);

                $controller = new \App\Http\Controllers\Owner\CalendarController();

                try {
                    $response = $controller->showBooking($booking->id);
                    $responseData = $response->getData(true);

                    if ($responseData['success']) {
                        echo "✓ Controller method works correctly\n";
                        echo "Response contains HTML: " . (strlen($responseData['html']) > 0 ? 'Yes' : 'No') . "\n";
                    } else {
                        echo "✗ Controller method failed: " . $responseData['message'] . "\n";
                    }
                } catch (\Exception $e) {
                    echo "✗ Controller method threw exception: " . $e->getMessage() . "\n";
                    echo "Stack trace: " . $e->getTraceAsString() . "\n";
                }

            } else {
                echo "No bookings found. Creating a test booking...\n";

                // Check if business has services
                $services = $business->services()->get();
                echo "Business has " . $services->count() . " services\n";

                if ($services->count() > 0) {
                    $service = $services->first();

                    // Create a test booking
                    $booking = $business->bookings()->create([
                        'customer_name' => 'Test Customer',
                        'customer_email' => '<EMAIL>',
                        'customer_phone' => '************',
                        'start_datetime' => now()->addDay(),
                        'end_datetime' => now()->addDay()->addHour(),
                        'total_duration_minutes' => 60,
                        'participant_count' => 1,
                        'subtotal' => 100.00,
                        'total_amount' => 100.00,
                        'paid_amount' => 0.00,
                        'status' => 'confirmed',
                        'payment_status' => 'pending',
                        'notes' => 'Test booking for debugging',
                    ]);

                    // Create booking service
                    $booking->bookingServices()->create([
                        'service_id' => $service->id,
                        'quantity' => 1,
                        'unit_price' => 100.00,
                        'total_price' => 100.00,
                        'duration_minutes' => 60,
                        'start_datetime' => $booking->start_datetime,
                        'end_datetime' => $booking->end_datetime,
                        'service_data' => $service->toArray(),
                    ]);

                    echo "Created test booking with ID: " . $booking->id . "\n";

                    // Now test the controller
                    auth()->login($owner);
                    $controller = new \App\Http\Controllers\Owner\CalendarController();

                    try {
                        $response = $controller->showBooking($booking->id);
                        $responseData = $response->getData(true);

                        if ($responseData['success']) {
                            echo "✓ Controller method works with test booking\n";
                        } else {
                            echo "✗ Controller method failed with test booking: " . $responseData['message'] . "\n";
                        }
                    } catch (\Exception $e) {
                        echo "✗ Controller method threw exception with test booking: " . $e->getMessage() . "\n";
                    }
                } else {
                    echo "No services found. Cannot create test booking.\n";
                }
            }
        } else {
            echo "Owner has no businesses\n";
        }
    } else {
        echo "No owner users found\n";
    }

} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\nTest completed.\n";
