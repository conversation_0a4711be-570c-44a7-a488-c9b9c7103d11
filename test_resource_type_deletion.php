<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\ResourceType;
use App\Models\Business;
use App\Models\User;
use Illuminate\Support\Facades\DB;

echo "Testing Resource Type Deletion...\n\n";

try {
    // Find a business with resource types
    $business = Business::with('resourceTypes')->first();
    
    if (!$business) {
        echo "❌ No business found\n";
        exit;
    }
    
    echo "✅ Found business: {$business->name} (ID: {$business->id})\n";
    
    // Find a resource type without resources
    $resourceType = $business->resourceTypes()
        ->whereDoesntHave('resources')
        ->first();
    
    if (!$resourceType) {
        echo "❌ No unused resource type found\n";
        
        // Create a test resource type
        $resourceType = ResourceType::create([
            'business_id' => $business->id,
            'name' => 'Test Delete Type',
            'slug' => 'test-delete-type',
            'description' => 'Test resource type for deletion',
            'icon' => 'fas fa-cube',
            'color' => '#007bff',
        ]);
        
        echo "✅ Created test resource type: {$resourceType->name} (ID: {$resourceType->id})\n";
    } else {
        echo "✅ Found unused resource type: {$resourceType->name} (ID: {$resourceType->id})\n";
    }
    
    // Check resource count
    $resourceCount = $resourceType->resources()->count();
    echo "📊 Resource count: {$resourceCount}\n";
    
    if ($resourceCount > 0) {
        echo "❌ Cannot test deletion - resource type has resources\n";
        exit;
    }
    
    // Test the checkForRelatedData method logic
    echo "\n🔍 Checking for related data...\n";
    
    // Check service-resource relationships
    $serviceResourceCount = DB::table('service_resources')
        ->whereIn('resource_id', function($query) use ($resourceType) {
            $query->select('id')
                  ->from('resources')
                  ->where('resource_type_id', $resourceType->id);
        })
        ->count();
    
    echo "📊 Service-resource relationships: {$serviceResourceCount}\n";
    
    // Check booking-service-resource relationships
    $bookingResourceCount = DB::table('booking_service_resources')
        ->whereIn('resource_id', function($query) use ($resourceType) {
            $query->select('id')
                  ->from('resources')
                  ->where('resource_type_id', $resourceType->id);
        })
        ->count();
    
    echo "📊 Booking-resource relationships: {$bookingResourceCount}\n";
    
    if ($serviceResourceCount > 0 || $bookingResourceCount > 0) {
        echo "❌ Cannot delete - has related data\n";
        exit;
    }
    
    echo "✅ No related data found\n";
    
    // Attempt deletion
    echo "\n🗑️ Attempting deletion...\n";
    
    DB::transaction(function () use ($resourceType) {
        $deleted = $resourceType->delete();
        
        if (!$deleted) {
            throw new \Exception('Failed to delete resource type from database.');
        }
        
        echo "✅ Resource type deleted successfully!\n";
    });
    
    // Verify deletion
    $exists = ResourceType::find($resourceType->id);
    if ($exists) {
        echo "❌ Resource type still exists in database\n";
    } else {
        echo "✅ Resource type successfully removed from database\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "🔍 Trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nTest completed.\n";
