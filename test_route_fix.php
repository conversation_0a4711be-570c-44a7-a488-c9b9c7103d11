<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing route fix...\n\n";

try {
    $bookingId = 4; // Use the booking ID we found earlier
    
    echo "Testing route generation methods:\n";
    echo "1. route() with placeholder: " . route('owner.calendar.booking', ':id') . "\n";
    echo "2. route() with actual ID: " . route('owner.calendar.booking', $bookingId) . "\n";
    
    // Test the JavaScript-style replacement
    $urlTemplate = route('owner.calendar.booking', ':id');
    $finalUrl = str_replace(':id', $bookingId, $urlTemplate);
    echo "3. JavaScript-style replacement: " . $finalUrl . "\n\n";
    
    // Test if this URL works
    echo "Testing the generated URL...\n";
    
    // Get a business owner user
    $owner = \App\Models\User::role('Business Owner')->first();
    auth()->login($owner);
    
    // Create a request using the properly generated route
    $request = Request::create($finalUrl, 'GET');
    $request->setUserResolver(function () use ($owner) {
        return $owner;
    });
    
    // Set up session
    $session = app('session.store');
    $request->setLaravelSession($session);
    
    echo "Testing URL: " . $finalUrl . "\n";
    echo "Authenticated user: " . (auth()->check() ? auth()->user()->name : 'None') . "\n";
    
    try {
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);
        $response = $kernel->handle($request);
        
        echo "Response status: " . $response->getStatusCode() . "\n";
        
        if ($response->getStatusCode() === 200) {
            echo "✓ Route works correctly!\n";
            
            $content = $response->getContent();
            $json = json_decode($content, true);
            
            if ($json && isset($json['success'])) {
                echo "JSON response success: " . ($json['success'] ? 'true' : 'false') . "\n";
                if ($json['success']) {
                    echo "HTML content length: " . strlen($json['html']) . " characters\n";
                    echo "✓ Booking details loaded successfully!\n";
                }
            }
        } else {
            echo "✗ Route still returns status: " . $response->getStatusCode() . "\n";
            echo "Response preview: " . substr($response->getContent(), 0, 300) . "...\n";
        }
        
    } catch (\Exception $e) {
        echo "✗ Request failed: " . $e->getMessage() . "\n";
    }

} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
