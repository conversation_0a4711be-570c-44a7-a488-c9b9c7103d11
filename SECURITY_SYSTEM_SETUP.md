# Security System Setup Guide

## 🚀 **INSTALLATION INSTRUCTIONS**

### **1. Database Migration**
Run all security-related migrations to set up the enhanced database schema:

```bash
# Run all migrations
php artisan migrate

# Verify tables were created
php artisan migrate:status
```

**Expected Tables:**
- `roles` (enhanced with security fields)
- `role_audit_logs` (comprehensive audit logging)
- `security_alerts` (security incident management)
- `security_logs` (detailed security logging)
- `security_metrics` (performance tracking)
- `compliance_reports` (report storage)

### **2. Seed Enhanced Permissions and Roles**
Initialize the hierarchical role system with comprehensive permissions:

```bash
# Seed roles and permissions
php artisan db:seed --class=RoleAndPermissionSeeder

# Verify roles were created
php artisan tinker
>>> App\Models\EnhancedRole::with('permissions')->get()
```

**Expected Roles:**
- Super Admin (Level 0) - 150+ permissions
- Admin (Level 1) - 100+ permissions  
- Business Owner (Level 2) - 80+ permissions
- Manager (Level 3) - 50+ permissions
- Staff (Level 4) - 30+ permissions
- Customer (Level 5) - 10+ permissions

### **3. Configure Application Settings**

#### **Environment Variables**
Add these to your `.env` file:

```env
# Security Settings
SECURITY_MONITORING_ENABLED=true
SECURITY_ALERT_EMAIL=<EMAIL>
DATA_RETENTION_ENABLED=true

# 2FA Settings
TWO_FACTOR_ENABLED=true
TWO_FACTOR_REQUIRED_FOR_SUPER_ADMIN=true
TWO_FACTOR_SESSION_DURATION=30

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=info
LOG_SECURITY_CHANNEL=security
```

#### **Logging Configuration**
Add security logging channel to `config/logging.php`:

```php
'channels' => [
    // ... existing channels
    
    'security' => [
        'driver' => 'daily',
        'path' => storage_path('logs/security.log'),
        'level' => 'info',
        'days' => 365, // Keep security logs for 1 year
    ],
],
```

### **4. Set Up Scheduled Tasks**

#### **Add to Crontab**
```bash
# Edit crontab
crontab -e

# Add Laravel scheduler
* * * * * cd /path/to/bookkei && php artisan schedule:run >> /dev/null 2>&1
```

#### **Verify Scheduled Tasks**
```bash
# List scheduled tasks
php artisan schedule:list

# Test security monitoring
php artisan security:monitor --scan
```

### **5. Configure Storage Directories**
Create necessary directories for archives and logs:

```bash
# Create storage directories
mkdir -p storage/app/archives/audit_logs
mkdir -p storage/app/archives/security_alerts
mkdir -p storage/app/archives/user_sessions
mkdir -p storage/app/archives/login_attempts
mkdir -p storage/logs

# Set permissions
chmod -R 755 storage/app/archives
chmod -R 755 storage/logs
```

### **6. Cache Configuration**
Clear and optimize caches:

```bash
# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🔧 **CONFIGURATION**

### **Security Dashboard Access**
The security dashboard is available at:
- **URL**: `/admin/security/dashboard`
- **Required Permission**: `manage roles`
- **Minimum Role**: Admin (Level 1)

### **Default Super Admin Account**
A default Super Admin account is created during seeding:
- **Email**: `<EMAIL>`
- **Password**: `password` (change immediately!)
- **Role**: Super Admin (Level 0)

### **2FA Configuration**
Two-Factor Authentication settings:
- **Global Toggle**: Admin Settings → Two-Factor Authentication
- **Super Admin**: Always required (configurable)
- **Role Operations**: Configurable requirement
- **Session Duration**: 30 minutes (configurable)

## 🛡️ **SECURITY VERIFICATION**

### **1. Test Role Hierarchy**
```bash
php artisan tinker

# Test role hierarchy
>>> $user = App\Models\User::first()
>>> $user->assignRole('Admin')
>>> $user->hasRole('Admin') // Should return true
>>> $user->can('manage users') // Should return true
```

### **2. Test Security Monitoring**
```bash
# Run security scan
php artisan security:monitor --scan

# Check for alerts
php artisan tinker
>>> App\Models\SecurityAlert::count()
```

### **3. Test Business Data Isolation**
```bash
# Test with different user roles
php artisan tinker

# Create test business owner
>>> $owner = App\Models\User::factory()->create()
>>> $owner->assignRole('Business Owner')
>>> $business = App\Models\Business::factory()->create(['owner_id' => $owner->id])
```

### **4. Test Audit Logging**
```bash
# Check audit logs
php artisan tinker
>>> App\Models\RoleAuditLog::latest()->take(5)->get()
```

## 📊 **MONITORING & MAINTENANCE**

### **Daily Checks**
1. **Security Dashboard**: Check for critical alerts
2. **Audit Logs**: Review high-risk activities
3. **Compliance Score**: Monitor compliance metrics
4. **System Performance**: Check response times

### **Weekly Tasks**
1. **Data Retention**: Verify automated cleanup
2. **Archive Management**: Check archive integrity
3. **User Access Review**: Audit user permissions
4. **Security Reports**: Generate compliance reports

### **Monthly Tasks**
1. **Full Security Audit**: Comprehensive system review
2. **Permission Review**: Validate role assignments
3. **Compliance Assessment**: Full compliance check
4. **Performance Optimization**: Database maintenance

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

#### **Migration Errors**
```bash
# If migrations fail
php artisan migrate:rollback
php artisan migrate:fresh --seed
```

#### **Permission Errors**
```bash
# Reset permissions
php artisan permission:cache-reset
php artisan db:seed --class=RoleAndPermissionSeeder --force
```

#### **Security Alerts Not Working**
```bash
# Check queue workers
php artisan queue:work

# Test notification system
php artisan tinker
>>> App\Models\User::first()->notify(new App\Notifications\SecurityAlertNotification(App\Models\SecurityAlert::first()))
```

#### **Scheduled Tasks Not Running**
```bash
# Check cron logs
tail -f /var/log/cron

# Test scheduler
php artisan schedule:run
```

### **Log Files**
Monitor these log files for issues:
- `storage/logs/laravel.log` - General application logs
- `storage/logs/security.log` - Security-specific logs
- `storage/logs/security-monitoring.log` - Automated monitoring logs
- `storage/logs/data-retention.log` - Data retention logs

## 📞 **SUPPORT**

### **Emergency Procedures**
1. **Critical Security Alert**: Check `/admin/security/alerts`
2. **System Compromise**: Disable affected users immediately
3. **Data Breach**: Follow incident response procedures
4. **System Failure**: Check logs and restart services

### **Contact Information**
- **System Administrator**: [Your contact info]
- **Security Team**: [Security team contact]
- **Emergency Contact**: [24/7 emergency contact]

## ✅ **VERIFICATION CHECKLIST**

Before going live, verify:

- [ ] All migrations completed successfully
- [ ] Roles and permissions seeded correctly
- [ ] Security dashboard accessible
- [ ] Scheduled tasks configured
- [ ] Storage directories created
- [ ] Default admin account secured
- [ ] 2FA configured and tested
- [ ] Security monitoring active
- [ ] Audit logging functional
- [ ] Business data isolation working
- [ ] Compliance reporting operational
- [ ] Backup procedures in place
- [ ] Monitoring alerts configured
- [ ] Documentation reviewed
- [ ] Team training completed

---

**Setup Complete!** 🎉

Your comprehensive role and permissions management system is now fully operational with enterprise-grade security, real-time monitoring, and compliance reporting capabilities.
