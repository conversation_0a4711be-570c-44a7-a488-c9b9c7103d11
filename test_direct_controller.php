<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing direct controller access vs middleware...\n\n";

try {
    // Get a business owner user and booking
    $owner = \App\Models\User::role('Business Owner')->first();
    $business = $owner->ownedBusinesses()->first();
    $booking = $business->bookings()->first();
    
    echo "Testing with:\n";
    echo "- Owner: " . $owner->name . " (ID: " . $owner->id . ")\n";
    echo "- Business: " . $business->name . " (ID: " . $business->id . ")\n";
    echo "- Booking: " . $booking->id . "\n\n";
    
    // Test 1: Direct controller call (bypassing middleware)
    echo "=== Test 1: Direct Controller Call ===\n";
    auth()->login($owner);
    
    $controller = new \App\Http\Controllers\Owner\CalendarController();
    
    try {
        $response = $controller->showBooking($booking->id);
        $data = $response->getData(true);
        
        echo "✓ Direct controller call successful\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        if ($data['success']) {
            echo "HTML length: " . strlen($data['html']) . " characters\n";
        } else {
            echo "Error message: " . $data['message'] . "\n";
        }
    } catch (\Exception $e) {
        echo "✗ Direct controller call failed: " . $e->getMessage() . "\n";
    }
    
    // Test 2: Test middleware individually
    echo "\n=== Test 2: Testing Middleware ===\n";
    
    // Test configure.owner middleware
    $request = Request::create('/owner/calendar/booking/' . $booking->id, 'GET');
    $request->setUserResolver(function () use ($owner) {
        return $owner;
    });
    
    echo "Testing configure.owner middleware...\n";
    $configureOwnerMiddleware = new \App\Http\Middleware\ConfigureOwnerPanel();
    
    try {
        $response = $configureOwnerMiddleware->handle($request, function ($req) {
            return response('Middleware passed');
        });
        echo "✓ configure.owner middleware passed\n";
    } catch (\Exception $e) {
        echo "✗ configure.owner middleware failed: " . $e->getMessage() . "\n";
    }
    
    // Test 3: Check route parameter binding
    echo "\n=== Test 3: Route Parameter Binding ===\n";
    
    // Check if the booking ID parameter is being bound correctly
    $router = app('router');
    $routes = $router->getRoutes();
    
    foreach ($routes as $route) {
        if ($route->getName() === 'owner.calendar.booking') {
            echo "Route URI pattern: " . $route->uri() . "\n";
            echo "Route parameters: " . implode(', ', $route->parameterNames()) . "\n";
            
            // Test if the route matches our URL
            $url = 'owner/calendar/booking/' . $booking->id;
            echo "Testing URL: " . $url . "\n";
            
            try {
                $request = Request::create('/' . $url, 'GET');
                $routeMatch = $router->getRoutes()->match($request);
                echo "✓ Route matches successfully\n";
                echo "Matched route: " . $routeMatch->getName() . "\n";
                echo "Parameters: " . json_encode($routeMatch->parameters()) . "\n";
            } catch (\Exception $e) {
                echo "✗ Route matching failed: " . $e->getMessage() . "\n";
            }
            break;
        }
    }
    
    // Test 4: Full request simulation with proper setup
    echo "\n=== Test 4: Full Request Simulation ===\n";
    
    // Create a proper request with all necessary headers and setup
    $request = Request::create(
        url('owner/calendar/booking/' . $booking->id),
        'GET',
        [],
        [],
        [],
        [
            'HTTP_HOST' => 'localhost',
            'HTTP_USER_AGENT' => 'Test Agent',
            'HTTP_ACCEPT' => 'application/json',
        ]
    );
    
    // Set up the session and authentication
    $session = app('session.store');
    $request->setLaravelSession($session);
    $request->setUserResolver(function () use ($owner) {
        return $owner;
    });
    
    // Manually authenticate
    auth()->login($owner);
    
    echo "Request URL: " . $request->url() . "\n";
    echo "Request method: " . $request->method() . "\n";
    echo "Authenticated user: " . (auth()->check() ? auth()->user()->name : 'None') . "\n";
    
    try {
        // Try to handle the request through the application
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);
        $response = $kernel->handle($request);
        
        echo "Response status: " . $response->getStatusCode() . "\n";
        
        if ($response->getStatusCode() === 200) {
            echo "✓ Full request successful\n";
            
            $content = $response->getContent();
            $json = json_decode($content, true);
            
            if ($json && isset($json['success'])) {
                echo "JSON response success: " . ($json['success'] ? 'true' : 'false') . "\n";
            } else {
                echo "Response is not JSON or missing success field\n";
                echo "Content preview: " . substr($content, 0, 200) . "...\n";
            }
        } else {
            echo "✗ Full request failed with status: " . $response->getStatusCode() . "\n";
            echo "Response content preview: " . substr($response->getContent(), 0, 500) . "...\n";
        }
        
    } catch (\Exception $e) {
        echo "✗ Full request threw exception: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }

} catch (\Exception $e) {
    echo "Setup error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\nTest completed.\n";
