<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleAndPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create comprehensive permission structure
        $permissionCategories = [
            'System Administration' => [
                'manage system settings',
                'manage server configuration',
                'manage backups',
                'manage audit logs',
                'manage integrations',
                'manage security settings',
                'view system monitoring',
                'manage database',
                'manage file system',
                'manage email settings',
                'manage sms settings',
                'manage payment gateways',
            ],
            'User Management' => [
                'manage users',
                'create users',
                'edit users',
                'delete users',
                'view users',
                'manage user roles',
                'assign roles',
                'revoke roles',
                'view user activity',
                'manage user permissions',
                'reset user passwords',
                'deactivate users',
            ],
            'Role & Permission Management' => [
                'manage roles',
                'create roles',
                'edit roles',
                'delete roles',
                'view roles',
                'manage permissions',
                'assign permissions',
                'revoke permissions',
                'view permission reports',
                'manage role hierarchy',
                'create permission templates',
                'manage security policies',
            ],
            'Business Management' => [
                'manage businesses',
                'create businesses',
                'edit businesses',
                'delete businesses',
                'view businesses',
                'manage business settings',
                'manage business categories',
                'manage operating hours',
                'manage holidays',
                'manage locations',
                'view business reports',
                'manage business branding',
            ],
            'Service Management' => [
                'manage services',
                'create services',
                'edit services',
                'delete services',
                'view services',
                'manage service categories',
                'manage service pricing',
                'manage service availability',
                'manage service resources',
                'manage service dependencies',
                'view service reports',
                'manage service intervals',
            ],
            'Booking & Calendar Management' => [
                'manage bookings',
                'create bookings',
                'edit bookings',
                'delete bookings',
                'view bookings',
                'view calendar',
                'manage calendar',
                'check-in bookings',
                'check-out bookings',
                'cancel bookings',
                'reschedule bookings',
                'manage waiting lists',
                'send notifications',
                'manage recurring bookings',
            ],
            'Resource Management' => [
                'manage resources',
                'create resources',
                'edit resources',
                'delete resources',
                'view resources',
                'manage resource types',
                'manage resource availability',
                'assign resources',
                'view resource utilization',
                'manage resource maintenance',
                'manage resource bookings',
                'view resource reports',
            ],
            'Customer Management' => [
                'manage customers',
                'create customers',
                'edit customers',
                'delete customers',
                'view customers',
                'view customer history',
                'manage customer preferences',
                'send customer notifications',
                'export customer data',
                'manage customer reviews',
                'view customer reports',
                'manage customer communications',
            ],
            'Reports & Analytics' => [
                'view reports',
                'view booking reports',
                'view revenue reports',
                'view customer reports',
                'view resource utilization',
                'view business analytics',
                'export reports',
                'schedule reports',
                'manage report templates',
                'view real-time analytics',
                'view performance metrics',
                'view audit reports',
            ],
            'Financial Management' => [
                'view financial data',
                'manage payments',
                'process refunds',
                'view revenue reports',
                'manage pricing',
                'manage discounts',
                'manage taxes',
                'view financial analytics',
                'export financial data',
                'manage payment methods',
                'view transaction history',
                'manage invoices',
            ],
        ];

        // Create all permissions
        foreach ($permissionCategories as $category => $permissions) {
            foreach ($permissions as $permission) {
                Permission::firstOrCreate(['name' => $permission]);
            }
        }

        // Create hierarchical roles with specific permissions
        $this->createHierarchicalRoles($permissionCategories);
    }

    private function createHierarchicalRoles($permissionCategories)
    {
        // Level 0: Super Admin (Highest Privilege)
        $superAdminRole = Role::firstOrCreate(['name' => 'Super Admin']);
        $superAdminRole->syncPermissions(Permission::all());

        // Level 1: Admin (Operational Administration)
        $adminRole = Role::firstOrCreate(['name' => 'Admin']);
        $adminPermissions = [
            // User Management (excluding system-level operations)
            'manage users', 'create users', 'edit users', 'view users',
            'manage user roles', 'assign roles', 'view user activity',
            'reset user passwords', 'deactivate users',

            // Business Management
            'manage businesses', 'create businesses', 'edit businesses',
            'view businesses', 'manage business settings', 'manage business categories',
            'manage operating hours', 'manage holidays', 'manage locations',
            'view business reports', 'manage business branding',

            // Service Management
            'manage services', 'create services', 'edit services', 'view services',
            'manage service categories', 'manage service pricing', 'manage service availability',
            'manage service resources', 'manage service dependencies', 'view service reports',

            // Booking & Calendar Management
            'manage bookings', 'create bookings', 'edit bookings', 'view bookings',
            'view calendar', 'manage calendar', 'check-in bookings', 'check-out bookings',
            'cancel bookings', 'reschedule bookings', 'manage waiting lists',
            'send notifications', 'manage recurring bookings',

            // Customer Management
            'manage customers', 'create customers', 'edit customers', 'view customers',
            'view customer history', 'manage customer preferences', 'send customer notifications',
            'export customer data', 'manage customer reviews', 'view customer reports',

            // Reports & Analytics
            'view reports', 'view booking reports', 'view revenue reports',
            'view customer reports', 'view resource utilization', 'view business analytics',
            'export reports', 'view performance metrics',

            // Financial Management (limited)
            'view financial data', 'view revenue reports', 'view financial analytics',
            'view transaction history',
        ];
        $adminRole->syncPermissions($adminPermissions);

        // Level 2: Business Owner (Business-specific Management)
        $ownerRole = Role::firstOrCreate(['name' => 'Business Owner']);
        $ownerPermissions = [
            // Own Business Management
            'view businesses', 'manage business settings', 'manage operating hours',
            'manage holidays', 'manage locations', 'view business reports',
            'manage business branding',

            // Service Management for own business
            'manage services', 'create services', 'edit services', 'view services',
            'manage service categories', 'manage service pricing', 'manage service availability',
            'manage service resources', 'view service reports',

            // Booking & Calendar Management for own business
            'manage bookings', 'create bookings', 'edit bookings', 'view bookings',
            'view calendar', 'manage calendar', 'check-in bookings', 'check-out bookings',
            'cancel bookings', 'reschedule bookings', 'manage waiting lists',
            'send notifications',

            // Customer Management for own business
            'view customers', 'view customer history', 'manage customer preferences',
            'send customer notifications', 'view customer reports',

            // Reports for own business
            'view reports', 'view booking reports', 'view revenue reports',
            'view customer reports', 'view business analytics',

            // Financial Management for own business
            'view financial data', 'view revenue reports', 'view financial analytics',
            'view transaction history',
        ];
        $ownerRole->syncPermissions($ownerPermissions);

        // Level 3: Manager (Operational Management)
        $managerRole = Role::firstOrCreate(['name' => 'Manager']);
        $managerPermissions = [
            // Service Management (limited)
            'view services', 'manage service availability',

            // Booking & Calendar Management
            'manage bookings', 'create bookings', 'edit bookings', 'view bookings',
            'view calendar', 'check-in bookings', 'check-out bookings',
            'cancel bookings', 'reschedule bookings', 'manage waiting lists',

            // Customer Management
            'view customers', 'view customer history', 'send customer notifications',

            // Reports (limited)
            'view reports', 'view booking reports', 'view customer reports',
        ];
        $managerRole->syncPermissions($managerPermissions);

        // Level 4: Staff (Basic Operations)
        $staffRole = Role::firstOrCreate(['name' => 'Staff']);
        $staffPermissions = [
            // Basic booking operations
            'view bookings', 'check-in bookings', 'check-out bookings',
            'view calendar',

            // Basic customer operations
            'view customers', 'view customer history',

            // Basic reports
            'view reports',
        ];
        $staffRole->syncPermissions($staffPermissions);

        // Level 5: Customer (End User)
        $customerRole = Role::firstOrCreate(['name' => 'Customer']);
        // Customers have no admin permissions - they use the customer portal

        // Create default super admin user
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        if (!$superAdmin->hasRole('Super Admin')) {
            $superAdmin->assignRole($superAdminRole);
        }
    }
}
