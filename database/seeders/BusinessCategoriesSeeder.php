<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BusinessCategory;

class BusinessCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Health & Wellness',
                'slug' => 'health-wellness',
                'description' => 'Medical practices, clinics, wellness centers, and health services',
                'icon' => 'fas fa-heartbeat',
                'color' => '#28a745',
                'sort_order' => 1,
            ],
            [
                'name' => 'Beauty & Spa',
                'slug' => 'beauty-spa',
                'description' => 'Beauty salons, spas, massage therapy, and cosmetic services',
                'icon' => 'fas fa-spa',
                'color' => '#e83e8c',
                'sort_order' => 2,
            ],
            [
                'name' => 'Fitness & Sports',
                'slug' => 'fitness-sports',
                'description' => 'Gyms, personal training, sports facilities, and fitness classes',
                'icon' => 'fas fa-dumbbell',
                'color' => '#fd7e14',
                'sort_order' => 3,
            ],
            [
                'name' => 'Education & Training',
                'slug' => 'education-training',
                'description' => 'Schools, tutoring, workshops, and educational services',
                'icon' => 'fas fa-graduation-cap',
                'color' => '#007bff',
                'sort_order' => 4,
            ],
            [
                'name' => 'Professional Services',
                'slug' => 'professional-services',
                'description' => 'Consulting, legal, accounting, and business services',
                'icon' => 'fas fa-briefcase',
                'color' => '#6c757d',
                'sort_order' => 5,
            ],
            [
                'name' => 'Automotive',
                'slug' => 'automotive',
                'description' => 'Car repair, maintenance, detailing, and automotive services',
                'icon' => 'fas fa-car',
                'color' => '#dc3545',
                'sort_order' => 6,
            ],
            [
                'name' => 'Home Services',
                'slug' => 'home-services',
                'description' => 'Cleaning, maintenance, repair, and home improvement services',
                'icon' => 'fas fa-home',
                'color' => '#20c997',
                'sort_order' => 7,
            ],
            [
                'name' => 'Entertainment',
                'slug' => 'entertainment',
                'description' => 'Event venues, entertainment services, and recreational activities',
                'icon' => 'fas fa-music',
                'color' => '#6f42c1',
                'sort_order' => 8,
            ],
            [
                'name' => 'Food & Dining',
                'slug' => 'food-dining',
                'description' => 'Restaurants, catering, food services, and dining experiences',
                'icon' => 'fas fa-utensils',
                'color' => '#ffc107',
                'sort_order' => 9,
            ],
            [
                'name' => 'Technology',
                'slug' => 'technology',
                'description' => 'IT services, computer repair, software development, and tech support',
                'icon' => 'fas fa-laptop',
                'color' => '#17a2b8',
                'sort_order' => 10,
            ],
        ];

        foreach ($categories as $category) {
            BusinessCategory::firstOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }
    }
}
