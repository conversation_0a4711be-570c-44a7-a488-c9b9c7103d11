<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ResourceType;
use App\Models\Business;

class ResourceTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $resourceTypes = [
            [
                'name' => 'Room',
                'slug' => 'room',
                'description' => 'Physical rooms and spaces for appointments',
                'icon' => 'fas fa-door-open',
                'color' => '#007bff',
            ],
            [
                'name' => 'Equipment',
                'slug' => 'equipment',
                'description' => 'Medical equipment, machines, and tools',
                'icon' => 'fas fa-tools',
                'color' => '#28a745',
            ],
            [
                'name' => 'Staff',
                'slug' => 'staff',
                'description' => 'Doctors, therapists, and service providers',
                'icon' => 'fas fa-user-md',
                'color' => '#17a2b8',
            ],
            [
                'name' => 'Vehicle',
                'slug' => 'vehicle',
                'description' => 'Cars, trucks, and transportation resources',
                'icon' => 'fas fa-car',
                'color' => '#dc3545',
            ],
            [
                'name' => 'Table',
                'slug' => 'table',
                'description' => 'Dining tables, treatment tables, and workstations',
                'icon' => 'fas fa-table',
                'color' => '#ffc107',
            ],
            [
                'name' => 'Court',
                'slug' => 'court',
                'description' => 'Sports courts, tennis courts, and playing fields',
                'icon' => 'fas fa-basketball-ball',
                'color' => '#fd7e14',
            ],
            [
                'name' => 'Computer',
                'slug' => 'computer',
                'description' => 'Computers, laptops, and IT equipment',
                'icon' => 'fas fa-desktop',
                'color' => '#6c757d',
            ],
            [
                'name' => 'Bed',
                'slug' => 'bed',
                'description' => 'Hospital beds, massage beds, and treatment beds',
                'icon' => 'fas fa-bed',
                'color' => '#e83e8c',
            ],
            [
                'name' => 'Workspace',
                'slug' => 'workspace',
                'description' => 'Office spaces, coworking areas, and study rooms',
                'icon' => 'fas fa-laptop-house',
                'color' => '#20c997',
            ],
            [
                'name' => 'Other',
                'slug' => 'other',
                'description' => 'Other types of resources not listed above',
                'icon' => 'fas fa-cube',
                'color' => '#6f42c1',
            ],
        ];

        // Create resource types for each business
        $businesses = Business::all();

        foreach ($businesses as $business) {
            foreach ($resourceTypes as $resourceType) {
                ResourceType::firstOrCreate(
                    [
                        'business_id' => $business->id,
                        'slug' => $resourceType['slug']
                    ],
                    array_merge($resourceType, ['business_id' => $business->id])
                );
            }
        }
    }
}
