<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ServiceCategory;
use App\Models\Business;

class ServiceCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing businesses
        $testBusiness = Business::where('slug', 'test-business')->first();
        $drJonathan = Business::where('slug', 'dr-jonathan')->first();

        if (!$testBusiness || !$drJonathan) {
            $this->command->error('Required businesses not found. Please ensure Test Business and DR jonathan exist.');
            return;
        }

        // Service categories for Test Business (Technology)
        $techCategories = [
            [
                'business_id' => $testBusiness->id,
                'name' => 'Software Development',
                'slug' => 'software-development',
                'description' => 'Custom software development, web applications, and mobile app development services',
                'icon' => 'fas fa-code',
                'color' => '#007bff',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'business_id' => $testBusiness->id,
                'name' => 'IT Consulting',
                'slug' => 'it-consulting',
                'description' => 'Technology consulting, system architecture, and digital transformation services',
                'icon' => 'fas fa-laptop-code',
                'color' => '#28a745',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'business_id' => $testBusiness->id,
                'name' => 'Cloud Services',
                'slug' => 'cloud-services',
                'description' => 'Cloud migration, infrastructure setup, and cloud-based solutions',
                'icon' => 'fas fa-cloud',
                'color' => '#17a2b8',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'business_id' => $testBusiness->id,
                'name' => 'Cybersecurity',
                'slug' => 'cybersecurity',
                'description' => 'Security audits, penetration testing, and cybersecurity consulting',
                'icon' => 'fas fa-shield-alt',
                'color' => '#dc3545',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'business_id' => $testBusiness->id,
                'name' => 'Data Analytics',
                'slug' => 'data-analytics',
                'description' => 'Business intelligence, data analysis, and reporting solutions',
                'icon' => 'fas fa-chart-bar',
                'color' => '#6f42c1',
                'sort_order' => 5,
                'is_active' => true,
            ],
        ];

        // Service categories for DR jonathan (Health & Wellness)
        $healthCategories = [
            [
                'business_id' => $drJonathan->id,
                'name' => 'General Consultation',
                'slug' => 'general-consultation',
                'description' => 'General medical consultations and health check-ups',
                'icon' => 'fas fa-stethoscope',
                'color' => '#28a745',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'business_id' => $drJonathan->id,
                'name' => 'Preventive Care',
                'slug' => 'preventive-care',
                'description' => 'Preventive health services, vaccinations, and wellness programs',
                'icon' => 'fas fa-heartbeat',
                'color' => '#e83e8c',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'business_id' => $drJonathan->id,
                'name' => 'Diagnostic Services',
                'slug' => 'diagnostic-services',
                'description' => 'Medical tests, laboratory services, and diagnostic procedures',
                'icon' => 'fas fa-microscope',
                'color' => '#fd7e14',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'business_id' => $drJonathan->id,
                'name' => 'Specialist Referrals',
                'slug' => 'specialist-referrals',
                'description' => 'Referrals to medical specialists and specialized treatments',
                'icon' => 'fas fa-user-md',
                'color' => '#007bff',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'business_id' => $drJonathan->id,
                'name' => 'Telemedicine',
                'slug' => 'telemedicine',
                'description' => 'Remote consultations and virtual health services',
                'icon' => 'fas fa-video',
                'color' => '#20c997',
                'sort_order' => 5,
                'is_active' => true,
            ],
        ];

        // Combine all categories
        $allCategories = array_merge($techCategories, $healthCategories);

        // Create the service categories
        foreach ($allCategories as $categoryData) {
            ServiceCategory::firstOrCreate(
                [
                    'business_id' => $categoryData['business_id'],
                    'slug' => $categoryData['slug']
                ],
                $categoryData
            );
        }

        $this->command->info('Created ' . count($allCategories) . ' service categories successfully!');
        $this->command->info('- ' . count($techCategories) . ' categories for Test Business (Technology)');
        $this->command->info('- ' . count($healthCategories) . ' categories for DR jonathan (Health & Wellness)');
    }
}
