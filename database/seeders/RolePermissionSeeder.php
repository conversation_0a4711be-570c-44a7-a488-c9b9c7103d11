<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            'manage users',
            'manage roles',
            'manage businesses',
            'manage services',
            'manage bookings',
            'view calendar',
            'manage settings',  // Added settings permission
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles
        $roles = [
            'admin' => $permissions,
            'manager' => [
                'manage businesses',
                'manage services',
                'manage bookings',
                'view calendar',
            ],
            'staff' => [
                'manage bookings',
                'view calendar',
            ],
            'customer' => [],
        ];

        foreach ($roles as $role => $rolePermissions) {
            $createdRole = Role::create(['name' => $role]);
            $createdRole->syncPermissions($rolePermissions);
        }

        // Assign admin role to admin user (if exists)
        $adminUser = User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            $adminUser->assignRole('admin');
        }
    }
}
