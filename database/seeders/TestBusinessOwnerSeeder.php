<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class TestBusinessOwnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test business owner
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Business Owner',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Assign Business Owner role
        $role = Role::where('name', 'Business Owner')->first();
        if ($role && !$user->hasRole($role)) {
            $user->assignRole($role);
        }

        echo "Test business owner created: {$user->email}\n";
    }
}
