<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Business;
use App\Models\CustomerTag;
use App\Models\CustomerBusinessProfile;
use App\Models\CustomerTagAssignment;
use App\Models\CustomerLoyaltyPoint;
use App\Models\CustomerCommunication;
use Carbon\Carbon;

class CustomerManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first business for testing
        $business = Business::first();
        
        if (!$business) {
            $this->command->error('No business found. Please create a business first.');
            return;
        }

        $this->command->info("Creating customer management data for business: {$business->name}");

        // Create customer tags
        $this->createCustomerTags($business);

        // Create sample customers
        $this->createSampleCustomers($business);

        $this->command->info('Customer management data created successfully!');
    }

    /**
     * Create customer tags for the business.
     */
    private function createCustomerTags($business)
    {
        $tags = [
            [
                'name' => 'VIP Customer',
                'color' => '#ffc107',
                'description' => 'High-value customers with premium status',
            ],
            [
                'name' => 'New Customer',
                'color' => '#28a745',
                'description' => 'Recently acquired customers (within 30 days)',
            ],
            [
                'name' => 'Regular Customer',
                'color' => '#007bff',
                'description' => 'Frequent visitors with multiple bookings',
            ],
            [
                'name' => 'At Risk',
                'color' => '#dc3545',
                'description' => 'Customers who haven\'t visited recently',
            ],
            [
                'name' => 'Birthday This Month',
                'color' => '#e83e8c',
                'description' => 'Customers celebrating birthdays this month',
            ],
            [
                'name' => 'Referral Source',
                'color' => '#17a2b8',
                'description' => 'Customers who have referred others',
            ],
            [
                'name' => 'Corporate Client',
                'color' => '#6f42c1',
                'description' => 'Business or corporate customers',
            ],
        ];

        foreach ($tags as $tagData) {
            CustomerTag::create(array_merge($tagData, [
                'business_id' => $business->id,
                'is_active' => true,
            ]));
        }

        $this->command->info('Created customer tags');
    }

    /**
     * Create sample customers for the business.
     */
    private function createSampleCustomers($business)
    {
        $customers = [
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'date_of_birth' => '1985-03-15',
                'gender' => 'female',
                'profile' => [
                    'status' => 'vip',
                    'customer_since' => '2023-01-15',
                    'total_visits' => 25,
                    'total_spent' => 2500.00,
                    'loyalty_tier' => 'gold',
                    'notes' => 'Prefers morning appointments. Allergic to certain fragrances.',
                ],
                'tags' => ['VIP Customer', 'Regular Customer'],
                'points' => 500,
            ],
            [
                'name' => 'Michael Chen',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'date_of_birth' => '1990-07-22',
                'gender' => 'male',
                'profile' => [
                    'status' => 'active',
                    'customer_since' => '2024-01-10',
                    'total_visits' => 3,
                    'total_spent' => 180.00,
                    'loyalty_tier' => 'bronze',
                    'notes' => 'New customer, very satisfied with services.',
                ],
                'tags' => ['New Customer'],
                'points' => 90,
            ],
            [
                'name' => 'Emily Rodriguez',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'date_of_birth' => '1988-11-08',
                'gender' => 'female',
                'profile' => [
                    'status' => 'active',
                    'customer_since' => '2023-06-20',
                    'total_visits' => 12,
                    'total_spent' => 960.00,
                    'loyalty_tier' => 'silver',
                    'notes' => 'Regular customer, books monthly appointments.',
                ],
                'tags' => ['Regular Customer'],
                'points' => 240,
            ],
            [
                'name' => 'David Thompson',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'date_of_birth' => '1975-05-12',
                'gender' => 'male',
                'profile' => [
                    'status' => 'active',
                    'customer_since' => '2022-08-05',
                    'total_visits' => 8,
                    'total_spent' => 640.00,
                    'loyalty_tier' => 'bronze',
                    'notes' => 'Corporate client, books for team events.',
                ],
                'tags' => ['Corporate Client'],
                'points' => 160,
            ],
            [
                'name' => 'Lisa Wang',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'date_of_birth' => '1992-12-03',
                'gender' => 'female',
                'profile' => [
                    'status' => 'inactive',
                    'customer_since' => '2023-03-10',
                    'total_visits' => 5,
                    'total_spent' => 300.00,
                    'loyalty_tier' => 'bronze',
                    'last_visit_date' => '2023-08-15',
                    'notes' => 'Has not visited in several months.',
                ],
                'tags' => ['At Risk'],
                'points' => 75,
            ],
        ];

        foreach ($customers as $customerData) {
            // Create user
            $user = User::create([
                'name' => $customerData['name'],
                'email' => $customerData['email'],
                'phone' => $customerData['phone'],
                'date_of_birth' => $customerData['date_of_birth'],
                'gender' => $customerData['gender'],
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]);

            // Create customer business profile
            $profileData = $customerData['profile'];
            $profile = CustomerBusinessProfile::create([
                'business_id' => $business->id,
                'customer_id' => $user->id,
                'status' => $profileData['status'],
                'customer_since' => $profileData['customer_since'],
                'last_visit_date' => $profileData['last_visit_date'] ?? now()->subDays(rand(1, 30)),
                'total_visits' => $profileData['total_visits'],
                'total_spent' => $profileData['total_spent'],
                'average_order_value' => $profileData['total_spent'] / $profileData['total_visits'],
                'loyalty_tier' => $profileData['loyalty_tier'],
                'loyalty_points_balance' => $customerData['points'],
                'lifetime_value' => $profileData['total_spent'],
                'notes' => $profileData['notes'],
                'marketing_consent' => true,
                'marketing_consent_date' => now(),
            ]);

            // Assign tags
            $tags = CustomerTag::where('business_id', $business->id)
                ->whereIn('name', $customerData['tags'])
                ->get();

            foreach ($tags as $tag) {
                CustomerTagAssignment::create([
                    'business_id' => $business->id,
                    'customer_id' => $user->id,
                    'customer_tag_id' => $tag->id,
                    'assigned_by' => 1, // Assuming admin user ID 1
                    'assigned_at' => now(),
                ]);
            }

            // Create loyalty points history
            CustomerLoyaltyPoint::create([
                'business_id' => $business->id,
                'customer_id' => $user->id,
                'type' => 'earned',
                'points' => 50,
                'description' => 'Welcome bonus',
                'processed_by' => 1,
                'running_balance' => 50,
            ]);

            if ($customerData['points'] > 50) {
                CustomerLoyaltyPoint::create([
                    'business_id' => $business->id,
                    'customer_id' => $user->id,
                    'type' => 'earned',
                    'points' => $customerData['points'] - 50,
                    'description' => 'Points from bookings',
                    'processed_by' => 1,
                    'running_balance' => $customerData['points'],
                ]);
            }

            // Create sample communication
            CustomerCommunication::create([
                'business_id' => $business->id,
                'customer_id' => $user->id,
                'sent_by' => 1,
                'type' => 'email',
                'direction' => 'outbound',
                'subject' => 'Welcome to our business!',
                'message' => 'Thank you for choosing our services. We look forward to serving you!',
                'status' => 'sent',
                'sent_at' => now()->subDays(rand(1, 30)),
            ]);
        }

        $this->command->info('Created sample customers with profiles, tags, and loyalty points');
    }
}
