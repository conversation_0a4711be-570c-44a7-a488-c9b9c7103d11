<?php

namespace Database\Seeders;

use App\Models\Business;
use App\Models\Service;
use App\Models\User;
use Illuminate\Database\Seeder;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the test user
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            echo "Test user not found. Please run TestUserSeeder first.\n";
            return;
        }

        // Create test business
        $business = Business::firstOrCreate(
            ['slug' => 'test-business'],
            [
                'name' => 'Test Business',
                'description' => 'A test business for booking system',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'timezone' => 'UTC',
                'currency' => 'USD',
                'language' => 'en',
                'multi_branch' => false,
                'online_booking_enabled' => true,
                'booking_advance_days' => 30,
                'booking_advance_hours' => 2,
                'cancellation_hours' => 24,
                'is_active' => true,
                'owner_id' => $user->id,
            ]
        );

        // Create test services
        $services = [
            [
                'name' => 'Basic Consultation',
                'slug' => 'basic-consultation',
                'description' => 'A basic consultation service',
                'duration_minutes' => 60,
                'base_price' => 100.00,
                'max_participants' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Premium Service',
                'slug' => 'premium-service',
                'description' => 'A premium service offering',
                'duration_minutes' => 90,
                'base_price' => 150.00,
                'max_participants' => 2,
                'is_active' => true,
            ],
        ];

        foreach ($services as $serviceData) {
            Service::firstOrCreate(
                [
                    'business_id' => $business->id,
                    'slug' => $serviceData['slug']
                ],
                array_merge($serviceData, ['business_id' => $business->id])
            );
        }

        echo "Test business and services created successfully.\n";
        echo "Business: {$business->name} (ID: {$business->id})\n";
        echo "Services: " . Service::where('business_id', $business->id)->count() . " created\n";
    }
}
