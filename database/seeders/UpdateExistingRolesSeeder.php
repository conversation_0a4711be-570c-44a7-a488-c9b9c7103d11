<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\DB;

class UpdateExistingRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update existing roles with enhanced attributes
        $roleUpdates = [
            'Super Admin' => [
                'hierarchy_level' => 0,
                'security_level' => 5,
                'is_system_role' => true,
                'description' => 'Highest privilege level with complete system access and administration capabilities.',
            ],
            'Admin' => [
                'hierarchy_level' => 1,
                'security_level' => 4,
                'is_system_role' => true,
                'description' => 'Administrative role with operational management capabilities across the system.',
            ],
            'Business Owner' => [
                'hierarchy_level' => 2,
                'security_level' => 3,
                'is_system_role' => true,
                'description' => 'Business-specific management role with control over owned business operations.',
            ],
            'Manager' => [
                'hierarchy_level' => 3,
                'security_level' => 3,
                'is_system_role' => true,
                'description' => 'Operational management role with booking and customer management capabilities.',
            ],
            'Staff' => [
                'hierarchy_level' => 4,
                'security_level' => 2,
                'is_system_role' => true,
                'description' => 'Basic operational role with limited booking and customer interaction capabilities.',
            ],
            'Customer' => [
                'hierarchy_level' => 5,
                'security_level' => 1,
                'is_system_role' => true,
                'description' => 'End-user role with access to customer portal and booking capabilities.',
            ],
        ];

        foreach ($roleUpdates as $roleName => $attributes) {
            $role = Role::where('name', $roleName)->first();
            
            if ($role) {
                // Update the role with enhanced attributes
                DB::table('roles')
                    ->where('id', $role->id)
                    ->update([
                        'hierarchy_level' => $attributes['hierarchy_level'],
                        'security_level' => $attributes['security_level'],
                        'is_system_role' => $attributes['is_system_role'],
                        'description' => $attributes['description'],
                        'updated_at' => now(),
                    ]);

                $this->command->info("Updated role: {$roleName}");
            } else {
                $this->command->warn("Role not found: {$roleName}");
            }
        }

        $this->command->info('Existing roles have been updated with enhanced security attributes.');
    }
}
