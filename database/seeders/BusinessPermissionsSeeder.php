<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class BusinessPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions for business management
        $permissions = [
            // User management
            'manage users',
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Role management
            'manage roles',
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',

            // Business management
            'manage businesses',
            'view businesses',
            'create businesses',
            'edit businesses',
            'delete businesses',

            // Service management
            'manage services',
            'view services',
            'create services',
            'edit services',
            'delete services',

            // Resource management
            'manage resources',
            'view resources',
            'create resources',
            'edit resources',
            'delete resources',

            // Booking management
            'manage bookings',
            'view bookings',
            'create bookings',
            'edit bookings',
            'cancel bookings',
            'check-in bookings',
            'check-out bookings',

            // Calendar management
            'view calendar',
            'manage calendar',
            'create calendar events',
            'edit calendar events',
            'delete calendar events',

            // Payment management
            'manage payments',
            'view payments',
            'process payments',
            'refund payments',

            // Reporting
            'view reports',
            'export reports',

            // Waiting list management
            'manage waiting lists',
            'view waiting lists',
            'notify waiting lists',

            // Notification management
            'manage notifications',
            'view notifications',
            'send notifications',

            // Settings management
            'manage settings',
            'view settings',
            'edit settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to existing roles
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $superAdminRole->givePermissionTo($permissions);
        }

        // Create Business Owner role
        $businessOwnerRole = Role::firstOrCreate(['name' => 'Business Owner']);
        $businessOwnerPermissions = [
            'manage users',
            'view users',
            'create users',
            'edit users',
            'manage roles',
            'view roles',
            'manage businesses',
            'view businesses',
            'edit businesses',
            'manage services',
            'view services',
            'create services',
            'edit services',
            'delete services',
            'manage resources',
            'view resources',
            'create resources',
            'edit resources',
            'delete resources',
            'manage bookings',
            'view bookings',
            'create bookings',
            'edit bookings',
            'cancel bookings',
            'check-in bookings',
            'check-out bookings',
            'view calendar',
            'manage calendar',
            'create calendar events',
            'edit calendar events',
            'delete calendar events',
            'manage payments',
            'view payments',
            'process payments',
            'view reports',
            'export reports',
            'manage waiting lists',
            'view waiting lists',
            'notify waiting lists',
            'manage notifications',
            'view notifications',
            'send notifications',
            'manage settings',
            'view settings',
            'edit settings',
        ];
        $businessOwnerRole->givePermissionTo($businessOwnerPermissions);

        // Create Staff role
        $staffRole = Role::firstOrCreate(['name' => 'Staff']);
        $staffPermissions = [
            'view businesses',
            'view services',
            'view resources',
            'manage bookings',
            'view bookings',
            'create bookings',
            'edit bookings',
            'check-in bookings',
            'check-out bookings',
            'view calendar',
            'create calendar events',
            'view payments',
            'process payments',
            'view waiting lists',
            'notify waiting lists',
            'view notifications',
            'send notifications',
        ];
        $staffRole->givePermissionTo($staffPermissions);

        // Create Receptionist role
        $receptionistRole = Role::firstOrCreate(['name' => 'Receptionist']);
        $receptionistPermissions = [
            'view businesses',
            'view services',
            'view resources',
            'manage bookings',
            'view bookings',
            'create bookings',
            'edit bookings',
            'check-in bookings',
            'check-out bookings',
            'view calendar',
            'create calendar events',
            'view payments',
            'manage waiting lists',
            'view waiting lists',
            'notify waiting lists',
            'view notifications',
            'send notifications',
        ];
        $receptionistRole->givePermissionTo($receptionistPermissions);

        // Create Customer role
        $customerRole = Role::firstOrCreate(['name' => 'Customer']);
        $customerPermissions = [
            'view businesses',
            'view services',
            'create bookings',
            'view bookings',
            'cancel bookings',
        ];
        $customerRole->givePermissionTo($customerPermissions);
    }
}
