<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Assign Super Admin role
        $role = Role::where('name', 'Super Admin')->first();
        if ($role && !$user->hasRole($role)) {
            $user->assignRole($role);
        }

        echo "Test user created: {$user->email}\n";
    }
}
