<?php

namespace Database\Seeders;

use App\Models\Business;
use App\Models\Resource;
use App\Models\ResourceType;
use Illuminate\Database\Seeder;

class ResourcesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first available business (or create one if none exists)
        $business = Business::first();
        
        if (!$business) {
            // Create a default business if none exists
            $business = Business::create([
                'name' => 'Demo Business',
                'slug' => 'demo-business',
                'description' => 'A demo business for testing resources',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'timezone' => 'UTC',
                'currency' => 'USD',
                'language' => 'en',
                'multi_branch' => false,
                'online_booking_enabled' => true,
                'booking_advance_days' => 30,
                'booking_advance_hours' => 2,
                'cancellation_hours' => 24,
                'is_active' => true,
                'owner_id' => 1, // Assuming first user exists
            ]);
        }

        // Get resource types
        $roomType = ResourceType::where('slug', 'room')->first();
        $equipmentType = ResourceType::where('slug', 'equipment')->first();
        $staffType = ResourceType::where('slug', 'staff')->first();
        $vehicleType = ResourceType::where('slug', 'vehicle')->first();
        $workspaceType = ResourceType::where('slug', 'workspace')->first();
        $otherType = ResourceType::where('slug', 'other')->first();

        // Create 6 diverse resources
        $resources = [
            [
                'business_id' => $business->id,
                'resource_type_id' => $roomType?->id ?? 1,
                'name' => 'Conference Room A',
                'description' => 'Large conference room with projector and whiteboard, seats up to 12 people',
                'capacity' => 12,
                'hourly_rate' => 50.00,
                'specifications' => [
                    'size' => '400 sq ft',
                    'equipment' => ['Projector', 'Whiteboard', 'Conference Phone', 'WiFi'],
                    'features' => ['Air Conditioning', 'Natural Light', 'Sound Proof']
                ],
                'availability_rules' => [
                    'min_booking_hours' => 1,
                    'max_booking_hours' => 8,
                    'advance_booking_days' => 30
                ],
                'requires_approval' => false,
                'is_active' => true,
            ],
            [
                'business_id' => $business->id,
                'resource_type_id' => $equipmentType?->id ?? 2,
                'name' => 'Professional Camera Kit',
                'description' => 'Complete photography equipment including DSLR camera, lenses, and lighting',
                'capacity' => 1,
                'hourly_rate' => 75.00,
                'specifications' => [
                    'camera' => 'Canon EOS R5',
                    'lenses' => ['24-70mm f/2.8', '70-200mm f/2.8', '50mm f/1.4'],
                    'accessories' => ['Tripod', 'Flash', 'Reflectors', 'Memory Cards']
                ],
                'availability_rules' => [
                    'min_booking_hours' => 2,
                    'max_booking_hours' => 12,
                    'advance_booking_days' => 14
                ],
                'requires_approval' => true,
                'is_active' => true,
            ],
            [
                'business_id' => $business->id,
                'resource_type_id' => $staffType?->id ?? 3,
                'name' => 'Dr. Sarah Johnson',
                'description' => 'Senior consultant with 15+ years experience in business strategy',
                'capacity' => 1,
                'hourly_rate' => 200.00,
                'specifications' => [
                    'specialization' => 'Business Strategy',
                    'experience' => '15+ years',
                    'certifications' => ['MBA', 'PMP', 'Six Sigma Black Belt'],
                    'languages' => ['English', 'Spanish', 'French']
                ],
                'availability_rules' => [
                    'min_booking_hours' => 1,
                    'max_booking_hours' => 6,
                    'advance_booking_days' => 21
                ],
                'requires_approval' => true,
                'is_active' => true,
            ],
            [
                'business_id' => $business->id,
                'resource_type_id' => $vehicleType?->id ?? 4,
                'name' => 'Delivery Van #1',
                'description' => 'Large cargo van perfect for deliveries and transportation services',
                'capacity' => 2,
                'hourly_rate' => 35.00,
                'specifications' => [
                    'make' => 'Ford Transit',
                    'year' => '2022',
                    'cargo_space' => '246 cubic feet',
                    'features' => ['GPS Navigation', 'Backup Camera', 'Climate Control'],
                    'fuel_type' => 'Gasoline'
                ],
                'availability_rules' => [
                    'min_booking_hours' => 2,
                    'max_booking_hours' => 10,
                    'advance_booking_days' => 7
                ],
                'requires_approval' => false,
                'is_active' => true,
            ],
            [
                'business_id' => $business->id,
                'resource_type_id' => $workspaceType?->id ?? 5,
                'name' => 'Private Office Suite',
                'description' => 'Fully furnished private office with desk, chair, and high-speed internet',
                'capacity' => 4,
                'hourly_rate' => 25.00,
                'specifications' => [
                    'size' => '200 sq ft',
                    'furniture' => ['Executive Desk', 'Ergonomic Chairs', 'Filing Cabinet', 'Bookshelf'],
                    'technology' => ['High-speed WiFi', 'Ethernet Port', 'Power Outlets'],
                    'amenities' => ['Coffee Machine', 'Printer Access', 'Phone Line']
                ],
                'availability_rules' => [
                    'min_booking_hours' => 1,
                    'max_booking_hours' => 12,
                    'advance_booking_days' => 30
                ],
                'requires_approval' => false,
                'is_active' => true,
            ],
            [
                'business_id' => $business->id,
                'resource_type_id' => $otherType?->id ?? 6,
                'name' => 'Event Sound System',
                'description' => 'Professional audio equipment for events, presentations, and performances',
                'capacity' => 1,
                'hourly_rate' => 60.00,
                'specifications' => [
                    'speakers' => '2x 15" Main Speakers + 2x 12" Monitors',
                    'mixer' => '16-channel Digital Mixer',
                    'microphones' => ['4x Wireless Handheld', '2x Wireless Lapel', '4x Wired'],
                    'accessories' => ['Cables', 'Stands', 'Power Distribution']
                ],
                'availability_rules' => [
                    'min_booking_hours' => 3,
                    'max_booking_hours' => 12,
                    'advance_booking_days' => 14
                ],
                'requires_approval' => true,
                'is_active' => true,
            ],
        ];

        foreach ($resources as $resourceData) {
            Resource::firstOrCreate(
                [
                    'business_id' => $resourceData['business_id'],
                    'name' => $resourceData['name']
                ],
                $resourceData
            );
        }

        $this->command->info('6 diverse resources have been created successfully!');
    }
}
