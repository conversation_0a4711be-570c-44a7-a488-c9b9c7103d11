<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Business;
use App\Models\Service;
use App\Models\ServiceCategory;
use App\Models\BusinessLandingPage;
use App\Models\BusinessLandingPageSection;
use App\Models\LandingServiceSettings;
use App\Models\BusinessSeoSettings;
use App\Models\Staff;
use App\Models\Review;
use App\Models\BusinessOperatingHours;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class SampleBusinessDataSeeder extends Seeder
{
    public function run()
    {
        // Create sample business owners
        $owners = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'business_name' => 'Elegant Beauty Salon',
                'business_type' => 'beauty_salon',
                'description' => 'Premium beauty salon offering professional hair styling, skincare treatments, and wellness services in a luxurious environment.',
                'slug' => 'elegant-beauty-salon'
            ],
            [
                'name' => 'Dr. <PERSON>',
                'email' => '<EMAIL>',
                'business_name' => 'Chen Family Dental',
                'business_type' => 'dental_clinic',
                'description' => 'Modern dental practice providing comprehensive oral health care with state-of-the-art technology and gentle, personalized treatment.',
                'slug' => 'chen-family-dental'
            ],
            [
                'name' => '<PERSON> <PERSON>',
                'email' => '<EMAIL>',
                'business_name' => 'FitnessPlus Studio',
                'business_type' => 'fitness_center',
                'description' => 'Dynamic fitness studio offering personal training, group classes, and wellness programs to help you achieve your health goals.',
                'slug' => 'fitnessplus-studio'
            ]
        ];

        foreach ($owners as $ownerData) {
            $this->createSampleBusiness($ownerData);
        }
    }

    private function createSampleBusiness($ownerData)
    {
        // Check if user already exists
        $owner = User::where('email', $ownerData['email'])->first();

        if (!$owner) {
            // Create owner user
            $owner = User::create([
                'name' => $ownerData['name'],
                'email' => $ownerData['email'],
                'password' => Hash::make('password123'),
                'email_verified_at' => now()
            ]);
        }

        // Check if business already exists
        $existingBusiness = Business::where('owner_id', $owner->id)->first();
        if ($existingBusiness) {
            echo "Business already exists for {$ownerData['name']}: {$ownerData['business_name']}\n";
            return;
        }

        // Create business
        $business = Business::create([
            'owner_id' => $owner->id,
            'name' => $ownerData['business_name'],
            'description' => $ownerData['description'],
            'phone' => $this->generatePhone(),
            'email' => $ownerData['email'],
            'website' => 'https://' . $ownerData['slug'] . '.com',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'is_active' => true,
            'online_booking_enabled' => true,
            'landing_page_enabled' => true,
            'landing_page_slug' => $ownerData['slug'],
            'landing_page_status' => 'published'
        ]);

        // Create service categories and services
        $this->createServicesForBusiness($business, $ownerData['business_type']);

        // Create staff members
        $this->createStaffForBusiness($business, $ownerData['business_type']);

        // Create operating hours
        $this->createOperatingHours($business);

        // Create landing page
        $this->createLandingPage($business, $ownerData);

        // Create reviews
        $this->createReviews($business);

        echo "Created sample business: {$ownerData['business_name']}\n";
    }

    private function createServicesForBusiness($business, $businessType)
    {
        $serviceData = $this->getServicesByType($businessType);

        foreach ($serviceData['categories'] as $categoryData) {
            $category = ServiceCategory::create([
                'business_id' => $business->id,
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'is_active' => true,
                'sort_order' => $categoryData['sort_order']
            ]);

            foreach ($categoryData['services'] as $serviceData) {
                Service::create([
                    'business_id' => $business->id,
                    'service_category_id' => $category->id,
                    'name' => $serviceData['name'],
                    'description' => $serviceData['long_description'],
                    'short_description' => $serviceData['description'],
                    'duration_minutes' => $serviceData['duration'],
                    'base_price' => $serviceData['price'],
                    'is_active' => true,
                    'is_public' => true,
                    'online_booking_enabled' => true,
                    'slug' => Str::slug($serviceData['name']),
                    'landing_display_order' => $serviceData['display_order'],
                    'featured_on_landing' => $serviceData['display_order'] <= 3,
                    'show_price_on_landing' => true,
                    'show_duration_on_landing' => true,
                    'show_description_on_landing' => true,
                    'quick_booking_enabled' => true
                ]);
            }
        }
    }

    private function createStaffForBusiness($business, $businessType)
    {
        $staffData = $this->getStaffByType($businessType);

        foreach ($staffData as $member) {
            Staff::create([
                'business_id' => $business->id,
                'name' => $member['name'],
                'position' => $member['position'],
                'bio' => $member['bio'],
                'years_experience' => $member['experience'],
                'is_active' => true,
                'accepts_bookings' => true,
                'show_on_landing' => true,
                'sort_order' => $member['sort_order']
            ]);
        }
    }

    private function createOperatingHours($business)
    {
        $hours = [
            ['day_of_week' => 1, 'open_time' => '09:00', 'close_time' => '18:00'],
            ['day_of_week' => 2, 'open_time' => '09:00', 'close_time' => '18:00'],
            ['day_of_week' => 3, 'open_time' => '09:00', 'close_time' => '18:00'],
            ['day_of_week' => 4, 'open_time' => '09:00', 'close_time' => '20:00'],
            ['day_of_week' => 5, 'open_time' => '09:00', 'close_time' => '20:00'],
            ['day_of_week' => 6, 'open_time' => '08:00', 'close_time' => '17:00'],
            ['day_of_week' => 0, 'is_closed' => true]
        ];

        foreach ($hours as $hour) {
            BusinessOperatingHour::create(array_merge($hour, ['business_id' => $business->id]));
        }
    }

    private function createLandingPage($business, $ownerData)
    {
        // Create landing page
        $landingPage = BusinessLandingPage::create([
            'business_id' => $business->id,
            'page_title' => $business->name . ' - Professional ' . ucwords(str_replace('_', ' ', $ownerData['business_type'])),
            'page_description' => $business->description,
            'meta_title' => $business->name . ' | Book Online',
            'meta_description' => $business->description,
            'meta_keywords' => $this->generateKeywords($ownerData['business_type']),
            'custom_slug' => $ownerData['slug'],
            'theme' => 'modern',
            'domain_type' => 'subdirectory',
            'is_published' => true,
            'booking_enabled' => true,
            'branding_config' => $this->getBrandingConfig($ownerData['business_type'])
        ]);

        // Create landing page sections
        $this->createLandingPageSections($landingPage, $ownerData);

        // Create service settings
        LandingServiceSettings::create([
            'business_id' => $business->id,
            'display_format' => 'enhanced_cards',
            'layout_style' => 'grid',
            'columns_count' => 3,
            'show_pricing' => true,
            'show_duration' => true,
            'show_descriptions' => true,
            'show_booking_buttons' => true,
            'homepage_display_count' => 6,
            'enable_service_pages' => true,
            'track_service_analytics' => true
        ]);

        // Create SEO settings
        BusinessSeoSettings::create([
            'business_id' => $business->id,
            'og_title' => $business->name,
            'og_description' => $business->description,
            'og_type' => 'business.business',
            'twitter_card' => 'summary_large_image',
            'twitter_title' => $business->name,
            'twitter_description' => $business->description,
            'business_schema' => $this->generateBusinessSchema($business)
        ]);
    }

    private function createReviews($business)
    {
        $reviews = [
            ['name' => 'Jennifer Smith', 'rating' => 5, 'text' => 'Absolutely amazing service! The staff is professional and the results exceeded my expectations.'],
            ['name' => 'Robert Johnson', 'rating' => 5, 'text' => 'Best experience I\'ve had. Highly recommend to anyone looking for quality service.'],
            ['name' => 'Lisa Brown', 'rating' => 4, 'text' => 'Great service and friendly staff. Will definitely be coming back.'],
            ['name' => 'David Wilson', 'rating' => 5, 'text' => 'Professional, clean, and excellent results. Worth every penny!'],
            ['name' => 'Emily Davis', 'rating' => 5, 'text' => 'Outstanding service from start to finish. The team really knows what they\'re doing.']
        ];

        foreach ($reviews as $reviewData) {
            Review::create([
                'business_id' => $business->id,
                'customer_name' => $reviewData['name'],
                'rating' => $reviewData['rating'],
                'review_text' => $reviewData['text'],
                'is_approved' => true,
                'is_featured' => true,
                'created_at' => now()->subDays(rand(1, 30))
            ]);
        }
    }

    private function generatePhone()
    {
        return '(' . rand(200, 999) . ') ' . rand(200, 999) . '-' . rand(1000, 9999);
    }

    private function generateAddress()
    {
        $streets = ['Main St', 'Oak Ave', 'Pine Rd', 'Elm St', 'Maple Dr', 'Cedar Ln'];
        return rand(100, 9999) . ' ' . $streets[array_rand($streets)];
    }

    private function generateKeywords($businessType)
    {
        $keywords = [
            'beauty_salon' => 'beauty salon, hair styling, skincare, spa, beauty treatments, hair care',
            'dental_clinic' => 'dental care, dentist, oral health, teeth cleaning, dental clinic',
            'fitness_center' => 'fitness, gym, personal training, workout, health, wellness'
        ];

        return $keywords[$businessType] ?? 'professional services, booking, appointments';
    }

    private function getBrandingConfig($businessType)
    {
        $configs = [
            'beauty_salon' => ['primary_color' => '#e91e63', 'secondary_color' => '#f8bbd9', 'accent_color' => '#ad1457'],
            'dental_clinic' => ['primary_color' => '#2196f3', 'secondary_color' => '#bbdefb', 'accent_color' => '#1565c0'],
            'fitness_center' => ['primary_color' => '#4caf50', 'secondary_color' => '#c8e6c9', 'accent_color' => '#2e7d32']
        ];

        return $configs[$businessType] ?? ['primary_color' => '#007bff', 'secondary_color' => '#6c757d', 'accent_color' => '#28a745'];
    }

    private function getServicesByType($businessType)
    {
        $services = [
            'beauty_salon' => [
                'categories' => [
                    [
                        'name' => 'Hair Services',
                        'description' => 'Professional hair styling and treatments',
                        'sort_order' => 1,
                        'services' => [
                            [
                                'name' => 'Haircut & Style',
                                'description' => 'Professional haircut with styling',
                                'long_description' => 'Get a fresh new look with our expert haircut and styling service. Our experienced stylists will work with you to create the perfect cut that complements your face shape and lifestyle.',
                                'duration' => 60,
                                'price' => 65.00,
                                'display_order' => 1,
                                'preparation' => 'Come with clean, dry hair',
                                'aftercare' => 'Use recommended hair products for best results'
                            ],
                            [
                                'name' => 'Hair Coloring',
                                'description' => 'Full color or highlights',
                                'long_description' => 'Transform your look with our professional hair coloring services. From subtle highlights to bold color changes, our colorists use premium products for beautiful, long-lasting results.',
                                'duration' => 120,
                                'price' => 120.00,
                                'display_order' => 2
                            ],
                            [
                                'name' => 'Hair Treatment',
                                'description' => 'Deep conditioning and repair',
                                'long_description' => 'Restore your hair\'s health and shine with our intensive treatment services. Perfect for damaged, dry, or chemically treated hair.',
                                'duration' => 45,
                                'price' => 45.00,
                                'display_order' => 3
                            ]
                        ]
                    ],
                    [
                        'name' => 'Skincare',
                        'description' => 'Facial treatments and skincare services',
                        'sort_order' => 2,
                        'services' => [
                            [
                                'name' => 'Classic Facial',
                                'description' => 'Deep cleansing and moisturizing facial',
                                'long_description' => 'Rejuvenate your skin with our signature facial treatment. Includes cleansing, exfoliation, extraction, and moisturizing for a healthy, glowing complexion.',
                                'duration' => 75,
                                'price' => 85.00,
                                'display_order' => 4
                            ],
                            [
                                'name' => 'Anti-Aging Treatment',
                                'description' => 'Advanced anti-aging facial therapy',
                                'long_description' => 'Combat signs of aging with our specialized treatment using premium products and techniques to reduce fine lines and improve skin texture.',
                                'duration' => 90,
                                'price' => 125.00,
                                'display_order' => 5
                            ]
                        ]
                    ]
                ]
            ],
            'dental_clinic' => [
                'categories' => [
                    [
                        'name' => 'General Dentistry',
                        'description' => 'Comprehensive dental care services',
                        'sort_order' => 1,
                        'services' => [
                            [
                                'name' => 'Dental Cleaning',
                                'description' => 'Professional teeth cleaning and examination',
                                'long_description' => 'Maintain optimal oral health with our thorough dental cleaning service. Includes plaque removal, polishing, and comprehensive oral examination.',
                                'duration' => 60,
                                'price' => 120.00,
                                'display_order' => 1,
                                'preparation' => 'Brush teeth before appointment',
                                'aftercare' => 'Avoid eating for 30 minutes after fluoride treatment'
                            ],
                            [
                                'name' => 'Dental Filling',
                                'description' => 'Tooth restoration with composite fillings',
                                'long_description' => 'Restore damaged teeth with our high-quality composite fillings. Natural-looking results that blend seamlessly with your existing teeth.',
                                'duration' => 45,
                                'price' => 180.00,
                                'display_order' => 2
                            ],
                            [
                                'name' => 'Root Canal Treatment',
                                'description' => 'Advanced endodontic therapy',
                                'long_description' => 'Save your natural tooth with our gentle root canal treatment. Using modern techniques to ensure comfort throughout the procedure.',
                                'duration' => 90,
                                'price' => 850.00,
                                'display_order' => 3
                            ]
                        ]
                    ],
                    [
                        'name' => 'Cosmetic Dentistry',
                        'description' => 'Aesthetic dental treatments',
                        'sort_order' => 2,
                        'services' => [
                            [
                                'name' => 'Teeth Whitening',
                                'description' => 'Professional teeth whitening treatment',
                                'long_description' => 'Achieve a brighter, whiter smile with our professional whitening treatment. Safe and effective results in just one visit.',
                                'duration' => 75,
                                'price' => 350.00,
                                'display_order' => 4
                            ]
                        ]
                    ]
                ]
            ],
            'fitness_center' => [
                'categories' => [
                    [
                        'name' => 'Personal Training',
                        'description' => 'One-on-one fitness coaching',
                        'sort_order' => 1,
                        'services' => [
                            [
                                'name' => 'Personal Training Session',
                                'description' => 'Individual fitness coaching session',
                                'long_description' => 'Achieve your fitness goals with personalized training sessions. Our certified trainers create custom workout plans tailored to your needs and fitness level.',
                                'duration' => 60,
                                'price' => 75.00,
                                'display_order' => 1,
                                'preparation' => 'Wear comfortable workout clothes and bring water',
                                'aftercare' => 'Stay hydrated and follow recommended recovery routine'
                            ],
                            [
                                'name' => 'Fitness Assessment',
                                'description' => 'Comprehensive fitness evaluation',
                                'long_description' => 'Get a complete assessment of your current fitness level, including body composition analysis, strength testing, and personalized recommendations.',
                                'duration' => 45,
                                'price' => 50.00,
                                'display_order' => 2
                            ]
                        ]
                    ],
                    [
                        'name' => 'Group Classes',
                        'description' => 'Fun and energetic group fitness classes',
                        'sort_order' => 2,
                        'services' => [
                            [
                                'name' => 'Yoga Class',
                                'description' => 'Relaxing yoga session for all levels',
                                'long_description' => 'Find balance and flexibility in our welcoming yoga classes. Suitable for beginners to advanced practitioners.',
                                'duration' => 60,
                                'price' => 25.00,
                                'display_order' => 3
                            ],
                            [
                                'name' => 'HIIT Training',
                                'description' => 'High-intensity interval training',
                                'long_description' => 'Burn calories and build strength with our dynamic HIIT classes. Maximum results in minimum time.',
                                'duration' => 45,
                                'price' => 30.00,
                                'display_order' => 4
                            ]
                        ]
                    ]
                ]
            ]
        ];

        return $services[$businessType] ?? ['categories' => []];
    }

    private function getStaffByType($businessType)
    {
        $staff = [
            'beauty_salon' => [
                ['name' => 'Jessica Martinez', 'position' => 'Senior Hair Stylist', 'bio' => 'Jessica has over 8 years of experience in hair styling and specializes in modern cuts and color techniques.', 'experience' => 8, 'sort_order' => 1],
                ['name' => 'Amanda Chen', 'position' => 'Esthetician', 'bio' => 'Amanda is our skincare specialist with expertise in facial treatments and anti-aging therapies.', 'experience' => 5, 'sort_order' => 2],
                ['name' => 'Rachel Thompson', 'position' => 'Hair Colorist', 'bio' => 'Rachel is passionate about creating beautiful color transformations and has trained with top colorists.', 'experience' => 6, 'sort_order' => 3]
            ],
            'dental_clinic' => [
                ['name' => 'Dr. Michael Chen', 'position' => 'Lead Dentist', 'bio' => 'Dr. Chen has been practicing dentistry for over 15 years and specializes in general and cosmetic dentistry.', 'experience' => 15, 'sort_order' => 1],
                ['name' => 'Dr. Sarah Williams', 'position' => 'Associate Dentist', 'bio' => 'Dr. Williams focuses on preventive care and has a gentle approach that puts patients at ease.', 'experience' => 8, 'sort_order' => 2],
                ['name' => 'Lisa Rodriguez', 'position' => 'Dental Hygienist', 'bio' => 'Lisa provides thorough cleanings and patient education to help maintain optimal oral health.', 'experience' => 10, 'sort_order' => 3]
            ],
            'fitness_center' => [
                ['name' => 'Maria Rodriguez', 'position' => 'Head Trainer', 'bio' => 'Maria is a certified personal trainer with expertise in strength training and weight management.', 'experience' => 7, 'sort_order' => 1],
                ['name' => 'Jake Thompson', 'position' => 'Fitness Coach', 'bio' => 'Jake specializes in HIIT training and helps clients achieve their fitness goals through dynamic workouts.', 'experience' => 4, 'sort_order' => 2],
                ['name' => 'Emma Davis', 'position' => 'Yoga Instructor', 'bio' => 'Emma brings peace and mindfulness to her yoga classes, suitable for all experience levels.', 'experience' => 6, 'sort_order' => 3]
            ]
        ];

        return $staff[$businessType] ?? [];
    }

    private function createLandingPageSections($landingPage, $ownerData)
    {
        // Use the model's generateDefaultSections method which now includes all sections
        $landingPage->generateDefaultSections();
    }

    private function generateBusinessSchema($business)
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $business->name,
            'description' => $business->description,
            'telephone' => $business->phone,
            'email' => $business->email,
            'url' => $business->website,
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => $business->address,
                'addressLocality' => $business->city,
                'addressRegion' => $business->state,
                'postalCode' => $business->postal_code,
                'addressCountry' => $business->country
            ],
            'openingHours' => [
                'Mo-Fr 09:00-18:00',
                'Sa 08:00-17:00'
            ]
        ];
    }
}
