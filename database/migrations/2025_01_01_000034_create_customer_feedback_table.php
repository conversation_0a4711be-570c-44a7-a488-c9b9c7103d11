<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_feedback', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('service_id')->nullable()->constrained()->onDelete('set null');
            
            $table->enum('type', ['review', 'complaint', 'suggestion', 'compliment', 'survey_response']);
            $table->integer('rating')->nullable(); // 1-5 stars
            $table->string('title')->nullable();
            $table->text('comment');
            $table->json('survey_responses')->nullable(); // For structured survey data
            $table->enum('status', ['pending', 'reviewed', 'responded', 'resolved'])->default('pending');
            $table->text('internal_notes')->nullable();
            $table->text('response')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->foreignId('responded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->boolean('is_public')->default(false);
            $table->boolean('is_featured')->default(false);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['business_id', 'customer_id']);
            $table->index(['business_id', 'type']);
            $table->index(['business_id', 'rating']);
            $table->index(['business_id', 'status']);
            $table->index(['business_id', 'is_public']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_feedback');
    }
};
