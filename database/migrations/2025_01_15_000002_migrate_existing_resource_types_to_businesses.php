<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only run if business_id column exists and there are existing resource types
        if (Schema::hasColumn('resource_types', 'business_id')) {
            $existingResourceTypes = DB::table('resource_types')->whereNull('business_id')->get();
            $businesses = DB::table('businesses')->get();
            
            if ($existingResourceTypes->isNotEmpty() && $businesses->isNotEmpty()) {
                // For each business, create copies of all existing global resource types
                foreach ($businesses as $business) {
                    foreach ($existingResourceTypes as $resourceType) {
                        // Check if this business already has this resource type
                        $exists = DB::table('resource_types')
                            ->where('business_id', $business->id)
                            ->where('slug', $resourceType->slug)
                            ->exists();
                            
                        if (!$exists) {
                            DB::table('resource_types')->insert([
                                'business_id' => $business->id,
                                'name' => $resourceType->name,
                                'slug' => $resourceType->slug,
                                'description' => $resourceType->description,
                                'icon' => $resourceType->icon,
                                'color' => $resourceType->color,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);
                        }
                    }
                }
                
                // Delete the original global resource types
                DB::table('resource_types')->whereNull('business_id')->delete();
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not easily reversible as it involves data transformation
        // If needed, you would need to manually restore the global resource types
    }
};
