<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_landing_pages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            
            // URL and Domain Configuration
            $table->string('custom_slug')->unique()->index();
            $table->string('custom_domain')->nullable()->unique();
            $table->enum('domain_type', ['subdirectory', 'subdomain', 'custom'])->default('subdirectory');
            $table->boolean('ssl_enabled')->default(true);
            
            // Page Configuration
            $table->string('page_title');
            $table->text('page_description')->nullable();
            $table->string('theme')->default('default');
            $table->json('theme_config')->nullable(); // Colors, fonts, layout settings
            $table->string('logo_url')->nullable();
            $table->json('branding_config')->nullable(); // Brand colors, typography
            
            // SEO Configuration
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->json('open_graph_config')->nullable(); // Social media sharing
            $table->json('schema_markup')->nullable(); // Structured data
            
            // Content Configuration
            $table->json('hero_section')->nullable(); // Hero banner content
            $table->json('about_section')->nullable(); // About business content
            $table->json('services_section')->nullable(); // Services showcase
            $table->json('contact_section')->nullable(); // Contact information
            $table->json('testimonials_section')->nullable(); // Customer reviews
            $table->json('gallery_section')->nullable(); // Image gallery
            $table->json('custom_sections')->nullable(); // Additional custom sections
            
            // Booking Integration
            $table->boolean('booking_enabled')->default(true);
            $table->json('booking_config')->nullable(); // Booking widget settings
            $table->string('booking_button_text')->default('Book Now');
            $table->string('booking_button_color')->default('#007bff');
            
            // Analytics and Tracking
            $table->string('google_analytics_id')->nullable();
            $table->string('facebook_pixel_id')->nullable();
            $table->json('tracking_codes')->nullable(); // Additional tracking scripts
            
            // Performance and Cache
            $table->boolean('cache_enabled')->default(true);
            $table->integer('cache_duration')->default(3600); // Cache duration in seconds
            $table->timestamp('last_generated_at')->nullable();
            
            // Status and Visibility
            $table->boolean('is_published')->default(false);
            $table->boolean('is_indexed')->default(true); // Search engine indexing
            $table->timestamp('published_at')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['business_id', 'is_published']);
            $table->index(['custom_slug', 'is_published']);
            $table->index(['domain_type', 'is_published']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_landing_pages');
    }
};
