<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_loyalty_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            
            // Points transaction details
            $table->enum('type', ['earned', 'redeemed', 'expired', 'adjusted', 'bonus']);
            $table->integer('points'); // Can be positive or negative
            $table->decimal('points_value', 10, 2)->nullable(); // Monetary value of points
            $table->text('description');
            
            // Reference tracking
            $table->string('reference_type')->nullable(); // booking, purchase, referral, etc.
            $table->unsignedBigInteger('reference_id')->nullable(); // ID of the referenced entity
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Expiration tracking
            $table->date('expires_at')->nullable();
            $table->boolean('is_expired')->default(false);
            
            // Balance tracking (for performance)
            $table->integer('running_balance')->default(0);
            
            $table->timestamps();

            // Add indexes for performance
            $table->index(['business_id', 'customer_id', 'created_at']);
            $table->index(['business_id', 'customer_id', 'type']);
            $table->index(['expires_at', 'is_expired']);
            $table->index(['reference_type', 'reference_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_loyalty_points');
    }
};
