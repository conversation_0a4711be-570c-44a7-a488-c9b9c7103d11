<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('resource_types', function (Blueprint $table) {
            // Add business_id column if it doesn't exist
            if (!Schema::hasColumn('resource_types', 'business_id')) {
                $table->foreignId('business_id')->nullable()->after('id')->constrained()->onDelete('cascade');
                
                // Remove the unique constraint on slug
                $table->dropUnique(['slug']);
                
                // Add new unique constraint for business_id + slug
                $table->unique(['business_id', 'slug']);
                
                // Add index for business_id + name
                $table->index(['business_id', 'name']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('resource_types', function (Blueprint $table) {
            if (Schema::hasColumn('resource_types', 'business_id')) {
                // Drop the new constraints
                $table->dropUnique(['business_id', 'slug']);
                $table->dropIndex(['business_id', 'name']);
                
                // Restore original unique constraint on slug
                $table->unique('slug');
                
                // Drop the business_id column
                $table->dropForeign(['business_id']);
                $table->dropColumn('business_id');
            }
        });
    }
};
