<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('booking_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_price', 10, 2);
            $table->integer('duration_minutes');
            $table->datetime('start_datetime');
            $table->datetime('end_datetime');
            $table->json('service_data')->nullable(); // Snapshot of service at booking time
            $table->timestamps();

            $table->index(['booking_id', 'service_id']);
            $table->index(['service_id', 'start_datetime']);
        });

        Schema::create('booking_service_resources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_service_id')->constrained()->onDelete('cascade');
            $table->foreignId('resource_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->default(1);
            $table->datetime('start_datetime');
            $table->datetime('end_datetime');
            $table->timestamps();

            $table->unique(['booking_service_id', 'resource_id'], 'booking_service_resource_unique');
            $table->index(['resource_id', 'start_datetime', 'end_datetime'], 'booking_resource_time_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('booking_service_resources');
        Schema::dropIfExists('booking_services');
    }
};
