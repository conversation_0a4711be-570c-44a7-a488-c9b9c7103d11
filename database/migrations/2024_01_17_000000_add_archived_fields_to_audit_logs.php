<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add archival fields to role_audit_logs table
        Schema::table('role_audit_logs', function (Blueprint $table) {
            $table->timestamp('archived_at')->nullable()->after('updated_at');
            $table->string('archive_file')->nullable()->after('archived_at');
            
            // Add index for archived data
            $table->index('archived_at');
        });

        // Add archival fields to security_alerts table if it exists
        if (Schema::hasTable('security_alerts')) {
            Schema::table('security_alerts', function (Blueprint $table) {
                $table->timestamp('archived_at')->nullable()->after('updated_at');
                $table->string('archive_file')->nullable()->after('archived_at');
                
                // Add index for archived data
                $table->index('archived_at');
            });
        }

        // Create security_logs table for additional logging
        Schema::create('security_logs', function (Blueprint $table) {
            $table->id();
            
            // Log classification
            $table->string('level', 20); // info, warning, error, critical
            $table->string('category', 50); // authentication, authorization, data_access, etc.
            $table->string('event_type', 100);
            
            // User and session info
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('session_id')->nullable();
            
            // Request details
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->string('request_method', 10)->nullable();
            $table->text('request_url')->nullable();
            
            // Event details
            $table->text('message');
            $table->json('context')->nullable(); // Additional context data
            $table->json('metadata')->nullable(); // System metadata
            
            // Timing
            $table->timestamp('event_timestamp')->default(now());
            $table->timestamps();
            
            // Indexes for performance
            $table->index('level');
            $table->index('category');
            $table->index('event_type');
            $table->index('user_id');
            $table->index('ip_address');
            $table->index('event_timestamp');
            $table->index('created_at');
            $table->index(['level', 'category']);
            $table->index(['user_id', 'event_timestamp']);
            
            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });

        // Create security_metrics table for performance tracking
        Schema::create('security_metrics', function (Blueprint $table) {
            $table->id();
            
            // Metric identification
            $table->string('metric_name', 100);
            $table->string('metric_type', 50); // counter, gauge, histogram
            $table->string('category', 50); // security, performance, compliance
            
            // Metric value
            $table->decimal('value', 15, 4);
            $table->string('unit', 20)->nullable(); // count, percentage, seconds, etc.
            
            // Dimensions
            $table->json('dimensions')->nullable(); // Additional metric dimensions
            $table->json('tags')->nullable(); // Metric tags for filtering
            
            // Timing
            $table->timestamp('recorded_at')->default(now());
            $table->timestamps();
            
            // Indexes for performance
            $table->index('metric_name');
            $table->index('metric_type');
            $table->index('category');
            $table->index('recorded_at');
            $table->index(['metric_name', 'recorded_at']);
            $table->index(['category', 'recorded_at']);
        });

        // Create compliance_reports table for storing generated reports
        Schema::create('compliance_reports', function (Blueprint $table) {
            $table->id();
            
            // Report identification
            $table->string('report_type', 100); // security_audit, compliance_check, risk_assessment
            $table->string('report_name');
            $table->string('report_format', 20); // pdf, csv, json
            
            // Report period
            $table->date('period_start');
            $table->date('period_end');
            
            // Report metadata
            $table->json('parameters')->nullable(); // Report generation parameters
            $table->json('summary')->nullable(); // Executive summary data
            $table->decimal('compliance_score', 5, 2)->nullable(); // Overall compliance score
            $table->string('risk_level', 20)->nullable(); // low, medium, high, critical
            
            // File storage
            $table->string('file_path')->nullable(); // Path to stored report file
            $table->bigInteger('file_size')->nullable(); // File size in bytes
            $table->string('file_hash')->nullable(); // File integrity hash
            
            // Generation info
            $table->unsignedBigInteger('generated_by');
            $table->timestamp('generated_at')->default(now());
            $table->integer('generation_time_ms')->nullable(); // Generation time in milliseconds
            
            // Access tracking
            $table->integer('download_count')->default(0);
            $table->timestamp('last_accessed_at')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index('report_type');
            $table->index('report_format');
            $table->index('period_start');
            $table->index('period_end');
            $table->index('generated_by');
            $table->index('generated_at');
            $table->index(['report_type', 'generated_at']);
            $table->index(['period_start', 'period_end']);
            
            // Foreign key constraint
            $table->foreign('generated_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop new tables
        Schema::dropIfExists('compliance_reports');
        Schema::dropIfExists('security_metrics');
        Schema::dropIfExists('security_logs');
        
        // Remove archival fields from existing tables
        if (Schema::hasTable('security_alerts')) {
            Schema::table('security_alerts', function (Blueprint $table) {
                $table->dropIndex(['archived_at']);
                $table->dropColumn(['archived_at', 'archive_file']);
            });
        }
        
        Schema::table('role_audit_logs', function (Blueprint $table) {
            $table->dropIndex(['archived_at']);
            $table->dropColumn(['archived_at', 'archive_file']);
        });
    }
};
