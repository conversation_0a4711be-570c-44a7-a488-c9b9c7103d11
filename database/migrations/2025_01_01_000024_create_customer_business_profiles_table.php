<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_business_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            
            // Customer status within this business
            $table->enum('status', ['active', 'inactive', 'vip', 'blocked', 'prospect'])->default('active');
            $table->date('customer_since')->useCurrent();
            $table->date('last_visit_date')->nullable();
            $table->integer('total_visits')->default(0);
            $table->decimal('total_spent', 12, 2)->default(0);
            $table->decimal('average_order_value', 10, 2)->default(0);
            
            // Business-specific preferences
            $table->json('preferences')->nullable(); // Preferred services, times, staff, etc.
            $table->json('communication_preferences')->nullable(); // Email, SMS, push notifications
            $table->text('notes')->nullable(); // Internal notes about the customer
            $table->text('special_requirements')->nullable(); // Allergies, accessibility needs, etc.
            
            // Loyalty and engagement
            $table->integer('loyalty_points_balance')->default(0);
            $table->enum('loyalty_tier', ['bronze', 'silver', 'gold', 'platinum', 'diamond'])->default('bronze');
            $table->decimal('lifetime_value', 12, 2)->default(0);
            $table->integer('referrals_made')->default(0);
            $table->integer('no_show_count')->default(0);
            $table->integer('cancellation_count')->default(0);
            
            // Marketing and segmentation
            $table->boolean('marketing_consent')->default(false);
            $table->timestamp('marketing_consent_date')->nullable();
            $table->json('custom_fields')->nullable(); // Business-specific custom fields
            
            $table->timestamps();

            // Ensure unique customer profile per business
            $table->unique(['business_id', 'customer_id']);
            
            // Add indexes for performance
            $table->index(['business_id', 'status']);
            $table->index(['business_id', 'loyalty_tier']);
            $table->index(['business_id', 'total_spent']);
            $table->index(['business_id', 'last_visit_date']);
            $table->index(['customer_since']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_business_profiles');
    }
};
