<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Service Display Configuration for Landing Pages
            $table->boolean('is_public')->default(true)->after('is_active');
            $table->boolean('featured_on_landing')->default(false)->after('is_public');
            $table->integer('landing_display_order')->nullable()->after('featured_on_landing');
            $table->json('landing_display_config')->nullable()->after('landing_display_order');

            // Service Presentation Settings
            $table->boolean('show_price_on_landing')->default(true)->after('landing_display_config');
            $table->boolean('show_duration_on_landing')->default(true)->after('show_price_on_landing');
            $table->boolean('show_description_on_landing')->default(true)->after('show_duration_on_landing');
            $table->boolean('show_image_on_landing')->default(true)->after('show_description_on_landing');

            // Service SEO for Landing Pages
            $table->string('landing_page_title')->nullable()->after('show_image_on_landing');
            $table->text('landing_page_description')->nullable()->after('landing_page_title');
            $table->text('landing_page_keywords')->nullable()->after('landing_page_description');

            // Service Booking Integration
            $table->boolean('quick_booking_enabled')->default(true)->after('landing_page_keywords');
            $table->string('booking_button_text')->nullable()->after('quick_booking_enabled');
            $table->string('booking_button_color')->nullable()->after('booking_button_text');

            // Service Analytics
            $table->integer('landing_page_views')->default(0)->after('booking_button_color');
            $table->integer('landing_page_clicks')->default(0)->after('landing_page_views');
            $table->timestamp('last_landing_view')->nullable()->after('landing_page_clicks');

            // Add indexes for performance
            $table->index(['business_id', 'is_public', 'is_active'], 'services_business_public_active_idx');
            $table->index(['business_id', 'featured_on_landing', 'landing_display_order'], 'services_business_featured_order_idx');
            $table->index(['is_public', 'featured_on_landing'], 'services_public_featured_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropIndex('services_business_public_active_idx');
            $table->dropIndex('services_business_featured_order_idx');
            $table->dropIndex('services_public_featured_idx');

            $table->dropColumn([
                'is_public',
                'featured_on_landing',
                'landing_display_order',
                'landing_display_config',
                'show_price_on_landing',
                'show_duration_on_landing',
                'show_description_on_landing',
                'show_image_on_landing',
                'landing_page_title',
                'landing_page_description',
                'landing_page_keywords',
                'quick_booking_enabled',
                'booking_button_text',
                'booking_button_color',
                'landing_page_views',
                'landing_page_clicks',
                'last_landing_view',
            ]);
        });
    }
};
