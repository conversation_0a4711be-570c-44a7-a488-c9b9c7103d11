<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_landing_page_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_landing_page_id')->constrained()->onDelete('cascade');

            // Section Configuration
            $table->string('section_type'); // hero, about, services, contact, testimonials, gallery, custom
            $table->string('section_name');
            $table->text('section_description')->nullable();

            // Content and Layout
            $table->json('content_data'); // Section-specific content
            $table->json('layout_config')->nullable(); // Layout settings
            $table->json('style_config')->nullable(); // Custom styling

            // Visibility and Ordering
            $table->boolean('is_visible')->default(true);
            $table->integer('sort_order')->default(0);

            // Responsive Configuration
            $table->json('mobile_config')->nullable(); // Mobile-specific settings
            $table->json('tablet_config')->nullable(); // Tablet-specific settings

            // Animation and Effects
            $table->json('animation_config')->nullable(); // Animation settings
            $table->boolean('parallax_enabled')->default(false);

            $table->timestamps();

            // Indexes with shorter names
            $table->index(['business_landing_page_id', 'section_type'], 'blp_sections_page_type_idx');
            $table->index(['business_landing_page_id', 'is_visible', 'sort_order'], 'blp_sections_page_visible_order_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_landing_page_sections');
    }
};
