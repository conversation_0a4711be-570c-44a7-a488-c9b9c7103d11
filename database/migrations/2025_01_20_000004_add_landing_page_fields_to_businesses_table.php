<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            // Landing Page Configuration
            $table->boolean('landing_page_enabled')->default(true)->after('online_booking_enabled');
            $table->string('landing_page_slug')->nullable()->unique()->after('slug');
            $table->enum('landing_page_status', ['draft', 'published', 'maintenance'])->default('draft')->after('landing_page_slug');
            
            // Domain Configuration
            $table->string('custom_domain')->nullable()->after('landing_page_status');
            $table->enum('domain_type', ['subdirectory', 'subdomain', 'custom'])->default('subdirectory')->after('custom_domain');
            $table->boolean('ssl_enabled')->default(true)->after('domain_type');
            
            // Landing Page Theme and Branding
            $table->string('landing_page_theme')->default('default')->after('ssl_enabled');
            $table->json('landing_page_config')->nullable()->after('landing_page_theme');
            
            // SEO and Analytics
            $table->boolean('seo_optimized')->default(false)->after('landing_page_config');
            $table->timestamp('landing_page_last_updated')->nullable()->after('seo_optimized');
            
            // Add indexes for performance
            $table->index(['landing_page_slug', 'landing_page_status']);
            $table->index(['domain_type', 'landing_page_enabled']);
            $table->index(['owner_id', 'landing_page_enabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['landing_page_slug', 'landing_page_status']);
            $table->dropIndex(['domain_type', 'landing_page_enabled']);
            $table->dropIndex(['owner_id', 'landing_page_enabled']);
            
            // Drop columns
            $table->dropColumn([
                'landing_page_enabled',
                'landing_page_slug',
                'landing_page_status',
                'custom_domain',
                'domain_type',
                'ssl_enabled',
                'landing_page_theme',
                'landing_page_config',
                'seo_optimized',
                'landing_page_last_updated'
            ]);
        });
    }
};
