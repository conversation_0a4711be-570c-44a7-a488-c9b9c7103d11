<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('businesses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('website')->nullable();
            $table->string('logo')->nullable();
            $table->json('branding')->nullable(); // colors, themes, etc.
            $table->string('timezone')->default('UTC');
            $table->string('currency', 3)->default('USD');
            $table->string('language', 2)->default('en');
            $table->boolean('multi_branch')->default(false);
            $table->boolean('online_booking_enabled')->default(true);
            $table->integer('booking_advance_days')->default(30);
            $table->integer('booking_advance_hours')->default(2);
            $table->integer('cancellation_hours')->default(24);
            $table->json('business_rules')->nullable();
            $table->boolean('is_active')->default(true);
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            
            $table->index(['slug', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('businesses');
    }
};
