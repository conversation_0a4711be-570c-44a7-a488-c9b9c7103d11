<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_seo_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            
            // Basic SEO
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('canonical_url')->nullable();
            
            // Open Graph (Social Media)
            $table->string('og_title')->nullable();
            $table->text('og_description')->nullable();
            $table->string('og_image')->nullable();
            $table->string('og_type')->default('business.business');
            $table->string('og_locale')->default('en_US');
            
            // Twitter Cards
            $table->string('twitter_card')->default('summary_large_image');
            $table->string('twitter_site')->nullable();
            $table->string('twitter_creator')->nullable();
            $table->string('twitter_title')->nullable();
            $table->text('twitter_description')->nullable();
            $table->string('twitter_image')->nullable();
            
            // Schema.org Structured Data
            $table->json('business_schema')->nullable(); // LocalBusiness schema
            $table->json('service_schema')->nullable(); // Service schema
            $table->json('review_schema')->nullable(); // Review schema
            $table->json('faq_schema')->nullable(); // FAQ schema
            $table->json('custom_schema')->nullable(); // Additional schemas
            
            // Local SEO
            $table->string('business_type')->nullable(); // Restaurant, Salon, etc.
            $table->json('opening_hours_schema')->nullable(); // Structured opening hours
            $table->string('price_range')->nullable(); // $, $$, $$$, $$$$
            $table->json('geo_coordinates')->nullable(); // Latitude, longitude
            $table->string('google_my_business_id')->nullable();
            
            // Technical SEO
            $table->json('robots_meta')->nullable(); // Robots directives
            $table->boolean('sitemap_enabled')->default(true);
            $table->timestamp('sitemap_last_generated')->nullable();
            $table->json('hreflang_tags')->nullable(); // Multi-language support
            
            // Analytics and Tracking
            $table->string('google_analytics_id')->nullable();
            $table->string('google_tag_manager_id')->nullable();
            $table->string('facebook_pixel_id')->nullable();
            $table->json('custom_tracking_codes')->nullable();
            
            // Performance
            $table->boolean('amp_enabled')->default(false);
            $table->json('critical_css')->nullable();
            $table->boolean('lazy_loading_enabled')->default(true);
            
            $table->timestamps();
            
            // Ensure one SEO setting per business
            $table->unique('business_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_seo_settings');
    }
};
