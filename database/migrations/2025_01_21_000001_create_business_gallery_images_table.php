<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_gallery_images', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained('business_gallery_categories')->onDelete('set null');

            // File Information
            $table->string('filename');
            $table->string('original_name');
            $table->string('path');
            $table->string('url')->nullable();
            $table->string('mime_type');
            $table->integer('file_size')->comment('File size in bytes');
            $table->integer('width')->nullable();
            $table->integer('height')->nullable();

            // Image Metadata
            $table->string('title')->nullable();
            $table->string('alt_text')->nullable();
            $table->text('description')->nullable();
            $table->json('tags')->nullable();

            // Display Settings
            $table->boolean('is_featured')->default(false)->comment('Featured in galleries');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);

            // Additional Metadata
            $table->json('metadata')->nullable()->comment('Additional image metadata');
            $table->json('exif_data')->nullable()->comment('EXIF data from image');

            // Timestamps
            $table->timestamp('uploaded_at')->useCurrent();
            $table->timestamps();

            // Indexes for performance
            $table->index(['business_id', 'is_active', 'sort_order'], 'gallery_img_business_active_sort_idx');
            $table->index(['business_id', 'is_featured'], 'gallery_img_business_featured_idx');
            $table->index(['business_id', 'category_id'], 'gallery_img_business_category_idx');
            $table->index('uploaded_at', 'gallery_img_uploaded_at_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_gallery_images');
    }
};
