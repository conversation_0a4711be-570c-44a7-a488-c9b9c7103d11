<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_gallery_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');

            // Category Information
            $table->string('name');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->string('color')->nullable()->comment('Hex color for category display');

            // Display Settings
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);

            // Timestamps
            $table->timestamps();

            // Indexes for performance
            $table->index(['business_id', 'is_active', 'sort_order'], 'gallery_cat_business_active_sort_idx');
            $table->unique(['business_id', 'slug'], 'gallery_cat_business_slug_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_gallery_categories');
    }
};
