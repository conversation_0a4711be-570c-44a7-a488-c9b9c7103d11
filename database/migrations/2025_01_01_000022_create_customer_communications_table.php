<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_communications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('sent_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Communication details
            $table->enum('type', ['email', 'sms', 'push', 'in_app', 'phone_call', 'note']);
            $table->enum('direction', ['outbound', 'inbound']);
            $table->string('subject')->nullable();
            $table->text('message');
            $table->json('metadata')->nullable(); // Store additional data like email headers, SMS provider info, etc.
            
            // Status tracking
            $table->enum('status', ['pending', 'sent', 'delivered', 'read', 'failed', 'bounced'])->default('pending');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->text('failure_reason')->nullable();
            
            // Campaign tracking
            $table->string('campaign_id')->nullable();
            $table->string('template_id')->nullable();
            
            $table->timestamps();

            // Add indexes for performance
            $table->index(['business_id', 'customer_id', 'created_at']);
            $table->index(['business_id', 'type', 'status']);
            $table->index(['campaign_id']);
            $table->index(['sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_communications');
    }
};
