<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->string('group')->default('general');
            $table->text('value')->nullable();
            $table->string('display_name');
            $table->string('type')->default('text'); // text, textarea, select, checkbox, file, etc.
            $table->text('options')->nullable(); // For select, radio, checkbox options as JSON
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false);
            $table->integer('order')->default(0);
            $table->timestamps();
        });

        // Insert default settings
        DB::table('settings')->insert([
            [
                'key' => 'app_name',
                'group' => 'general',
                'value' => config('app.name'),
                'display_name' => 'Application Name',
                'type' => 'text',
                'options' => null,
                'description' => 'The name of the application',
                'is_public' => true,
                'order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'company_email',
                'group' => 'contact',
                'value' => '<EMAIL>',
                'display_name' => 'Company Email',
                'type' => 'text',
                'options' => null,
                'description' => 'Primary contact email',
                'is_public' => true,
                'order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'company_phone',
                'group' => 'contact',
                'value' => '+1234567890',
                'display_name' => 'Company Phone',
                'type' => 'text',
                'options' => null,
                'description' => 'Primary contact phone number',
                'is_public' => true,
                'order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'app_logo',
                'group' => 'appearance',
                'value' => null,
                'display_name' => 'Application Logo',
                'type' => 'file',
                'options' => null,
                'description' => 'Logo of the application',
                'is_public' => true,
                'order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'app_footer',
                'group' => 'appearance',
                'value' => 'Copyright © ' . date('Y') . ' Bookkei. All rights reserved.',
                'display_name' => 'Footer Text',
                'type' => 'text',
                'options' => null,
                'description' => 'Text displayed in the footer',
                'is_public' => true,
                'order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'mail_from_address',
                'group' => 'mail',
                'value' => '<EMAIL>',
                'display_name' => 'Mail From Address',
                'type' => 'text',
                'options' => null,
                'description' => 'Email address used for sending system emails',
                'is_public' => false,
                'order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'mail_from_name',
                'group' => 'mail',
                'value' => config('app.name'),
                'display_name' => 'Mail From Name',
                'type' => 'text',
                'options' => null,
                'description' => 'Name used for sending system emails',
                'is_public' => false,
                'order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'allow_registration',
                'group' => 'security',
                'value' => '1',
                'display_name' => 'Allow Registration',
                'type' => 'checkbox',
                'options' => null,
                'description' => 'Allow new users to register',
                'is_public' => false,
                'order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'default_role',
                'group' => 'security',
                'value' => 'customer',
                'display_name' => 'Default User Role',
                'type' => 'select',
                'options' => json_encode(['customer' => 'Customer', 'staff' => 'Staff']),
                'description' => 'Default role assigned to new users',
                'is_public' => false,
                'order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
