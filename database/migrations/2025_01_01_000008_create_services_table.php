<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->string('icon')->nullable();
            $table->string('color', 7)->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['business_id', 'slug']);
        });

        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_category_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            $table->integer('duration_minutes');
            $table->decimal('base_price', 10, 2);
            $table->decimal('deposit_amount', 10, 2)->nullable();
            $table->boolean('deposit_required')->default(false);
            $table->integer('buffer_time_before')->default(0); // minutes
            $table->integer('buffer_time_after')->default(0); // minutes
            $table->integer('max_advance_booking_days')->nullable();
            $table->integer('min_advance_booking_hours')->nullable();
            $table->integer('max_participants')->default(1);
            $table->json('pricing_variables')->nullable(); // Dynamic pricing rules
            $table->json('booking_rules')->nullable();
            $table->boolean('online_booking_enabled')->default(true);
            $table->boolean('requires_approval')->default(false);
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['business_id', 'slug']);
            $table->index(['business_id', 'service_category_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
        Schema::dropIfExists('service_categories');
    }
};
