<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('landing_page_analytics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('landing_page_id')->nullable()->constrained('business_landing_pages')->onDelete('cascade');
            $table->foreignId('service_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('event_type'); // page_view, service_view, booking_attempt, booking_success, contact_form, etc.
            $table->string('page')->nullable(); // home, services, about, contact, etc.
            $table->text('user_agent')->nullable();
            $table->string('ip_address')->nullable(); // hashed for privacy
            $table->string('session_id')->nullable();
            $table->string('referrer')->nullable();
            $table->json('metadata')->nullable(); // additional event data
            $table->timestamps();

            // Indexes for performance
            $table->index(['business_id', 'event_type', 'created_at']);
            $table->index(['business_id', 'service_id', 'created_at']);
            $table->index(['session_id', 'created_at']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('landing_page_analytics');
    }
};
