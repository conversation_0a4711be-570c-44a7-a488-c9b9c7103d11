<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            // Add hierarchy and security fields
            $table->integer('hierarchy_level')->default(5)->after('guard_name');
            $table->text('description')->nullable()->after('hierarchy_level');
            $table->boolean('is_system_role')->default(false)->after('description');
            $table->integer('max_users')->nullable()->after('is_system_role');
            $table->integer('security_level')->default(2)->after('max_users');
            
            // Add encrypted permissions for sensitive data
            $table->json('encrypted_permissions')->nullable()->after('security_level');
            
            // Add audit fields
            $table->unsignedBigInteger('created_by')->nullable()->after('encrypted_permissions');
            $table->unsignedBigInteger('updated_by')->nullable()->after('created_by');
            
            // Add indexes for performance
            $table->index('hierarchy_level');
            $table->index('security_level');
            $table->index('is_system_role');
            
            // Add foreign key constraints
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['created_by']);
            $table->dropForeign(['updated_by']);
            
            // Drop indexes
            $table->dropIndex(['hierarchy_level']);
            $table->dropIndex(['security_level']);
            $table->dropIndex(['is_system_role']);
            
            // Drop columns
            $table->dropColumn([
                'hierarchy_level',
                'description',
                'is_system_role',
                'max_users',
                'security_level',
                'encrypted_permissions',
                'created_by',
                'updated_by',
            ]);
        });
    }
};
