<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('security_alerts', function (Blueprint $table) {
            $table->id();
            
            // Alert classification
            $table->string('type', 50); // privilege_escalation, brute_force, etc.
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('status', ['open', 'investigating', 'resolved', 'false_positive', 'ignored'])->default('open');
            
            // Associated user (if applicable)
            $table->unsignedBigInteger('user_id')->nullable();
            
            // Alert details
            $table->string('message');
            $table->json('details')->nullable(); // Additional context data
            
            // Request information
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamp('timestamp')->default(now());
            
            // Resolution tracking
            $table->unsignedBigInteger('resolved_by')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->text('resolution_notes')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index('type');
            $table->index('severity');
            $table->index('status');
            $table->index('user_id');
            $table->index('ip_address');
            $table->index('timestamp');
            $table->index('created_at');
            $table->index(['type', 'severity']);
            $table->index(['status', 'severity']);
            $table->index(['user_id', 'created_at']);
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('resolved_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('security_alerts');
    }
};
