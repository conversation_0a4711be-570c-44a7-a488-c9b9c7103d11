<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('landing_service_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');

            // Homepage Service Display Settings
            $table->integer('homepage_display_count')->default(6);
            $table->boolean('show_pricing')->default(true);
            $table->boolean('show_duration')->default(true);
            $table->boolean('show_description')->default(true);
            $table->boolean('show_images')->default(true);
            $table->boolean('show_categories')->default(true);

            // Layout Configuration
            $table->enum('layout_type', ['grid', 'list', 'carousel', 'masonry'])->default('grid');
            $table->integer('grid_columns')->default(3);
            $table->boolean('enable_filtering')->default(true);
            $table->boolean('enable_sorting')->default(true);
            $table->boolean('enable_search')->default(true);

            // Service Display Options
            $table->json('featured_services')->nullable(); // Array of service IDs
            $table->json('hidden_services')->nullable(); // Array of service IDs to hide
            $table->enum('service_order', ['manual', 'alphabetical', 'price_low_high', 'price_high_low', 'duration', 'popularity'])->default('manual');
            $table->boolean('group_by_category')->default(true);

            // Presentation Settings
            $table->string('service_card_style')->default('modern'); // modern, classic, minimal, creative
            $table->boolean('show_service_icons')->default(true);
            $table->boolean('show_availability_status')->default(true);
            $table->boolean('show_special_offers')->default(true);
            $table->boolean('show_reviews_rating')->default(true);

            // Booking Integration
            $table->boolean('enable_quick_booking')->default(true);
            $table->string('booking_button_style')->default('primary'); // primary, secondary, outline
            $table->string('booking_button_text')->default('Book Now');
            $table->boolean('show_booking_calendar')->default(false);

            // SEO and Analytics
            $table->boolean('enable_service_seo')->default(true);
            $table->boolean('generate_service_sitemap')->default(true);
            $table->boolean('track_service_analytics')->default(true);
            $table->json('seo_config')->nullable();

            // Mobile and Responsive Settings
            $table->json('mobile_config')->nullable();
            $table->json('tablet_config')->nullable();
            $table->boolean('mobile_optimized')->default(true);

            $table->timestamps();

            // Indexes
            $table->index(['business_id', 'layout_type']);
            $table->index(['business_id', 'enable_quick_booking']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('landing_service_settings');
    }
};
