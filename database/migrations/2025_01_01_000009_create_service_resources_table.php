<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_resources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->foreignId('resource_id')->constrained()->onDelete('cascade');
            $table->integer('quantity_required')->default(1);
            $table->boolean('is_required')->default(true);
            $table->integer('setup_time_minutes')->default(0);
            $table->integer('cleanup_time_minutes')->default(0);
            $table->timestamps();
            
            $table->unique(['service_id', 'resource_id']);
        });

        Schema::create('service_addons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->foreignId('addon_service_id')->constrained('services')->onDelete('cascade');
            $table->decimal('addon_price', 10, 2)->nullable(); // Override addon service price
            $table->boolean('is_required')->default(false);
            $table->integer('max_quantity')->default(1);
            $table->timestamps();
            
            $table->unique(['service_id', 'addon_service_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_addons');
        Schema::dropIfExists('service_resources');
    }
};
