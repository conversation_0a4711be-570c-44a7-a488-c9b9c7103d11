<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('business_landing_pages', function (Blueprint $table) {
            $table->json('theme_customization')->nullable()->after('theme_config');
            $table->longText('custom_css')->nullable()->after('theme_customization');
            $table->timestamp('last_customized_at')->nullable()->after('custom_css');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('business_landing_pages', function (Blueprint $table) {
            $table->dropColumn(['theme_customization', 'custom_css', 'last_customized_at']);
        });
    }
};
