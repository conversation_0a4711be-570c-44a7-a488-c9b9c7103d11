<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_tags', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug');
            $table->string('color', 7)->nullable(); // hex color
            $table->text('description')->nullable();
            $table->timestamps();

            $table->unique('slug');
        });

        Schema::create('business_tag_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('business_tag_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['business_id', 'business_tag_id'], 'business_tag_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_tag_assignments');
        Schema::dropIfExists('business_tags');
    }
};
