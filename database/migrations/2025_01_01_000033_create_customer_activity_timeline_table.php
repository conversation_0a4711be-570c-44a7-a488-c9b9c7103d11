<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_activity_timeline', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            
            $table->enum('activity_type', [
                'booking_created', 'booking_modified', 'booking_cancelled', 'booking_completed',
                'communication_sent', 'communication_received', 'points_awarded', 'points_redeemed',
                'tag_assigned', 'tag_removed', 'profile_updated', 'note_added', 'status_changed',
                'referral_made', 'feedback_received', 'campaign_sent', 'login', 'registration'
            ]);
            
            $table->string('title');
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Store additional data like booking_id, points, etc.
            $table->string('icon')->nullable();
            $table->string('color')->default('#6c757d');
            $table->boolean('is_important')->default(false);
            $table->boolean('is_system_generated')->default(true);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['business_id', 'customer_id', 'created_at']);
            $table->index(['business_id', 'activity_type']);
            $table->index(['business_id', 'is_important']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_activity_timeline');
    }
};
