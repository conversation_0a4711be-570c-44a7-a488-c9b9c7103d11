<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('rating')->unsigned()->comment('Rating from 1 to 5');
            $table->string('title')->nullable();
            $table->text('comment')->nullable();
            $table->json('rating_breakdown')->nullable()->comment('Detailed ratings for different aspects');
            $table->boolean('is_verified')->default(false)->comment('Whether this review is from a verified booking');
            $table->boolean('is_approved')->default(true)->comment('Whether this review is approved for display');
            $table->boolean('is_featured')->default(false)->comment('Whether this review is featured');
            $table->timestamp('reviewed_at')->useCurrent();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();

            // Ensure a user can only review a service once per booking
            $table->unique(['user_id', 'service_id', 'booking_id']);

            // Add indexes for performance
            $table->index(['service_id', 'is_approved', 'rating']);
            $table->index(['user_id', 'reviewed_at']);
            $table->index(['is_featured', 'rating']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_reviews');
    }
};
