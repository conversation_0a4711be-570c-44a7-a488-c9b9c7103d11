<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_availability', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->date('date');
            $table->time('start_time');
            $table->time('end_time');
            $table->integer('max_bookings')->default(1)->comment('Maximum concurrent bookings for this slot');
            $table->integer('current_bookings')->default(0)->comment('Current number of bookings');
            $table->boolean('is_available')->default(true);
            $table->string('unavailable_reason')->nullable();
            $table->json('staff_ids')->nullable()->comment('Specific staff members available for this slot');
            $table->json('resource_ids')->nullable()->comment('Resources allocated for this slot');
            $table->decimal('price_override', 10, 2)->nullable()->comment('Override price for this specific slot');
            $table->text('notes')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();

            // Ensure no overlapping slots for the same service
            $table->unique(['service_id', 'date', 'start_time']);

            // Add indexes for performance
            $table->index(['service_id', 'date', 'is_available']);
            $table->index(['business_id', 'date']);
            $table->index(['date', 'start_time', 'end_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_availability');
    }
};
