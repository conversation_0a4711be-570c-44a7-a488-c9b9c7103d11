<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('two_factor_settings', function (Blueprint $table) {
            $table->id();
            $table->string('setting_key')->unique();
            $table->string('setting_value');
            $table->string('description')->nullable();
            $table->string('category')->default('security');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Insert default 2FA settings
        DB::table('two_factor_settings')->insert([
            [
                'setting_key' => '2fa_enabled',
                'setting_value' => 'false',
                'description' => 'Enable or disable two-factor authentication globally',
                'category' => 'security',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => '2fa_required_for_roles',
                'setting_value' => 'false',
                'description' => 'Require 2FA for role management operations',
                'category' => 'security',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => '2fa_required_for_super_admin',
                'setting_value' => 'true',
                'description' => 'Always require 2FA for Super Admin operations',
                'category' => 'security',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => '2fa_session_duration',
                'setting_value' => '30',
                'description' => '2FA verification validity duration in minutes',
                'category' => 'security',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => '2fa_code_expiry',
                'setting_value' => '10',
                'description' => '2FA code expiry time in minutes',
                'category' => 'security',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('two_factor_settings');
    }
};
