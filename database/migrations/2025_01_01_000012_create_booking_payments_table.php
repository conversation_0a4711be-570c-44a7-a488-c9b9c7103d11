<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('booking_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->string('payment_reference')->unique();
            $table->enum('payment_type', ['deposit', 'partial', 'full', 'refund']);
            $table->enum('payment_method', ['cash', 'card', 'bank_transfer', 'online', 'other']);
            $table->decimal('amount', 10, 2);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->datetime('processed_at')->nullable();
            $table->string('gateway')->nullable(); // Payment gateway used
            $table->string('gateway_transaction_id')->nullable();
            $table->json('gateway_response')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['booking_id', 'payment_type']);
            $table->index(['payment_reference']);
            $table->index(['status', 'processed_at']);
        });

        Schema::create('availability_blocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('business_branch_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('resource_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->datetime('start_datetime');
            $table->datetime('end_datetime');
            $table->enum('block_type', ['maintenance', 'holiday', 'private_event', 'staff_break', 'other']);
            $table->boolean('affects_all_resources')->default(false);
            $table->json('affected_services')->nullable(); // Array of service IDs
            $table->boolean('is_recurring')->default(false);
            $table->json('recurrence_data')->nullable();
            $table->timestamps();

            $table->index(['business_id', 'start_datetime', 'end_datetime'], 'availability_business_time_idx');
            $table->index(['resource_id', 'start_datetime', 'end_datetime'], 'availability_resource_time_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('availability_blocks');
        Schema::dropIfExists('booking_payments');
    }
};
