<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waiting_lists', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained('users')->onDelete('cascade');
            
            // Customer information (for guest entries)
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone')->nullable();
            
            // Preferences
            $table->date('preferred_date');
            $table->time('preferred_time_start')->nullable();
            $table->time('preferred_time_end')->nullable();
            $table->json('preferred_days_of_week')->nullable(); // Array of day numbers
            $table->integer('participant_count')->default(1);
            $table->text('notes')->nullable();
            
            // Status
            $table->enum('status', ['active', 'notified', 'booked', 'expired', 'cancelled'])->default('active');
            $table->datetime('notified_at')->nullable();
            $table->datetime('expires_at')->nullable();
            $table->integer('priority')->default(0); // Higher number = higher priority
            
            $table->timestamps();
            
            $table->index(['business_id', 'service_id', 'status']);
            $table->index(['customer_id', 'status']);
            $table->index(['preferred_date', 'status']);
        });

        Schema::create('booking_reminders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->enum('reminder_type', ['email', 'sms', 'push', 'call']);
            $table->integer('hours_before'); // Hours before appointment
            $table->datetime('scheduled_at');
            $table->datetime('sent_at')->nullable();
            $table->enum('status', ['pending', 'sent', 'failed', 'cancelled'])->default('pending');
            $table->text('message')->nullable();
            $table->json('delivery_data')->nullable(); // Response from delivery service
            $table->timestamps();
            
            $table->index(['booking_id', 'reminder_type']);
            $table->index(['scheduled_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('booking_reminders');
        Schema::dropIfExists('waiting_lists');
    }
};
