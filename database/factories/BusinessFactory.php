<?php

namespace Database\Factories;

use App\Models\Business;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Business>
 */
class BusinessFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Business::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->company();
        
        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->paragraph(3),
            'address' => $this->faker->address(),
            'email' => $this->faker->companyEmail(),
            'phone' => $this->faker->phoneNumber(),
            'website' => $this->faker->url(),
            'timezone' => $this->faker->randomElement([
                'UTC',
                'America/New_York',
                'America/Chicago',
                'America/Denver',
                'America/Los_Angeles',
                'Europe/London',
                'Europe/Paris',
                'Asia/Tokyo'
            ]),
            'currency' => $this->faker->randomElement(['USD', 'EUR', 'GBP', 'CAD', 'AUD']),
            'language' => $this->faker->randomElement(['en', 'es', 'fr', 'de', 'it']),
            'multi_branch' => $this->faker->boolean(30), // 30% chance of being multi-branch
            'online_booking_enabled' => $this->faker->boolean(80), // 80% chance of online booking
            'booking_advance_days' => $this->faker->numberBetween(7, 90),
            'booking_advance_hours' => $this->faker->numberBetween(1, 48),
            'cancellation_hours' => $this->faker->numberBetween(1, 72),
            'business_rules' => [
                'allow_same_day_booking' => $this->faker->boolean(),
                'require_deposit' => $this->faker->boolean(),
                'auto_confirm_bookings' => $this->faker->boolean(70),
                'send_reminders' => $this->faker->boolean(90),
            ],
            'branding' => [
                'primary_color' => $this->faker->hexColor(),
                'secondary_color' => $this->faker->hexColor(),
                'theme' => $this->faker->randomElement(['default', 'modern', 'classic']),
            ],
            'is_active' => $this->faker->boolean(95), // 95% chance of being active
            'owner_id' => User::factory(),
        ];
    }

    /**
     * Indicate that the business is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the business is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the business supports multi-branch operations.
     */
    public function multiBranch(): static
    {
        return $this->state(fn (array $attributes) => [
            'multi_branch' => true,
        ]);
    }

    /**
     * Indicate that the business is single-branch.
     */
    public function singleBranch(): static
    {
        return $this->state(fn (array $attributes) => [
            'multi_branch' => false,
        ]);
    }

    /**
     * Indicate that the business has online booking enabled.
     */
    public function withOnlineBooking(): static
    {
        return $this->state(fn (array $attributes) => [
            'online_booking_enabled' => true,
        ]);
    }

    /**
     * Indicate that the business has online booking disabled.
     */
    public function withoutOnlineBooking(): static
    {
        return $this->state(fn (array $attributes) => [
            'online_booking_enabled' => false,
        ]);
    }

    /**
     * Configure the business for a specific owner.
     */
    public function forOwner(User $owner): static
    {
        return $this->state(fn (array $attributes) => [
            'owner_id' => $owner->id,
        ]);
    }
}
