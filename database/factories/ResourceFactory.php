<?php

namespace Database\Factories;

use App\Models\Resource;
use App\Models\Business;
use App\Models\ResourceType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Resource>
 */
class ResourceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Resource::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->randomElement([
            'Conference Room A',
            'Meeting Room B',
            'Office Space 1',
            'Laptop Computer',
            'Projector',
            'Company Car',
            'Desk Setup',
            'Workstation',
            'Studio Room',
            'Equipment Set'
        ]) . ' ' . $this->faker->randomNumber(2);

        return [
            'business_id' => Business::factory(),
            'business_branch_id' => null, // Can be set later if needed
            'resource_type_id' => ResourceType::factory(),
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->sentence(8),
            'capacity' => $this->faker->numberBetween(1, 20),
            'hourly_rate' => $this->faker->randomFloat(2, 10, 500),
            'specifications' => [
                'size' => $this->faker->randomElement(['Small', 'Medium', 'Large']),
                'features' => $this->faker->words(3),
            ],
            'availability_rules' => [
                'advance_booking_hours' => $this->faker->numberBetween(1, 48),
                'max_booking_duration' => $this->faker->numberBetween(1, 8),
            ],
            'requires_approval' => $this->faker->boolean(30),
            'is_active' => $this->faker->boolean(90),
        ];
    }

    /**
     * Indicate that the resource is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the resource is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the resource requires approval.
     */
    public function requiresApproval(): static
    {
        return $this->state(fn (array $attributes) => [
            'requires_approval' => true,
        ]);
    }

    /**
     * Indicate that the resource is a room type.
     */
    public function room(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Room ' . $this->faker->randomNumber(2),
            'description' => 'A physical room or space for meetings and appointments',
            'capacity' => $this->faker->numberBetween(2, 50),
            'specifications' => [
                'size' => $this->faker->randomElement(['Small', 'Medium', 'Large']),
                'features' => ['Projector', 'Whiteboard', 'Conference Table'],
                'wifi' => true,
                'air_conditioning' => true,
            ],
        ]);
    }

    /**
     * Indicate that the resource is equipment.
     */
    public function equipment(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Equipment ' . $this->faker->randomNumber(2),
            'description' => 'Tools and equipment for business operations',
            'capacity' => 1,
            'specifications' => [
                'type' => $this->faker->randomElement(['Electronic', 'Mechanical', 'Digital']),
                'model' => $this->faker->word(),
                'year' => $this->faker->year(),
            ],
        ]);
    }
}
