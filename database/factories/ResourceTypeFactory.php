<?php

namespace Database\Factories;

use App\Models\ResourceType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ResourceType>
 */
class ResourceTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ResourceType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->randomElement([
            'Conference Room',
            'Meeting Room',
            'Office Space',
            'Equipment',
            'Vehicle',
            'Computer',
            'Projector',
            'Desk',
            'Chair',
            'Table',
            'Workspace',
            'Studio',
            'Laboratory',
            'Workshop',
            'Storage Room'
        ]) . ' ' . $this->faker->randomNumber(2);

        $icons = [
            'fas fa-door-open',
            'fas fa-tools',
            'fas fa-car',
            'fas fa-laptop',
            'fas fa-chair',
            'fas fa-table',
            'fas fa-cube',
            'fas fa-desktop',
            'fas fa-bed',
            'fas fa-couch',
            'fas fa-wrench',
            'fas fa-hammer',
            'fas fa-user-md',
            'fas fa-stethoscope',
            'fas fa-basketball-ball'
        ];

        $colors = [
            '#007bff',
            '#28a745',
            '#dc3545',
            '#ffc107',
            '#17a2b8',
            '#6f42c1',
            '#e83e8c',
            '#fd7e14',
            '#20c997',
            '#6c757d'
        ];

        return [
            'business_id' => \App\Models\Business::factory(),
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->sentence(10),
            'icon' => $this->faker->randomElement($icons),
            'color' => $this->faker->randomElement($colors),
        ];
    }

    /**
     * Indicate that the resource type is for rooms.
     */
    public function room(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Room ' . $this->faker->randomNumber(2),
            'description' => 'A physical room or space for appointments and meetings',
            'icon' => 'fas fa-door-open',
            'color' => '#007bff',
        ]);
    }

    /**
     * Indicate that the resource type is for equipment.
     */
    public function equipment(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Equipment ' . $this->faker->randomNumber(2),
            'description' => 'Tools, machines, and equipment for business operations',
            'icon' => 'fas fa-tools',
            'color' => '#28a745',
        ]);
    }

    /**
     * Indicate that the resource type is for vehicles.
     */
    public function vehicle(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Vehicle ' . $this->faker->randomNumber(2),
            'description' => 'Cars, trucks, and transportation resources',
            'icon' => 'fas fa-car',
            'color' => '#dc3545',
        ]);
    }

    /**
     * Indicate that the resource type is for computers.
     */
    public function computer(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Computer ' . $this->faker->randomNumber(2),
            'description' => 'Computers, laptops, and IT equipment',
            'icon' => 'fas fa-laptop',
            'color' => '#6c757d',
        ]);
    }
}
