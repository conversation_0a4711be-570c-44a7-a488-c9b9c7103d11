<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AdminRedirectTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations
        $this->artisan('migrate');
        
        // Create roles with hierarchy levels
        Role::create([
            'name' => 'Super Admin',
            'guard_name' => 'web',
            'hierarchy_level' => 0
        ]);
        
        Role::create([
            'name' => 'Admin',
            'guard_name' => 'web',
            'hierarchy_level' => 1
        ]);
        
        Role::create([
            'name' => 'Business Owner',
            'guard_name' => 'web',
            'hierarchy_level' => 2
        ]);
        
        Role::create([
            'name' => 'Customer',
            'guard_name' => 'web',
            'hierarchy_level' => 3
        ]);
    }

    public function test_customer_accessing_admin_dashboard_gets_redirected_to_customer_dashboard()
    {
        $user = User::factory()->create();
        $user->assignRole('Customer');
        
        $response = $this->actingAs($user)->get('/admin/dashboard');
        
        $response->assertRedirect(route('customer.dashboard'));
        $response->assertSessionHas('info', 'You have been redirected to your customer dashboard.');
    }

    public function test_business_owner_accessing_admin_dashboard_gets_redirected_to_owner_dashboard()
    {
        $user = User::factory()->create();
        $user->assignRole('Business Owner');
        
        // Create a business for the user
        $business = \App\Models\Business::factory()->create(['user_id' => $user->id]);
        
        $response = $this->actingAs($user)->get('/admin/dashboard');
        
        $response->assertRedirect(route('owner.dashboard'));
        $response->assertSessionHas('info', 'You have been redirected to your business dashboard.');
    }

    public function test_admin_can_access_admin_dashboard_without_redirect()
    {
        $user = User::factory()->create();
        $user->assignRole('Admin');
        
        $response = $this->actingAs($user)->get('/admin/dashboard');
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
    }

    public function test_super_admin_can_access_admin_dashboard_without_redirect()
    {
        $user = User::factory()->create();
        $user->assignRole('Super Admin');
        
        $response = $this->actingAs($user)->get('/admin/dashboard');
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
    }

    public function test_unauthenticated_user_gets_redirected_to_login()
    {
        $response = $this->get('/admin/dashboard');
        
        $response->assertRedirect('/login');
    }
}
