<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AdminSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations
        $this->artisan('migrate');

        // Create roles with hierarchy levels
        Role::create([
            'name' => 'Super Admin',
            'guard_name' => 'web',
            'hierarchy_level' => 0
        ]);

        Role::create([
            'name' => 'Admin',
            'guard_name' => 'web',
            'hierarchy_level' => 1
        ]);

        Role::create([
            'name' => 'Business Owner',
            'guard_name' => 'web',
            'hierarchy_level' => 2
        ]);

        Role::create([
            'name' => 'Customer',
            'guard_name' => 'web',
            'hierarchy_level' => 3
        ]);
    }

    /** @test */
    public function unauthenticated_users_cannot_access_admin_dashboard()
    {
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function customers_cannot_access_admin_dashboard()
    {
        $user = User::factory()->create();
        $user->assignRole('Customer');

        $response = $this->actingAs($user)->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    /** @test */
    public function business_owners_cannot_access_admin_dashboard()
    {
        $user = User::factory()->create();
        $user->assignRole('Business Owner');

        $response = $this->actingAs($user)->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    /** @test */
    public function admins_can_access_admin_dashboard()
    {
        $user = User::factory()->create();
        $user->assignRole('Admin');

        $response = $this->actingAs($user)->get('/admin/dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function super_admins_can_access_admin_dashboard()
    {
        $user = User::factory()->create();
        $user->assignRole('Super Admin');

        $response = $this->actingAs($user)->get('/admin/dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function unauthorized_access_attempts_are_logged()
    {
        $user = User::factory()->create();
        $user->assignRole('Customer');

        // Clear any existing logs
        \Illuminate\Support\Facades\Log::shouldReceive('warning')
            ->once()
            ->with('Unauthorized access attempt', \Mockery::type('array'));

        $this->actingAs($user)->get('/admin/dashboard');
    }
}
