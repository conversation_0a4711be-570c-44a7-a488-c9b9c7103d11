<?php

namespace Tests\Feature;

use App\Models\Business;
use App\Models\BusinessCategory;
use App\Models\BusinessLandingPage;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class LandingPageSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $business;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a business category
        $this->category = BusinessCategory::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'description' => 'Test category description',
            'is_active' => true,
        ]);

        // Create a user
        $this->user = User::create([
            'name' => 'Test Owner',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);

        // Create a business
        $this->business = Business::create([
            'owner_id' => $this->user->id,
            'name' => 'Test Business',
            'description' => 'A test business for landing page testing',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'website' => 'https://testbusiness.com',
            'timezone' => 'UTC',
            'currency' => 'USD',
            'language' => 'en',
            'is_active' => true,
        ]);

        // Attach category to business
        $this->business->categories()->attach($this->category->id);
    }

    /** @test */
    public function user_can_create_business_with_landing_page()
    {
        // Delete the existing business to test creation
        $this->business->delete();

        $response = $this->actingAs($this->user)->post(route('owner.business.store'), [
            'name' => 'New Test Business',
            'description' => 'A new test business',
            'category_id' => $this->category->id,
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'timezone' => 'UTC',
            'currency' => 'USD',
            'language' => 'en',
            
            // Landing page data
            'landing_page_slug' => 'new-test-business',
            'page_title' => 'Welcome to New Test Business',
            'page_description' => 'We provide excellent services',
            'landing_page_theme' => 'default',
            'domain_type' => 'subdirectory',
            'booking_enabled' => true,
            
            // SEO data
            'meta_title' => 'New Test Business - Professional Services',
            'meta_description' => 'Professional services by New Test Business',
            'meta_keywords' => 'business, services, professional',
        ]);

        $response->assertRedirect(route('owner.dashboard'));
        
        // Verify business was created
        $business = Business::where('name', 'New Test Business')->first();
        $this->assertNotNull($business);
        
        // Verify landing page was created
        $landingPage = $business->landingPage;
        $this->assertNotNull($landingPage);
        $this->assertEquals('new-test-business', $landingPage->custom_slug);
        $this->assertEquals('Welcome to New Test Business', $landingPage->page_title);
        $this->assertFalse($landingPage->is_published); // Should start as draft
        
        // Verify SEO settings were created
        $seoSettings = $business->seoSettings;
        $this->assertNotNull($seoSettings);
        $this->assertEquals('New Test Business - Professional Services', $seoSettings->meta_title);
    }

    /** @test */
    public function landing_page_can_be_published_and_accessed()
    {
        // Create a landing page for the business
        $landingPage = BusinessLandingPage::create([
            'business_id' => $this->business->id,
            'custom_slug' => 'test-business',
            'page_title' => 'Test Business Landing',
            'page_description' => 'Welcome to our test business',
            'theme' => 'default',
            'domain_type' => 'subdirectory',
            'booking_enabled' => true,
            'is_published' => true,
            'published_at' => now(),
        ]);

        // Generate default sections
        $landingPage->generateDefaultSections();

        // Test that the landing page can be accessed
        $response = $this->get(route('landing-page.show', $landingPage->custom_slug));
        $response->assertStatus(200);
        $response->assertSee('Test Business Landing');
        $response->assertSee('Welcome to our test business');
    }

    /** @test */
    public function owner_can_manage_landing_page()
    {
        // Create a landing page
        $landingPage = BusinessLandingPage::create([
            'business_id' => $this->business->id,
            'custom_slug' => 'test-business',
            'page_title' => 'Test Business Landing',
            'theme' => 'default',
            'domain_type' => 'subdirectory',
            'is_published' => false,
        ]);

        // Test landing page index
        $response = $this->actingAs($this->user)->get(route('owner.landing-page.index'));
        $response->assertStatus(200);
        $response->assertSee('Landing Page Management');

        // Test landing page edit
        $response = $this->actingAs($this->user)->get(route('owner.landing-page.edit'));
        $response->assertStatus(200);
        $response->assertSee('Edit Landing Page');

        // Test landing page update
        $response = $this->actingAs($this->user)->put(route('owner.landing-page.update'), [
            'page_title' => 'Updated Landing Page Title',
            'page_description' => 'Updated description',
            'theme' => 'modern',
            'booking_enabled' => true,
        ]);

        $response->assertRedirect();
        
        $landingPage->refresh();
        $this->assertEquals('Updated Landing Page Title', $landingPage->page_title);
        $this->assertEquals('modern', $landingPage->theme);

        // Test publishing
        $response = $this->actingAs($this->user)->post(route('owner.landing-page.publish'));
        $response->assertRedirect();
        
        $landingPage->refresh();
        $this->assertTrue($landingPage->is_published);
        $this->assertNotNull($landingPage->published_at);
    }

    /** @test */
    public function landing_page_slug_must_be_unique()
    {
        // Create first landing page
        BusinessLandingPage::create([
            'business_id' => $this->business->id,
            'custom_slug' => 'unique-slug',
            'page_title' => 'First Page',
            'theme' => 'default',
            'domain_type' => 'subdirectory',
        ]);

        // Create another business
        $anotherUser = User::create([
            'name' => 'Another Owner',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);

        $anotherBusiness = Business::create([
            'owner_id' => $anotherUser->id,
            'name' => 'Another Business',
            'timezone' => 'UTC',
            'currency' => 'USD',
            'language' => 'en',
            'is_active' => true,
        ]);

        // Try to create landing page with same slug - should fail
        $response = $this->actingAs($anotherUser)->post(route('owner.landing-page.store'), [
            'custom_slug' => 'unique-slug',
            'page_title' => 'Second Page',
            'theme' => 'default',
            'domain_type' => 'subdirectory',
        ]);

        $response->assertSessionHasErrors('custom_slug');
    }

    /** @test */
    public function landing_page_generates_proper_seo_data()
    {
        $landingPage = BusinessLandingPage::create([
            'business_id' => $this->business->id,
            'custom_slug' => 'test-business',
            'page_title' => 'Test Business Landing',
            'theme' => 'default',
            'domain_type' => 'subdirectory',
            'is_published' => true,
        ]);

        // Create SEO settings
        $this->business->seoSettings()->create([
            'meta_title' => 'Test Business - Professional Services',
            'meta_description' => 'Professional services by Test Business',
            'business_type' => 'LocalBusiness',
            'sitemap_enabled' => true,
        ]);

        // Test sitemap generation
        $response = $this->get(route('landing-page.sitemap', $landingPage->custom_slug));
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/xml; charset=UTF-8');
    }

    /** @test */
    public function unpublished_landing_pages_return_404()
    {
        $landingPage = BusinessLandingPage::create([
            'business_id' => $this->business->id,
            'custom_slug' => 'unpublished-page',
            'page_title' => 'Unpublished Page',
            'theme' => 'default',
            'domain_type' => 'subdirectory',
            'is_published' => false,
        ]);

        $response = $this->get(route('landing-page.show', $landingPage->custom_slug));
        $response->assertStatus(404);
    }
}
