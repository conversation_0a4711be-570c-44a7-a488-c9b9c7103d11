<?php

namespace Tests\Feature\Admin;

use App\Models\Setting;
use App\Models\User;
use App\Services\SmsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SmsSettingsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        Permission::create(['name' => 'manage notifications']);

        // Create admin role with permissions
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo('manage notifications');

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
        $this->admin->assignRole('admin');
    }

    public function test_sms_settings_page_can_be_accessed(): void
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/settings/sms');

        $response->assertStatus(200);
        $response->assertViewIs('admin.settings.sms');
        $response->assertViewHas(['business', 'businesses', 'settings']);
    }

    public function test_sms_settings_can_be_updated(): void
    {
        $response = $this->actingAs($this->admin)
            ->post('/admin/settings/sms', [
                'sms_enabled' => '1',
                'sms_provider' => 'twilio',
                'sms_api_key' => 'test_account_sid',
                'sms_api_secret' => 'test_auth_token',
                'sms_from_number' => '+**********',
                'sms_booking_confirmation' => '1',
                'sms_booking_reminder' => '1',
                'sms_waiting_list_notification' => '0',
            ]);

        $response->assertRedirect('/admin/settings/sms');
        $response->assertSessionHas('success', 'SMS settings updated successfully.');

        // Verify settings were saved
        $this->assertTrue(Setting::getValue('sms_enabled'));
        $this->assertEquals('twilio', Setting::getValue('sms_provider'));
        $this->assertEquals('test_account_sid', Setting::getValue('sms_api_key'));
        $this->assertEquals('test_auth_token', Setting::getValue('sms_api_secret'));
        $this->assertEquals('+**********', Setting::getValue('sms_from_number'));
        $this->assertTrue(Setting::getValue('sms_booking_confirmation'));
        $this->assertTrue(Setting::getValue('sms_booking_reminder'));
        $this->assertFalse(Setting::getValue('sms_waiting_list_notification'));
    }

    public function test_sms_settings_validation_requires_credentials_when_enabled(): void
    {
        $response = $this->actingAs($this->admin)
            ->post('/admin/settings/sms', [
                'sms_enabled' => '1',
                'sms_provider' => 'twilio',
                'sms_api_key' => '',
                'sms_api_secret' => '',
                'sms_from_number' => '',
                'sms_booking_confirmation' => '1',
                'sms_booking_reminder' => '1',
                'sms_waiting_list_notification' => '1',
            ]);

        $response->assertSessionHasErrors(['sms_api_key', 'sms_api_secret', 'sms_from_number']);
    }

    public function test_sms_settings_allows_empty_credentials_when_disabled(): void
    {
        $response = $this->actingAs($this->admin)
            ->post('/admin/settings/sms', [
                'sms_enabled' => '0',
                'sms_provider' => 'twilio',
                'sms_api_key' => '',
                'sms_api_secret' => '',
                'sms_from_number' => '',
                'sms_booking_confirmation' => '1',
                'sms_booking_reminder' => '1',
                'sms_waiting_list_notification' => '1',
            ]);

        $response->assertRedirect('/admin/settings/sms');
        $response->assertSessionHas('success');
    }

    public function test_aws_sns_requires_region_when_enabled(): void
    {
        $response = $this->actingAs($this->admin)
            ->post('/admin/settings/sms', [
                'sms_enabled' => '1',
                'sms_provider' => 'aws_sns',
                'sms_api_key' => 'test_access_key',
                'sms_api_secret' => 'test_secret_key',
                'sms_from_number' => '',
                'aws_region' => '',
                'sms_booking_confirmation' => '1',
                'sms_booking_reminder' => '1',
                'sms_waiting_list_notification' => '1',
            ]);

        $response->assertSessionHasErrors(['aws_region']);
    }

    public function test_aws_sns_allows_empty_from_number(): void
    {
        $response = $this->actingAs($this->admin)
            ->post('/admin/settings/sms', [
                'sms_enabled' => '1',
                'sms_provider' => 'aws_sns',
                'sms_api_key' => 'test_access_key',
                'sms_api_secret' => 'test_secret_key',
                'sms_from_number' => '',
                'aws_region' => 'us-east-1',
                'sms_booking_confirmation' => '1',
                'sms_booking_reminder' => '1',
                'sms_waiting_list_notification' => '1',
            ]);

        $response->assertRedirect('/admin/settings/sms');
        $response->assertSessionHas('success');
    }

    public function test_test_sms_requires_authentication(): void
    {
        $response = $this->postJson('/admin/settings/sms/test', [
            'test_phone' => '+**********'
        ]);

        $response->assertStatus(401);
    }

    public function test_test_sms_requires_phone_number(): void
    {
        $response = $this->actingAs($this->admin)
            ->postJson('/admin/settings/sms/test', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['test_phone']);
    }

    public function test_test_sms_fails_when_sms_disabled(): void
    {
        Setting::setValue('sms_enabled', false);

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/settings/sms/test', [
                'test_phone' => '+**********'
            ]);

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false,
            'message' => 'SMS notifications are disabled. Please enable them first.'
        ]);
    }

    public function test_test_sms_can_be_sent_when_enabled(): void
    {
        // Mock the SmsService
        $this->mock(SmsService::class, function ($mock) {
            $mock->shouldReceive('testSms')
                ->once()
                ->with('+**********', \Mockery::type('string'))
                ->andReturn([
                    'success' => true,
                    'message' => 'SMS sent successfully via Twilio',
                    'data' => [
                        'provider' => 'twilio',
                        'message_id' => 'test_message_id'
                    ]
                ]);
        });

        Setting::setValue('sms_enabled', true);

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/settings/sms/test', [
                'test_phone' => '+**********'
            ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Test SMS sent successfully to +**********'
        ]);
    }

    public function test_unauthorized_user_cannot_access_sms_settings(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->get('/admin/settings/sms');

        $response->assertStatus(403);
    }

    public function test_unauthorized_user_cannot_update_sms_settings(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->post('/admin/settings/sms', [
                'sms_enabled' => '1',
                'sms_provider' => 'twilio',
            ]);

        $response->assertStatus(403);
    }

    public function test_unauthorized_user_cannot_send_test_sms(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->postJson('/admin/settings/sms/test', [
                'test_phone' => '+**********'
            ]);

        $response->assertStatus(403);
    }
}
