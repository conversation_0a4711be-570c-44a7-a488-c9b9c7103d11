<?php

namespace Tests\Feature\Owner;

use App\Models\Business;
use App\Models\ResourceType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ResourceTypeDeleteDebugTest extends TestCase
{
    use RefreshDatabase;

    public function test_debug_resource_type_deletion()
    {
        // Create roles if they don't exist
        $businessOwnerRole = Role::firstOrCreate(['name' => 'Business Owner']);

        // Create a user and assign role
        $user = User::factory()->create();
        $user->assignRole($businessOwnerRole);

        // Create an active business owned by the user
        $business = Business::factory()->active()->create(['owner_id' => $user->id]);

        // Create a resource type
        $resourceType = ResourceType::factory()->create([
            'business_id' => $business->id,
            'name' => 'Test Resource Type',
            'slug' => 'test-resource-type',
        ]);

        $this->actingAs($user);

        // Debug: Check if resource type exists
        $this->assertDatabaseHas('resource_types', [
            'id' => $resourceType->id,
            'business_id' => $business->id,
            'slug' => 'test-resource-type',
        ]);

        // Debug: Check if user has business
        $userBusiness = $user->ownedBusinesses()->active()->first();
        $this->assertNotNull($userBusiness);
        $this->assertEquals($business->id, $userBusiness->id);

        // Debug: Check if business has resource type
        $businessResourceType = $business->resourceTypes()->where('slug', 'test-resource-type')->first();
        $this->assertNotNull($businessResourceType);

        // Try to access the route
        $response = $this->deleteJson("/owner/resource-types/test-resource-type");

        // Debug the response
        if ($response->status() !== 200) {
            dump('Response status: ' . $response->status());
            dump('Response content: ' . $response->getContent());
        }

        $response->assertStatus(200);
    }
}
