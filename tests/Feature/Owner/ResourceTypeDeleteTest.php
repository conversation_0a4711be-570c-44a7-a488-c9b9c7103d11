<?php

namespace Tests\Feature\Owner;

use App\Models\Business;
use App\Models\Resource;
use App\Models\ResourceType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ResourceTypeDeleteTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $business;
    protected $resourceType;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles if they don't exist
        $businessOwnerRole = Role::firstOrCreate(['name' => 'Business Owner']);

        // Create a user and assign role
        $this->user = User::factory()->create();
        $this->user->assignRole($businessOwnerRole);

        // Create an active business owned by the user
        $this->business = Business::factory()->active()->create(['owner_id' => $this->user->id]);

        // Create a resource type
        $this->resourceType = ResourceType::factory()->create([
            'business_id' => $this->business->id,
            'name' => 'Test Resource Type',
        ]);
    }

    /** @test */
    public function it_can_delete_resource_type_without_resources()
    {
        $this->actingAs($this->user);

        $response = $this->deleteJson("/owner/resource-types/{$this->resourceType->slug}");

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => 'Resource type deleted successfully.',
                 ]);

        $this->assertDatabaseMissing('resource_types', [
            'id' => $this->resourceType->id,
        ]);
    }

    /** @test */
    public function it_cannot_delete_resource_type_with_resources()
    {
        $this->actingAs($this->user);

        // Create a resource associated with this resource type
        Resource::factory()->create([
            'business_id' => $this->business->id,
            'resource_type_id' => $this->resourceType->id,
        ]);

        $response = $this->deleteJson("/owner/resource-types/{$this->resourceType->slug}");

        $response->assertStatus(422)
                 ->assertJson([
                     'success' => false,
                 ])
                 ->assertJsonFragment([
                     'message' => 'Cannot delete resource type. It has 1 resource(s).',
                 ]);

        $this->assertDatabaseHas('resource_types', [
            'id' => $this->resourceType->id,
        ]);
    }

    /** @test */
    public function it_prevents_unauthorized_access_to_other_business_resource_types()
    {
        // Create another user and business
        $otherUser = User::factory()->create();
        $otherUser->assignRole('Business Owner');
        $otherBusiness = Business::factory()->active()->create(['owner_id' => $otherUser->id]);
        $otherResourceType = ResourceType::factory()->create([
            'business_id' => $otherBusiness->id,
        ]);

        $this->actingAs($this->user);

        $response = $this->deleteJson("/owner/resource-types/{$otherResourceType->slug}");

        $response->assertStatus(403);
    }

    /** @test */
    public function it_handles_database_errors_gracefully()
    {
        $this->actingAs($this->user);

        // Mock a database error by using an invalid resource type ID
        $response = $this->deleteJson("/owner/resource-types/non-existent-slug");

        $response->assertStatus(404);
    }
}
