<?php

namespace Tests\Feature\Owner;

use App\Models\User;
use App\Models\Business;
use App\Models\ResourceType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class ResourceTypeTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $business;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'Business Owner']);

        // Create user and business
        $this->user = User::factory()->create();
        $this->user->assignRole('Business Owner');

        $this->business = Business::factory()->create([
            'owner_id' => $this->user->id,
        ]);

        // Create some default resource types
        ResourceType::factory()->count(3)->create();
    }

    /** @test */
    public function owner_can_view_resource_types_index()
    {
        $response = $this->actingAs($this->user)
            ->get(route('owner.resource-types.index'));

        $response->assertStatus(200);
        $response->assertViewIs('owner.resource-types.index');
        $response->assertViewHas(['resourceTypes', 'stats']);
    }

    /** @test */
    public function owner_can_create_resource_type()
    {
        $resourceTypeData = [
            'name' => 'Test Resource Type',
            'description' => 'A test resource type',
            'icon' => 'fas fa-test',
            'color' => '#ff0000',
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('owner.resource-types.store'), $resourceTypeData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('resource_types', [
            'name' => 'Test Resource Type',
            'description' => 'A test resource type',
        ]);
    }

    /** @test */
    public function owner_can_update_resource_type()
    {
        $resourceType = ResourceType::factory()->create();

        $updateData = [
            'name' => 'Updated Resource Type',
            'description' => 'Updated description',
            'icon' => 'fas fa-updated',
            'color' => '#00ff00',
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('owner.resource-types.update', $resourceType), $updateData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('resource_types', [
            'id' => $resourceType->id,
            'name' => 'Updated Resource Type',
        ]);
    }

    /** @test */
    public function owner_can_delete_unused_resource_type()
    {
        $resourceType = ResourceType::factory()->create();

        $response = $this->actingAs($this->user)
            ->deleteJson(route('owner.resource-types.destroy', $resourceType));

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseMissing('resource_types', [
            'id' => $resourceType->id,
        ]);
    }

    /** @test */
    public function owner_can_get_resource_types_for_select()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('owner.resource-types.select'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => ['id', 'name', 'slug', 'icon', 'color', 'resources_count']
        ]);
    }

    /** @test */
    public function owner_can_get_analytics()
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('owner.resource-types.analytics'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => ['name', 'resources_count', 'total_bookings', 'color']
        ]);
    }

    /** @test */
    public function owner_can_export_resource_types()
    {
        $response = $this->actingAs($this->user)
            ->get(route('owner.resource-types.export'));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function resource_type_validation_works()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('owner.resource-types.store'), []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    /** @test */
    public function duplicate_resource_type_names_are_not_allowed()
    {
        $existingType = ResourceType::factory()->create(['name' => 'Existing Type']);

        $response = $this->actingAs($this->user)
            ->postJson(route('owner.resource-types.store'), [
                'name' => 'Existing Type',
                'description' => 'Test description',
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }
}
