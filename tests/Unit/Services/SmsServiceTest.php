<?php

namespace Tests\Unit\Services;

use App\Models\Setting;
use App\Services\SmsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class SmsServiceTest extends TestCase
{
    use RefreshDatabase;

    protected SmsService $smsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->smsService = new SmsService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_sms_sending_fails_when_disabled(): void
    {
        Setting::setValue('sms_enabled', false);

        $result = $this->smsService->sendSms('+**********', 'Test message');

        $this->assertFalse($result['success']);
        $this->assertEquals('SMS notifications are disabled', $result['message']);
    }

    public function test_sms_sending_fails_with_invalid_provider(): void
    {
        Setting::setValue('sms_enabled', true);
        Setting::setValue('sms_provider', 'invalid_provider');

        $result = $this->smsService->sendSms('+**********', 'Test message');

        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid SMS provider configured', $result['message']);
    }

    public function test_twilio_validation_fails_with_missing_credentials(): void
    {
        $result = $this->smsService->validateCredentials('twilio', [
            'api_key' => '',
            'api_secret' => ''
        ]);

        $this->assertFalse($result['valid']);
        $this->assertEquals('Account SID and Auth Token are required', $result['message']);
    }

    public function test_nexmo_validation_fails_with_missing_credentials(): void
    {
        $result = $this->smsService->validateCredentials('nexmo', [
            'api_key' => '',
            'api_secret' => ''
        ]);

        $this->assertFalse($result['valid']);
        $this->assertEquals('API Key and API Secret are required', $result['message']);
    }

    public function test_aws_validation_fails_with_missing_credentials(): void
    {
        $result = $this->smsService->validateCredentials('aws_sns', [
            'api_key' => '',
            'api_secret' => ''
        ]);

        $this->assertFalse($result['valid']);
        $this->assertEquals('Access Key ID and Secret Access Key are required', $result['message']);
    }

    public function test_validation_fails_with_invalid_provider(): void
    {
        $result = $this->smsService->validateCredentials('invalid_provider', []);

        $this->assertFalse($result['valid']);
        $this->assertEquals('Invalid provider', $result['message']);
    }

    public function test_twilio_sending_fails_with_missing_credentials(): void
    {
        Setting::setValue('sms_enabled', true);
        Setting::setValue('sms_provider', 'twilio');
        Setting::setValue('sms_api_key', '');
        Setting::setValue('sms_api_secret', '');
        Setting::setValue('sms_from_number', '');

        $result = $this->smsService->sendSms('+**********', 'Test message');

        $this->assertFalse($result['success']);
        $this->assertEquals('Twilio credentials not configured properly', $result['message']);
    }

    public function test_nexmo_sending_fails_with_missing_credentials(): void
    {
        Setting::setValue('sms_enabled', true);
        Setting::setValue('sms_provider', 'nexmo');
        Setting::setValue('sms_api_key', '');
        Setting::setValue('sms_api_secret', '');
        Setting::setValue('sms_from_number', '');

        $result = $this->smsService->sendSms('+**********', 'Test message');

        $this->assertFalse($result['success']);
        $this->assertEquals('Nexmo credentials not configured properly', $result['message']);
    }

    public function test_aws_sns_sending_fails_with_missing_credentials(): void
    {
        Setting::setValue('sms_enabled', true);
        Setting::setValue('sms_provider', 'aws_sns');
        Setting::setValue('sms_api_key', '');
        Setting::setValue('sms_api_secret', '');

        $result = $this->smsService->sendSms('+**********', 'Test message');

        $this->assertFalse($result['success']);
        $this->assertEquals('AWS SNS credentials not configured properly', $result['message']);
    }

    public function test_test_sms_calls_send_sms(): void
    {
        Setting::setValue('sms_enabled', false);

        $result = $this->smsService->testSms('+**********', 'Test message');

        $this->assertFalse($result['success']);
        $this->assertEquals('SMS notifications are disabled', $result['message']);
    }

    public function test_sms_service_handles_exceptions_gracefully(): void
    {
        Setting::setValue('sms_enabled', true);
        Setting::setValue('sms_provider', 'twilio');
        Setting::setValue('sms_api_key', 'invalid_sid');
        Setting::setValue('sms_api_secret', 'invalid_token');
        Setting::setValue('sms_from_number', '+**********');

        // This will fail with invalid credentials, but should handle the exception
        $result = $this->smsService->sendSms('+**********', 'Test message');

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Twilio error:', $result['message']);
    }

    public function test_aws_sns_uses_default_region_when_not_set(): void
    {
        Setting::setValue('sms_enabled', true);
        Setting::setValue('sms_provider', 'aws_sns');
        Setting::setValue('sms_api_key', 'test_key');
        Setting::setValue('sms_api_secret', 'test_secret');
        // Don't set aws_region, should use default

        $result = $this->smsService->sendSms('+**********', 'Test message');

        // Should fail with invalid credentials but not because of missing region
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('AWS SNS error:', $result['message']);
    }

    public function test_provider_specific_validation_with_valid_structure(): void
    {
        // Test that validation methods exist and have proper structure
        $this->assertTrue(method_exists($this->smsService, 'validateCredentials'));

        // Test each provider validation method exists (indirectly)
        $twilioResult = $this->smsService->validateCredentials('twilio', ['api_key' => 'test', 'api_secret' => 'test']);
        $nexmoResult = $this->smsService->validateCredentials('nexmo', ['api_key' => 'test', 'api_secret' => 'test']);
        $awsResult = $this->smsService->validateCredentials('aws_sns', ['api_key' => 'test', 'api_secret' => 'test']);

        // All should fail with invalid credentials but not with method errors
        $this->assertFalse($twilioResult['valid']);
        $this->assertFalse($nexmoResult['valid']);
        $this->assertFalse($awsResult['valid']);

        // All should have proper message structure
        $this->assertArrayHasKey('message', $twilioResult);
        $this->assertArrayHasKey('message', $nexmoResult);
        $this->assertArrayHasKey('message', $awsResult);
    }
}
