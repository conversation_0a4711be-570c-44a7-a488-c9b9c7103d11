<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Business;
use App\Models\BusinessGalleryImage;

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Verifying Gallery Functionality\n";
echo "==============================\n";

// Check if test data exists
$user = User::where('email', '<EMAIL>')->first();
$business = Business::where('slug', 'test-business')->first();

if (!$user) {
    echo "❌ Test user not found. Run: php artisan db:seed --class=TestUserSeeder\n";
    exit;
}

if (!$business) {
    echo "❌ Test business not found. Run: php artisan db:seed --class=TestDataSeeder\n";
    exit;
}

$images = $business->galleryImages()->get();
if ($images->count() === 0) {
    echo "❌ No test images found. Run: php create_test_gallery_images.php\n";
    exit;
}

echo "✅ Test user found: {$user->email}\n";
echo "✅ Test business found: {$business->name}\n";
echo "✅ Test images found: {$images->count()} images\n\n";

// Check routes
echo "Checking routes...\n";
try {
    $testImage = $images->first();
    $toggleRoute = route('owner.gallery.toggle-featured', $testImage->id);
    $destroyRoute = route('owner.gallery.destroy', $testImage->id);

    echo "✅ Toggle featured route: {$toggleRoute}\n";
    echo "✅ Destroy route: {$destroyRoute}\n";
} catch (Exception $e) {
    echo "❌ Route generation failed: {$e->getMessage()}\n";
}

// Check controller functionality
echo "\nChecking controller functionality...\n";

use App\Http\Controllers\Owner\GalleryController;
use Illuminate\Support\Facades\Auth;

try {

    Auth::login($user);
    $controller = new GalleryController(app(\App\Services\GalleryImageService::class));

    // Test toggle featured
    $testImage = $images->first();
    $originalStatus = $testImage->is_featured;

    $response = $controller->toggleFeatured($testImage);
    $data = $response->getData(true);

    if ($data['success']) {
        echo "✅ Toggle featured controller method working\n";

        // Reset the status
        $testImage->refresh();
        if ($testImage->is_featured !== $originalStatus) {
            $controller->toggleFeatured($testImage); // Reset to original
        }
    } else {
        echo "❌ Toggle featured controller method failed\n";
    }

} catch (Exception $e) {
    echo "❌ Controller test failed: {$e->getMessage()}\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "TESTING INSTRUCTIONS\n";
echo str_repeat("=", 50) . "\n";

echo "1. Start the development server:\n";
echo "   php artisan serve\n\n";

echo "2. Open your browser and navigate to:\n";
echo "   http://127.0.0.1:8000/owner/gallery\n\n";

echo "3. Login with:\n";
echo "   Email: <EMAIL>\n";
echo "   Password: password\n\n";

echo "4. Test the functionality:\n";
echo "   - Click the 'Test JS' button to verify JavaScript is working\n";
echo "   - Click the yellow star icon to toggle featured status\n";
echo "   - Click the red trash icon to delete an image\n";
echo "   - Check browser console (F12) for debug messages\n\n";

echo "5. Expected behavior:\n";
echo "   - Toggle Featured: Should show success message and update star color\n";
echo "   - Delete: Should show confirmation dialog and remove image\n";
echo "   - All actions should show toastr notifications\n\n";

echo "If buttons still don't work, check:\n";
echo "- Browser console for JavaScript errors\n";
echo "- Network tab for failed AJAX requests\n";
echo "- Laravel logs for server-side errors\n";

echo "\nAll checks completed!\n";
