<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON>tie\Permission\Traits\HasRoles;

class User extends Authenticatable // implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'email_verified_at',
        'password',
        'phone',
        'profile_image',
        'date_of_birth',
        'gender',
        'timezone',
        'language',
        'preferences',
        'privacy_settings',
        'last_login_at',
        'last_login_ip',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'preferences' => 'array',
            'privacy_settings' => 'array',
            'last_login_at' => 'datetime',
            'two_factor_enabled' => 'boolean',
        ];
    }

    // Business relationships
    public function ownedBusinesses()
    {
        return $this->hasMany(Business::class, 'owner_id');
    }

    /**
     * Get the user's primary business (first owned business)
     * This is a convenience method for single-business owners
     */
    public function business()
    {
        return $this->hasOne(Business::class, 'owner_id');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'customer_id');
    }

    public function customerBookings()
    {
        return $this->hasMany(Booking::class, 'customer_id');
    }

    public function bookingsByEmail()
    {
        return $this->hasMany(Booking::class, 'customer_email', 'email');
    }

    public function waitingLists()
    {
        return $this->hasMany(WaitingList::class, 'customer_id');
    }

    // Favorites relationships
    public function favorites()
    {
        return $this->hasMany(CustomerFavorite::class);
    }

    public function favoriteServices()
    {
        return $this->belongsToMany(Service::class, 'customer_favorites')
                    ->withTimestamps()
                    ->orderBy('customer_favorites.created_at', 'desc');
    }

    /**
     * Check if user has favorited a specific service.
     */
    public function hasFavorited($serviceId): bool
    {
        return $this->favoriteServices()->where('service_id', $serviceId)->exists();
    }

    // Customer business profile relationships
    public function businessProfiles()
    {
        return $this->hasMany(CustomerBusinessProfile::class, 'customer_id');
    }

    public function getBusinessProfile($businessId)
    {
        return $this->businessProfiles()->where('business_id', $businessId)->first();
    }

    // Customer tag relationships
    public function tagAssignments()
    {
        return $this->hasMany(CustomerTagAssignment::class, 'customer_id');
    }

    public function tags()
    {
        return $this->belongsToMany(CustomerTag::class, 'customer_tag_assignments', 'customer_id', 'customer_tag_id')
                    ->withPivot(['business_id', 'assigned_by', 'assigned_at'])
                    ->withTimestamps();
    }

    // Customer communication relationships
    public function communications()
    {
        return $this->hasMany(CustomerCommunication::class, 'customer_id');
    }

    public function sentCommunications()
    {
        return $this->hasMany(CustomerCommunication::class, 'sent_by');
    }

    // Customer loyalty points relationships
    public function loyaltyPoints()
    {
        return $this->hasMany(CustomerLoyaltyPoint::class, 'customer_id');
    }

    public function processedLoyaltyPoints()
    {
        return $this->hasMany(CustomerLoyaltyPoint::class, 'processed_by');
    }

    /**
     * Get customer's loyalty points balance for a specific business.
     */
    public function getLoyaltyPointsBalance($businessId): int
    {
        return CustomerLoyaltyPoint::getCustomerBalance($businessId, $this->id);
    }

    /**
     * Check if customer has a tag in a specific business.
     */
    public function hasTag($businessId, $tagId): bool
    {
        return $this->tagAssignments()
                    ->where('business_id', $businessId)
                    ->where('customer_tag_id', $tagId)
                    ->exists();
    }

    /**
     * Add a service to favorites.
     */
    public function addToFavorites($serviceId): bool
    {
        if (!$this->hasFavorited($serviceId)) {
            $this->favoriteServices()->attach($serviceId);
            return true;
        }
        return false;
    }

    /**
     * Remove a service from favorites.
     */
    public function removeFromFavorites($serviceId): bool
    {
        if ($this->hasFavorited($serviceId)) {
            $this->favoriteServices()->detach($serviceId);
            return true;
        }
        return false;
    }

    /**
     * Toggle favorite status for a service.
     */
    public function toggleFavorite($serviceId): array
    {
        if ($this->hasFavorited($serviceId)) {
            $this->removeFromFavorites($serviceId);
            return ['favorited' => false, 'action' => 'removed'];
        } else {
            $this->addToFavorites($serviceId);
            return ['favorited' => true, 'action' => 'added'];
        }
    }
}
