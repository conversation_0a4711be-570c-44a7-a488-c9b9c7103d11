<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_number',
        'business_id',
        'business_branch_id',
        'customer_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'start_datetime',
        'end_datetime',
        'total_duration_minutes',
        'participant_count',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'deposit_amount',
        'paid_amount',
        'status',
        'payment_status',
        'notes',
        'special_requests',
        'internal_notes',
        'metadata',
        'is_recurring',
        'recurring_group_id',
        'recurring_sequence',
        'recurrence_data',
        'cancelled_at',
        'cancelled_by',
        'cancellation_reason',
        'checked_in_at',
        'checked_out_at',
    ];

    protected $casts = [
        'start_datetime' => 'datetime',
        'end_datetime' => 'datetime',
        'total_duration_minutes' => 'integer',
        'participant_count' => 'integer',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'deposit_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'metadata' => 'array',
        'is_recurring' => 'boolean',
        'recurring_sequence' => 'integer',
        'recurrence_data' => 'array',
        'cancelled_at' => 'datetime',
        'checked_in_at' => 'datetime',
        'checked_out_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (empty($booking->booking_number)) {
                $booking->booking_number = static::generateBookingNumber();
            }
        });
    }

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function bookingServices(): HasMany
    {
        return $this->hasMany(BookingService::class);
    }

    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'booking_services')
                    ->withPivot(['quantity', 'unit_price', 'total_price', 'duration_minutes', 'start_datetime', 'end_datetime', 'service_data'])
                    ->withTimestamps();
    }

    public function payments(): HasMany
    {
        return $this->hasMany(BookingPayment::class);
    }

    public function reminders(): HasMany
    {
        return $this->hasMany(BookingReminder::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPaymentStatus($query, $paymentStatus)
    {
        return $query->where('payment_status', $paymentStatus);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_datetime', '>', now());
    }

    public function scopePast($query)
    {
        return $query->where('end_datetime', '<', now());
    }

    public function scopeToday($query)
    {
        return $query->whereDate('start_datetime', today());
    }

    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_datetime', [$startDate, $endDate]);
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    // Helper methods
    public static function generateBookingNumber()
    {
        $prefix = 'BK';
        $timestamp = now()->format('ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $prefix . $timestamp . $random;
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'warning',
            'confirmed' => 'info',
            'in_progress' => 'primary',
            'completed' => 'success',
            'cancelled' => 'danger',
            'no_show' => 'secondary',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    public function getPaymentStatusColorAttribute()
    {
        $colors = [
            'pending' => 'warning',
            'partial' => 'info',
            'paid' => 'success',
            'refunded' => 'danger',
        ];

        return $colors[$this->payment_status] ?? 'secondary';
    }

    // Add hex color methods for calendar display
    public function getStatusHexColorAttribute()
    {
        $colors = [
            'pending' => '#ffc107',
            'confirmed' => '#17a2b8',
            'in_progress' => '#007bff',
            'completed' => '#28a745',
            'cancelled' => '#dc3545',
            'no_show' => '#6c757d',
        ];

        return $colors[$this->status] ?? '#6c757d';
    }

    public function getPaymentStatusHexColorAttribute()
    {
        $colors = [
            'pending' => '#ffc107',
            'partial' => '#17a2b8',
            'paid' => '#28a745',
            'refunded' => '#dc3545',
        ];

        return $colors[$this->payment_status] ?? '#6c757d';
    }

    public function getRemainingAmountAttribute()
    {
        return $this->total_amount - $this->paid_amount;
    }

    public function getIsFullyPaidAttribute()
    {
        return $this->paid_amount >= $this->total_amount;
    }

    public function getCanBeCancelledAttribute()
    {
        if (in_array($this->status, ['cancelled', 'completed', 'no_show'])) {
            return false;
        }

        $business = $this->business;
        if (!$business || !$business->cancellation_hours) {
            return true; // Allow cancellation if no business rules
        }

        $cancellationDeadline = $this->start_datetime->copy()->subHours($business->cancellation_hours);

        return now() <= $cancellationDeadline;
    }

    public function getCanBeCheckedInAttribute()
    {
        return $this->status === 'confirmed' &&
               $this->start_datetime <= now() &&
               $this->end_datetime >= now() &&
               !$this->checked_in_at;
    }

    public function getCanBeCheckedOutAttribute()
    {
        return $this->checked_in_at && !$this->checked_out_at;
    }

    public function getCanBeEditedAttribute()
    {
        if (in_array($this->status, ['cancelled', 'completed', 'no_show'])) {
            return false;
        }

        // Allow editing if booking is in the future
        return $this->start_datetime->isFuture();
    }

    public function getDurationInHoursAttribute()
    {
        return round($this->total_duration_minutes / 60, 2);
    }

    public function getFormattedDurationAttribute()
    {
        $hours = floor($this->total_duration_minutes / 60);
        $minutes = $this->total_duration_minutes % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    public function cancel($reason = null, $cancelledBy = null)
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancelled_by' => $cancelledBy,
            'cancellation_reason' => $reason,
        ]);

        // Cancel any pending reminders
        $this->reminders()->where('status', 'pending')->update(['status' => 'cancelled']);
    }

    public function checkIn()
    {
        $this->update([
            'status' => 'in_progress',
            'checked_in_at' => now(),
        ]);
    }

    public function checkOut()
    {
        $this->update([
            'status' => 'completed',
            'checked_out_at' => now(),
        ]);
    }

    public function updatePaymentStatus()
    {
        $totalPaid = $this->payments()->where('status', 'completed')->sum('amount');
        $this->update(['paid_amount' => $totalPaid]);

        if ($totalPaid >= $this->total_amount) {
            $this->update(['payment_status' => 'paid']);
        } elseif ($totalPaid > 0) {
            $this->update(['payment_status' => 'partial']);
        } else {
            $this->update(['payment_status' => 'pending']);
        }
    }
}
