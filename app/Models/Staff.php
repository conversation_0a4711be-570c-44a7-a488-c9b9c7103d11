<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Staff extends Model
{
    use HasFactory;

    protected $table = 'staff';

    protected $fillable = [
        'business_id',
        'name',
        'position',
        'bio',
        'years_experience',
        'email',
        'phone',
        'photo_url',
        'is_active',
        'accepts_bookings',
        'show_on_landing',
        'sort_order',
        'social_links',
        'specializations',
        'languages',
        'working_hours',
        'hourly_rate',
        'commission_rate',
        'hire_date',
        'emergency_contact',
        'notes'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'accepts_bookings' => 'boolean',
        'show_on_landing' => 'boolean',
        'social_links' => 'array',
        'specializations' => 'array',
        'languages' => 'array',
        'working_hours' => 'array',
        'emergency_contact' => 'array',
        'hire_date' => 'date',
        'hourly_rate' => 'decimal:2',
        'commission_rate' => 'decimal:2'
    ];

    /**
     * Get the business that owns the staff member.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the bookings assigned to this staff member.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'staff_id');
    }

    /**
     * Get the services this staff member can provide.
     */
    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'staff_services')
                    ->withPivot(['is_primary', 'skill_level'])
                    ->withTimestamps();
    }

    /**
     * Get the availability blocks for this staff member.
     */
    public function availabilityBlocks(): HasMany
    {
        return $this->hasMany(AvailabilityBlock::class, 'staff_id');
    }

    /**
     * Scope to get only active staff members.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get staff members who accept bookings.
     */
    public function scopeAcceptsBookings($query)
    {
        return $query->where('accepts_bookings', true);
    }

    /**
     * Scope to get staff members shown on landing page.
     */
    public function scopeShowOnLanding($query)
    {
        return $query->where('show_on_landing', true);
    }

    /**
     * Get the full name attribute.
     */
    public function getFullNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get the display name for the staff member.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->position ? "{$this->name} - {$this->position}" : $this->name;
    }

    /**
     * Check if staff member is available for booking.
     */
    public function isAvailableForBooking(): bool
    {
        return $this->is_active && $this->accepts_bookings;
    }

    /**
     * Get staff member's primary services.
     */
    public function getPrimaryServices()
    {
        return $this->services()->wherePivot('is_primary', true)->get();
    }

    /**
     * Get staff member's working hours for a specific day.
     */
    public function getWorkingHoursForDay($dayOfWeek)
    {
        $workingHours = $this->working_hours ?? [];
        return $workingHours[$dayOfWeek] ?? null;
    }

    /**
     * Check if staff member is working on a specific day.
     */
    public function isWorkingOnDay($dayOfWeek): bool
    {
        $hours = $this->getWorkingHoursForDay($dayOfWeek);
        return $hours && !($hours['is_off'] ?? false);
    }

    /**
     * Get staff member's total bookings count.
     */
    public function getTotalBookingsAttribute(): int
    {
        return $this->bookings()->count();
    }

    /**
     * Get staff member's completed bookings count.
     */
    public function getCompletedBookingsAttribute(): int
    {
        return $this->bookings()->where('status', 'completed')->count();
    }

    /**
     * Get staff member's average rating.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->bookings()
                    ->whereNotNull('rating')
                    ->avg('rating') ?? 0.0;
    }
}
