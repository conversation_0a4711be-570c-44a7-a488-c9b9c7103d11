<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Review extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'service_id',
        'booking_id',
        'customer_name',
        'customer_email',
        'customer_photo',
        'rating',
        'review_text',
        'service_name',
        'is_approved',
        'is_featured',
        'is_verified',
        'review_source',
        'helpful_votes',
        'response_text',
        'response_date',
        'photos',
        'tags'
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'is_featured' => 'boolean',
        'is_verified' => 'boolean',
        'rating' => 'integer',
        'helpful_votes' => 'integer',
        'response_date' => 'datetime',
        'photos' => 'array',
        'tags' => 'array'
    ];

    /**
     * Get the business that owns the review.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the service that was reviewed.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the booking associated with this review.
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Scope to get only approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope to get only featured reviews.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get only verified reviews.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get reviews with a minimum rating.
     */
    public function scopeMinRating($query, $rating)
    {
        return $query->where('rating', '>=', $rating);
    }

    /**
     * Get the star rating as a formatted string.
     */
    public function getStarRatingAttribute(): string
    {
        return str_repeat('★', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }

    /**
     * Get the review excerpt (limited text).
     */
    public function getExcerptAttribute(): string
    {
        return \Illuminate\Support\Str::limit($this->review_text, 150);
    }

    /**
     * Check if the review has a response.
     */
    public function hasResponse(): bool
    {
        return !empty($this->response_text);
    }

    /**
     * Get the time since the review was created.
     */
    public function getTimeSinceAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }
}
