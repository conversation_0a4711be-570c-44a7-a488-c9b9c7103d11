<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BusinessGalleryImage extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'business_gallery_images';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'business_id',
        'category_id',
        'filename',
        'original_name',
        'path',
        'url',
        'mime_type',
        'file_size',
        'width',
        'height',
        'title',
        'alt_text',
        'description',
        'tags',
        'is_featured',
        'is_active',
        'sort_order',
        'metadata',
        'exif_data',
        'uploaded_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'tags' => 'array',
        'metadata' => 'array',
        'exif_data' => 'array',
        'uploaded_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        // Delete file when model is deleted
        static::deleting(function ($image) {
            $image->deleteFile();
        });
    }

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(BusinessGalleryCategory::class, 'category_id');
    }

    // Accessors
    public function getFullUrlAttribute(): string
    {
        if ($this->url) {
            return $this->url;
        }

        return Storage::url($this->path);
    }

    public function getThumbnailUrlAttribute(): string
    {
        // Generate thumbnail path
        $pathInfo = pathinfo($this->path);
        $thumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];

        if (Storage::exists($thumbnailPath)) {
            return Storage::url($thumbnailPath);
        }

        // Fallback to original image
        return $this->full_url;
    }

    public function getMediumUrlAttribute(): string
    {
        // Generate medium size path
        $pathInfo = pathinfo($this->path);
        $mediumPath = $pathInfo['dirname'] . '/medium/' . $pathInfo['filename'] . '_medium.' . $pathInfo['extension'];

        if (Storage::exists($mediumPath)) {
            return Storage::url($mediumPath);
        }

        // Fallback to original image
        return $this->full_url;
    }

    public function getDimensionsAttribute(): array
    {
        return [
            'width' => $this->width,
            'height' => $this->height,
            'aspect_ratio' => $this->width && $this->height ? round($this->width / $this->height, 2) : null,
        ];
    }

    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    // Methods
    public function deleteFile(): bool
    {
        try {
            // Delete original file
            if (Storage::exists($this->path)) {
                Storage::delete($this->path);
            }

            // Delete thumbnails and other sizes
            $pathInfo = pathinfo($this->path);
            $directory = $pathInfo['dirname'];
            $filename = $pathInfo['filename'];
            $extension = $pathInfo['extension'];

            $sizesToDelete = ['thumbnails', 'medium', 'large'];
            foreach ($sizesToDelete as $size) {
                $sizePath = $directory . '/' . $size . '/' . $filename . '_' . substr($size, 0, -1) . '.' . $extension;
                if (Storage::exists($sizePath)) {
                    Storage::delete($sizePath);
                }
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to delete gallery image file', [
                'image_id' => $this->id,
                'path' => $this->path,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function generateThumbnails(): bool
    {
        // This would integrate with an image processing service
        // For now, we'll just return true
        return true;
    }

    public function updateSortOrder(int $newOrder): bool
    {
        $this->sort_order = $newOrder;
        return $this->save();
    }

    public function toggleFeatured(): bool
    {
        $this->is_featured = !$this->is_featured;
        return $this->save();
    }

    public function toggleActive(): bool
    {
        $this->is_active = !$this->is_active;
        return $this->save();
    }
}
