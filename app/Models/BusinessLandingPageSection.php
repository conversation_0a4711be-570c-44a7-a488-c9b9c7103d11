<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessLandingPageSection extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_landing_page_id',
        'section_type',
        'section_name',
        'section_description',
        'content_data',
        'layout_config',
        'style_config',
        'is_visible',
        'sort_order',
        'mobile_config',
        'tablet_config',
        'animation_config',
        'parallax_enabled',
    ];

    protected $casts = [
        'content_data' => 'array',
        'layout_config' => 'array',
        'style_config' => 'array',
        'mobile_config' => 'array',
        'tablet_config' => 'array',
        'animation_config' => 'array',
        'is_visible' => 'boolean',
        'parallax_enabled' => 'boolean',
    ];

    // Relationships
    public function landingPage(): BelongsTo
    {
        return $this->belongsTo(BusinessLandingPage::class, 'business_landing_page_id');
    }

    // Scopes
    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('section_type', $type);
    }

    // Helper methods
    public function getDefaultContentForType()
    {
        $business = $this->landingPage->business;

        switch ($this->section_type) {
            case 'hero':
                return [
                    'title' => $business->name,
                    'subtitle' => $business->description ?? 'Welcome to our business',
                    'background_image' => null,
                    'background_video' => null,
                    'cta_text' => 'Book Now',
                    'cta_url' => '#booking',
                    'secondary_cta_text' => 'Learn More',
                    'secondary_cta_url' => '#about',
                    'overlay_opacity' => 0.5,
                    'text_alignment' => 'center'
                ];

            case 'about':
                return [
                    'title' => 'About ' . $business->name,
                    'content' => $business->description ?? 'Learn more about our business and what makes us special.',
                    'image' => null,
                    'features' => [
                        'Professional service',
                        'Experienced team',
                        'Customer satisfaction'
                    ],
                    'stats' => [
                        ['value' => '100+', 'label' => 'Happy Customers'],
                        ['value' => '5+', 'label' => 'Years Experience'],
                        ['value' => '24/7', 'label' => 'Support']
                    ],
                    'layout' => 'image-left'
                ];

            case 'services':
                return [
                    'title' => 'Our Services',
                    'subtitle' => 'Discover what we offer',
                    'show_prices' => true,
                    'show_duration' => true,
                    'show_description' => true,
                    'layout' => 'grid',
                    'columns' => 3,
                    'featured_services' => []
                ];

            case 'testimonials':
                return [
                    'title' => 'What Our Customers Say',
                    'subtitle' => 'Don\'t just take our word for it',
                    'layout' => 'carousel',
                    'columns' => 3,
                    'show_ratings' => true,
                    'show_photos' => true,
                    'auto_rotate' => true,
                    'rotation_speed' => 5,
                    'max_testimonials' => 6,
                    'min_rating' => 4,
                    'featured_only' => false,
                    'show_service_name' => true,
                    'testimonials' => []
                ];

            case 'gallery':
                return [
                    'title' => 'Gallery',
                    'subtitle' => 'Take a look at our work',
                    'layout' => 'masonry',
                    'columns' => 4,
                    'show_captions' => true,
                    'lightbox_enabled' => true,
                    'lazy_loading' => true,
                    'hover_effects' => true,
                    'show_load_more' => false,
                    'images_per_page' => 12,
                    'image_quality' => 'medium',
                    'filter_enabled' => false,
                    'categories' => [],
                    'images' => []
                ];

            case 'contact':
                return [
                    'title' => 'Get in Touch',
                    'subtitle' => 'We\'d love to hear from you',
                    'show_map' => true,
                    'show_hours' => true,
                    'show_contact_form' => true,
                    'show_social_links' => true,
                    'map_zoom' => 15,
                    'contact_form_fields' => ['name', 'email', 'phone', 'message']
                ];

            case 'team':
                return [
                    'title' => 'Meet Our Team',
                    'subtitle' => 'The people behind our success',
                    'layout' => 'grid',
                    'columns' => 3,
                    'show_bio' => true,
                    'show_experience' => true,
                    'show_specializations' => true,
                    'show_contact' => false,
                    'show_social_links' => false,
                    'show_booking_button' => true,
                    'team_filter' => 'active_only',
                    'sort_order' => 'sort_order',
                    'max_members' => 0,
                    'image_style' => 'circle',
                    'enable_modal' => true,
                    'team_members' => []
                ];

            case 'faq':
                return [
                    'title' => 'Frequently Asked Questions',
                    'subtitle' => 'Find answers to common questions',
                    'layout' => 'accordion',
                    'search_enabled' => true,
                    'categories_enabled' => false,
                    'allow_expand_all' => true,
                    'show_contact_cta' => true,
                    'contact_cta_text' => 'Still have questions? Contact us!',
                    'questions' => [
                        [
                            'question' => 'How do I book an appointment?',
                            'answer' => 'You can book an appointment through our online booking system by clicking the "Book Now" button, calling us directly, or visiting our location.',
                            'category' => 'Booking',
                            'sort_order' => 1,
                            'featured' => false
                        ],
                        [
                            'question' => 'What is your cancellation policy?',
                            'answer' => 'We require at least 24 hours notice for cancellations. Cancellations made less than 24 hours in advance may be subject to a cancellation fee.',
                            'category' => 'Policies',
                            'sort_order' => 2,
                            'featured' => false
                        ]
                    ]
                ];

            case 'features':
                return [
                    'title' => 'Why Choose ' . $business->name,
                    'subtitle' => 'What sets us apart from the competition',
                    'features' => [
                        [
                            'icon' => 'fas fa-star',
                            'title' => 'Quality Service',
                            'description' => 'We provide top-notch service with attention to detail.'
                        ],
                        [
                            'icon' => 'fas fa-clock',
                            'title' => 'Convenient Hours',
                            'description' => 'Flexible scheduling to fit your busy lifestyle.'
                        ],
                        [
                            'icon' => 'fas fa-users',
                            'title' => 'Expert Team',
                            'description' => 'Our experienced professionals are here to help you.'
                        ]
                    ],
                    'layout' => 'grid',
                    'columns' => 3
                ];

            case 'pricing':
                return [
                    'title' => 'Our Pricing',
                    'subtitle' => 'Choose the package that works best for you',
                    'show_features' => true,
                    'show_popular_badge' => true,
                    'show_booking_button' => true,
                    'layout' => 'cards',
                    'columns' => 3,
                    'packages' => [
                        [
                            'name' => 'Basic',
                            'price' => '$99',
                            'period' => 'per service',
                            'description' => 'Perfect for getting started',
                            'features' => ['Feature 1', 'Feature 2', 'Feature 3'],
                            'popular' => false,
                            'button_text' => 'Choose Plan',
                            'button_url' => '#booking'
                        ],
                        [
                            'name' => 'Premium',
                            'price' => '$199',
                            'period' => 'per service',
                            'description' => 'Our most popular package',
                            'features' => ['All Basic features', 'Premium Feature 1', 'Premium Feature 2'],
                            'popular' => true,
                            'button_text' => 'Choose Plan',
                            'button_url' => '#booking'
                        ]
                    ]
                ];

            case 'cta':
                return [
                    'title' => 'Ready to Get Started?',
                    'subtitle' => 'Book your appointment today and experience our exceptional service',
                    'button_text' => 'Book Now',
                    'button_url' => '#booking',
                    'secondary_button_text' => 'Call Us',
                    'secondary_button_url' => 'tel:' . ($business->phone ?? ''),
                    'background_color' => '#007bff',
                    'text_color' => '#ffffff',
                    'background_image' => null,
                    'text_alignment' => 'center',
                    'section_padding' => 'large',
                    'border_radius' => 'medium',
                    'enable_animation' => true,
                    'full_width' => false
                ];

            case 'booking':
                return [
                    'title' => 'Book Your Appointment',
                    'subtitle' => 'Schedule your visit with us',
                    'show_calendar' => true,
                    'show_services' => true,
                    'show_staff' => false,
                    'require_phone' => true,
                    'require_email' => true,
                    'allow_notes' => true,
                    'confirmation_message' => 'Thank you for your booking! We\'ll confirm your appointment shortly.'
                ];

            default:
                return [
                    'title' => 'Custom Section',
                    'content' => 'Add your custom content here.',
                    'layout' => 'default'
                ];
        }
    }

    public function renderContent()
    {
        $content = $this->content_data ?? $this->getDefaultContentForType();
        $business = $this->landingPage->business;

        // Replace dynamic placeholders
        $placeholders = [
            '{{business_name}}' => $business->name,
            '{{business_phone}}' => $business->phone,
            '{{business_email}}' => $business->email,
            '{{business_address}}' => $business->address,
            '{{business_website}}' => $business->website,
        ];

        return $this->replacePlaceholders($content, $placeholders);
    }

    private function replacePlaceholders($content, $placeholders)
    {
        if (is_array($content)) {
            foreach ($content as $key => $value) {
                $content[$key] = $this->replacePlaceholders($value, $placeholders);
            }
        } elseif (is_string($content)) {
            $content = str_replace(array_keys($placeholders), array_values($placeholders), $content);
        }

        return $content;
    }

    public function duplicate()
    {
        $newSection = $this->replicate();
        $newSection->section_name = $this->section_name . ' (Copy)';
        $newSection->sort_order = $this->landingPage->sections()->max('sort_order') + 1;
        $newSection->save();

        return $newSection;
    }
}
