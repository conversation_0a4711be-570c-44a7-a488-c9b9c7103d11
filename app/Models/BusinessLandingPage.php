<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class BusinessLandingPage extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'custom_slug',
        'custom_domain',
        'domain_type',
        'ssl_enabled',
        'page_title',
        'page_description',
        'theme',
        'theme_config',
        'theme_customization',
        'custom_css',
        'last_customized_at',
        'logo_url',
        'branding_config',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'open_graph_config',
        'schema_markup',
        'hero_section',
        'about_section',
        'services_section',
        'contact_section',
        'testimonials_section',
        'gallery_section',
        'custom_sections',
        'booking_enabled',
        'booking_config',
        'booking_button_text',
        'booking_button_color',
        'google_analytics_id',
        'facebook_pixel_id',
        'tracking_codes',
        'cache_enabled',
        'cache_duration',
        'last_generated_at',
        'is_published',
        'is_indexed',
        'published_at',
    ];

    protected $casts = [
        'ssl_enabled' => 'boolean',
        'theme_config' => 'array',
        'theme_customization' => 'array',
        'last_customized_at' => 'datetime',
        'branding_config' => 'array',
        'open_graph_config' => 'array',
        'schema_markup' => 'array',
        'hero_section' => 'array',
        'about_section' => 'array',
        'services_section' => 'array',
        'contact_section' => 'array',
        'testimonials_section' => 'array',
        'gallery_section' => 'array',
        'custom_sections' => 'array',
        'booking_enabled' => 'boolean',
        'booking_config' => 'array',
        'tracking_codes' => 'array',
        'cache_enabled' => 'boolean',
        'is_published' => 'boolean',
        'is_indexed' => 'boolean',
        'last_generated_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($landingPage) {
            if (empty($landingPage->custom_slug)) {
                $landingPage->custom_slug = Str::slug($landingPage->business->name);
            }

            // Ensure unique slug
            $originalSlug = $landingPage->custom_slug;
            $counter = 1;
            while (static::where('custom_slug', $landingPage->custom_slug)->exists()) {
                $landingPage->custom_slug = $originalSlug . '-' . $counter;
                $counter++;
            }
        });

        static::saving(function ($landingPage) {
            if ($landingPage->is_published && !$landingPage->published_at) {
                $landingPage->published_at = now();
            }
        });
    }

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function sections(): HasMany
    {
        return $this->hasMany(BusinessLandingPageSection::class)->orderBy('sort_order');
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeByDomainType($query, $type)
    {
        return $query->where('domain_type', $type);
    }

    // Helper methods
    public function getFullUrlAttribute()
    {
        switch ($this->domain_type) {
            case 'custom':
                return ($this->ssl_enabled ? 'https://' : 'http://') . $this->custom_domain;
            case 'subdomain':
                return ($this->ssl_enabled ? 'https://' : 'http://') . $this->custom_slug . '.bookkei.com';
            default: // subdirectory
                return url('/' . $this->custom_slug);
        }
    }

    public function getRouteKeyName()
    {
        return 'custom_slug';
    }

    public function generateDefaultSections()
    {
        $defaultSections = [
            [
                'section_type' => 'hero',
                'section_name' => 'Hero Section',
                'section_description' => 'Main banner with call-to-action',
                'content_data' => [
                    'title' => $this->business->name,
                    'subtitle' => $this->business->description ?? 'Welcome to our business',
                    'background_image' => null,
                    'cta_text' => 'Book Now',
                    'cta_url' => '#booking',
                    'secondary_cta_text' => 'Learn More',
                    'secondary_cta_url' => '#about'
                ],
                'sort_order' => 1,
                'is_visible' => true
            ],
            [
                'section_type' => 'about',
                'section_name' => 'About Us',
                'section_description' => 'Information about your business',
                'content_data' => [
                    'title' => 'About ' . $this->business->name,
                    'content' => $this->business->description ?? 'Learn more about our business and what makes us special.',
                    'image' => null,
                    'features' => [
                        'Professional service',
                        'Experienced team',
                        'Customer satisfaction'
                    ],
                    'layout' => 'image-left'
                ],
                'sort_order' => 2,
                'is_visible' => true
            ],
            [
                'section_type' => 'services',
                'section_name' => 'Our Services',
                'section_description' => 'Showcase your services and offerings',
                'content_data' => [
                    'title' => 'Our Services',
                    'subtitle' => 'Discover what we offer',
                    'show_prices' => true,
                    'show_duration' => true,
                    'show_descriptions' => true,
                    'layout' => 'grid',
                    'columns' => 3
                ],
                'sort_order' => 3,
                'is_visible' => true
            ],
            [
                'section_type' => 'features',
                'section_name' => 'Why Choose Us',
                'section_description' => 'Highlight your key features and benefits',
                'content_data' => [
                    'title' => 'Why Choose ' . $this->business->name,
                    'subtitle' => 'What sets us apart from the competition',
                    'features' => [
                        [
                            'icon' => 'fas fa-star',
                            'title' => 'Quality Service',
                            'description' => 'We provide top-notch service with attention to detail.'
                        ],
                        [
                            'icon' => 'fas fa-clock',
                            'title' => 'Convenient Hours',
                            'description' => 'Flexible scheduling to fit your busy lifestyle.'
                        ],
                        [
                            'icon' => 'fas fa-users',
                            'title' => 'Expert Team',
                            'description' => 'Our experienced professionals are here to help you.'
                        ]
                    ],
                    'layout' => 'grid'
                ],
                'sort_order' => 4,
                'is_visible' => true
            ],
            [
                'section_type' => 'testimonials',
                'section_name' => 'Customer Reviews',
                'section_description' => 'Display customer testimonials and reviews',
                'content_data' => [
                    'title' => 'What Our Customers Say',
                    'subtitle' => 'Read reviews from our satisfied clients',
                    'show_ratings' => true,
                    'show_photos' => true,
                    'layout' => 'carousel',
                    'auto_rotate' => true
                ],
                'sort_order' => 5,
                'is_visible' => true
            ],
            [
                'section_type' => 'team',
                'section_name' => 'Our Team',
                'section_description' => 'Meet your professional team members',
                'content_data' => [
                    'title' => 'Meet Our Team',
                    'subtitle' => 'Get to know the professionals who will serve you',
                    'show_bio' => true,
                    'show_experience' => true,
                    'show_specializations' => true,
                    'layout' => 'grid',
                    'columns' => 3
                ],
                'sort_order' => 6,
                'is_visible' => true
            ],
            [
                'section_type' => 'gallery',
                'section_name' => 'Gallery',
                'section_description' => 'Showcase your work and facilities',
                'content_data' => [
                    'title' => 'Our Work',
                    'subtitle' => 'Take a look at our facilities and previous work',
                    'layout' => 'masonry',
                    'columns' => 4,
                    'lightbox_enabled' => true,
                    'show_captions' => true
                ],
                'sort_order' => 7,
                'is_visible' => false
            ],
            [
                'section_type' => 'pricing',
                'section_name' => 'Pricing',
                'section_description' => 'Display your pricing packages',
                'content_data' => [
                    'title' => 'Our Pricing',
                    'subtitle' => 'Choose the package that works best for you',
                    'show_features' => true,
                    'show_popular_badge' => true,
                    'layout' => 'cards',
                    'columns' => 3
                ],
                'sort_order' => 8,
                'is_visible' => false
            ],
            [
                'section_type' => 'faq',
                'section_name' => 'FAQ',
                'section_description' => 'Frequently asked questions',
                'content_data' => [
                    'title' => 'Frequently Asked Questions',
                    'subtitle' => 'Find answers to common questions',
                    'layout' => 'accordion',
                    'search_enabled' => true,
                    'categories' => []
                ],
                'sort_order' => 9,
                'is_visible' => false
            ],
            [
                'section_type' => 'cta',
                'section_name' => 'Call to Action',
                'section_description' => 'Encourage visitors to take action',
                'content_data' => [
                    'title' => 'Ready to Get Started?',
                    'subtitle' => 'Book your appointment today and experience our exceptional service',
                    'button_text' => 'Book Now',
                    'button_url' => '#booking',
                    'secondary_button_text' => 'Call Us',
                    'secondary_button_url' => 'tel:' . ($this->business->phone ?? ''),
                    'background_color' => '#007bff',
                    'text_color' => '#ffffff'
                ],
                'sort_order' => 10,
                'is_visible' => true
            ],
            [
                'section_type' => 'contact',
                'section_name' => 'Contact Us',
                'section_description' => 'Contact information and form',
                'content_data' => [
                    'title' => 'Get in Touch',
                    'subtitle' => 'We\'d love to hear from you',
                    'show_map' => true,
                    'show_hours' => true,
                    'show_contact_form' => true,
                    'show_social_links' => true,
                    'map_zoom' => 15
                ],
                'sort_order' => 11,
                'is_visible' => true
            ]
        ];

        foreach ($defaultSections as $sectionData) {
            $this->sections()->create($sectionData);
        }
    }

    public function generateSitemap()
    {
        // Generate XML sitemap for the landing page
        $urls = [
            [
                'url' => $this->full_url,
                'lastmod' => $this->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '1.0'
            ]
        ];

        // Add service pages if they exist
        if ($this->business->services()->where('is_active', true)->exists()) {
            $urls[] = [
                'url' => $this->full_url . '/services',
                'lastmod' => $this->business->services()->max('updated_at'),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ];
        }

        return $urls;
    }
}
