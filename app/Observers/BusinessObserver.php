<?php

namespace App\Observers;

use App\Models\Business;
use App\Services\LandingPageCacheService;

class BusinessObserver
{
    protected $cacheService;

    public function __construct(LandingPageCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle the Business "updated" event.
     */
    public function updated(Business $business): void
    {
        $this->clearBusinessCache($business);
    }

    /**
     * Handle the Business "deleted" event.
     */
    public function deleted(Business $business): void
    {
        $this->clearBusinessCache($business);
    }

    /**
     * Clear cache for the business.
     */
    private function clearBusinessCache(Business $business): void
    {
        $this->cacheService->clearBusinessCache(
            $business->id,
            $business->landing_page_slug
        );
    }
}
