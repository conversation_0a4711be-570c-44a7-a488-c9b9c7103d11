<?php

namespace App\Observers;

use App\Models\Service;
use App\Services\LandingPageCacheService;

class ServiceObserver
{
    protected $cacheService;

    public function __construct(LandingPageCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle the Service "created" event.
     */
    public function created(Service $service): void
    {
        $this->clearServiceCache($service);
    }

    /**
     * Handle the Service "updated" event.
     */
    public function updated(Service $service): void
    {
        $this->clearServiceCache($service);
    }

    /**
     * Handle the Service "deleted" event.
     */
    public function deleted(Service $service): void
    {
        $this->clearServiceCache($service);
    }

    /**
     * Clear cache for the service's business.
     */
    private function clearServiceCache(Service $service): void
    {
        $business = $service->business;
        
        if ($business) {
            $this->cacheService->clearServicesCache($business->id);
            $this->cacheService->clearLandingPageCache($business->landing_page_slug);
        }
    }
}
