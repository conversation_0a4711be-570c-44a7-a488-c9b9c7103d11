<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\Staff;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $business = $this->getUserBusiness();

        if (!$business) {
            return redirect()->route('owner.business.create')
                ->with('error', 'Please create your business first.');
        }

        $staff = Staff::where('business_id', $business->id)->get();

        return view('owner.staff.index', compact('staff', 'business'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('owner.staff.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $business = $this->getUserBusiness();

        if (!$business) {
            return redirect()->route('owner.business.create')
                ->with('error', 'Please create your business first.');
        }

        // Staff creation logic would go here
        return redirect()->route('owner.staff.index')
            ->with('success', 'Staff member added successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Staff $staff)
    {
        $this->authorizeStaff($staff);
        return view('owner.staff.show', compact('staff'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Staff $staff)
    {
        $this->authorizeStaff($staff);
        return view('owner.staff.edit', compact('staff'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Staff $staff)
    {
        $this->authorizeStaff($staff);

        // Staff update logic would go here
        return redirect()->route('owner.staff.index')
            ->with('success', 'Staff member updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Staff $staff)
    {
        $this->authorizeStaff($staff);

        $staff->delete();

        return redirect()->route('owner.staff.index')
            ->with('success', 'Staff member deleted successfully!');
    }

    /**
     * Get user's business.
     */
    private function getUserBusiness()
    {
        return Business::where('owner_id', Auth::id())->first();
    }

    /**
     * Authorize staff access.
     */
    private function authorizeStaff(Staff $staff)
    {
        $business = $this->getUserBusiness();

        if (!$business || $staff->business_id !== $business->id) {
            abort(403, 'Unauthorized access to this staff member.');
        }
    }
}
