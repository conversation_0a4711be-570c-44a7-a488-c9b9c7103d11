<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Review;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReviewController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();

        if (!$business) {
            return redirect()->route('owner.business.create')
                ->with('error', 'Please create your business first.');
        }

        $query = Review::where('business_id', $business->id)
            ->with(['service', 'customer']);

        // Apply filters
        if ($request->filled('rating')) {
            $query->where('rating', $request->rating);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('service_id')) {
            $query->where('service_id', $request->service_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('review_text', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        $reviews = $query->orderBy('created_at', 'desc')->paginate(15);

        $services = $business->services()->where('is_active', true)->get();

        $stats = [
            'total_reviews' => Review::where('business_id', $business->id)->count(),
            'average_rating' => Review::where('business_id', $business->id)->avg('rating'),
            'pending_reviews' => Review::where('business_id', $business->id)->where('status', 'pending')->count(),
            'featured_reviews' => Review::where('business_id', $business->id)->where('is_featured', true)->count(),
        ];

        return view('owner.reviews.index', compact('reviews', 'services', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $business = $this->getUserBusiness();

        if (!$business) {
            return redirect()->route('owner.business.create')
                ->with('error', 'Please create your business first.');
        }

        $services = $business->services()->where('is_active', true)->get();

        return view('owner.reviews.create', compact('services'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $business = $this->getUserBusiness();

        if (!$business) {
            return redirect()->route('owner.business.create')
                ->with('error', 'Please create your business first.');
        }

        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'service_id' => 'required|exists:services,id',
            'rating' => 'required|integer|min:1|max:5',
            'review_text' => 'required|string|max:1000',
            'is_featured' => 'boolean',
            'status' => 'required|in:pending,approved,rejected'
        ]);

        $validated['business_id'] = $business->id;
        $validated['is_featured'] = $request->has('is_featured');

        Review::create($validated);

        return redirect()->route('owner.reviews.index')
            ->with('success', 'Review created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Review $review)
    {
        $this->authorizeReview($review);

        $review->load(['service', 'customer']);

        return view('owner.reviews.show', compact('review'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Review $review)
    {
        $this->authorizeReview($review);

        $business = $this->getUserBusiness();
        $services = $business->services()->where('is_active', true)->get();

        return view('owner.reviews.edit', compact('review', 'services'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Review $review)
    {
        $this->authorizeReview($review);

        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'service_id' => 'required|exists:services,id',
            'rating' => 'required|integer|min:1|max:5',
            'review_text' => 'required|string|max:1000',
            'is_featured' => 'boolean',
            'status' => 'required|in:pending,approved,rejected'
        ]);

        $validated['is_featured'] = $request->has('is_featured');

        $review->update($validated);

        return redirect()->route('owner.reviews.index')
            ->with('success', 'Review updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Review $review)
    {
        $this->authorizeReview($review);

        $review->delete();

        return redirect()->route('owner.reviews.index')
            ->with('success', 'Review deleted successfully!');
    }

    /**
     * Toggle review featured status.
     */
    public function toggleFeatured(Review $review)
    {
        $this->authorizeReview($review);

        $review->update(['is_featured' => !$review->is_featured]);

        return response()->json([
            'success' => true,
            'is_featured' => $review->is_featured,
            'message' => $review->is_featured ? 'Review marked as featured' : 'Review removed from featured'
        ]);
    }

    /**
     * Update review status.
     */
    public function updateStatus(Request $request, Review $review)
    {
        $this->authorizeReview($review);

        $validated = $request->validate([
            'status' => 'required|in:pending,approved,rejected'
        ]);

        $review->update($validated);

        return response()->json([
            'success' => true,
            'status' => $review->status,
            'message' => "Review {$review->status} successfully"
        ]);
    }

    /**
     * Get user's business.
     */
    private function getUserBusiness()
    {
        return Business::where('owner_id', Auth::id())->first();
    }

    /**
     * Authorize review access.
     */
    private function authorizeReview(Review $review)
    {
        $business = $this->getUserBusiness();

        if (!$business || $review->business_id !== $business->id) {
            abort(403, 'Unauthorized access to this review.');
        }
    }
}
