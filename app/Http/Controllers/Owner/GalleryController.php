<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\Business;
use App\Models\BusinessGalleryImage;
use App\Models\BusinessGalleryCategory;
use App\Services\GalleryImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GalleryController extends Controller
{
    protected $galleryService;

    public function __construct(GalleryImageService $galleryService)
    {
        $this->galleryService = $galleryService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();

        if (!$business) {
            return redirect()->route('owner.business.create')
                ->with('error', 'Please create your business first.');
        }

        // Get filter parameters
        $categoryId = $request->get('category');
        $search = $request->get('search');
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        // Build query
        $query = $business->galleryImages()->with('category');

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('alt_text', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $query->orderBy($sortBy, $sortOrder);

        $images = $query->paginate(24);
        $categories = $business->galleryCategories()->ordered()->get();

        return view('owner.gallery.index', compact('business', 'images', 'categories', 'categoryId', 'search', 'sortBy', 'sortOrder'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $business = $this->getUserBusiness();
        $categories = $business->galleryCategories()->ordered()->get();

        return view('owner.gallery.create', compact('business', 'categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'images' => 'required|array|min:1',
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:10240', // 10MB max
            'category_id' => 'nullable|exists:business_gallery_categories,id',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'tags' => 'nullable|string',
            'is_featured' => 'nullable|boolean',
        ]);

        // Validate category belongs to business
        if ($validated['category_id']) {
            $category = BusinessGalleryCategory::where('id', $validated['category_id'])
                ->where('business_id', $business->id)
                ->first();

            if (!$category) {
                return back()->withErrors(['category_id' => 'Invalid category selected.']);
            }
        }

        try {
            $options = [
                'category_id' => $validated['category_id'] ?? null,
                'title' => $validated['title'] ?? null,
                'description' => $validated['description'] ?? null,
                'tags' => $validated['tags'] ? explode(',', $validated['tags']) : [],
                'is_featured' => $validated['is_featured'] ?? false,
            ];

            $result = $this->galleryService->uploadImages($business, $request->file('images'), $options);

            $message = count($result['uploaded']) . ' image(s) uploaded successfully!';
            if (!empty($result['errors'])) {
                $message .= ' ' . count($result['errors']) . ' image(s) failed to upload.';
            }

            return redirect()->route('owner.gallery.index')->with('success', $message);

        } catch (\Exception $e) {
            return back()->withErrors(['upload' => 'Failed to upload images: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(BusinessGalleryImage $image)
    {
        $business = $this->getUserBusiness();

        // Ensure image belongs to the business
        if ($image->business_id !== $business->id) {
            abort(404);
        }

        return view('owner.gallery.show', compact('business', 'image'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BusinessGalleryImage $image)
    {
        $business = $this->getUserBusiness();

        // Ensure image belongs to the business
        if ($image->business_id !== $business->id) {
            abort(404);
        }

        $categories = $business->galleryCategories()->ordered()->get();

        return view('owner.gallery.edit', compact('business', 'image', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BusinessGalleryImage $image)
    {
        $business = $this->getUserBusiness();

        // Ensure image belongs to the business
        if ($image->business_id !== $business->id) {
            abort(404);
        }

        $validated = $request->validate([
            'title' => 'nullable|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category_id' => 'nullable|exists:business_gallery_categories,id',
            'tags' => 'nullable|string',
            'is_featured' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
        ]);

        // Validate category belongs to business
        if ($validated['category_id']) {
            $category = BusinessGalleryCategory::where('id', $validated['category_id'])
                ->where('business_id', $business->id)
                ->first();

            if (!$category) {
                return back()->withErrors(['category_id' => 'Invalid category selected.']);
            }
        }

        // Process tags
        if (isset($validated['tags'])) {
            $validated['tags'] = explode(',', $validated['tags']);
        }

        try {
            $this->galleryService->updateImage($image, $validated);
            return redirect()->route('owner.gallery.index')->with('success', 'Image updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['update' => 'Failed to update image: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BusinessGalleryImage $image)
    {
        $business = $this->getUserBusiness();

        // Ensure image belongs to the business
        if ($image->business_id !== $business->id) {
            abort(404);
        }

        try {
            $this->galleryService->deleteImage($image);
            return redirect()->route('owner.gallery.index')->with('success', 'Image deleted successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['delete' => 'Failed to delete image: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle image featured status.
     */
    public function toggleFeatured(BusinessGalleryImage $image)
    {
        $business = $this->getUserBusiness();

        // Ensure image belongs to the business
        if ($image->business_id !== $business->id) {
            abort(404);
        }

        try {
            $image->toggleFeatured();
            $status = $image->is_featured ? 'featured' : 'unfeatured';
            return response()->json(['success' => true, 'message' => "Image {$status} successfully!"]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update image status.'], 500);
        }
    }

    /**
     * Toggle image active status.
     */
    public function toggleActive(BusinessGalleryImage $image)
    {
        $business = $this->getUserBusiness();

        // Ensure image belongs to the business
        if ($image->business_id !== $business->id) {
            abort(404);
        }

        try {
            $image->toggleActive();
            $status = $image->is_active ? 'activated' : 'deactivated';
            return response()->json(['success' => true, 'message' => "Image {$status} successfully!"]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update image status.'], 500);
        }
    }

    /**
     * Update image order.
     */
    public function updateOrder(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'images' => 'required|array',
            'images.*.id' => 'required|exists:business_gallery_images,id',
            'images.*.sort_order' => 'required|integer|min:0',
        ]);

        try {
            $this->galleryService->updateImageOrder($business, $validated['images']);
            return response()->json(['success' => true, 'message' => 'Image order updated successfully!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update image order.'], 500);
        }
    }

    /**
     * Bulk delete images.
     */
    public function bulkDelete(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'image_ids' => 'required|array|min:1',
            'image_ids.*' => 'required|exists:business_gallery_images,id',
        ]);

        try {
            $deletedCount = 0;
            foreach ($validated['image_ids'] as $imageId) {
                $image = BusinessGalleryImage::where('id', $imageId)
                    ->where('business_id', $business->id)
                    ->first();

                if ($image && $this->galleryService->deleteImage($image)) {
                    $deletedCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "{$deletedCount} image(s) deleted successfully!"
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to delete images.'], 500);
        }
    }

    /**
     * Get gallery statistics.
     */
    public function getStats()
    {
        $business = $this->getUserBusiness();

        $stats = [
            'total_images' => $business->galleryImages()->count(),
            'active_images' => $business->activeGalleryImages()->count(),
            'featured_images' => $business->featuredGalleryImages()->count(),
            'total_categories' => $business->galleryCategories()->count(),
            'total_storage' => $business->galleryImages()->sum('file_size'),
        ];

        return response()->json($stats);
    }
}
