<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessLandingPage;
use App\Models\BusinessSeoSettings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class BusinessLandingPageController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'business.isolation']);
    }

    /**
     * Show the landing page management dashboard
     */
    public function index()
    {
        $user = Auth::user();
        $business = $user->business;

        // Check if user has a business
        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first before setting up a landing page.');
        }

        $landingPage = $business->landingPage;

        if (!$landingPage) {
            return redirect()->route('owner.landing-page.create')
                ->with('info', 'Create your business landing page to get started.');
        }

        $analytics = $this->getLandingPageAnalytics($landingPage);

        return view('owner.landing-page.index', compact('business', 'landingPage', 'analytics'));
    }

    /**
     * Show the form for creating a new landing page
     */
    public function create()
    {
        $user = Auth::user();
        $business = $user->business;

        // Check if user has a business
        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first before setting up a landing page.');
        }

        if ($business->landingPage) {
            return redirect()->route('owner.landing-page.index')
                ->with('info', 'You already have a landing page. You can edit it from the dashboard.');
        }

        $themes = $this->getAvailableThemes();
        $suggestedSlug = Str::slug($business->name);

        return view('owner.landing-page.create', compact('business', 'themes', 'suggestedSlug'));
    }

    /**
     * Store a newly created landing page
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        // Check if user has a business
        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first before setting up a landing page.');
        }

        if ($business->landingPage) {
            return redirect()->route('owner.landing-page.index')
                ->with('error', 'You already have a landing page.');
        }

        $validated = $request->validate([
            'custom_slug' => [
                'required',
                'string',
                'min:3',
                'max:50',
                'regex:/^[a-z0-9-]+$/',
                'unique:business_landing_pages,custom_slug',
                function ($attribute, $value, $fail) {
                    $reservedSlugs = ['admin', 'api', 'www', 'mail', 'ftp', 'blog', 'shop', 'store', 'app', 'dashboard'];
                    if (in_array($value, $reservedSlugs)) {
                        $fail('This slug is reserved and cannot be used.');
                    }
                }
            ],
            'page_title' => 'required|string|max:255',
            'page_description' => 'nullable|string|max:500',
            'theme' => 'required|string|in:default,modern,elegant,minimal,creative',
            'domain_type' => 'required|in:subdirectory,subdomain,custom',
            'custom_domain' => 'nullable|required_if:domain_type,custom|string|max:255',
            'booking_enabled' => 'boolean',
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        DB::transaction(function () use ($validated, $business) {
            // Create landing page
            $landingPage = BusinessLandingPage::create([
                'business_id' => $business->id,
                'custom_slug' => $validated['custom_slug'],
                'page_title' => $validated['page_title'],
                'page_description' => $validated['page_description'],
                'theme' => $validated['theme'],
                'domain_type' => $validated['domain_type'],
                'custom_domain' => $validated['custom_domain'] ?? null,
                'booking_enabled' => $validated['booking_enabled'] ?? true,
                'meta_title' => $validated['meta_title'],
                'meta_description' => $validated['meta_description'],
                'meta_keywords' => $validated['meta_keywords'],
                'is_published' => false, // Start as draft
            ]);

            // Generate default sections
            $landingPage->generateDefaultSections();

            // Create SEO settings
            BusinessSeoSettings::create([
                'business_id' => $business->id,
                'meta_title' => $validated['meta_title'] ?? $validated['page_title'],
                'meta_description' => $validated['meta_description'] ?? $validated['page_description'],
                'meta_keywords' => $validated['meta_keywords'],
                'business_type' => $this->getBusinessTypeFromCategory($business),
            ]);

            // Update business landing page fields
            $business->update([
                'landing_page_enabled' => true,
                'landing_page_slug' => $validated['custom_slug'],
                'landing_page_status' => 'draft',
                'landing_page_theme' => $validated['theme'],
                'landing_page_last_updated' => now(),
            ]);
        });

        return redirect()->route('owner.landing-page.edit')
            ->with('success', 'Landing page created successfully! You can now customize it and publish when ready.');
    }

    /**
     * Show the form for editing the landing page
     */
    public function edit()
    {
        $user = Auth::user();
        $business = $user->business;

        // Check if user has a business
        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first before setting up a landing page.');
        }

        $landingPage = $business->landingPage;

        if (!$landingPage) {
            return redirect()->route('owner.landing-page.create');
        }

        $themes = $this->getAvailableThemes();
        $sections = $landingPage->sections()->ordered()->get();

        return view('owner.landing-page.edit', compact('business', 'landingPage', 'themes', 'sections'));
    }

    /**
     * Update the landing page
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        // Check if user has a business
        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first before setting up a landing page.');
        }

        $landingPage = $business->landingPage;

        if (!$landingPage) {
            return redirect()->route('owner.landing-page.create');
        }

        $validated = $request->validate([
            'page_title' => 'required|string|max:255',
            'page_description' => 'nullable|string|max:500',
            'theme' => 'required|string|in:default,modern,elegant,minimal,creative',
            'booking_enabled' => 'boolean',
            'booking_button_text' => 'nullable|string|max:50',
            'booking_button_color' => 'nullable|string|max:7',
        ]);

        $landingPage->update($validated);

        $business->update([
            'landing_page_theme' => $validated['theme'],
            'landing_page_last_updated' => now(),
        ]);

        return redirect()->back()->with('success', 'Landing page updated successfully!');
    }

    /**
     * Publish the landing page
     */
    public function publish()
    {
        $user = Auth::user();
        $business = $user->business;

        // Check if user has a business
        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first before setting up a landing page.');
        }

        $landingPage = $business->landingPage;

        if (!$landingPage) {
            return redirect()->route('owner.landing-page.create');
        }

        $landingPage->update([
            'is_published' => true,
            'published_at' => now(),
        ]);

        $business->update([
            'landing_page_status' => 'published',
        ]);

        return redirect()->back()->with('success', 'Landing page published successfully! It\'s now live at: ' . $landingPage->full_url);
    }

    /**
     * Show the form for editing a specific section
     */
    public function editSection($sectionId)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first.');
        }

        $landingPage = $business->landingPage;
        if (!$landingPage) {
            return redirect()->route('owner.landing-page.create');
        }

        $section = $landingPage->sections()->findOrFail($sectionId);

        return view('owner.landing-page.sections.edit', compact('business', 'landingPage', 'section'));
    }

    /**
     * Upload background image for a section
     */
    public function uploadSectionImage(Request $request, $sectionId)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return response()->json([
                'success' => false,
                'message' => 'You need to create a business first.'
            ], 403);
        }

        $landingPage = $business->landingPage;
        if (!$landingPage) {
            return response()->json([
                'success' => false,
                'message' => 'Landing page not found.'
            ], 404);
        }

        $section = $landingPage->sections()->findOrFail($sectionId);

        $validated = $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        ]);

        try {
            // Delete old background image if exists
            $currentContent = $section->content_data ?? [];
            if (!empty($currentContent['background_image'])) {
                $oldImagePath = str_replace('/storage/', '', $currentContent['background_image']);
                if (Storage::disk('public')->exists($oldImagePath)) {
                    Storage::disk('public')->delete($oldImagePath);
                }
            }

            // Store new image
            $imagePath = $request->file('image')->store('landing-page-sections', 'public');
            $imageUrl = Storage::url($imagePath);

            // Update section content with new image URL
            $contentData = $section->content_data ?? [];
            $contentData['background_image'] = $imageUrl;

            $section->update([
                'content_data' => $contentData
            ]);

            // Update landing page timestamp
            $landingPage->touch();

            return response()->json([
                'success' => true,
                'message' => 'Background image uploaded successfully!',
                'image_url' => $imageUrl
            ]);

        } catch (\Exception $e) {
            \Log::error('Section image upload failed', [
                'section_id' => $sectionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image. Please try again.'
            ], 500);
        }
    }

    /**
     * Update a specific section
     */
    public function updateSection(Request $request, $sectionId)
    {
        try {
            $user = Auth::user();
            $business = $user->business;

            if (!$business) {
                \Log::error('Business not found for user', ['user_id' => $user->id]);
                return response()->json(['error' => 'Business not found'], 404);
            }

            $landingPage = $business->landingPage;
            if (!$landingPage) {
                \Log::error('Landing page not found for business', ['business_id' => $business->id]);
                return response()->json(['error' => 'Landing page not found'], 404);
            }

            $section = $landingPage->sections()->find($sectionId);
            if (!$section) {
                \Log::error('Section not found', ['section_id' => $sectionId, 'landing_page_id' => $landingPage->id]);
                return response()->json(['error' => 'Section not found'], 404);
            }

            // Log the incoming request data for debugging
            \Log::info('Section update request', [
                'section_id' => $sectionId,
                'section_type' => $section->section_type,
                'request_data' => $request->all()
            ]);

            // Special logging for testimonials section
            if ($section->section_type === 'testimonials') {
                \Log::info('Testimonials section specific data', [
                    'section_id' => $sectionId,
                    'layout' => $request->get('layout'),
                    'rotation_speed' => $request->get('rotation_speed'),
                    'show_ratings' => $request->get('show_ratings'),
                    'show_photos' => $request->get('show_photos'),
                    'auto_rotate' => $request->get('auto_rotate'),
                    'max_testimonials' => $request->get('max_testimonials'),
                    'min_rating' => $request->get('min_rating'),
                    'featured_only' => $request->get('featured_only'),
                    'show_service_name' => $request->get('show_service_name'),
                    'current_content' => $section->content_data
                ]);
            }

            // Special logging for team section
            if ($section->section_type === 'team') {
                \Log::info('Team section specific data', [
                    'section_id' => $sectionId,
                    'layout' => $request->get('layout'),
                    'columns' => $request->get('columns'),
                    'show_bio' => $request->get('show_bio'),
                    'show_experience' => $request->get('show_experience'),
                    'show_specializations' => $request->get('show_specializations'),
                    'show_contact' => $request->get('show_contact'),
                    'show_social_links' => $request->get('show_social_links'),
                    'show_booking_button' => $request->get('show_booking_button'),
                    'team_filter' => $request->get('team_filter'),
                    'sort_order' => $request->get('sort_order'),
                    'max_members' => $request->get('max_members'),
                    'image_style' => $request->get('image_style'),
                    'enable_modal' => $request->get('enable_modal'),
                    'current_content' => $section->content_data
                ]);
            }

            // Special logging for gallery section
            if ($section->section_type === 'gallery') {
                \Log::info('Gallery section specific data', [
                    'section_id' => $sectionId,
                    'layout' => $request->get('layout'),
                    'columns' => $request->get('columns'),
                    'show_captions' => $request->get('show_captions'),
                    'lightbox_enabled' => $request->get('lightbox_enabled'),
                    'lazy_loading' => $request->get('lazy_loading'),
                    'hover_effects' => $request->get('hover_effects'),
                    'show_load_more' => $request->get('show_load_more'),
                    'filter_enabled' => $request->get('filter_enabled'),
                    'images_per_page' => $request->get('images_per_page'),
                    'image_quality' => $request->get('image_quality'),
                    'categories' => $request->get('categories'),
                    'current_content' => $section->content_data
                ]);
            }

            // Special logging for pricing section
            if ($section->section_type === 'pricing') {
                $packages = $request->get('packages', []);
                \Log::info('Pricing section specific data', [
                    'section_id' => $sectionId,
                    'layout' => $request->get('layout'),
                    'columns' => $request->get('columns'),
                    'show_features' => $request->get('show_features'),
                    'show_popular_badge' => $request->get('show_popular_badge'),
                    'show_booking_button' => $request->get('show_booking_button'),
                    'packages_count' => count($packages),
                    'packages_raw' => $packages,
                    'packages_processed' => isset($validated['packages']) ? $validated['packages'] : 'not_yet_processed',
                    'current_content' => $section->content_data
                ]);
            }

            // Special logging for FAQ section
            if ($section->section_type === 'faq') {
                $questions = $request->get('questions', []);
                \Log::info('FAQ section specific data', [
                    'section_id' => $sectionId,
                    'layout' => $request->get('layout'),
                    'search_enabled' => $request->get('search_enabled'),
                    'allow_expand_all' => $request->get('allow_expand_all'),
                    'show_contact_cta' => $request->get('show_contact_cta'),
                    'contact_cta_text' => $request->get('contact_cta_text'),
                    'questions_count' => count($questions),
                    'questions_raw' => $questions,
                    'questions_processed' => isset($validated['questions']) ? $validated['questions'] : 'not_yet_processed',
                    'current_content' => $section->content_data
                ]);
            }

            // Special logging for CTA section
            if ($section->section_type === 'cta') {
                \Log::info('CTA section specific data', [
                    'section_id' => $sectionId,
                    'background_color' => $request->get('background_color'),
                    'background_color_text' => $request->get('background_color_text'),
                    'text_color' => $request->get('text_color'),
                    'text_color_text' => $request->get('text_color_text'),
                    'background_image' => $request->get('background_image'),
                    'text_alignment' => $request->get('text_alignment'),
                    'section_padding' => $request->get('section_padding'),
                    'border_radius' => $request->get('border_radius'),
                    'enable_animation' => $request->get('enable_animation'),
                    'full_width' => $request->get('full_width'),
                    'current_content' => $section->content_data
                ]);
            }

            // Validate based on section type
            $rules = $this->getSectionValidationRules($section->section_type);

            // Log validation rules for debugging
            \Log::info('Section validation rules', [
                'section_id' => $sectionId,
                'section_type' => $section->section_type,
                'rules' => $rules,
                'request_data_keys' => array_keys($request->all())
            ]);

            $validated = $request->validate($rules);

            // Special processing for pricing section packages
            if ($section->section_type === 'pricing' && isset($validated['packages'])) {
                foreach ($validated['packages'] as $index => $package) {
                    // Convert features string to array
                    if (isset($package['features']) && is_string($package['features'])) {
                        $features = array_filter(
                            array_map('trim', explode("\n", $package['features'])),
                            function($feature) {
                                return !empty($feature);
                            }
                        );
                        $validated['packages'][$index]['features'] = array_values($features);
                    }

                    // Ensure boolean fields are properly converted
                    $validated['packages'][$index]['popular'] = isset($package['popular']) && $package['popular'] == '1';
                }
            }

            // Special processing for FAQ section questions
            if ($section->section_type === 'faq' && isset($validated['questions'])) {
                foreach ($validated['questions'] as $index => $question) {
                    // Ensure boolean fields are properly converted
                    $validated['questions'][$index]['featured'] = isset($question['featured']) && $question['featured'] == '1';

                    // Ensure sort_order is an integer
                    if (isset($question['sort_order'])) {
                        $validated['questions'][$index]['sort_order'] = (int) $question['sort_order'];
                    }
                }
            }

            // Special processing for CTA section
            if ($section->section_type === 'cta') {
                // Sync color picker values with text inputs (prefer text input values)
                if (isset($validated['background_color_text']) && !empty($validated['background_color_text'])) {
                    $validated['background_color'] = $validated['background_color_text'];
                }
                if (isset($validated['text_color_text']) && !empty($validated['text_color_text'])) {
                    $validated['text_color'] = $validated['text_color_text'];
                }

                // Remove the duplicate text fields from final data
                unset($validated['background_color_text'], $validated['text_color_text']);

                // Ensure boolean fields are properly converted
                $validated['enable_animation'] = isset($validated['enable_animation']) && $validated['enable_animation'] == '1';
                $validated['full_width'] = isset($validated['full_width']) && $validated['full_width'] == '1';
            }

            // Log validated data
            \Log::info('Validated section data', ['validated' => $validated]);

            // Log checkbox data for debugging
            \Log::info('Checkbox data', [
                'is_visible_raw' => $request->get('is_visible'),
                'is_visible_boolean' => $request->boolean('is_visible', false),
                'all_request_data' => $request->all()
            ]);

            // Update section content
            $updateData = [
                'content_data' => $validated,
                'is_visible' => $request->boolean('is_visible', false),
            ];

            $section->update($updateData);

            // Update landing page timestamp
            $landingPage->touch();

            // Log successful update
            \Log::info('Section updated successfully', [
                'section_id' => $sectionId,
                'updated_data' => $updateData
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Section updated successfully!',
                    'section' => $section->fresh()
                ]);
            }

            return redirect()->route('owner.landing-page.edit')
                ->with('success', 'Section updated successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation error in section update', [
                'section_id' => $sectionId,
                'section_type' => $section->section_type ?? 'unknown',
                'errors' => $e->errors(),
                'request_data' => $request->all(),
                'validation_rules' => $this->getSectionValidationRules($section->section_type ?? 'default')
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed. Please check your input and try again.',
                    'errors' => $e->errors(),
                    'debug_info' => config('app.debug') ? [
                        'section_type' => $section->section_type ?? 'unknown',
                        'validation_rules' => array_keys($this->getSectionValidationRules($section->section_type ?? 'default'))
                    ] : null
                ], 422);
            }

            return redirect()->back()->withErrors($e->errors())->withInput();

        } catch (\Exception $e) {
            \Log::error('Error updating section', [
                'section_id' => $sectionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while updating the section',
                    'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'An error occurred while updating the section')
                ->withInput();
        }
    }

    /**
     * Get section content for AJAX requests
     */
    public function getSectionContent($sectionId)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $landingPage = $business->landingPage;
        if (!$landingPage) {
            return response()->json(['error' => 'Landing page not found'], 404);
        }

        $section = $landingPage->sections()->findOrFail($sectionId);

        return response()->json([
            'section' => $section,
            'content' => $section->content_data ?? $section->getDefaultContentForType(),
            'validation_rules' => $this->getSectionValidationRules($section->section_type)
        ]);
    }

    /**
     * Unpublish the landing page
     */
    public function unpublish()
    {
        $user = Auth::user();
        $business = $user->business;

        // Check if user has a business
        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first before setting up a landing page.');
        }

        $landingPage = $business->landingPage;

        if (!$landingPage) {
            return redirect()->route('owner.landing-page.create');
        }

        $landingPage->update([
            'is_published' => false,
        ]);

        $business->update([
            'landing_page_status' => 'draft',
        ]);

        return redirect()->back()->with('success', 'Landing page unpublished successfully!');
    }

    /**
     * Preview the landing page
     */
    public function preview()
    {
        $user = Auth::user();
        $business = $user->business;

        // Check if user has a business
        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first before setting up a landing page.');
        }

        $landingPage = $business->landingPage;

        if (!$landingPage) {
            return redirect()->route('owner.landing-page.create');
        }

        return view('landing-page.preview', compact('business', 'landingPage'));
    }

    /**
     * Check slug availability
     */
    public function checkSlug(Request $request)
    {
        $slug = $request->get('slug');
        $user = Auth::user();
        $business = $user->business;

        // If user has no business, just check if slug exists globally
        if (!$business) {
            $exists = BusinessLandingPage::where('custom_slug', $slug)->exists();
        } else {
            $exists = BusinessLandingPage::where('custom_slug', $slug)
                ->when($business->landingPage, function ($query) use ($business) {
                    return $query->where('business_id', '!=', $business->id);
                })
                ->exists();
        }

        $reservedSlugs = ['admin', 'api', 'www', 'mail', 'ftp', 'blog', 'shop', 'store', 'app', 'dashboard'];
        $isReserved = in_array($slug, $reservedSlugs);

        return response()->json([
            'available' => !$exists && !$isReserved,
            'message' => $exists ? 'This slug is already taken.' : ($isReserved ? 'This slug is reserved.' : 'Slug is available!')
        ]);
    }

    /**
     * Get validation rules for section types
     */
    private function getSectionValidationRules($sectionType)
    {
        switch ($sectionType) {
            case 'hero':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'background_image' => 'nullable|string',
                    'background_video' => 'nullable|string',
                    'cta_text' => 'nullable|string|max:50',
                    'cta_url' => 'nullable|string|max:255',
                    'secondary_cta_text' => 'nullable|string|max:50',
                    'secondary_cta_url' => 'nullable|string|max:255',
                    'overlay_opacity' => 'nullable|numeric|between:0,1',
                    'text_alignment' => 'nullable|string|in:left,center,right'
                ];

            case 'about':
                return [
                    'title' => 'required|string|max:255',
                    'content' => 'required|string',
                    'image' => 'nullable|string',
                    'features' => 'nullable|array',
                    'features.*' => 'nullable|string|max:255',
                    'stats' => 'nullable|array',
                    'stats.*.value' => 'nullable|string|max:50',
                    'stats.*.label' => 'nullable|string|max:100',
                    'layout' => 'nullable|string|in:image-left,image-right,image-top,no-image'
                ];

            case 'services':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'layout' => 'nullable|string|in:grid,list,carousel',
                    'columns' => 'nullable|integer|between:1,4',
                    'show_prices' => 'nullable|boolean',
                    'show_duration' => 'nullable|boolean',
                    'show_description' => 'nullable|boolean',
                    'show_booking_button' => 'nullable|boolean'
                ];

            case 'features':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'layout' => 'nullable|string|in:grid,list,carousel',
                    'columns' => 'nullable|integer|between:1,4',
                    'features' => 'nullable|array',
                    'features.*.icon' => 'nullable|string|max:100',
                    'features.*.title' => 'nullable|string|max:255',
                    'features.*.description' => 'nullable|string|max:1000'
                ];

            case 'testimonials':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'layout' => 'nullable|string|in:grid,list,carousel',
                    'columns' => 'nullable|integer|between:1,4',
                    'show_ratings' => 'nullable|boolean',
                    'show_photos' => 'nullable|boolean',
                    'auto_rotate' => 'nullable|boolean',
                    'rotation_speed' => 'nullable|integer|between:3,10',
                    'max_testimonials' => 'nullable|integer|between:3,20',
                    'min_rating' => 'nullable|integer|between:1,5',
                    'featured_only' => 'nullable|boolean',
                    'show_service_name' => 'nullable|boolean',
                    'testimonials' => 'nullable|array',
                    'testimonials.*.name' => 'nullable|string|max:255',
                    'testimonials.*.content' => 'nullable|string|max:1000',
                    'testimonials.*.rating' => 'nullable|integer|between:1,5',
                    'testimonials.*.photo' => 'nullable|string',
                    'testimonials.*.position' => 'nullable|string|max:255',
                    'testimonials.*.company' => 'nullable|string|max:255'
                ];

            case 'team':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'layout' => 'nullable|string|in:grid,list,carousel',
                    'columns' => 'nullable|integer|between:1,4',
                    'show_bio' => 'nullable|boolean',
                    'show_experience' => 'nullable|boolean',
                    'show_specializations' => 'nullable|boolean',
                    'show_contact' => 'nullable|boolean',
                    'show_social_links' => 'nullable|boolean',
                    'show_booking_button' => 'nullable|boolean',
                    'team_filter' => 'nullable|string|in:all,active_only,landing_visible,accepts_bookings',
                    'sort_order' => 'nullable|string|in:sort_order,name,experience,position',
                    'max_members' => 'nullable|integer|between:0,20',
                    'image_style' => 'nullable|string|in:square,circle,rounded',
                    'enable_modal' => 'nullable|boolean',
                    'team_members' => 'nullable|array',
                    'team_members.*.name' => 'nullable|string|max:255',
                    'team_members.*.position' => 'nullable|string|max:255',
                    'team_members.*.bio' => 'nullable|string|max:1000',
                    'team_members.*.photo' => 'nullable|string',
                    'team_members.*.email' => 'nullable|email|max:255',
                    'team_members.*.phone' => 'nullable|string|max:20',
                    'team_members.*.social_links' => 'nullable|array'
                ];

            case 'gallery':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'layout' => 'nullable|string|in:grid,masonry,carousel,slider',
                    'columns' => 'nullable|integer|between:1,6',
                    'show_captions' => 'nullable|boolean',
                    'lightbox_enabled' => 'nullable|boolean',
                    'lazy_loading' => 'nullable|boolean',
                    'hover_effects' => 'nullable|boolean',
                    'show_load_more' => 'nullable|boolean',
                    'images_per_page' => 'nullable|integer|between:6,50',
                    'image_quality' => 'nullable|string|in:thumbnail,medium,high,original',
                    'filter_enabled' => 'nullable|boolean',
                    'categories' => 'nullable|array',
                    'categories.*.name' => 'nullable|string|max:100',
                    'categories.*.slug' => 'nullable|string|max:100'
                ];

            case 'pricing':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'layout' => 'nullable|string|in:cards,table,list',
                    'columns' => 'nullable|integer|between:1,4',
                    'show_features' => 'nullable|boolean',
                    'show_popular_badge' => 'nullable|boolean',
                    'show_booking_button' => 'nullable|boolean',
                    'packages' => 'nullable|array',
                    'packages.*.name' => 'nullable|string|max:255',
                    'packages.*.price' => 'nullable|string|max:50',
                    'packages.*.period' => 'nullable|string|max:50',
                    'packages.*.description' => 'nullable|string|max:500',
                    'packages.*.features' => 'nullable|string',
                    'packages.*.popular' => 'nullable|boolean',
                    'packages.*.button_text' => 'nullable|string|max:50',
                    'packages.*.button_url' => 'nullable|string|max:255'
                ];

            case 'faq':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'layout' => 'nullable|string|in:accordion,list,grid',
                    'search_enabled' => 'nullable|boolean',
                    'categories_enabled' => 'nullable|boolean',
                    'allow_expand_all' => 'nullable|boolean',
                    'show_contact_cta' => 'nullable|boolean',
                    'contact_cta_text' => 'nullable|string|max:255',
                    'questions' => 'nullable|array',
                    'questions.*.question' => 'nullable|string|max:500',
                    'questions.*.answer' => 'nullable|string|max:2000',
                    'questions.*.category' => 'nullable|string|max:100',
                    'questions.*.sort_order' => 'nullable|integer|between:1,999',
                    'questions.*.featured' => 'nullable|boolean'
                ];

            case 'cta':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'button_text' => 'nullable|string|max:50',
                    'button_url' => 'nullable|string|max:255',
                    'secondary_button_text' => 'nullable|string|max:50',
                    'secondary_button_url' => 'nullable|string|max:255',
                    'background_color' => 'nullable|string|max:7',
                    'background_color_text' => 'nullable|string|max:7',
                    'text_color' => 'nullable|string|max:7',
                    'text_color_text' => 'nullable|string|max:7',
                    'background_image' => 'nullable|string',
                    'text_alignment' => 'nullable|string|in:left,center,right',
                    'section_padding' => 'nullable|string|in:small,medium,large',
                    'border_radius' => 'nullable|string|in:none,small,medium,large',
                    'enable_animation' => 'nullable|boolean',
                    'full_width' => 'nullable|boolean'
                ];

            case 'contact':
                return [
                    'title' => 'required|string|max:255',
                    'subtitle' => 'nullable|string|max:500',
                    'show_map' => 'nullable|boolean',
                    'show_hours' => 'nullable|boolean',
                    'show_contact_form' => 'nullable|boolean',
                    'show_social_links' => 'nullable|boolean',
                    'map_zoom' => 'nullable|integer|between:1,20',
                    'contact_form_fields' => 'nullable|array',
                    'contact_form_fields.*' => 'nullable|string|in:name,email,phone,subject,message,company'
                ];

            default:
                return [
                    'title' => 'required|string|max:255',
                    'content' => 'required|string',
                    'layout' => 'nullable|string'
                ];
        }
    }

    /**
     * Get available themes
     */
    private function getAvailableThemes()
    {
        return [
            'default' => [
                'name' => 'Default',
                'description' => 'Clean and professional design',
                'preview' => '/images/themes/default-preview.jpg'
            ],
            'modern' => [
                'name' => 'Modern',
                'description' => 'Contemporary and sleek design',
                'preview' => '/images/themes/modern-preview.jpg'
            ],
            'elegant' => [
                'name' => 'Elegant',
                'description' => 'Sophisticated and refined design',
                'preview' => '/images/themes/elegant-preview.jpg'
            ],
            'minimal' => [
                'name' => 'Minimal',
                'description' => 'Simple and clean design',
                'preview' => '/images/themes/minimal-preview.jpg'
            ],
            'creative' => [
                'name' => 'Creative',
                'description' => 'Bold and artistic design',
                'preview' => '/images/themes/creative-preview.jpg'
            ],
        ];
    }

    /**
     * Get business type from category
     */
    private function getBusinessTypeFromCategory($business)
    {
        $category = $business->categories()->first();

        if (!$category) {
            return 'LocalBusiness';
        }

        $categoryMappings = [
            'salon' => 'BeautySalon',
            'spa' => 'DaySpa',
            'restaurant' => 'Restaurant',
            'clinic' => 'MedicalClinic',
            'fitness' => 'ExerciseGym',
            'automotive' => 'AutomotiveBusiness',
            'retail' => 'Store',
        ];

        return $categoryMappings[strtolower($category->slug)] ?? 'LocalBusiness';
    }

    /**
     * Get landing page analytics
     */
    private function getLandingPageAnalytics($landingPage)
    {
        // This would integrate with actual analytics service
        return [
            'views' => 0,
            'unique_visitors' => 0,
            'bounce_rate' => 0,
            'conversion_rate' => 0,
            'bookings_from_landing' => 0,
        ];
    }
}
