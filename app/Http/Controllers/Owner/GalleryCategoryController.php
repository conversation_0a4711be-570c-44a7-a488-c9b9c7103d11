<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\BusinessGalleryCategory;
use App\Services\GalleryImageService;
use Illuminate\Http\Request;

class GalleryCategoryController extends Controller
{
    protected $galleryService;

    public function __construct(GalleryImageService $galleryService)
    {
        $this->galleryService = $galleryService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $business = $this->getUserBusiness();
        $categories = $business->galleryCategories()->withCount('images')->ordered()->get();

        return view('owner.gallery.categories.index', compact('business', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $business = $this->getUserBusiness();
        return view('owner.gallery.categories.create', compact('business'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        try {
            $this->galleryService->createCategory($business, $validated);
            return redirect()->route('owner.gallery.categories.index')
                ->with('success', 'Category created successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['create' => 'Failed to create category: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(BusinessGalleryCategory $category)
    {
        $business = $this->getUserBusiness();
        
        // Ensure category belongs to the business
        if ($category->business_id !== $business->id) {
            abort(404);
        }

        $images = $category->images()->with('category')->paginate(24);

        return view('owner.gallery.categories.show', compact('business', 'category', 'images'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BusinessGalleryCategory $category)
    {
        $business = $this->getUserBusiness();
        
        // Ensure category belongs to the business
        if ($category->business_id !== $business->id) {
            abort(404);
        }

        return view('owner.gallery.categories.edit', compact('business', 'category'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BusinessGalleryCategory $category)
    {
        $business = $this->getUserBusiness();
        
        // Ensure category belongs to the business
        if ($category->business_id !== $business->id) {
            abort(404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean',
        ]);

        try {
            $this->galleryService->updateCategory($category, $validated);
            return redirect()->route('owner.gallery.categories.index')
                ->with('success', 'Category updated successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['update' => 'Failed to update category: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BusinessGalleryCategory $category)
    {
        $business = $this->getUserBusiness();
        
        // Ensure category belongs to the business
        if ($category->business_id !== $business->id) {
            abort(404);
        }

        try {
            $this->galleryService->deleteCategory($category);
            return redirect()->route('owner.gallery.categories.index')
                ->with('success', 'Category deleted successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['delete' => 'Failed to delete category: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle category active status.
     */
    public function toggleActive(BusinessGalleryCategory $category)
    {
        $business = $this->getUserBusiness();
        
        // Ensure category belongs to the business
        if ($category->business_id !== $business->id) {
            abort(404);
        }

        try {
            $category->toggleActive();
            $status = $category->is_active ? 'activated' : 'deactivated';
            return response()->json(['success' => true, 'message' => "Category {$status} successfully!"]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update category status.'], 500);
        }
    }
}
