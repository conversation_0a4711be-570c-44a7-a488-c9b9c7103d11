<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ServiceCategoryController extends Controller
{
    /**
     * Display a listing of service categories.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();
        
        $query = $business->serviceCategories()
            ->withCount(['services', 'activeServices']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $categories = $query->ordered()->paginate(15);
        
        // Get statistics
        $stats = $this->getCategoryStats($business);

        return view('owner.service-categories.index', compact('categories', 'stats'));
    }

    /**
     * Store a newly created service category.
     */
    public function store(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Check for unique name within business
        $existingCategory = $business->serviceCategories()
            ->where('name', $request->name)
            ->first();

        if ($existingCategory) {
            return back()->withErrors(['name' => 'A service category with this name already exists.'])->withInput();
        }

        $category = $business->serviceCategories()->create([
            'name' => $request->name,
            'description' => $request->description,
            'icon' => $request->icon ?: 'fas fa-tag',
            'color' => $request->color ?: '#007bff',
            'sort_order' => $request->sort_order ?? ($business->serviceCategories()->max('sort_order') + 1),
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('owner.service-categories.index')
            ->with('success', 'Service category created successfully.');
    }

    /**
     * Update the specified service category.
     */
    public function update(Request $request, ServiceCategory $serviceCategory)
    {
        $business = $this->getUserBusiness();
        
        if ($serviceCategory->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Check for unique name within business (excluding current category)
        $existingCategory = $business->serviceCategories()
            ->where('name', $request->name)
            ->where('id', '!=', $serviceCategory->id)
            ->first();

        if ($existingCategory) {
            return back()->withErrors(['name' => 'A service category with this name already exists.'])->withInput();
        }

        $serviceCategory->update([
            'name' => $request->name,
            'description' => $request->description,
            'icon' => $request->icon ?: 'fas fa-tag',
            'color' => $request->color ?: '#007bff',
            'sort_order' => $request->sort_order ?? $serviceCategory->sort_order,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('owner.service-categories.index')
            ->with('success', 'Service category updated successfully.');
    }

    /**
     * Remove the specified service category.
     */
    public function destroy(ServiceCategory $serviceCategory)
    {
        $business = $this->getUserBusiness();
        
        if ($serviceCategory->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        // Check if category has services
        $hasServices = $serviceCategory->services()->exists();
        
        if ($hasServices) {
            return back()->with('error', 'Cannot delete category with existing services. Move services to another category first.');
        }

        $categoryName = $serviceCategory->name;
        $serviceCategory->delete();

        return redirect()->route('owner.service-categories.index')
            ->with('success', "Service category '{$categoryName}' deleted successfully.");
    }

    /**
     * Toggle category status.
     */
    public function toggleStatus(ServiceCategory $serviceCategory)
    {
        $business = $this->getUserBusiness();
        
        if ($serviceCategory->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $serviceCategory->update(['is_active' => !$serviceCategory->is_active]);
        
        $status = $serviceCategory->is_active ? 'activated' : 'deactivated';
        
        return back()->with('success', "Category {$status} successfully.");
    }

    /**
     * Duplicate a service category.
     */
    public function duplicate(ServiceCategory $serviceCategory)
    {
        $business = $this->getUserBusiness();
        
        if ($serviceCategory->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $newCategory = $serviceCategory->replicate();
        $newCategory->name = $serviceCategory->name . ' (Copy)';
        $newCategory->slug = null; // Will be auto-generated
        $newCategory->sort_order = $business->serviceCategories()->max('sort_order') + 1;
        $newCategory->save();

        return back()->with('success', 'Service category duplicated successfully.');
    }

    /**
     * Update category sort order.
     */
    public function updateOrder(Request $request)
    {
        $business = $this->getUserBusiness();
        
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:service_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        DB::transaction(function () use ($request, $business) {
            foreach ($request->categories as $categoryData) {
                $category = ServiceCategory::where('id', $categoryData['id'])
                    ->where('business_id', $business->id)
                    ->first();
                
                if ($category) {
                    $category->update(['sort_order' => $categoryData['sort_order']]);
                }
            }
        });

        return response()->json(['success' => true, 'message' => 'Category order updated successfully.']);
    }

    /**
     * Get category statistics.
     */
    private function getCategoryStats($business)
    {
        $totalCategories = $business->serviceCategories()->count();
        $activeCategories = $business->serviceCategories()->where('is_active', true)->count();
        $categoriesWithServices = $business->serviceCategories()
            ->whereHas('services')
            ->count();
        $emptyCategories = $totalCategories - $categoriesWithServices;

        return [
            'total_categories' => $totalCategories,
            'active_categories' => $activeCategories,
            'categories_with_services' => $categoriesWithServices,
            'empty_categories' => $emptyCategories,
        ];
    }
}
