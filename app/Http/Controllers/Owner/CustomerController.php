<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Booking;
use App\Models\CustomerBusinessProfile;
use App\Models\CustomerTag;
use App\Models\CustomerTagAssignment;
use App\Models\CustomerCommunication;
use App\Models\CustomerLoyaltyPoint;
use App\Models\CustomerReferral;
use App\Models\CustomerActivityTimeline;
use App\Models\CustomerEmergencyContact;
use App\Models\CustomerAddress;
use App\Models\CustomerFeedback;
use Carbon\Carbon;
use Illuminate\Validation\Rule;

class CustomerController extends Controller
{
    /**
     * Display customer dashboard with metrics and overview.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();

        // Get filter parameters
        $status = $request->get('status', 'all');
        $loyaltyTier = $request->get('loyalty_tier', 'all');
        $search = $request->get('search');
        $tagId = $request->get('tag');
        $sortBy = $request->get('sort_by', 'last_visit_date');
        $sortOrder = $request->get('sort_order', 'desc');

        // Build customer query with business isolation
        $customersQuery = $this->buildCustomerQuery($business->id, $status, $loyaltyTier, $search, $tagId, $sortBy, $sortOrder);

        // Get paginated customers
        $customers = $customersQuery->with([
            'customer',
            'customer.bookings' => function($query) use ($business) {
                $query->where('business_id', $business->id)->latest()->limit(3);
            }
        ])->paginate(20);

        // Get dashboard metrics
        $metrics = $this->getDashboardMetrics($business->id);

        // Get available tags for filtering
        $tags = CustomerTag::forBusiness($business->id)->active()->get();

        return view('owner.customers.index', compact(
            'customers',
            'metrics',
            'tags',
            'status',
            'loyaltyTier',
            'search',
            'tagId',
            'sortBy',
            'sortOrder'
        ));
    }

    /**
     * Show customer profile with detailed information.
     */
    public function show($id)
    {
        $business = $this->getUserBusiness();

        // Get customer profile with business isolation
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $id)
            ->with(['customer', 'business'])
            ->firstOrFail();

        // Get customer's bookings for this business
        $bookings = Booking::where('business_id', $business->id)
            ->where('customer_id', $id)
            ->with(['services'])
            ->orderBy('start_datetime', 'desc')
            ->paginate(10);

        // Get customer's tags
        $customerTags = CustomerTagAssignment::forBusiness($business->id)
            ->forCustomer($id)
            ->with('tag')
            ->get();

        // Get available tags for assignment
        $availableTags = CustomerTag::forBusiness($business->id)
            ->active()
            ->whereNotIn('id', $customerTags->pluck('customer_tag_id'))
            ->get();

        // Get communication history
        $communications = CustomerCommunication::forBusiness($business->id)
            ->forCustomer($id)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get loyalty points history
        $loyaltyHistory = CustomerLoyaltyPoint::forBusiness($business->id)
            ->forCustomer($id)
            ->with('processedBy')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get customer analytics
        $analytics = $this->getCustomerAnalytics($business->id, $id);

        return view('owner.customers.show', compact(
            'customerProfile',
            'bookings',
            'customerTags',
            'availableTags',
            'communications',
            'loyaltyHistory',
            'analytics'
        ));
    }

    /**
     * Show form for creating a new customer.
     */
    public function create()
    {
        $business = $this->getUserBusiness();
        $tags = CustomerTag::forBusiness($business->id)->active()->get();

        return view('owner.customers.create', compact('tags'));
    }

    /**
     * Store a new customer.
     */
    public function store(Request $request)
    {
        try {
            $business = $this->getUserBusiness();

            // Log the incoming request for debugging
            \Log::info('Customer creation attempt', [
                'business_id' => $business->id,
                'request_data' => $request->all()
            ]);

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'phone' => 'nullable|string|max:20',
                'date_of_birth' => 'nullable|date|before:today',
                'gender' => 'nullable|in:male,female,other,prefer_not_to_say',
                'notes' => 'nullable|string|max:1000',
                'special_requirements' => 'nullable|string|max:500',
                'marketing_consent' => 'boolean',
                'tags' => 'nullable|array',
                'tags.*' => 'exists:customer_tags,id',
            ]);

            \Log::info('Customer validation passed', ['validated_data' => $validated]);
        } catch (\Exception $e) {
            \Log::error('Error in customer store method before transaction', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->withInput()
                ->with('error', 'Validation failed: ' . $e->getMessage());
        }

        DB::beginTransaction();

        try {
            // Create user account
            \Log::info('Creating user account');
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'] ?? null,
                'date_of_birth' => $validated['date_of_birth'] ?? null,
                'gender' => $validated['gender'] ?? null,
                'password' => bcrypt(\Illuminate\Support\Str::random(12)), // Random password
                'email_verified_at' => now(),
            ]);
            \Log::info('User created successfully', ['user_id' => $user->id]);

            // Create customer business profile
            \Log::info('Creating customer business profile');
            $profile = CustomerBusinessProfile::create([
                'business_id' => $business->id,
                'customer_id' => $user->id,
                'status' => 'active',
                'customer_since' => now()->toDateString(),
                'notes' => $validated['notes'] ?? null,
                'special_requirements' => $validated['special_requirements'] ?? null,
                'marketing_consent' => $validated['marketing_consent'] ?? false,
                'marketing_consent_date' => $validated['marketing_consent'] ? now() : null,
            ]);
            \Log::info('Customer profile created successfully', ['profile_id' => $profile->id]);

            // Assign tags if provided
            if (!empty($validated['tags'])) {
                \Log::info('Assigning tags', ['tags' => $validated['tags']]);
                try {
                    foreach ($validated['tags'] as $tagId) {
                        CustomerTagAssignment::assignTag($business->id, $user->id, $tagId);
                    }
                    \Log::info('Tags assigned successfully');
                } catch (\Exception $e) {
                    \Log::warning('Failed to assign tags', ['error' => $e->getMessage()]);
                    // Continue without failing the entire process
                }
            }

            // Award welcome bonus points
            \Log::info('Awarding welcome bonus points');
            try {
                CustomerLoyaltyPoint::awardPoints(
                    $business->id,
                    $user->id,
                    50, // Welcome bonus
                    'Welcome bonus for new customer'
                );
                \Log::info('Welcome bonus points awarded successfully');
            } catch (\Exception $e) {
                \Log::warning('Failed to award welcome bonus points', ['error' => $e->getMessage()]);
                // Continue without failing the entire process
            }

            DB::commit();
            \Log::info('Customer creation completed successfully', ['user_id' => $user->id]);

            return redirect()->route('owner.customers.show', $user->id)
                ->with('success', 'Customer created successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Customer creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'business_id' => $business->id,
                'validated_data' => $validated
            ]);

            return back()->withInput()
                ->with('error', 'Failed to create customer: ' . $e->getMessage());
        }
    }

    /**
     * Show form for editing customer.
     */
    public function edit($id)
    {
        $business = $this->getUserBusiness();

        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $id)
            ->with('customer')
            ->firstOrFail();

        $tags = CustomerTag::forBusiness($business->id)->active()->get();

        $customerTags = CustomerTagAssignment::forBusiness($business->id)
            ->forCustomer($id)
            ->pluck('customer_tag_id')
            ->toArray();

        return view('owner.customers.edit', compact('customerProfile', 'tags', 'customerTags'));
    }

    /**
     * Update customer information.
     */
    public function update(Request $request, $id)
    {
        $business = $this->getUserBusiness();

        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $id)
            ->with('customer')
            ->firstOrFail();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($customerProfile->customer_id)
            ],
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other,prefer_not_to_say',
            'status' => 'required|in:active,inactive,vip,blocked,prospect',
            'notes' => 'nullable|string|max:1000',
            'special_requirements' => 'nullable|string|max:500',
            'marketing_consent' => 'boolean',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:customer_tags,id',
        ]);

        DB::beginTransaction();

        try {
            // Update user information
            $customerProfile->customer->update([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'] ?? null,
                'date_of_birth' => $validated['date_of_birth'] ?? null,
                'gender' => $validated['gender'] ?? null,
            ]);

            // Update customer profile
            $customerProfile->update([
                'status' => $validated['status'],
                'notes' => $validated['notes'] ?? null,
                'special_requirements' => $validated['special_requirements'] ?? null,
                'marketing_consent' => $validated['marketing_consent'] ?? false,
                'marketing_consent_date' => $validated['marketing_consent'] ? now() : null,
            ]);

            // Update tags
            if (isset($validated['tags'])) {
                // Remove existing tags
                CustomerTagAssignment::forBusiness($business->id)
                    ->forCustomer($id)
                    ->delete();

                // Assign new tags
                foreach ($validated['tags'] as $tagId) {
                    CustomerTagAssignment::assignTag($business->id, $id, $tagId);
                }
            }

            DB::commit();

            return redirect()->route('owner.customers.show', $id)
                ->with('success', 'Customer updated successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to update customer: ' . $e->getMessage());
        }
    }

    /**
     * Build customer query with filters and business isolation.
     */
    private function buildCustomerQuery($businessId, $status, $loyaltyTier, $search, $tagId, $sortBy, $sortOrder)
    {
        $query = CustomerBusinessProfile::forBusiness($businessId);

        // Apply status filter
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        // Apply loyalty tier filter
        if ($loyaltyTier !== 'all') {
            $query->where('loyalty_tier', $loyaltyTier);
        }

        // Apply search filter
        if ($search) {
            $query->whereHas('customer', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Apply tag filter
        if ($tagId) {
            $query->whereHas('customer.tagAssignments', function($q) use ($businessId, $tagId) {
                $q->where('business_id', $businessId)
                  ->where('customer_tag_id', $tagId);
            });
        }

        // Apply sorting
        $query->orderBy($sortBy, $sortOrder);

        return $query;
    }

    /**
     * Get dashboard metrics for the business.
     */
    private function getDashboardMetrics($businessId)
    {
        $totalCustomers = CustomerBusinessProfile::forBusiness($businessId)->count();
        $activeCustomers = CustomerBusinessProfile::forBusiness($businessId)->active()->count();
        $vipCustomers = CustomerBusinessProfile::forBusiness($businessId)->vip()->count();
        $newCustomersThisMonth = CustomerBusinessProfile::forBusiness($businessId)
            ->where('customer_since', '>=', now()->startOfMonth())
            ->count();

        $totalRevenue = CustomerBusinessProfile::forBusiness($businessId)->sum('total_spent');
        $averageOrderValue = CustomerBusinessProfile::forBusiness($businessId)->avg('average_order_value');

        $atRiskCustomers = CustomerBusinessProfile::forBusiness($businessId)->atRisk()->count();

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'vip_customers' => $vipCustomers,
            'new_customers_this_month' => $newCustomersThisMonth,
            'total_revenue' => $totalRevenue,
            'average_order_value' => $averageOrderValue,
            'at_risk_customers' => $atRiskCustomers,
        ];
    }

    /**
     * Get analytics for a specific customer.
     */
    private function getCustomerAnalytics($businessId, $customerId)
    {
        $bookings = Booking::where('business_id', $businessId)
            ->where('customer_id', $customerId)
            ->get();

        $totalBookings = $bookings->count();
        $completedBookings = $bookings->where('status', 'completed')->count();
        $cancelledBookings = $bookings->where('status', 'cancelled')->count();
        $noShowBookings = $bookings->where('status', 'no_show')->count();

        $monthlyBookings = $bookings->groupBy(function($booking) {
            return $booking->start_datetime->format('Y-m');
        })->map->count();

        $monthlySpending = $bookings->where('status', 'completed')
            ->groupBy(function($booking) {
                return $booking->start_datetime->format('Y-m');
            })->map(function($bookings) {
                return $bookings->sum('total_amount');
            });

        return [
            'total_bookings' => $totalBookings,
            'completed_bookings' => $completedBookings,
            'cancelled_bookings' => $cancelledBookings,
            'no_show_bookings' => $noShowBookings,
            'completion_rate' => $totalBookings > 0 ? round(($completedBookings / $totalBookings) * 100, 1) : 0,
            'monthly_bookings' => $monthlyBookings,
            'monthly_spending' => $monthlySpending,
        ];
    }

    /**
     * Send communication to customer.
     */
    public function sendCommunication(Request $request, $id)
    {
        $business = $this->getUserBusiness();

        // Verify customer belongs to this business
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $id)
            ->firstOrFail();

        $validated = $request->validate([
            'type' => 'required|in:email,sms,note',
            'subject' => 'required_if:type,email|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        try {
            // Create communication record
            $communication = CustomerCommunication::create([
                'business_id' => $business->id,
                'customer_id' => $id,
                'sent_by' => auth()->id(),
                'type' => $validated['type'],
                'direction' => 'outbound',
                'subject' => $validated['subject'] ?? null,
                'message' => $validated['message'],
                'status' => $validated['type'] === 'note' ? 'sent' : 'pending',
            ]);

            // For notes, mark as sent immediately
            if ($validated['type'] === 'note') {
                $communication->markAsSent();
            }

            // TODO: Integrate with actual email/SMS services
            // For now, we'll just mark as sent for demonstration
            if ($validated['type'] !== 'note') {
                $communication->markAsSent();
            }

            return response()->json([
                'success' => true,
                'message' => 'Communication sent successfully!',
                'communication' => $communication->load('sender')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send communication: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Award loyalty points to customer.
     */
    public function awardPoints(Request $request, $id)
    {
        $business = $this->getUserBusiness();

        // Verify customer belongs to this business
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $id)
            ->firstOrFail();

        $validated = $request->validate([
            'points' => 'required|integer|min:1|max:10000',
            'description' => 'required|string|max:255',
        ]);

        try {
            // Award points
            $transaction = CustomerLoyaltyPoint::awardPoints(
                $business->id,
                $id,
                $validated['points'],
                $validated['description']
            );

            // Update customer profile balance
            $customerProfile->increment('loyalty_points_balance', $validated['points']);

            return response()->json([
                'success' => true,
                'message' => 'Points awarded successfully!',
                'transaction' => $transaction,
                'new_balance' => $customerProfile->fresh()->loyalty_points_balance
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to award points: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign tag to customer.
     */
    public function assignTag(Request $request, $id)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'tag_id' => 'required|exists:customer_tags,id',
        ]);

        // Verify tag belongs to this business
        $tag = CustomerTag::forBusiness($business->id)->findOrFail($validated['tag_id']);

        // Verify customer belongs to this business
        CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $id)
            ->firstOrFail();

        try {
            $assignment = CustomerTagAssignment::assignTag($business->id, $id, $validated['tag_id']);

            return response()->json([
                'success' => true,
                'message' => 'Tag assigned successfully!',
                'assignment' => $assignment->load('tag')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign tag: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove tag from customer.
     */
    public function removeTag(Request $request, $id)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'tag_id' => 'required|exists:customer_tags,id',
        ]);

        try {
            $removed = CustomerTagAssignment::removeTag($business->id, $id, $validated['tag_id']);

            if ($removed) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tag removed successfully!'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Tag assignment not found.'
                ], 404);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove tag: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export customers data.
     */
    public function export(Request $request)
    {
        $business = $this->getUserBusiness();

        $format = $request->get('format', 'csv');
        $status = $request->get('status', 'all');

        // Build query with filters
        $query = $this->buildCustomerQuery($business->id, $status, 'all', null, null, 'customer_since', 'asc');

        $customers = $query->with(['customer', 'customer.tagAssignments.tag'])->get();

        $data = $customers->map(function($profile) {
            return [
                'Name' => $profile->customer->name,
                'Email' => $profile->customer->email,
                'Phone' => $profile->customer->phone,
                'Status' => $profile->status,
                'Customer Since' => $profile->customer_since->format('Y-m-d'),
                'Last Visit' => $profile->last_visit_date?->format('Y-m-d'),
                'Total Visits' => $profile->total_visits,
                'Total Spent' => $profile->total_spent,
                'Loyalty Tier' => $profile->loyalty_tier,
                'Loyalty Points' => $profile->loyalty_points_balance,
                'Tags' => $profile->customer->tagAssignments->pluck('tag.name')->join(', '),
            ];
        });

        if ($format === 'csv') {
            $filename = 'customers_' . now()->format('Y-m-d_H-i-s') . '.csv';

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($data) {
                $file = fopen('php://output', 'w');

                // Add CSV headers
                if ($data->isNotEmpty()) {
                    fputcsv($file, array_keys($data->first()));
                }

                // Add data rows
                foreach ($data as $row) {
                    fputcsv($file, $row);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }

        // Default to JSON export
        return response()->json($data);
    }

    /**
     * Get customer report data for analytics.
     */
    public function getReportData(Request $request)
    {
        $business = $this->getUserBusiness();

        $dateRange = $request->get('date_range', '30');
        $customerStatus = $request->get('customer_status', 'all');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        // Calculate date range
        if ($dateRange === 'custom' && $startDate && $endDate) {
            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);
        } else {
            $days = (int) $dateRange;
            $start = now()->subDays($days);
            $end = now();
        }

        // Get metrics
        $metrics = $this->getReportMetrics($business->id, $start, $end, $customerStatus);

        // Get chart data
        $charts = $this->getReportCharts($business->id, $start, $end, $customerStatus);

        // Get top customers
        $topCustomers = $this->getTopCustomers($business->id, $start, $end, $customerStatus);

        return response()->json([
            'success' => true,
            'data' => [
                'metrics' => $metrics,
                'charts' => $charts,
                'top_customers' => $topCustomers
            ]
        ]);
    }

    /**
     * Export customer report.
     */
    public function exportReport(Request $request)
    {
        $business = $this->getUserBusiness();

        $dateRange = $request->get('date_range', '30');
        $customerStatus = $request->get('customer_status', 'all');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        // Calculate date range
        if ($dateRange === 'custom' && $startDate && $endDate) {
            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);
        } else {
            $days = (int) $dateRange;
            $start = now()->subDays($days);
            $end = now();
        }

        // Get report data
        $metrics = $this->getReportMetrics($business->id, $start, $end, $customerStatus);
        $topCustomers = $this->getTopCustomers($business->id, $start, $end, $customerStatus);

        $data = [
            'Report Generated' => now()->format('Y-m-d H:i:s'),
            'Business' => $business->name,
            'Date Range' => $start->format('Y-m-d') . ' to ' . $end->format('Y-m-d'),
            'Customer Status Filter' => ucfirst($customerStatus),
            '',
            'METRICS',
            'Total Customers' => $metrics['total_customers'],
            'New Customers' => $metrics['new_customers'],
            'Returning Customers' => $metrics['returning_customers'],
            'At Risk Customers' => $metrics['at_risk_customers'],
            '',
            'TOP CUSTOMERS'
        ];

        foreach ($topCustomers as $index => $customer) {
            $data["Customer " . ($index + 1) . " - " . $customer['name']] = [
                'Email' => $customer['email'],
                'Total Spent' => '$' . number_format($customer['total_spent'], 2),
                'Total Visits' => $customer['total_visits'],
                'Average Order' => '$' . number_format($customer['average_order_value'], 2),
                'Status' => ucfirst($customer['status'])
            ];
        }

        $filename = 'customer_report_' . now()->format('Y-m-d_H-i-s') . '.json';

        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Content-Type', 'application/json');
    }

    /**
     * Deactivate/Delete customer (soft delete).
     */
    public function destroy($id)
    {
        $business = $this->getUserBusiness();

        // Verify customer belongs to this business
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $id)
            ->firstOrFail();

        try {
            DB::beginTransaction();

            // Set customer status to inactive instead of hard delete
            $customerProfile->update([
                'status' => 'inactive',
                'notes' => ($customerProfile->notes ? $customerProfile->notes . "\n\n" : '') .
                          'Customer deactivated on ' . now()->format('Y-m-d H:i:s') . ' by ' . auth()->user()->name
            ]);

            // Log the deactivation
            \Log::info('Customer deactivated', [
                'business_id' => $business->id,
                'customer_id' => $id,
                'deactivated_by' => auth()->id(),
                'timestamp' => now()
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Customer deactivated successfully!'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to deactivate customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore deactivated customer.
     */
    public function restore($id)
    {
        $business = $this->getUserBusiness();

        // Verify customer belongs to this business
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $id)
            ->firstOrFail();

        try {
            DB::beginTransaction();

            // Restore customer status to active
            $customerProfile->update([
                'status' => 'active',
                'notes' => ($customerProfile->notes ? $customerProfile->notes . "\n\n" : '') .
                          'Customer reactivated on ' . now()->format('Y-m-d H:i:s') . ' by ' . auth()->user()->name
            ]);

            // Log the restoration
            \Log::info('Customer reactivated', [
                'business_id' => $business->id,
                'customer_id' => $id,
                'reactivated_by' => auth()->id(),
                'timestamp' => now()
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Customer reactivated successfully!'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to reactivate customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Import customers from CSV file.
     */
    public function import(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048',
            'skip_header' => 'boolean',
        ]);

        try {
            $file = $request->file('csv_file');
            $skipHeader = $validated['skip_header'] ?? true;

            $csvData = array_map('str_getcsv', file($file->getPathname()));

            if ($skipHeader && count($csvData) > 0) {
                array_shift($csvData); // Remove header row
            }

            $imported = 0;
            $errors = [];

            DB::beginTransaction();

            foreach ($csvData as $index => $row) {
                try {
                    // Expected CSV format: name, email, phone, date_of_birth, gender, notes
                    if (count($row) < 2) {
                        $errors[] = "Row " . ($index + 1) . ": Insufficient data";
                        continue;
                    }

                    $name = trim($row[0] ?? '');
                    $email = trim($row[1] ?? '');
                    $phone = trim($row[2] ?? '');
                    $dateOfBirth = trim($row[3] ?? '');
                    $gender = trim($row[4] ?? '');
                    $notes = trim($row[5] ?? '');

                    if (empty($name) || empty($email)) {
                        $errors[] = "Row " . ($index + 1) . ": Name and email are required";
                        continue;
                    }

                    // Check if user already exists
                    $existingUser = User::where('email', $email)->first();
                    if ($existingUser) {
                        // Check if customer profile already exists for this business
                        $existingProfile = CustomerBusinessProfile::forBusiness($business->id)
                            ->where('customer_id', $existingUser->id)
                            ->first();

                        if ($existingProfile) {
                            $errors[] = "Row " . ($index + 1) . ": Customer with email {$email} already exists";
                            continue;
                        }

                        $customer = $existingUser;
                    } else {
                        // Create new user
                        $customer = User::create([
                            'name' => $name,
                            'email' => $email,
                            'phone' => $phone,
                            'date_of_birth' => $dateOfBirth ? Carbon::parse($dateOfBirth) : null,
                            'gender' => $gender ?: null,
                            'password' => Hash::make(Str::random(12)), // Random password
                            'email_verified_at' => now(),
                        ]);

                        // Assign customer role
                        $customer->assignRole('Customer');
                    }

                    // Create customer business profile
                    CustomerBusinessProfile::create([
                        'business_id' => $business->id,
                        'customer_id' => $customer->id,
                        'status' => 'active',
                        'customer_since' => now(),
                        'notes' => $notes,
                        'marketing_consent' => false,
                    ]);

                    $imported++;

                } catch (\Exception $e) {
                    $errors[] = "Row " . ($index + 1) . ": " . $e->getMessage();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Successfully imported {$imported} customers",
                'imported' => $imported,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to import customers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations on customers.
     */
    public function bulkAction(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete,assign_tag,remove_tag,export',
            'customer_ids' => 'required|array|min:1',
            'customer_ids.*' => 'integer|exists:users,id',
            'tag_id' => 'required_if:action,assign_tag,remove_tag|exists:customer_tags,id',
        ]);

        try {
            $customerIds = $validated['customer_ids'];
            $action = $validated['action'];

            // Verify all customers belong to this business
            $customerProfiles = CustomerBusinessProfile::forBusiness($business->id)
                ->whereIn('customer_id', $customerIds)
                ->get();

            if ($customerProfiles->count() !== count($customerIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some customers do not belong to your business.'
                ], 403);
            }

            DB::beginTransaction();

            $processed = 0;
            $errors = [];

            switch ($action) {
                case 'activate':
                    foreach ($customerProfiles as $profile) {
                        $profile->update(['status' => 'active']);
                        $processed++;
                    }
                    $message = "Successfully activated {$processed} customers";
                    break;

                case 'deactivate':
                    foreach ($customerProfiles as $profile) {
                        $profile->update(['status' => 'inactive']);
                        $processed++;
                    }
                    $message = "Successfully deactivated {$processed} customers";
                    break;

                case 'assign_tag':
                    $tagId = $validated['tag_id'];
                    foreach ($customerIds as $customerId) {
                        try {
                            CustomerTagAssignment::assignTag($business->id, $customerId, $tagId);
                            $processed++;
                        } catch (\Exception $e) {
                            $errors[] = "Customer ID {$customerId}: " . $e->getMessage();
                        }
                    }
                    $message = "Successfully assigned tag to {$processed} customers";
                    break;

                case 'remove_tag':
                    $tagId = $validated['tag_id'];
                    foreach ($customerIds as $customerId) {
                        try {
                            CustomerTagAssignment::removeTag($business->id, $customerId, $tagId);
                            $processed++;
                        } catch (\Exception $e) {
                            $errors[] = "Customer ID {$customerId}: " . $e->getMessage();
                        }
                    }
                    $message = "Successfully removed tag from {$processed} customers";
                    break;

                case 'export':
                    // Export selected customers
                    $customers = $customerProfiles->load(['customer', 'customer.tagAssignments.tag']);

                    $data = $customers->map(function($profile) {
                        return [
                            'Name' => $profile->customer->name,
                            'Email' => $profile->customer->email,
                            'Phone' => $profile->customer->phone,
                            'Status' => $profile->status,
                            'Customer Since' => $profile->customer_since->format('Y-m-d'),
                            'Last Visit' => $profile->last_visit_date?->format('Y-m-d'),
                            'Total Visits' => $profile->total_visits,
                            'Total Spent' => $profile->total_spent,
                            'Loyalty Tier' => $profile->loyalty_tier,
                            'Tags' => $profile->customer->tagAssignments->pluck('tag.name')->join(', '),
                        ];
                    });

                    $filename = 'selected_customers_' . now()->format('Y-m-d_H-i-s') . '.csv';

                    $headers = [
                        'Content-Type' => 'text/csv',
                        'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                    ];

                    $callback = function() use ($data) {
                        $file = fopen('php://output', 'w');

                        if ($data->isNotEmpty()) {
                            fputcsv($file, array_keys($data->first()));
                        }

                        foreach ($data as $row) {
                            fputcsv($file, $row);
                        }

                        fclose($file);
                    };

                    DB::commit();
                    return response()->stream($callback, 200, $headers);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $message,
                'processed' => $processed,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Bulk operation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customers with birthdays this month.
     */
    public function getBirthdayCustomers(Request $request)
    {
        $business = $this->getUserBusiness();
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        $customers = CustomerBusinessProfile::forBusiness($business->id)
            ->with(['customer'])
            ->whereHas('customer', function($query) use ($month) {
                $query->whereMonth('date_of_birth', $month);
            })
            ->active()
            ->get()
            ->map(function($profile) use ($year) {
                $customer = $profile->customer;
                $birthday = $customer->date_of_birth;

                if ($birthday) {
                    $thisYearBirthday = $birthday->setYear($year);
                    $age = $year - $birthday->year;

                    return [
                        'id' => $customer->id,
                        'name' => $customer->name,
                        'email' => $customer->email,
                        'phone' => $customer->phone,
                        'birthday' => $birthday->format('M j'),
                        'birthday_full' => $thisYearBirthday->format('Y-m-d'),
                        'age' => $age,
                        'days_until' => now()->diffInDays($thisYearBirthday, false),
                        'status' => $profile->status,
                        'loyalty_tier' => $profile->loyalty_tier,
                    ];
                }
                return null;
            })
            ->filter()
            ->sortBy('birthday_full')
            ->values();

        return response()->json([
            'success' => true,
            'customers' => $customers,
            'total' => $customers->count()
        ]);
    }

    /**
     * Send birthday wishes to customers.
     */
    public function sendBirthdayWishes(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'customer_ids' => 'required|array|min:1',
            'customer_ids.*' => 'integer|exists:users,id',
            'message_template' => 'required|string|max:500',
            'send_method' => 'required|in:email,sms,both',
        ]);

        try {
            $customerIds = $validated['customer_ids'];
            $messageTemplate = $validated['message_template'];
            $sendMethod = $validated['send_method'];

            // Verify all customers belong to this business
            $customerProfiles = CustomerBusinessProfile::forBusiness($business->id)
                ->whereIn('customer_id', $customerIds)
                ->with('customer')
                ->get();

            if ($customerProfiles->count() !== count($customerIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some customers do not belong to your business.'
                ], 403);
            }

            DB::beginTransaction();

            $sent = 0;
            $errors = [];

            foreach ($customerProfiles as $profile) {
                try {
                    $customer = $profile->customer;

                    // Personalize message
                    $personalizedMessage = str_replace(
                        ['{name}', '{business_name}'],
                        [$customer->name, $business->name],
                        $messageTemplate
                    );

                    // Create communication records
                    if (in_array($sendMethod, ['email', 'both'])) {
                        CustomerCommunication::create([
                            'business_id' => $business->id,
                            'customer_id' => $customer->id,
                            'sent_by' => auth()->id(),
                            'type' => 'email',
                            'direction' => 'outbound',
                            'subject' => 'Happy Birthday from ' . $business->name,
                            'message' => $personalizedMessage,
                            'status' => 'sent', // For demo purposes
                        ]);
                    }

                    if (in_array($sendMethod, ['sms', 'both'])) {
                        CustomerCommunication::create([
                            'business_id' => $business->id,
                            'customer_id' => $customer->id,
                            'sent_by' => auth()->id(),
                            'type' => 'sms',
                            'direction' => 'outbound',
                            'message' => $personalizedMessage,
                            'status' => 'sent', // For demo purposes
                        ]);
                    }

                    $sent++;

                } catch (\Exception $e) {
                    $errors[] = "Customer {$profile->customer->name}: " . $e->getMessage();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Birthday wishes sent to {$sent} customers",
                'sent' => $sent,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to send birthday wishes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customer referrals.
     */
    public function getReferrals(Request $request, $customerId)
    {
        $business = $this->getUserBusiness();

        // Verify customer belongs to this business
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $customerId)
            ->firstOrFail();

        $referrals = CustomerReferral::forBusiness($business->id)
            ->where('referrer_id', $customerId)
            ->with(['referred'])
            ->orderBy('referred_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'referrals' => $referrals,
            'total_referrals' => $customerProfile->referrals_made,
        ]);
    }

    /**
     * Add customer referral.
     */
    public function addReferral(Request $request, $customerId)
    {
        $business = $this->getUserBusiness();

        // Verify customer belongs to this business
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $customerId)
            ->firstOrFail();

        $validated = $request->validate([
            'referred_name' => 'required|string|max:255',
            'referred_email' => 'required|email|max:255',
            'referred_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            // Create referral record
            $referral = CustomerReferral::create([
                'business_id' => $business->id,
                'referrer_id' => $customerId,
                'referred_name' => $validated['referred_name'],
                'referred_email' => $validated['referred_email'],
                'referred_phone' => $validated['referred_phone'],
                'status' => 'pending',
                'referred_at' => now(),
                'notes' => $validated['notes'],
            ]);

            // Update customer profile
            $customerProfile->recordReferral();

            // Create activity timeline entry
            CustomerActivityTimeline::createActivity(
                $business->id,
                $customerId,
                'referral_made',
                'Made a referral: ' . $validated['referred_name'],
                [
                    'description' => 'Customer referred ' . $validated['referred_name'] . ' (' . $validated['referred_email'] . ')',
                    'metadata' => ['referral_id' => $referral->id],
                    'is_important' => true,
                ]
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Referral added successfully!',
                'referral' => $referral,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to add referral: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customer activity timeline.
     */
    public function getActivityTimeline(Request $request, $customerId)
    {
        $business = $this->getUserBusiness();

        // Verify customer belongs to this business
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $customerId)
            ->firstOrFail();

        $activities = CustomerActivityTimeline::forBusiness($business->id)
            ->forCustomer($customerId)
            ->with(['creator'])
            ->paginate(20);

        return response()->json([
            'success' => true,
            'activities' => $activities,
        ]);
    }

    /**
     * Add manual activity to timeline.
     */
    public function addActivity(Request $request, $customerId)
    {
        $business = $this->getUserBusiness();

        // Verify customer belongs to this business
        $customerProfile = CustomerBusinessProfile::forBusiness($business->id)
            ->where('customer_id', $customerId)
            ->firstOrFail();

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'activity_type' => 'required|string|in:note_added,communication_sent,status_changed,profile_updated',
            'is_important' => 'boolean',
        ]);

        try {
            $activity = CustomerActivityTimeline::createActivity(
                $business->id,
                $customerId,
                $validated['activity_type'],
                $validated['title'],
                [
                    'description' => $validated['description'],
                    'is_important' => $validated['is_important'] ?? false,
                    'is_system_generated' => false,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Activity added successfully!',
                'activity' => $activity,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add activity: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customer statistics for dashboard.
     */
    public function getStats(Request $request)
    {
        $business = $this->getUserBusiness();
        $period = $request->get('period', '30'); // days

        $startDate = now()->subDays($period);

        $stats = [
            'new_customers' => CustomerBusinessProfile::forBusiness($business->id)
                ->where('customer_since', '>=', $startDate)
                ->count(),
            'returning_customers' => CustomerBusinessProfile::forBusiness($business->id)
                ->where('last_visit_date', '>=', $startDate)
                ->where('total_visits', '>', 1)
                ->count(),
            'at_risk_customers' => CustomerBusinessProfile::forBusiness($business->id)
                ->atRisk(90)
                ->count(),
            'total_communications' => CustomerCommunication::forBusiness($business->id)
                ->where('created_at', '>=', $startDate)
                ->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Get report metrics for the specified date range.
     */
    private function getReportMetrics($businessId, $start, $end, $status = 'all')
    {
        $query = CustomerBusinessProfile::forBusiness($businessId);

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $totalCustomers = $query->count();

        $newCustomers = CustomerBusinessProfile::forBusiness($businessId)
            ->whereBetween('customer_since', [$start, $end])
            ->when($status !== 'all', function($q) use ($status) {
                return $q->where('status', $status);
            })
            ->count();

        $returningCustomers = CustomerBusinessProfile::forBusiness($businessId)
            ->whereBetween('last_visit_date', [$start, $end])
            ->where('total_visits', '>', 1)
            ->when($status !== 'all', function($q) use ($status) {
                return $q->where('status', $status);
            })
            ->count();

        $atRiskCustomers = CustomerBusinessProfile::forBusiness($businessId)
            ->where('last_visit_date', '<', now()->subDays(90))
            ->when($status !== 'all', function($q) use ($status) {
                return $q->where('status', $status);
            })
            ->count();

        return [
            'total_customers' => $totalCustomers,
            'new_customers' => $newCustomers,
            'returning_customers' => $returningCustomers,
            'at_risk_customers' => $atRiskCustomers
        ];
    }

    /**
     * Get chart data for reports.
     */
    private function getReportCharts($businessId, $start, $end, $status = 'all')
    {
        // Customer acquisition trend
        $acquisitionData = CustomerBusinessProfile::forBusiness($businessId)
            ->whereBetween('customer_since', [$start, $end])
            ->when($status !== 'all', function($q) use ($status) {
                return $q->where('status', $status);
            })
            ->selectRaw('DATE(customer_since) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Status distribution
        $statusData = CustomerBusinessProfile::forBusiness($businessId)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        // Loyalty tier distribution
        $loyaltyData = CustomerBusinessProfile::forBusiness($businessId)
            ->when($status !== 'all', function($q) use ($status) {
                return $q->where('status', $status);
            })
            ->selectRaw('loyalty_tier, COUNT(*) as count')
            ->groupBy('loyalty_tier')
            ->get();

        // Value segments
        $valueData = CustomerBusinessProfile::forBusiness($businessId)
            ->when($status !== 'all', function($q) use ($status) {
                return $q->where('status', $status);
            })
            ->get()
            ->groupBy(function($profile) {
                return $profile->value_segment;
            })
            ->map->count();

        return [
            'acquisition' => [
                'labels' => $acquisitionData->pluck('date')->toArray(),
                'data' => $acquisitionData->pluck('count')->toArray()
            ],
            'status' => [
                'data' => [
                    $statusData->where('status', 'active')->first()->count ?? 0,
                    $statusData->where('status', 'inactive')->first()->count ?? 0,
                    $statusData->where('status', 'vip')->first()->count ?? 0,
                    $statusData->where('status', 'prospect')->first()->count ?? 0
                ]
            ],
            'loyalty' => [
                'data' => [
                    $loyaltyData->where('loyalty_tier', 'bronze')->first()->count ?? 0,
                    $loyaltyData->where('loyalty_tier', 'silver')->first()->count ?? 0,
                    $loyaltyData->where('loyalty_tier', 'gold')->first()->count ?? 0,
                    $loyaltyData->where('loyalty_tier', 'platinum')->first()->count ?? 0,
                    $loyaltyData->where('loyalty_tier', 'diamond')->first()->count ?? 0
                ]
            ],
            'value' => [
                'data' => [
                    $valueData['High Value'] ?? 0,
                    $valueData['Medium Value'] ?? 0,
                    $valueData['Low Value'] ?? 0,
                    $valueData['New Customer'] ?? 0
                ]
            ]
        ];
    }

    /**
     * Get top customers for reports.
     */
    private function getTopCustomers($businessId, $start, $end, $status = 'all', $limit = 10)
    {
        $query = CustomerBusinessProfile::forBusiness($businessId)
            ->with('customer')
            ->when($status !== 'all', function($q) use ($status) {
                return $q->where('status', $status);
            })
            ->orderBy('total_spent', 'desc')
            ->limit($limit);

        return $query->get()->map(function($profile) {
            return [
                'name' => $profile->customer->name,
                'email' => $profile->customer->email,
                'total_spent' => $profile->total_spent,
                'total_visits' => $profile->total_visits,
                'average_order_value' => $profile->average_order_value,
                'last_visit_date' => $profile->last_visit_date?->format('M j, Y'),
                'status' => $profile->status
            ];
        })->toArray();
    }
}
