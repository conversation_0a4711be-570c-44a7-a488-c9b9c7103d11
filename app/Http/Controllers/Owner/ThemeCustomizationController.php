<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Services\ThemeCustomizationService;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ThemeCustomizationController extends Controller
{
    protected $themeService;

    public function __construct(ThemeCustomizationService $themeService)
    {
        $this->themeService = $themeService;
    }

    /**
     * Display the theme customization interface.
     */
    public function index()
    {
        $business = $this->getUserBusiness();
        
        if (!$business) {
            return redirect()->route('owner.business.create')
                ->with('error', 'Please create your business first.');
        }

        $availableThemes = $this->themeService->getAvailableThemes();
        $currentCustomization = $this->themeService->getCustomization($business->id);
        $currentTheme = $business->landingPage->theme ?? 'default';

        return view('owner.theme-customization.index', compact(
            'business',
            'availableThemes',
            'currentCustomization',
            'currentTheme'
        ));
    }

    /**
     * Get theme configuration.
     */
    public function getThemeConfig($themeName)
    {
        $config = $this->themeService->getThemeConfig($themeName);
        
        if (!$config) {
            return response()->json(['error' => 'Theme not found'], 404);
        }

        $defaultCustomization = $this->themeService->getDefaultCustomization($themeName);

        return response()->json([
            'config' => $config,
            'default_customization' => $defaultCustomization
        ]);
    }

    /**
     * Preview theme with customization.
     */
    public function preview(Request $request)
    {
        $business = $this->getUserBusiness();
        
        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $themeName = $request->get('theme', 'default');
        $customization = $request->get('customization', []);

        // Generate preview CSS
        $customCSS = $this->themeService->generateCustomCSS($themeName, $customization);

        return response()->json([
            'success' => true,
            'custom_css' => $customCSS,
            'preview_url' => route('landing-page.index', $business->landing_page_slug) . '?preview=1'
        ]);
    }

    /**
     * Save theme customization.
     */
    public function save(Request $request)
    {
        $business = $this->getUserBusiness();
        
        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $validated = $request->validate([
            'theme' => 'required|string|in:default,modern,elegant,minimal,creative',
            'customization' => 'required|array',
            'customization.colors' => 'sometimes|array',
            'customization.fonts' => 'sometimes|array',
            'customization.layout' => 'sometimes|array',
            'customization.spacing' => 'sometimes|array',
            'customization.animations' => 'sometimes|array',
            'customization.effects' => 'sometimes|array',
        ]);

        $success = $this->themeService->saveCustomization(
            $business->id,
            $validated['theme'],
            $validated['customization']
        );

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Theme customization saved successfully!',
                'preview_url' => route('landing-page.index', $business->landing_page_slug)
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to save theme customization. Please try again.'
        ], 500);
    }

    /**
     * Reset theme to defaults.
     */
    public function reset(Request $request)
    {
        $business = $this->getUserBusiness();
        
        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $themeName = $request->get('theme', 'default');
        $defaultCustomization = $this->themeService->getDefaultCustomization($themeName);

        $success = $this->themeService->saveCustomization(
            $business->id,
            $themeName,
            $defaultCustomization
        );

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Theme reset to defaults successfully!',
                'customization' => $defaultCustomization
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to reset theme. Please try again.'
        ], 500);
    }

    /**
     * Export theme customization.
     */
    public function export()
    {
        $business = $this->getUserBusiness();
        
        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $exportData = $this->themeService->exportCustomization($business->id);

        if (!$exportData) {
            return response()->json(['error' => 'No customization found to export'], 404);
        }

        $filename = 'theme-customization-' . $business->landing_page_slug . '-' . date('Y-m-d') . '.json';

        return response()->json($exportData)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Content-Type', 'application/json');
    }

    /**
     * Import theme customization.
     */
    public function import(Request $request)
    {
        $business = $this->getUserBusiness();
        
        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $validated = $request->validate([
            'import_file' => 'required|file|mimes:json|max:1024', // 1MB max
        ]);

        try {
            $fileContent = file_get_contents($validated['import_file']->getRealPath());
            $importData = json_decode($fileContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid JSON file format.'
                ], 400);
            }

            $success = $this->themeService->importCustomization($business->id, $importData);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Theme customization imported successfully!',
                    'customization' => $this->themeService->getCustomization($business->id)
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to import theme customization.'
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing import file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current customization.
     */
    public function getCurrent()
    {
        $business = $this->getUserBusiness();
        
        if (!$business) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $customization = $this->themeService->getCustomization($business->id);
        $currentTheme = $business->landingPage->theme ?? 'default';

        return response()->json([
            'theme' => $currentTheme,
            'customization' => $customization
        ]);
    }

    /**
     * Get user's business.
     */
    private function getUserBusiness()
    {
        return Business::where('owner_id', Auth::id())->with('landingPage')->first();
    }
}
