<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\Booking;
use App\Models\Service;
use App\Models\Business;
use App\Models\AvailabilityBlock;
use App\Models\BusinessHoliday;
use App\Services\RecurringBookingService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CalendarController extends Controller
{
    /**
     * Display the calendar view.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();
        $view = $request->get('view', 'month'); // day, week, month
        $date = $request->get('date', now()->format('Y-m-d'));

        // Get services for filtering
        $services = $business->services()->active()->ordered()->get();

        return view('owner.calendar.index', compact('business', 'services', 'view', 'date'));
    }

    /**
     * Debug booking creation page.
     */
    public function debugBooking()
    {
        $business = $this->getUserBusiness();
        $services = $business->services()->active()->get();

        return view('owner.calendar.debug-booking', compact('services'));
    }

    /**
     * Diagnostic page for calendar system.
     */
    public function diagnostic()
    {
        $business = $this->getUserBusiness();
        $services = $business ? $business->services()->get() : collect();
        $recentBookings = $business ? $business->bookings()->latest()->limit(10)->get() : collect();

        // Check database tables
        $tableStatus = $this->checkDatabaseTables();

        return view('owner.calendar.diagnostic', compact('business', 'services', 'recentBookings', 'tableStatus'));
    }

    /**
     * Check if required database tables exist.
     */
    private function checkDatabaseTables()
    {
        $tables = [
            'businesses' => false,
            'business_operating_hours' => false,
            'business_holidays' => false,
            'availability_blocks' => false,
            'bookings' => false,
            'services' => false,
        ];

        try {
            foreach ($tables as $table => $exists) {
                $tables[$table] = \Schema::hasTable($table);
            }
        } catch (\Exception $e) {
            \Log::error('Error checking database tables', ['error' => $e->getMessage()]);
        }

        return $tables;
    }

    /**
     * Get calendar events data for the authenticated business owner.
     */
    public function events(Request $request)
    {
        $business = $this->getUserBusiness();
        $start = $request->get('start');
        $end = $request->get('end');
        $view = $request->get('view', 'all'); // all, bookings, blocks
        $serviceId = $request->get('service_id');
        $status = $request->get('status');

        // Advanced search parameters
        $clientSearch = $request->get('client_search');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $timeFrom = $request->get('time_from');
        $timeTo = $request->get('time_to');
        $paymentStatus = $request->get('payment_status');
        $amountFrom = $request->get('amount_from');
        $amountTo = $request->get('amount_to');

        $events = collect();

        // Get bookings for this business only
        if (in_array($view, ['all', 'bookings'])) {
            $bookingsQuery = $business->bookings()
                ->with(['bookingServices.service'])
                ->forDateRange($start, $end);

            if ($serviceId) {
                $bookingsQuery->whereHas('bookingServices', function ($query) use ($serviceId) {
                    $query->where('service_id', $serviceId);
                });
            }

            if ($status) {
                $bookingsQuery->where('status', $status);
            }

            // Client search
            if ($clientSearch) {
                $bookingsQuery->where(function ($query) use ($clientSearch) {
                    $query->where('customer_name', 'like', "%{$clientSearch}%")
                          ->orWhere('customer_email', 'like', "%{$clientSearch}%")
                          ->orWhere('customer_phone', 'like', "%{$clientSearch}%");
                });
            }

            // Date range filter (additional to calendar range)
            if ($dateFrom) {
                $bookingsQuery->whereDate('start_datetime', '>=', $dateFrom);
            }
            if ($dateTo) {
                $bookingsQuery->whereDate('start_datetime', '<=', $dateTo);
            }

            // Time range filter
            if ($timeFrom) {
                $bookingsQuery->whereTime('start_datetime', '>=', $timeFrom);
            }
            if ($timeTo) {
                $bookingsQuery->whereTime('start_datetime', '<=', $timeTo);
            }

            // Payment status filter
            if ($paymentStatus) {
                $bookingsQuery->where('payment_status', $paymentStatus);
            }

            // Amount range filter
            if ($amountFrom) {
                $bookingsQuery->where('total_amount', '>=', $amountFrom);
            }
            if ($amountTo) {
                $bookingsQuery->where('total_amount', '<=', $amountTo);
            }

            $bookings = $bookingsQuery->get();

            foreach ($bookings as $booking) {
                // Get service names safely
                $serviceNames = $booking->bookingServices->map(function ($bs) {
                    return $bs->service ? $bs->service->name : 'Service not found';
                })->join(', ');

                $events->push([
                    'id' => 'booking-' . $booking->id,
                    'title' => $booking->customer_name . ' - ' . $serviceNames,
                    'start' => $booking->start_datetime->toISOString(),
                    'end' => $booking->end_datetime->toISOString(),
                    'backgroundColor' => $this->getBookingColor($booking->status),
                    'borderColor' => $this->getBookingColor($booking->status),
                    'textColor' => '#fff',
                    'extendedProps' => [
                        'type' => 'booking',
                        'booking_id' => $booking->id,
                        'status' => $booking->status,
                        'payment_status' => $booking->payment_status,
                        'customer_name' => $booking->customer_name,
                        'customer_email' => $booking->customer_email,
                        'customer_phone' => $booking->customer_phone,
                        'total_amount' => $booking->total_amount,
                        'services' => $booking->bookingServices->map(function ($bs) {
                            return [
                                'name' => $bs->service ? $bs->service->name : 'Service not found',
                                'duration' => $bs->formatted_duration,
                                'price' => $bs->total_price,
                            ];
                        }),
                    ],
                ]);
            }
        }

        // Get availability blocks for this business only
        if (in_array($view, ['all', 'blocks'])) {
            $blocks = $business->availabilityBlocks()
                ->forDateRange($start, $end)
                ->get();

            foreach ($blocks as $block) {
                $events->push([
                    'id' => 'block-' . $block->id,
                    'title' => $block->title,
                    'start' => $block->start_datetime->toISOString(),
                    'end' => $block->end_datetime->toISOString(),
                    'backgroundColor' => $this->getBlockColor($block->block_type),
                    'borderColor' => $this->getBlockColor($block->block_type),
                    'textColor' => '#fff',
                    'display' => 'background',
                    'extendedProps' => [
                        'type' => 'block',
                        'block_id' => $block->id,
                        'block_type' => $block->block_type,
                        'description' => $block->description,
                        'affects_all_resources' => $block->affects_all_resources,
                    ],
                ]);
            }
        }

        // Get holidays for this business only
        if (in_array($view, ['all', 'holidays'])) {
            $holidays = $business->holidays()
                ->forDateRange($start, $end)
                ->where('is_active', true)
                ->get();

            foreach ($holidays as $holiday) {
                $events->push([
                    'id' => 'holiday-' . $holiday->id,
                    'title' => $holiday->name,
                    'start' => $holiday->start_date->toDateString(),
                    'end' => $holiday->end_date->addDay()->toDateString(), // FullCalendar end is exclusive
                    'backgroundColor' => '#6c757d',
                    'borderColor' => '#6c757d',
                    'textColor' => '#fff',
                    'display' => 'background',
                    'allDay' => true,
                    'extendedProps' => [
                        'type' => 'holiday',
                        'holiday_id' => $holiday->id,
                        'description' => $holiday->description,
                        'is_recurring' => $holiday->is_recurring,
                    ],
                ]);
            }
        }

        return response()->json($events);
    }

    /**
     * Show booking details for modal.
     */
    public function showBooking($bookingId)
    {
        try {
            $business = $this->getUserBusiness();

            if (!$business) {
                return response()->json([
                    'success' => false,
                    'message' => 'Business not found or access denied.',
                ], 403);
            }

            $booking = $business->bookings()
                ->with(['bookingServices.service', 'customer', 'business'])
                ->findOrFail($bookingId);

            // Ensure all required attributes are available
            if (!$booking->booking_number) {
                $booking->booking_number = 'N/A';
            }

            $html = view('owner.calendar.booking-modal', compact('booking'))->render();

            return response()->json([
                'success' => true,
                'html' => $html,
                'booking' => [
                    'id' => $booking->id,
                    'booking_number' => $booking->booking_number,
                    'customer_name' => $booking->customer_name,
                    'customer_email' => $booking->customer_email,
                    'customer_phone' => $booking->customer_phone,
                    'start_datetime' => $booking->start_datetime->format('M d, Y g:i A'),
                    'end_datetime' => $booking->end_datetime->format('M d, Y g:i A'),
                    'status' => $booking->status,
                    'payment_status' => $booking->payment_status,
                    'total_amount' => number_format($booking->total_amount, 2),
                    'paid_amount' => number_format($booking->paid_amount, 2),
                    'remaining_amount' => number_format($booking->total_amount - $booking->paid_amount, 2),
                    'notes' => $booking->notes,
                    'services' => $booking->bookingServices->map(function ($bs) {
                        return [
                            'service_name' => $bs->service ? $bs->service->name : 'Service not found',
                            'duration' => $bs->formatted_duration,
                            'total_price' => number_format($bs->total_price, 2),
                        ];
                    }),
                ],
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Booking not found.',
            ], 404);
        } catch (\Exception $e) {
            \Log::error('Error loading booking details: ' . $e->getMessage(), [
                'booking_id' => $bookingId,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error loading booking details. Please try again.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Show block details for modal.
     */
    public function showBlock($blockId)
    {
        $business = $this->getUserBusiness();
        $block = $business->availabilityBlocks()->findOrFail($blockId);

        $html = view('owner.calendar.block-modal', compact('block'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'block' => [
                'id' => $block->id,
                'title' => $block->title,
                'block_type' => $block->block_type,
                'start_datetime' => $block->start_datetime->format('M d, Y g:i A'),
                'end_datetime' => $block->end_datetime->format('M d, Y g:i A'),
                'duration' => $block->formatted_duration,
                'description' => $block->description,
                'affects_all_resources' => $block->affects_all_resources,
            ],
        ]);
    }





    /**
     * Create a new booking from calendar.
     */
    public function createBooking(Request $request)
    {
        $business = $this->getUserBusiness();

        try {
            // Log the incoming request for debugging
            \Log::info('Calendar booking creation request', [
                'request_data' => $request->all(),
                'business_id' => $business->id,
                'user_id' => auth()->id()
            ]);

            // Custom validation with more detailed error messages
            $validator = \Validator::make($request->all(), [
                'start_datetime' => 'required|date',
                'service_id' => 'required|integer|exists:services,id',
                'customer_name' => 'required|string|max:255',
                'customer_email' => 'required|email|max:255',
                'customer_phone' => 'nullable|string|max:20',
                'participant_count' => 'required|integer|min:1',
                'notes' => 'nullable|string|max:1000',
            ]);

            // Add custom datetime validation
            $validator->after(function ($validator) use ($request) {
                if ($request->start_datetime) {
                    try {
                        $startDateTime = Carbon::parse($request->start_datetime);
                        // Allow bookings from 5 minutes ago to account for timezone/clock differences
                        if ($startDateTime->isBefore(now()->subMinutes(5))) {
                            $validator->errors()->add('start_datetime', 'The booking time cannot be in the past.');
                        }
                    } catch (\Exception $e) {
                        $validator->errors()->add('start_datetime', 'Invalid date format.');
                    }
                }
            });

            if ($validator->fails()) {
                throw new \Illuminate\Validation\ValidationException($validator);
            }

            $validated = $validator->validated();

            // Ensure service belongs to this business
            $service = $business->services()->find($validated['service_id']);

            if (!$service) {
                \Log::error('Service not found or does not belong to business', [
                    'service_id' => $validated['service_id'],
                    'business_id' => $business->id,
                    'available_services' => $business->services()->pluck('id', 'name')->toArray()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Selected service is not available for this business.',
                    'errors' => ['service_id' => ['The selected service is invalid or not available.']]
                ], 422);
            }

            $startDateTime = Carbon::parse($validated['start_datetime']);
            $endDateTime = $startDateTime->copy()->addMinutes($service->total_duration);

            // Check availability
            $availability = $this->checkAvailability($business, $service, $startDateTime);

            if (!$availability['available']) {
                \Log::warning('Booking creation failed - availability check', [
                    'business_id' => $business->id,
                    'service_id' => $service->id,
                    'start_datetime' => $startDateTime,
                    'availability_message' => $availability['message']
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $availability['message'],
                ], 422);
            }

            DB::beginTransaction();

            // Create booking
            $booking = $business->bookings()->create([
                'customer_name' => $validated['customer_name'],
                'customer_email' => $validated['customer_email'],
                'customer_phone' => $validated['customer_phone'],
                'start_datetime' => $startDateTime,
                'end_datetime' => $endDateTime,
                'total_duration_minutes' => $service->total_duration,
                'participant_count' => $validated['participant_count'],
                'subtotal' => $service->base_price,
                'total_amount' => $service->base_price,
                'status' => 'confirmed',
                'payment_status' => 'pending',
                'notes' => $validated['notes'],
            ]);

            // Create booking service
            $booking->bookingServices()->create([
                'service_id' => $service->id,
                'quantity' => 1,
                'unit_price' => $service->base_price,
                'total_price' => $service->base_price,
                'duration_minutes' => $service->duration_minutes,
                'start_datetime' => $startDateTime,
                'end_datetime' => $startDateTime->copy()->addMinutes($service->duration_minutes),
                'service_data' => $service->toArray(),
            ]);

            DB::commit();

            \Log::info('Booking created successfully', [
                'booking_id' => $booking->id,
                'business_id' => $business->id,
                'service_id' => $service->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Booking created successfully.',
                'booking_id' => $booking->id,
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Booking creation validation failed', [
                'errors' => $e->errors(),
                'request_data' => $request->all(),
                'business_id' => $business->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', array_flatten($e->errors())),
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            DB::rollback();

            \Log::error('Booking creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'business_id' => $business->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create booking: ' . $e->getMessage(),
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Update booking from calendar (drag & drop).
     */
    public function updateBooking(Request $request, $bookingId)
    {
        $business = $this->getUserBusiness();

        $booking = $business->bookings()->findOrFail($bookingId);

        $validated = $request->validate([
            'start_datetime' => 'required|date',
            'end_datetime' => 'required|date|after:start_datetime',
        ]);

        $newStartDateTime = Carbon::parse($validated['start_datetime']);
        $newEndDateTime = Carbon::parse($validated['end_datetime']);

        // Check if the booking can be moved
        if ($booking->status === 'cancelled' || $booking->status === 'completed') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot move cancelled or completed bookings.',
            ], 422);
        }

        // Check availability for new time slot
        $service = $booking->bookingServices->first()->service;
        $availability = $this->checkAvailability($business, $service, $newStartDateTime, $booking->id);

        if (!$availability['available']) {
            return response()->json([
                'success' => false,
                'message' => $availability['message'],
            ], 422);
        }

        DB::beginTransaction();
        try {
            // Update booking
            $booking->update([
                'start_datetime' => $newStartDateTime,
                'end_datetime' => $newEndDateTime,
            ]);

            // Update booking services
            foreach ($booking->bookingServices as $bookingService) {
                $serviceDuration = $bookingService->duration_minutes;
                $bookingService->update([
                    'start_datetime' => $newStartDateTime,
                    'end_datetime' => $newStartDateTime->copy()->addMinutes($serviceDuration),
                ]);
                $newStartDateTime->addMinutes($serviceDuration);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Booking updated successfully.',
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update booking. Please try again.',
            ], 500);
        }
    }

    /**
     * Update booking status.
     */
    public function updateBookingStatus(Request $request, $bookingId)
    {
        $business = $this->getUserBusiness();

        $booking = $business->bookings()->findOrFail($bookingId);

        $validated = $request->validate([
            'status' => 'required|in:pending,confirmed,cancelled,completed,no_show',
            'cancellation_reason' => 'nullable|string|max:500',
        ]);

        $oldStatus = $booking->status;

        DB::beginTransaction();
        try {
            $updateData = ['status' => $validated['status']];

            if ($validated['status'] === 'cancelled') {
                $updateData['cancelled_at'] = now();
                $updateData['cancelled_by'] = Auth::id();
                $updateData['cancellation_reason'] = $validated['cancellation_reason'];
            }

            $booking->update($updateData);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Booking status updated from {$oldStatus} to {$validated['status']}.",
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update booking status. Please try again.',
            ], 500);
        }
    }

    /**
     * Create availability block.
     */
    public function createBlock(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'start_datetime' => 'required|date',
            'end_datetime' => 'required|date|after:start_datetime',
            'block_type' => 'required|in:maintenance,holiday,private_event,staff_break,other',
            'affects_all_resources' => 'boolean',
        ]);

        $block = $business->availabilityBlocks()->create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Availability block created successfully.',
            'block_id' => $block->id,
        ]);
    }

    /**
     * Update availability block.
     */
    public function updateBlock(Request $request, $blockId)
    {
        $business = $this->getUserBusiness();

        $block = $business->availabilityBlocks()->findOrFail($blockId);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'start_datetime' => 'required|date',
            'end_datetime' => 'required|date|after:start_datetime',
            'block_type' => 'required|in:maintenance,holiday,private_event,staff_break,other',
            'affects_all_resources' => 'boolean',
        ]);

        $block->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Availability block updated successfully.',
        ]);
    }

    /**
     * Delete availability block.
     */
    public function deleteBlock($blockId)
    {
        $business = $this->getUserBusiness();

        $block = $business->availabilityBlocks()->findOrFail($blockId);

        // Check if block is in the past
        if ($block->start_datetime->isPast()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete blocks that have already started.',
            ], 422);
        }

        $block->delete();

        return response()->json([
            'success' => true,
            'message' => 'Availability block deleted successfully.',
        ]);
    }

    /**
     * Get available time slots for a service on a specific date.
     */
    public function getAvailableSlots(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'service_id' => 'required|exists:services,id',
            'date' => 'required|date|after_or_equal:today',
        ]);

        $service = $business->services()->findOrFail($validated['service_id']);
        $date = Carbon::parse($validated['date']);

        $slots = $this->generateAvailableSlots($business, $service, $date);

        return response()->json([
            'success' => true,
            'slots' => $slots,
        ]);
    }

    /**
     * Helper method to get booking status color.
     */
    private function getBookingColor($status)
    {
        $colors = [
            'pending' => '#ffc107',      // Warning yellow
            'confirmed' => '#17a2b8',    // Info blue
            'in_progress' => '#007bff',  // Primary blue
            'completed' => '#28a745',    // Success green
            'cancelled' => '#dc3545',    // Danger red
            'no_show' => '#6c757d',      // Secondary gray
        ];

        return $colors[$status] ?? '#6c757d';
    }

    /**
     * Helper method to get availability block color.
     */
    private function getBlockColor($blockType)
    {
        $colors = [
            'maintenance' => '#fd7e14',  // Orange
            'holiday' => '#20c997',      // Teal
            'private_event' => '#6f42c1', // Purple
            'staff_break' => '#6c757d',  // Gray
            'other' => '#343a40',        // Dark
        ];

        return $colors[$blockType] ?? '#6c757d';
    }

    /**
     * Check availability for a service at a specific time.
     */
    private function checkAvailability($business, $service, $startDateTime, $excludeBookingId = null)
    {
        try {
            $endDateTime = $startDateTime->copy()->addMinutes($service->total_duration);

            // Check business operating hours
            $dayOfWeek = $startDateTime->dayOfWeek;
            $operatingHours = $business->operatingHours()
                ->where('day_of_week', $dayOfWeek)
                ->where('is_closed', false)
                ->first();

            if (!$operatingHours) {
                // If no operating hours are set, allow booking (assume 24/7 for now)
                // In production, you might want to require operating hours to be set
                \Log::warning('No operating hours found for business', [
                    'business_id' => $business->id,
                    'day_of_week' => $dayOfWeek,
                    'start_datetime' => $startDateTime
                ]);

                // For now, allow the booking but log the issue
                // return [
                //     'available' => false,
                //     'message' => 'Business operating hours not configured for this day.',
                // ];
            } else {
                $openTime = Carbon::parse($startDateTime->toDateString() . ' ' . $operatingHours->open_time);
                $closeTime = Carbon::parse($startDateTime->toDateString() . ' ' . $operatingHours->close_time);

                if ($startDateTime < $openTime || $endDateTime > $closeTime) {
                    return [
                        'available' => false,
                        'message' => 'Selected time is outside business hours (' . $operatingHours->open_time . ' - ' . $operatingHours->close_time . ').',
                    ];
                }
            }

            // Check for conflicting bookings
            try {
                $conflictingBookings = $business->bookings()
                    ->where('status', '!=', 'cancelled')
                    ->when($excludeBookingId, function ($query, $excludeBookingId) {
                        $query->where('id', '!=', $excludeBookingId);
                    })
                    ->where(function ($query) use ($startDateTime, $endDateTime) {
                        $query->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                              ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                              ->orWhere(function ($q) use ($startDateTime, $endDateTime) {
                                  $q->where('start_datetime', '<=', $startDateTime)
                                    ->where('end_datetime', '>=', $endDateTime);
                              });
                    })
                    ->exists();
            } catch (\Exception $e) {
                \Log::warning('Error checking conflicting bookings', [
                    'error' => $e->getMessage(),
                    'business_id' => $business->id
                ]);
                $conflictingBookings = false; // Assume no conflicts if we can't check
            }

            if ($conflictingBookings) {
                return [
                    'available' => false,
                    'message' => 'Time slot conflicts with existing booking.',
                ];
            }

            // Check for availability blocks
            try {
                $conflictingBlocks = $business->availabilityBlocks()
                    ->where(function ($query) use ($startDateTime, $endDateTime) {
                        $query->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                              ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                              ->orWhere(function ($q) use ($startDateTime, $endDateTime) {
                                  $q->where('start_datetime', '<=', $startDateTime)
                                    ->where('end_datetime', '>=', $endDateTime);
                              });
                    })
                    ->exists();
            } catch (\Exception $e) {
                \Log::warning('Error checking availability blocks', [
                    'error' => $e->getMessage(),
                    'business_id' => $business->id
                ]);
                $conflictingBlocks = false; // Assume no blocks if we can't check
            }

            if ($conflictingBlocks) {
                return [
                    'available' => false,
                    'message' => 'Time slot is blocked for maintenance or other reasons.',
                ];
            }

            // Check for holidays
            try {
                $holidayExists = $business->holidays()
                    ->where('is_active', true)
                    ->where(function ($query) use ($startDateTime) {
                        $query->whereDate('start_date', '<=', $startDateTime->toDateString())
                              ->whereDate('end_date', '>=', $startDateTime->toDateString());
                    })
                    ->exists();
            } catch (\Exception $e) {
                \Log::warning('Error checking holidays', [
                    'error' => $e->getMessage(),
                    'business_id' => $business->id
                ]);
                $holidayExists = false; // Assume no holidays if we can't check
            }

            if ($holidayExists) {
                return [
                    'available' => false,
                    'message' => 'Business is closed for holiday.',
                ];
            }

            return [
                'available' => true,
                'message' => 'Time slot is available.',
            ];

        } catch (\Exception $e) {
            \Log::error('Error checking availability', [
                'error' => $e->getMessage(),
                'business_id' => $business->id,
                'service_id' => $service->id,
                'start_datetime' => $startDateTime
            ]);

            return [
                'available' => false,
                'message' => 'Error checking availability. Please try again.',
            ];
        }
    }

    /**
     * Generate available time slots for a service on a specific date.
     */
    private function generateAvailableSlots($business, $service, $date)
    {
        $slots = [];
        $dayOfWeek = $date->dayOfWeek;

        // Get operating hours for the day
        $operatingHours = $business->operatingHours()
            ->where('day_of_week', $dayOfWeek)
            ->where('is_closed', false)
            ->first();

        if (!$operatingHours) {
            return $slots;
        }

        $openTime = Carbon::parse($date->toDateString() . ' ' . $operatingHours->open_time);
        $closeTime = Carbon::parse($date->toDateString() . ' ' . $operatingHours->close_time);

        // Generate slots every 30 minutes (or service interval)
        $interval = 30; // minutes
        $currentTime = $openTime->copy();

        while ($currentTime->copy()->addMinutes($service->total_duration) <= $closeTime) {
            $availability = $this->checkAvailability($business, $service, $currentTime);

            if ($availability['available']) {
                $slots[] = [
                    'time' => $currentTime->format('H:i'),
                    'datetime' => $currentTime->toISOString(),
                    'available' => true,
                ];
            }

            $currentTime->addMinutes($interval);
        }

        return $slots;
    }

    /**
     * Create recurring booking.
     */
    public function createRecurringBooking(Request $request)
    {
        $business = $this->getUserBusiness();

        try {
            $validated = $request->validate([
                'start_datetime' => 'required|date',
                'service_id' => 'required|exists:services,id',
                'customer_name' => 'required|string|max:255',
                'customer_email' => 'required|email|max:255',
                'customer_phone' => 'nullable|string|max:20',
                'participant_count' => 'required|integer|min:1',
                'notes' => 'nullable|string|max:1000',
                'recurrence_data' => 'required|array',
                'recurrence_data.pattern' => 'required|in:daily,weekly,monthly,yearly',
                'recurrence_data.interval' => 'required|integer|min:1',
                'recurrence_data.end_type' => 'required|in:never,after_occurrences,on_date',
                'recurrence_data.max_occurrences' => 'nullable|integer|min:1|max:100',
                'recurrence_data.end_date' => 'nullable|date',
                'recurrence_data.weekly_days' => 'nullable|array',
            ]);

            $service = $business->services()->findOrFail($validated['service_id']);

            // Use RecurringBookingService to create the bookings
            $recurringService = app(\App\Services\RecurringBookingService::class);

            $bookingData = [
                'business_id' => $business->id,
                'service_id' => $service->id,
                'customer_name' => $validated['customer_name'],
                'customer_email' => $validated['customer_email'],
                'customer_phone' => $validated['customer_phone'],
                'start_datetime' => $validated['start_datetime'],
                'total_duration_minutes' => $service->total_duration,
                'participant_count' => $validated['participant_count'],
                'subtotal' => $service->base_price,
                'total_amount' => $service->base_price,
                'status' => 'confirmed',
                'payment_status' => 'pending',
                'notes' => $validated['notes'],
            ];

            $createdBookings = $recurringService->createRecurringBookings($bookingData, $validated['recurrence_data']);

            return response()->json([
                'success' => true,
                'message' => 'Recurring booking created successfully. ' . count($createdBookings) . ' appointments scheduled.',
                'bookings_count' => count($createdBookings),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating recurring booking: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export calendar data.
     */
    public function export(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'format' => 'required|in:csv,pdf,ical',
            'date_range' => 'required|in:current_view,this_week,this_month,next_month,custom',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'include' => 'nullable|array',
        ]);

        // Determine date range
        $startDate = null;
        $endDate = null;

        switch ($validated['date_range']) {
            case 'this_week':
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                break;
            case 'this_month':
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                break;
            case 'next_month':
                $startDate = now()->addMonth()->startOfMonth();
                $endDate = now()->addMonth()->endOfMonth();
                break;
            case 'custom':
                $startDate = $validated['start_date'] ? Carbon::parse($validated['start_date']) : now()->startOfMonth();
                $endDate = $validated['end_date'] ? Carbon::parse($validated['end_date']) : now()->endOfMonth();
                break;
            default: // current_view
                $startDate = $validated['start_date'] ? Carbon::parse($validated['start_date']) : now()->startOfMonth();
                $endDate = $validated['end_date'] ? Carbon::parse($validated['end_date']) : now()->endOfMonth();
        }

        $include = $validated['include'] ?? ['bookings'];

        // Get data based on what to include
        $data = [];

        if (in_array('bookings', $include)) {
            $bookings = $business->bookings()
                ->with(['bookingServices.service'])
                ->whereBetween('start_datetime', [$startDate, $endDate])
                ->get();
            $data['bookings'] = $bookings;
        }

        if (in_array('blocks', $include)) {
            $blocks = $business->availabilityBlocks()
                ->whereBetween('start_datetime', [$startDate, $endDate])
                ->get();
            $data['blocks'] = $blocks;
        }

        // Generate export based on format
        switch ($validated['format']) {
            case 'csv':
                return $this->exportToCsv($data, $startDate, $endDate, $include);
            case 'pdf':
                return $this->exportToPdf($data, $startDate, $endDate, $include);
            case 'ical':
                return $this->exportToIcal($data, $startDate, $endDate, $include);
        }
    }

    /**
     * Export to CSV format.
     */
    private function exportToCsv($data, $startDate, $endDate, $include)
    {
        $filename = 'calendar_export_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data, $include) {
            $file = fopen('php://output', 'w');

            if (in_array('bookings', $include) && isset($data['bookings'])) {
                // Bookings header
                fputcsv($file, ['Type', 'Date', 'Time', 'Customer Name', 'Customer Email', 'Customer Phone', 'Service', 'Status', 'Payment Status', 'Amount', 'Notes']);

                foreach ($data['bookings'] as $booking) {
                    $services = $booking->bookingServices->pluck('service.name')->join(', ');
                    fputcsv($file, [
                        'Booking',
                        $booking->start_datetime->format('Y-m-d'),
                        $booking->start_datetime->format('H:i') . ' - ' . $booking->end_datetime->format('H:i'),
                        $booking->customer_name,
                        $booking->customer_email,
                        $booking->customer_phone,
                        $services,
                        $booking->status,
                        $booking->payment_status,
                        $booking->total_amount,
                        $booking->notes,
                    ]);
                }
            }

            if (in_array('blocks', $include) && isset($data['blocks'])) {
                // Add separator if bookings were included
                if (in_array('bookings', $include)) {
                    fputcsv($file, []);
                }

                // Blocks header
                fputcsv($file, ['Type', 'Date', 'Time', 'Title', 'Block Type', 'Description', 'Affects All Resources']);

                foreach ($data['blocks'] as $block) {
                    fputcsv($file, [
                        'Block',
                        $block->start_datetime->format('Y-m-d'),
                        $block->start_datetime->format('H:i') . ' - ' . $block->end_datetime->format('H:i'),
                        $block->title,
                        $block->block_type,
                        $block->description,
                        $block->affects_all_resources ? 'Yes' : 'No',
                    ]);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export to PDF format.
     */
    private function exportToPdf($data, $startDate, $endDate, $include)
    {
        // For now, return a simple response
        // In a real implementation, you would use a PDF library like DomPDF
        return response()->json([
            'success' => false,
            'message' => 'PDF export feature coming soon',
        ]);
    }

    /**
     * Export to iCal format.
     */
    private function exportToIcal($data, $startDate, $endDate, $include)
    {
        $filename = 'calendar_export_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.ics';

        $headers = [
            'Content-Type' => 'text/calendar',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data, $include) {
            echo "BEGIN:VCALENDAR\r\n";
            echo "VERSION:2.0\r\n";
            echo "PRODID:-//Bookkei//Calendar Export//EN\r\n";
            echo "CALSCALE:GREGORIAN\r\n";

            if (in_array('bookings', $include) && isset($data['bookings'])) {
                foreach ($data['bookings'] as $booking) {
                    $services = $booking->bookingServices->pluck('service.name')->join(', ');

                    echo "BEGIN:VEVENT\r\n";
                    echo "UID:" . $booking->id . "@bookkei.com\r\n";
                    echo "DTSTART:" . $booking->start_datetime->format('Ymd\THis\Z') . "\r\n";
                    echo "DTEND:" . $booking->end_datetime->format('Ymd\THis\Z') . "\r\n";
                    echo "SUMMARY:" . $services . " - " . $booking->customer_name . "\r\n";
                    echo "DESCRIPTION:Customer: " . $booking->customer_name . "\\nEmail: " . $booking->customer_email . "\\nPhone: " . $booking->customer_phone . "\\nStatus: " . $booking->status . "\r\n";
                    echo "END:VEVENT\r\n";
                }
            }

            if (in_array('blocks', $include) && isset($data['blocks'])) {
                foreach ($data['blocks'] as $block) {
                    echo "BEGIN:VEVENT\r\n";
                    echo "UID:block-" . $block->id . "@bookkei.com\r\n";
                    echo "DTSTART:" . $block->start_datetime->format('Ymd\THis\Z') . "\r\n";
                    echo "DTEND:" . $block->end_datetime->format('Ymd\THis\Z') . "\r\n";
                    echo "SUMMARY:" . $block->title . "\r\n";
                    echo "DESCRIPTION:" . $block->description . "\\nType: " . $block->block_type . "\r\n";
                    echo "END:VEVENT\r\n";
                }
            }

            echo "END:VCALENDAR\r\n";
        };

        return response()->stream($callback, 200, $headers);
    }
}
