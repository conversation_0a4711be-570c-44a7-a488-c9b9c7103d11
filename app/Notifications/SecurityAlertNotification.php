<?php

namespace App\Notifications;

use App\Models\SecurityAlert;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SecurityAlertNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $alert;

    /**
     * Create a new notification instance.
     */
    public function __construct(SecurityAlert $alert)
    {
        $this->alert = $alert;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        $channels = ['database'];
        
        // Send email for critical and high severity alerts
        if (in_array($this->alert->severity, ['critical', 'high'])) {
            $channels[] = 'mail';
        }
        
        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = $this->getEmailSubject();
        $greeting = $this->getEmailGreeting();
        $message = $this->getEmailMessage();
        
        $mail = (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($message)
            ->line('**Alert Details:**')
            ->line('Type: ' . $this->alert->type_name)
            ->line('Severity: ' . $this->alert->severity_name)
            ->line('Time: ' . $this->alert->created_at->format('Y-m-d H:i:s'))
            ->line('IP Address: ' . ($this->alert->ip_address ?? 'Unknown'));
            
        if ($this->alert->user) {
            $mail->line('User: ' . $this->alert->user->name . ' (' . $this->alert->user->email . ')');
        }
        
        if ($this->alert->details) {
            $mail->line('Additional Details:');
            foreach ($this->alert->details as $key => $value) {
                $mail->line('- ' . ucfirst(str_replace('_', ' ', $key)) . ': ' . (is_array($value) ? implode(', ', $value) : $value));
            }
        }
        
        $mail->action('View Security Dashboard', url('/admin/security/alerts'))
            ->line('Please investigate this alert immediately.')
            ->line('This is an automated security notification from the Bookkei system.');
            
        return $mail;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'alert_id' => $this->alert->id,
            'type' => $this->alert->type,
            'severity' => $this->alert->severity,
            'message' => $this->alert->message,
            'user_id' => $this->alert->user_id,
            'ip_address' => $this->alert->ip_address,
            'timestamp' => $this->alert->created_at->toISOString(),
            'details' => $this->alert->details,
        ];
    }

    /**
     * Get email subject based on alert severity
     */
    private function getEmailSubject(): string
    {
        $prefix = match ($this->alert->severity) {
            'critical' => '🚨 CRITICAL SECURITY ALERT',
            'high' => '⚠️ HIGH SECURITY ALERT',
            'medium' => '⚡ SECURITY ALERT',
            'low' => '📋 SECURITY NOTICE',
            default => '🔔 SECURITY ALERT',
        };
        
        return $prefix . ' - ' . $this->alert->type_name;
    }

    /**
     * Get email greeting based on alert severity
     */
    private function getEmailGreeting(): string
    {
        return match ($this->alert->severity) {
            'critical' => 'URGENT: Critical Security Alert Detected!',
            'high' => 'Important: High Priority Security Alert',
            'medium' => 'Security Alert Notification',
            'low' => 'Security Notice',
            default => 'Security Alert',
        };
    }

    /**
     * Get email message based on alert type
     */
    private function getEmailMessage(): string
    {
        $baseMessage = $this->alert->message;
        
        $typeMessages = [
            'privilege_escalation' => 'A user has attempted to escalate their privileges beyond their authorized level. This could indicate a compromised account or malicious activity.',
            'brute_force' => 'Multiple failed login attempts have been detected from the same IP address. This may indicate a brute force attack.',
            'unusual_access' => 'Unusual access patterns have been detected that deviate from normal user behavior.',
            'sensitive_access' => 'A user has accessed sensitive system permissions that require monitoring.',
            'hierarchy_violation' => 'An attempt to violate the role hierarchy has been detected.',
            'off_hours_access' => 'A high-privilege user has accessed the system during off-hours.',
        ];
        
        $additionalMessage = $typeMessages[$this->alert->type] ?? 'A security event has been detected that requires attention.';
        
        return $baseMessage . "\n\n" . $additionalMessage;
    }

    /**
     * Determine if the notification should be sent immediately
     */
    public function shouldSend(object $notifiable): bool
    {
        // Always send critical alerts
        if ($this->alert->severity === 'critical') {
            return true;
        }
        
        // Check user preferences for other severity levels
        // This could be expanded to include user notification preferences
        return true;
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'security_alert';
    }
}
