<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/dashboard';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        // Define route model bindings
        Route::model('location', \App\Models\BusinessBranch::class);

        // Custom binding for resource types with business isolation
        Route::bind('resourceType', function ($value) {
            // For owner routes, ensure resource type belongs to user's business
            if (request()->is('owner/*')) {
                $user = auth()->user();
                if ($user && $user->ownedBusinesses()->exists()) {
                    $business = $user->ownedBusinesses()->active()->first();
                    if ($business) {
                        return $business->resourceTypes()->where('slug', $value)->firstOrFail();
                    }
                }
                abort(403, 'Unauthorized access to resource type.');
            }

            // For admin routes, allow access to any resource type
            return \App\Models\ResourceType::where('slug', $value)->firstOrFail();
        });

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            // Landing page routes
            Route::middleware('web')
                ->group(base_path('routes/landing-pages.php'));
        });
    }
}
