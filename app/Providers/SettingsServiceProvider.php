<?php

namespace App\Providers;

use App\Models\Setting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the settings helper function
        $this->app->singleton('settings', function ($app) {
            return new \App\Services\SettingsService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only load settings if the database is available and the settings table exists
        try {
            if (Schema::hasTable('settings')) {
                $publicSettings = Setting::where('is_public', true)->get();

                foreach ($publicSettings as $setting) {
                    config(['settings.' . $setting->key => $setting->value]);
                }

                // Override mail config with settings from the database
                $mailFromAddress = Setting::getValue('mail_from_address');
                $mailFromName = Setting::getValue('mail_from_name');

                if ($mailFromAddress) {
                    config(['mail.from.address' => $mailFromAddress]);
                }

                if ($mailFromName) {
                    config(['mail.from.name' => $mailFromName]);
                }
            }
        } catch (\Exception $e) {
            // Database may not be available yet, that's ok during migrations
            report($e);
        }

        // Note: Helper function 'setting()' is defined in bootstrap/helpers.php
    }
}
