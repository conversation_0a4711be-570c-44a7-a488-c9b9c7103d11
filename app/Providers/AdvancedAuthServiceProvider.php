<?php

namespace App\Providers;

use App\Http\Middleware\PermissionMiddleware;
use App\Listeners\LogSuccessfulLogin;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class AdvancedAuthServiceProvider extends ServiceProvider
{
    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Register event listeners
        $this->app['events']->listen(Login::class, LogSuccessfulLogin::class);

        // Register the permission middleware
        Route::aliasMiddleware('permission', PermissionMiddleware::class);
    }
}
