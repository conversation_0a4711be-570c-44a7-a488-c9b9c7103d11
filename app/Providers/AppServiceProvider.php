<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register model observers for cache invalidation
        \App\Models\Business::observe(\App\Observers\BusinessObserver::class);
        \App\Models\Service::observe(\App\Observers\ServiceObserver::class);

        // Handle owner AdminLTE configuration
        if (request()->is('owner*')) {
            $this->configureOwnerAdminLTE();
        }
    }

    /**
     * Configure AdminLTE for owner panel
     */
    private function configureOwnerAdminLTE()
    {
        try {
            $ownerConfig = config('ownerlte');

            if ($ownerConfig && is_array($ownerConfig)) {
                // Get current adminlte config as fallback
                $adminConfig = config('adminlte', []);

                // Merge configurations, giving priority to owner config
                $mergedConfig = array_merge($adminConfig, $ownerConfig);

                // Ensure critical arrays exist and are valid
                $mergedConfig['plugins'] = $this->ensureValidPlugins(
                    $ownerConfig['plugins'] ?? [],
                    $adminConfig['plugins'] ?? []
                );

                $mergedConfig['menu'] = $this->ensureValidMenu($ownerConfig['menu'] ?? []);

                $mergedConfig['filters'] = $this->ensureValidFilters(
                    $ownerConfig['filters'] ?? [],
                    $adminConfig['filters'] ?? []
                );

                // Set the merged configuration
                config(['adminlte' => $mergedConfig]);
            }
        } catch (\Exception $e) {
            // Log the error but don't break the application
            Log::warning('Failed to configure owner AdminLTE: ' . $e->getMessage());

            // Ensure a basic configuration exists
            $this->ensureBasicAdminLTEConfig();
        }
    }

    /**
     * Ensure plugins configuration is valid
     */
    private function ensureValidPlugins(array $ownerPlugins, array $adminPlugins): array
    {
        $plugins = is_array($adminPlugins) ? $adminPlugins : [];

        if (is_array($ownerPlugins)) {
            foreach ($ownerPlugins as $pluginName => $pluginConfig) {
                if (is_string($pluginName) && is_array($pluginConfig)) {
                    $plugins[$pluginName] = $pluginConfig;
                }
            }
        }

        return $plugins;
    }

    /**
     * Ensure menu configuration is valid
     */
    private function ensureValidMenu(array $menu): array
    {
        return is_array($menu) ? $menu : [];
    }

    /**
     * Ensure filters configuration is valid
     */
    private function ensureValidFilters(array $ownerFilters, array $adminFilters): array
    {
        if (is_array($ownerFilters) && !empty($ownerFilters)) {
            return $ownerFilters;
        }

        return is_array($adminFilters) ? $adminFilters : [];
    }

    /**
     * Ensure basic AdminLTE configuration exists
     */
    private function ensureBasicAdminLTEConfig(): void
    {
        $currentConfig = config('adminlte', []);

        if (!isset($currentConfig['plugins']) || !is_array($currentConfig['plugins'])) {
            $currentConfig['plugins'] = [];
        }

        if (!isset($currentConfig['menu']) || !is_array($currentConfig['menu'])) {
            $currentConfig['menu'] = [];
        }

        if (!isset($currentConfig['filters']) || !is_array($currentConfig['filters'])) {
            $currentConfig['filters'] = [];
        }

        config(['adminlte' => $currentConfig]);
    }
}
