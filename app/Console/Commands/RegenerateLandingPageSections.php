<?php

namespace App\Console\Commands;

use App\Models\BusinessLandingPage;
use Illuminate\Console\Command;

class RegenerateLandingPageSections extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'landing-page:regenerate-sections {--force : Force regeneration even if sections exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate landing page sections with all new section types';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting landing page sections regeneration...');

        $landingPages = BusinessLandingPage::with(['business', 'sections'])->get();

        if ($landingPages->isEmpty()) {
            $this->warn('No landing pages found.');
            return;
        }

        $force = $this->option('force');
        $regenerated = 0;

        foreach ($landingPages as $landingPage) {
            $business = $landingPage->business;

            if (!$business) {
                $this->warn("Skipping landing page {$landingPage->id} - no associated business found.");
                continue;
            }

            $existingSections = $landingPage->sections()->count();

            if ($existingSections > 0 && !$force) {
                $this->line("Skipping {$business->name} - already has {$existingSections} sections (use --force to regenerate)");
                continue;
            }

            if ($force && $existingSections > 0) {
                // Delete existing sections
                $landingPage->sections()->delete();
                $this->line("Deleted {$existingSections} existing sections for {$business->name}");
            }

            // Generate new sections
            $landingPage->generateDefaultSections();
            $newSectionsCount = $landingPage->sections()->count();

            $this->info("✓ Generated {$newSectionsCount} sections for {$business->name}");
            $regenerated++;
        }

        $this->info("Regeneration complete! Updated {$regenerated} landing pages.");

        if ($regenerated > 0) {
            $this->line('');
            $this->line('New sections available:');
            $this->line('• Hero Section');
            $this->line('• About Us');
            $this->line('• Our Services');
            $this->line('• Why Choose Us (Features)');
            $this->line('• Customer Reviews (Testimonials)');
            $this->line('• Our Team');
            $this->line('• Gallery');
            $this->line('• Pricing');
            $this->line('• FAQ');
            $this->line('• Call to Action');
            $this->line('• Contact Us');
        }
    }
}
