<?php

namespace App\Services;

use App\Models\RoleAuditLog;
use App\Models\SecurityAlert;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class DataRetentionService
{
    /**
     * Data retention policies configuration
     */
    private array $retentionPolicies = [
        'audit_logs' => [
            'table' => 'role_audit_logs',
            'model' => RoleAuditLog::class,
            'retention_period_days' => 2555, // 7 years for compliance
            'archive_period_days' => 365, // Archive after 1 year
            'critical_retention_days' => 3650, // 10 years for critical events
        ],
        'security_alerts' => [
            'table' => 'security_alerts',
            'model' => SecurityAlert::class,
            'retention_period_days' => 1825, // 5 years
            'archive_period_days' => 365, // Archive after 1 year
            'critical_retention_days' => 2555, // 7 years for critical alerts
        ],
        'user_sessions' => [
            'table' => 'sessions',
            'model' => null,
            'retention_period_days' => 90, // 3 months
            'archive_period_days' => 30, // Archive after 1 month
        ],
        'login_attempts' => [
            'table' => 'failed_login_attempts',
            'model' => null,
            'retention_period_days' => 365, // 1 year
            'archive_period_days' => 90, // Archive after 3 months
        ],
    ];

    /**
     * Execute data retention policies
     */
    public function executeRetentionPolicies(): array
    {
        $results = [];
        
        foreach ($this->retentionPolicies as $dataType => $policy) {
            try {
                $result = $this->processRetentionPolicy($dataType, $policy);
                $results[$dataType] = $result;
                
                Log::info("Data retention policy executed for {$dataType}", $result);
            } catch (\Exception $e) {
                $results[$dataType] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'archived_count' => 0,
                    'deleted_count' => 0,
                ];
                
                Log::error("Data retention policy failed for {$dataType}", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }
        
        return $results;
    }

    /**
     * Process individual retention policy
     */
    private function processRetentionPolicy(string $dataType, array $policy): array
    {
        $archiveDate = now()->subDays($policy['archive_period_days']);
        $deleteDate = now()->subDays($policy['retention_period_days']);
        $criticalDeleteDate = now()->subDays($policy['critical_retention_days'] ?? $policy['retention_period_days']);
        
        $archivedCount = 0;
        $deletedCount = 0;
        
        // Archive old data
        if ($this->shouldArchive($dataType)) {
            $archivedCount = $this->archiveData($dataType, $policy, $archiveDate);
        }
        
        // Delete expired data
        $deletedCount = $this->deleteExpiredData($dataType, $policy, $deleteDate, $criticalDeleteDate);
        
        return [
            'success' => true,
            'archived_count' => $archivedCount,
            'deleted_count' => $deletedCount,
            'archive_date' => $archiveDate->format('Y-m-d'),
            'delete_date' => $deleteDate->format('Y-m-d'),
        ];
    }

    /**
     * Archive data to long-term storage
     */
    private function archiveData(string $dataType, array $policy, Carbon $archiveDate): int
    {
        if (!isset($policy['model']) || !$policy['model']) {
            return $this->archiveTableData($policy['table'], $archiveDate);
        }
        
        $model = $policy['model'];
        $query = $model::where('created_at', '<', $archiveDate);
        
        // Don't archive already archived data
        if (method_exists($model, 'whereNotArchived')) {
            $query->whereNotArchived();
        }
        
        $dataToArchive = $query->get();
        
        if ($dataToArchive->isEmpty()) {
            return 0;
        }
        
        // Create archive file
        $archiveFileName = $this->createArchiveFile($dataType, $dataToArchive, $archiveDate);
        
        // Mark data as archived
        $archivedCount = $this->markAsArchived($model, $dataToArchive, $archiveFileName);
        
        return $archivedCount;
    }

    /**
     * Delete expired data
     */
    private function deleteExpiredData(string $dataType, array $policy, Carbon $deleteDate, Carbon $criticalDeleteDate): int
    {
        if (!isset($policy['model']) || !$policy['model']) {
            return $this->deleteTableData($policy['table'], $deleteDate);
        }
        
        $model = $policy['model'];
        $deletedCount = 0;
        
        // Delete non-critical data
        $nonCriticalQuery = $model::where('created_at', '<', $deleteDate);
        
        // For audit logs and security alerts, check risk level
        if ($dataType === 'audit_logs') {
            $nonCriticalQuery->where('risk_level', '<=', 2);
        } elseif ($dataType === 'security_alerts') {
            $nonCriticalQuery->whereNotIn('severity', ['critical', 'high']);
        }
        
        $deletedCount += $nonCriticalQuery->delete();
        
        // Delete critical data (older retention period)
        $criticalQuery = $model::where('created_at', '<', $criticalDeleteDate);
        
        if ($dataType === 'audit_logs') {
            $criticalQuery->where('risk_level', '>', 2);
        } elseif ($dataType === 'security_alerts') {
            $criticalQuery->whereIn('severity', ['critical', 'high']);
        }
        
        $deletedCount += $criticalQuery->delete();
        
        return $deletedCount;
    }

    /**
     * Create archive file
     */
    private function createArchiveFile(string $dataType, $data, Carbon $archiveDate): string
    {
        $fileName = "archive_{$dataType}_{$archiveDate->format('Y_m_d')}.json";
        $archivePath = "archives/{$dataType}/{$fileName}";
        
        $archiveData = [
            'metadata' => [
                'data_type' => $dataType,
                'archive_date' => $archiveDate->toISOString(),
                'created_at' => now()->toISOString(),
                'record_count' => $data->count(),
                'retention_policy' => $this->retentionPolicies[$dataType],
            ],
            'data' => $data->toArray(),
        ];
        
        // Compress and encrypt the archive
        $compressedData = gzcompress(json_encode($archiveData), 9);
        $encryptedData = encrypt($compressedData);
        
        Storage::disk('local')->put($archivePath, $encryptedData);
        
        Log::info("Archive created for {$dataType}", [
            'file_name' => $fileName,
            'record_count' => $data->count(),
            'file_size' => strlen($encryptedData),
        ]);
        
        return $fileName;
    }

    /**
     * Mark data as archived
     */
    private function markAsArchived($model, $data, string $archiveFileName): int
    {
        $ids = $data->pluck('id')->toArray();
        
        // If the model supports archiving, mark as archived
        if (method_exists($model, 'markAsArchived')) {
            return $model::whereIn('id', $ids)->update([
                'archived_at' => now(),
                'archive_file' => $archiveFileName,
            ]);
        }
        
        // Otherwise, we'll delete the data after archiving
        return $model::whereIn('id', $ids)->delete();
    }

    /**
     * Archive table data (for tables without models)
     */
    private function archiveTableData(string $table, Carbon $archiveDate): int
    {
        $data = DB::table($table)
            ->where('created_at', '<', $archiveDate)
            ->get();
            
        if ($data->isEmpty()) {
            return 0;
        }
        
        $fileName = $this->createArchiveFile($table, $data, $archiveDate);
        
        // Delete the archived data
        return DB::table($table)
            ->where('created_at', '<', $archiveDate)
            ->delete();
    }

    /**
     * Delete table data (for tables without models)
     */
    private function deleteTableData(string $table, Carbon $deleteDate): int
    {
        return DB::table($table)
            ->where('created_at', '<', $deleteDate)
            ->delete();
    }

    /**
     * Check if data type should be archived
     */
    private function shouldArchive(string $dataType): bool
    {
        // Don't archive session data or login attempts
        return !in_array($dataType, ['user_sessions', 'login_attempts']);
    }

    /**
     * Get retention policy status
     */
    public function getRetentionPolicyStatus(): array
    {
        $status = [];
        
        foreach ($this->retentionPolicies as $dataType => $policy) {
            $archiveDate = now()->subDays($policy['archive_period_days']);
            $deleteDate = now()->subDays($policy['retention_period_days']);
            
            $stats = $this->getDataTypeStats($dataType, $policy, $archiveDate, $deleteDate);
            
            $status[$dataType] = [
                'policy' => $policy,
                'stats' => $stats,
                'compliance_status' => $this->checkComplianceStatus($stats),
                'next_action_date' => $this->getNextActionDate($dataType),
            ];
        }
        
        return $status;
    }

    /**
     * Get statistics for a data type
     */
    private function getDataTypeStats(string $dataType, array $policy, Carbon $archiveDate, Carbon $deleteDate): array
    {
        if (!isset($policy['model']) || !$policy['model']) {
            return $this->getTableStats($policy['table'], $archiveDate, $deleteDate);
        }
        
        $model = $policy['model'];
        
        return [
            'total_records' => $model::count(),
            'records_to_archive' => $model::where('created_at', '<', $archiveDate)->count(),
            'records_to_delete' => $model::where('created_at', '<', $deleteDate)->count(),
            'archived_records' => method_exists($model, 'archived') ? $model::archived()->count() : 0,
            'oldest_record' => $model::oldest('created_at')->value('created_at'),
            'newest_record' => $model::latest('created_at')->value('created_at'),
        ];
    }

    /**
     * Get table statistics
     */
    private function getTableStats(string $table, Carbon $archiveDate, Carbon $deleteDate): array
    {
        return [
            'total_records' => DB::table($table)->count(),
            'records_to_archive' => DB::table($table)->where('created_at', '<', $archiveDate)->count(),
            'records_to_delete' => DB::table($table)->where('created_at', '<', $deleteDate)->count(),
            'archived_records' => 0,
            'oldest_record' => DB::table($table)->min('created_at'),
            'newest_record' => DB::table($table)->max('created_at'),
        ];
    }

    /**
     * Check compliance status
     */
    private function checkComplianceStatus(array $stats): array
    {
        $compliance = [
            'is_compliant' => true,
            'issues' => [],
            'score' => 100,
        ];
        
        // Check if there are too many records to delete
        if ($stats['records_to_delete'] > 1000) {
            $compliance['is_compliant'] = false;
            $compliance['issues'][] = 'Large number of records pending deletion';
            $compliance['score'] -= 20;
        }
        
        // Check if there are too many records to archive
        if ($stats['records_to_archive'] > 5000) {
            $compliance['is_compliant'] = false;
            $compliance['issues'][] = 'Large number of records pending archival';
            $compliance['score'] -= 15;
        }
        
        // Check if oldest record is too old
        if ($stats['oldest_record'] && Carbon::parse($stats['oldest_record'])->lt(now()->subYears(10))) {
            $compliance['is_compliant'] = false;
            $compliance['issues'][] = 'Records older than 10 years detected';
            $compliance['score'] -= 25;
        }
        
        return $compliance;
    }

    /**
     * Get next action date for data type
     */
    private function getNextActionDate(string $dataType): Carbon
    {
        // Schedule next retention policy execution
        return now()->addDays(7); // Weekly execution
    }

    /**
     * Restore data from archive
     */
    public function restoreFromArchive(string $dataType, string $archiveFileName): array
    {
        $archivePath = "archives/{$dataType}/{$archiveFileName}";
        
        if (!Storage::disk('local')->exists($archivePath)) {
            throw new \Exception("Archive file not found: {$archiveFileName}");
        }
        
        // Decrypt and decompress the archive
        $encryptedData = Storage::disk('local')->get($archivePath);
        $compressedData = decrypt($encryptedData);
        $archiveData = json_decode(gzuncompress($compressedData), true);
        
        if (!$archiveData || !isset($archiveData['data'])) {
            throw new \Exception("Invalid archive file format");
        }
        
        $restoredCount = 0;
        $policy = $this->retentionPolicies[$dataType];
        
        if (isset($policy['model']) && $policy['model']) {
            $model = $policy['model'];
            foreach ($archiveData['data'] as $record) {
                // Remove archive-specific fields
                unset($record['archived_at'], $record['archive_file']);
                $model::create($record);
                $restoredCount++;
            }
        } else {
            // Restore to table directly
            foreach ($archiveData['data'] as $record) {
                DB::table($policy['table'])->insert($record);
                $restoredCount++;
            }
        }
        
        Log::info("Data restored from archive", [
            'data_type' => $dataType,
            'archive_file' => $archiveFileName,
            'restored_count' => $restoredCount,
        ]);
        
        return [
            'success' => true,
            'restored_count' => $restoredCount,
            'archive_metadata' => $archiveData['metadata'],
        ];
    }

    /**
     * Get available archives
     */
    public function getAvailableArchives(): array
    {
        $archives = [];
        
        foreach (array_keys($this->retentionPolicies) as $dataType) {
            $archivePath = "archives/{$dataType}";
            $files = Storage::disk('local')->files($archivePath);
            
            $archives[$dataType] = array_map(function ($file) {
                $fileName = basename($file);
                $size = Storage::disk('local')->size($file);
                $lastModified = Storage::disk('local')->lastModified($file);
                
                return [
                    'file_name' => $fileName,
                    'file_path' => $file,
                    'size_bytes' => $size,
                    'size_human' => $this->formatBytes($size),
                    'last_modified' => Carbon::createFromTimestamp($lastModified)->format('Y-m-d H:i:s'),
                ];
            }, $files);
        }
        
        return $archives;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get retention policy configuration
     */
    public function getRetentionPolicies(): array
    {
        return $this->retentionPolicies;
    }

    /**
     * Update retention policy
     */
    public function updateRetentionPolicy(string $dataType, array $newPolicy): bool
    {
        if (!isset($this->retentionPolicies[$dataType])) {
            throw new \Exception("Unknown data type: {$dataType}");
        }
        
        $this->retentionPolicies[$dataType] = array_merge(
            $this->retentionPolicies[$dataType],
            $newPolicy
        );
        
        // In a real implementation, this would be saved to configuration
        Log::info("Retention policy updated", [
            'data_type' => $dataType,
            'new_policy' => $newPolicy,
        ]);
        
        return true;
    }
}
