<?php

namespace App\Services;

use App\Models\Business;
use App\Models\BusinessLandingPage;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class ThemeCustomizationService
{
    const CACHE_TTL = 3600; // 1 hour
    const THEME_CACHE_PREFIX = 'theme_custom:';

    /**
     * Available themes with their configurations.
     */
    private $availableThemes = [
        'default' => [
            'name' => 'Default',
            'description' => 'Clean and professional design',
            'preview_image' => '/images/themes/default-preview.jpg',
            'customizable_elements' => ['colors', 'fonts', 'layout', 'spacing']
        ],
        'modern' => [
            'name' => 'Modern',
            'description' => 'Contemporary design with bold elements',
            'preview_image' => '/images/themes/modern-preview.jpg',
            'customizable_elements' => ['colors', 'fonts', 'layout', 'spacing', 'animations']
        ],
        'elegant' => [
            'name' => 'Elegant',
            'description' => 'Sophisticated and refined appearance',
            'preview_image' => '/images/themes/elegant-preview.jpg',
            'customizable_elements' => ['colors', 'fonts', 'layout', 'spacing']
        ],
        'minimal' => [
            'name' => 'Minimal',
            'description' => 'Simple and clean design',
            'preview_image' => '/images/themes/minimal-preview.jpg',
            'customizable_elements' => ['colors', 'fonts', 'spacing']
        ],
        'creative' => [
            'name' => 'Creative',
            'description' => 'Artistic and unique design',
            'preview_image' => '/images/themes/creative-preview.jpg',
            'customizable_elements' => ['colors', 'fonts', 'layout', 'spacing', 'animations', 'effects']
        ]
    ];

    /**
     * Get available themes.
     */
    public function getAvailableThemes(): array
    {
        return $this->availableThemes;
    }

    /**
     * Get theme configuration.
     */
    public function getThemeConfig($themeName): ?array
    {
        return $this->availableThemes[$themeName] ?? null;
    }

    /**
     * Get default customization options for a theme.
     */
    public function getDefaultCustomization($themeName): array
    {
        $baseCustomization = [
            'colors' => [
                'primary' => '#007bff',
                'secondary' => '#6c757d',
                'accent' => '#28a745',
                'background' => '#ffffff',
                'text' => '#333333',
                'text_light' => '#666666',
                'border' => '#dee2e6',
                'success' => '#28a745',
                'warning' => '#ffc107',
                'danger' => '#dc3545',
                'info' => '#17a2b8'
            ],
            'fonts' => [
                'primary_font' => 'Inter',
                'secondary_font' => 'Playfair Display',
                'font_sizes' => [
                    'xs' => '12px',
                    'sm' => '14px',
                    'base' => '16px',
                    'lg' => '18px',
                    'xl' => '20px',
                    '2xl' => '24px',
                    '3xl' => '30px',
                    '4xl' => '36px',
                    '5xl' => '48px'
                ]
            ],
            'layout' => [
                'container_width' => '1200px',
                'section_padding' => '80px',
                'element_spacing' => '20px',
                'border_radius' => '8px',
                'box_shadow' => '0 2px 10px rgba(0,0,0,0.1)'
            ],
            'spacing' => [
                'xs' => '4px',
                'sm' => '8px',
                'md' => '16px',
                'lg' => '24px',
                'xl' => '32px',
                '2xl' => '48px',
                '3xl' => '64px'
            ]
        ];

        // Add theme-specific customizations
        switch ($themeName) {
            case 'modern':
                $baseCustomization['animations'] = [
                    'enable_animations' => true,
                    'animation_speed' => 'normal',
                    'hover_effects' => true,
                    'scroll_animations' => true
                ];
                $baseCustomization['colors']['primary'] = '#6366f1';
                $baseCustomization['layout']['border_radius'] = '12px';
                break;

            case 'elegant':
                $baseCustomization['colors']['primary'] = '#8b5a3c';
                $baseCustomization['colors']['secondary'] = '#f5f5f0';
                $baseCustomization['fonts']['primary_font'] = 'Playfair Display';
                $baseCustomization['layout']['section_padding'] = '100px';
                break;

            case 'minimal':
                $baseCustomization['colors']['primary'] = '#000000';
                $baseCustomization['colors']['background'] = '#fafafa';
                $baseCustomization['layout']['section_padding'] = '60px';
                $baseCustomization['layout']['border_radius'] = '4px';
                break;

            case 'creative':
                $baseCustomization['colors']['primary'] = '#ff6b6b';
                $baseCustomization['colors']['accent'] = '#4ecdc4';
                $baseCustomization['animations'] = [
                    'enable_animations' => true,
                    'animation_speed' => 'fast',
                    'hover_effects' => true,
                    'scroll_animations' => true,
                    'parallax_effects' => true
                ];
                $baseCustomization['effects'] = [
                    'gradients' => true,
                    'shadows' => 'enhanced',
                    'blur_effects' => true
                ];
                break;
        }

        return $baseCustomization;
    }

    /**
     * Save theme customization.
     */
    public function saveCustomization($businessId, $themeName, $customization): bool
    {
        try {
            $landingPage = BusinessLandingPage::where('business_id', $businessId)->first();
            
            if (!$landingPage) {
                return false;
            }

            // Validate customization data
            $validatedCustomization = $this->validateCustomization($themeName, $customization);

            // Update landing page with new customization
            $landingPage->update([
                'theme' => $themeName,
                'theme_customization' => $validatedCustomization,
                'custom_css' => $this->generateCustomCSS($themeName, $validatedCustomization),
                'last_customized_at' => now()
            ]);

            // Clear theme cache
            $this->clearThemeCache($businessId);

            return true;

        } catch (\Exception $e) {
            \Log::error('Failed to save theme customization', [
                'business_id' => $businessId,
                'theme' => $themeName,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get theme customization for a business.
     */
    public function getCustomization($businessId): array
    {
        $cacheKey = self::THEME_CACHE_PREFIX . $businessId;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($businessId) {
            $landingPage = BusinessLandingPage::where('business_id', $businessId)->first();
            
            if (!$landingPage) {
                return $this->getDefaultCustomization('default');
            }

            $themeName = $landingPage->theme ?? 'default';
            $customization = $landingPage->theme_customization ?? [];

            // Merge with defaults to ensure all options are available
            $defaultCustomization = $this->getDefaultCustomization($themeName);
            
            return array_merge_recursive($defaultCustomization, $customization);
        });
    }

    /**
     * Generate custom CSS from customization settings.
     */
    public function generateCustomCSS($themeName, $customization): string
    {
        $css = ":root {\n";

        // Generate CSS custom properties for colors
        if (isset($customization['colors'])) {
            foreach ($customization['colors'] as $name => $value) {
                $css .= "  --color-{$name}: {$value};\n";
            }
        }

        // Generate CSS custom properties for fonts
        if (isset($customization['fonts'])) {
            $css .= "  --font-primary: '{$customization['fonts']['primary_font']}', sans-serif;\n";
            $css .= "  --font-secondary: '{$customization['fonts']['secondary_font']}', serif;\n";
            
            if (isset($customization['fonts']['font_sizes'])) {
                foreach ($customization['fonts']['font_sizes'] as $size => $value) {
                    $css .= "  --font-size-{$size}: {$value};\n";
                }
            }
        }

        // Generate CSS custom properties for layout
        if (isset($customization['layout'])) {
            foreach ($customization['layout'] as $name => $value) {
                $css .= "  --layout-{$name}: {$value};\n";
            }
        }

        // Generate CSS custom properties for spacing
        if (isset($customization['spacing'])) {
            foreach ($customization['spacing'] as $size => $value) {
                $css .= "  --spacing-{$size}: {$value};\n";
            }
        }

        $css .= "}\n\n";

        // Add theme-specific CSS
        $css .= $this->getThemeSpecificCSS($themeName, $customization);

        return $css;
    }

    /**
     * Get theme-specific CSS rules.
     */
    private function getThemeSpecificCSS($themeName, $customization): string
    {
        $css = '';

        // Base styles that apply to all themes
        $css .= "
        body {
            font-family: var(--font-primary);
            color: var(--color-text);
            background-color: var(--color-background);
        }

        .container {
            max-width: var(--layout-container_width);
        }

        .section {
            padding: var(--layout-section_padding) 0;
        }

        .btn-primary {
            background-color: var(--color-primary);
            border-color: var(--color-primary);
        }

        .btn-primary:hover {
            background-color: var(--color-accent);
            border-color: var(--color-accent);
        }

        .card {
            border-radius: var(--layout-border_radius);
            box-shadow: var(--layout-box_shadow);
        }
        ";

        // Theme-specific styles
        switch ($themeName) {
            case 'modern':
                if (isset($customization['animations']['enable_animations']) && $customization['animations']['enable_animations']) {
                    $css .= "
                    .animate-on-scroll {
                        transition: all 0.6s ease-in-out;
                    }

                    .card:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
                    }
                    ";
                }
                break;

            case 'elegant':
                $css .= "
                h1, h2, h3, h4, h5, h6 {
                    font-family: var(--font-secondary);
                }

                .hero-section {
                    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
                }
                ";
                break;

            case 'minimal':
                $css .= "
                .card {
                    border: 1px solid var(--color-border);
                    box-shadow: none;
                }

                .btn {
                    border-radius: var(--layout-border_radius);
                    font-weight: 500;
                }
                ";
                break;

            case 'creative':
                if (isset($customization['effects']['gradients']) && $customization['effects']['gradients']) {
                    $css .= "
                    .hero-section {
                        background: linear-gradient(45deg, var(--color-primary), var(--color-accent));
                    }

                    .service-card {
                        background: linear-gradient(135deg, #ffffff, #f8f9fa);
                    }
                    ";
                }
                break;
        }

        return $css;
    }

    /**
     * Validate customization data.
     */
    private function validateCustomization($themeName, $customization): array
    {
        $validated = [];
        $themeConfig = $this->getThemeConfig($themeName);
        
        if (!$themeConfig) {
            return [];
        }

        $allowedElements = $themeConfig['customizable_elements'];

        // Validate colors
        if (in_array('colors', $allowedElements) && isset($customization['colors'])) {
            $validated['colors'] = [];
            foreach ($customization['colors'] as $key => $value) {
                if ($this->isValidColor($value)) {
                    $validated['colors'][$key] = $value;
                }
            }
        }

        // Validate fonts
        if (in_array('fonts', $allowedElements) && isset($customization['fonts'])) {
            $validated['fonts'] = $customization['fonts'];
        }

        // Validate layout
        if (in_array('layout', $allowedElements) && isset($customization['layout'])) {
            $validated['layout'] = $customization['layout'];
        }

        // Validate spacing
        if (in_array('spacing', $allowedElements) && isset($customization['spacing'])) {
            $validated['spacing'] = $customization['spacing'];
        }

        // Validate animations
        if (in_array('animations', $allowedElements) && isset($customization['animations'])) {
            $validated['animations'] = $customization['animations'];
        }

        // Validate effects
        if (in_array('effects', $allowedElements) && isset($customization['effects'])) {
            $validated['effects'] = $customization['effects'];
        }

        return $validated;
    }

    /**
     * Check if a color value is valid.
     */
    private function isValidColor($color): bool
    {
        // Check hex colors
        if (preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color)) {
            return true;
        }

        // Check RGB/RGBA colors
        if (preg_match('/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(,\s*[\d.]+)?\s*\)$/', $color)) {
            return true;
        }

        // Check HSL/HSLA colors
        if (preg_match('/^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(,\s*[\d.]+)?\s*\)$/', $color)) {
            return true;
        }

        return false;
    }

    /**
     * Clear theme cache.
     */
    public function clearThemeCache($businessId): void
    {
        $cacheKey = self::THEME_CACHE_PREFIX . $businessId;
        Cache::forget($cacheKey);
    }

    /**
     * Export theme customization.
     */
    public function exportCustomization($businessId): ?array
    {
        $landingPage = BusinessLandingPage::where('business_id', $businessId)->first();
        
        if (!$landingPage) {
            return null;
        }

        return [
            'theme' => $landingPage->theme,
            'customization' => $landingPage->theme_customization,
            'exported_at' => now()->toISOString()
        ];
    }

    /**
     * Import theme customization.
     */
    public function importCustomization($businessId, $importData): bool
    {
        try {
            if (!isset($importData['theme']) || !isset($importData['customization'])) {
                return false;
            }

            return $this->saveCustomization(
                $businessId,
                $importData['theme'],
                $importData['customization']
            );

        } catch (\Exception $e) {
            \Log::error('Failed to import theme customization', [
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
