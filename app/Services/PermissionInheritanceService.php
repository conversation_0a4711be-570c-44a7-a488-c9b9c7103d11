<?php

namespace App\Services;

use App\Models\EnhancedRole;
use App\Models\User;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Collection;

class PermissionInheritanceService
{
    /**
     * Get role hierarchy visualization data
     */
    public function getRoleHierarchyVisualization(): array
    {
        $roles = EnhancedRole::with('permissions')->orderBy('hierarchy_level')->get();

        return [
            'nodes' => $this->buildHierarchyNodes($roles),
            'edges' => $this->buildHierarchyEdges($roles),
            'permissions_map' => $this->buildPermissionsMap($roles),
            'inheritance_rules' => $this->getInheritanceRules(),
        ];
    }

    /**
     * Build hierarchy nodes for visualization
     */
    private function buildHierarchyNodes(Collection $roles): array
    {
        return $roles->map(function ($role) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'hierarchy_level' => $role->hierarchy_level,
                'security_level' => $role->security_level,
                'user_count' => $role->users()->count(),
                'permission_count' => $role->permissions()->count(),
                'is_system_role' => $role->is_system_role,
                'color' => $this->getRoleColor($role),
                'size' => $this->getRoleSize($role),
                'position' => $this->calculateNodePosition($role),
            ];
        })->toArray();
    }

    /**
     * Build hierarchy edges for visualization
     */
    private function buildHierarchyEdges(Collection $roles): array
    {
        $edges = [];

        foreach ($roles as $role) {
            // Find roles that this role can manage (lower hierarchy levels)
            $manageableRoles = $roles->where('hierarchy_level', '>', $role->hierarchy_level);

            foreach ($manageableRoles as $manageable) {
                $edges[] = [
                    'from' => $role->id,
                    'to' => $manageable->id,
                    'type' => 'manages',
                    'strength' => $this->calculateRelationshipStrength($role, $manageable),
                    'color' => $this->getEdgeColor($role, $manageable),
                ];
            }
        }

        return $edges;
    }

    /**
     * Build permissions mapping for roles
     */
    private function buildPermissionsMap(Collection $roles): array
    {
        $permissionsMap = [];

        foreach ($roles as $role) {
            $permissions = $role->permissions()->get();
            $inheritedPermissions = $this->getInheritedPermissions($role);
            $effectivePermissions = $this->getEffectivePermissions($role);

            $permissionsMap[$role->id] = [
                'direct_permissions' => $permissions->pluck('name')->toArray(),
                'inherited_permissions' => $inheritedPermissions,
                'effective_permissions' => $effectivePermissions,
                'permission_categories' => $this->categorizePermissions($effectivePermissions),
                'sensitive_permissions' => $this->getSensitivePermissions($effectivePermissions),
            ];
        }

        return $permissionsMap;
    }

    /**
     * Get inherited permissions for a role
     */
    private function getInheritedPermissions(EnhancedRole $role): array
    {
        // In a hierarchical system, higher roles inherit permissions from lower roles
        $lowerRoles = EnhancedRole::where('hierarchy_level', '>', $role->hierarchy_level)->get();

        $inheritedPermissions = [];
        foreach ($lowerRoles as $lowerRole) {
            $permissions = $lowerRole->permissions()->pluck('name')->toArray();
            $inheritedPermissions = array_merge($inheritedPermissions, $permissions);
        }

        return array_unique($inheritedPermissions);
    }

    /**
     * Get effective permissions (direct + inherited)
     */
    private function getEffectivePermissions(EnhancedRole $role): array
    {
        $directPermissions = $role->permissions()->pluck('name')->toArray();
        $inheritedPermissions = $this->getInheritedPermissions($role);

        return array_unique(array_merge($directPermissions, $inheritedPermissions));
    }

    /**
     * Get permission inheritance tree for a specific permission
     */
    public function getPermissionInheritanceTree(string $permissionName): array
    {
        $permission = Permission::where('name', $permissionName)->first();

        if (!$permission) {
            return [];
        }

        $rolesWithPermission = $permission->roles()->orderBy('hierarchy_level')->get();

        return [
            'permission' => [
                'name' => $permission->name,
                'guard_name' => $permission->guard_name,
            ],
            'roles_with_direct_access' => $rolesWithPermission->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'hierarchy_level' => $role->hierarchy_level,
                    'access_type' => 'direct',
                ];
            })->toArray(),
            'roles_with_inherited_access' => $this->getRolesWithInheritedAccess($permissionName),
            'inheritance_path' => $this->getInheritancePath($permissionName),
        ];
    }

    /**
     * Get roles that inherit a specific permission
     */
    private function getRolesWithInheritedAccess(string $permissionName): array
    {
        $rolesWithDirect = Permission::where('name', $permissionName)
            ->first()
            ->roles()
            ->pluck('hierarchy_level')
            ->toArray();

        if (empty($rolesWithDirect)) {
            return [];
        }

        $minHierarchyLevel = min($rolesWithDirect);

        return EnhancedRole::where('hierarchy_level', '<', $minHierarchyLevel)
            ->get()
            ->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'hierarchy_level' => $role->hierarchy_level,
                    'access_type' => 'inherited',
                ];
            })
            ->toArray();
    }

    /**
     * Get inheritance path for a permission
     */
    private function getInheritancePath(string $permissionName): array
    {
        $permission = Permission::where('name', $permissionName)->first();
        $rolesWithPermission = $permission->roles()->orderBy('hierarchy_level')->get();

        $path = [];
        foreach ($rolesWithPermission as $role) {
            $higherRoles = EnhancedRole::where('hierarchy_level', '<', $role->hierarchy_level)
                ->orderBy('hierarchy_level')
                ->get();

            foreach ($higherRoles as $higherRole) {
                $path[] = [
                    'from' => $role->name,
                    'to' => $higherRole->name,
                    'inheritance_type' => 'hierarchical',
                ];
            }
        }

        return $path;
    }

    /**
     * Analyze permission conflicts and overlaps
     */
    public function analyzePermissionConflicts(): array
    {
        $roles = EnhancedRole::with('permissions')->get();
        $conflicts = [];
        $overlaps = [];

        foreach ($roles as $role) {
            $effectivePermissions = $this->getEffectivePermissions($role);

            // Check for conflicting permissions
            $conflictingPermissions = $this->findConflictingPermissions($effectivePermissions);
            if (!empty($conflictingPermissions)) {
                $conflicts[] = [
                    'role' => $role->name,
                    'conflicts' => $conflictingPermissions,
                ];
            }

            // Check for permission overlaps with other roles
            foreach ($roles as $otherRole) {
                if ($role->id !== $otherRole->id) {
                    $otherEffectivePermissions = $this->getEffectivePermissions($otherRole);
                    $overlap = array_intersect($effectivePermissions, $otherEffectivePermissions);

                    if (!empty($overlap) && count($overlap) > 5) { // Significant overlap threshold
                        $overlaps[] = [
                            'role1' => $role->name,
                            'role2' => $otherRole->name,
                            'overlapping_permissions' => $overlap,
                            'overlap_percentage' => (count($overlap) / count($effectivePermissions)) * 100,
                        ];
                    }
                }
            }
        }

        return [
            'conflicts' => $conflicts,
            'overlaps' => $overlaps,
            'recommendations' => $this->generateConflictRecommendations($conflicts, $overlaps),
        ];
    }

    /**
     * Get user permission inheritance analysis
     */
    public function getUserPermissionInheritance(User $user): array
    {
        $userRoles = $user->roles()->orderBy('hierarchy_level')->get();

        $analysis = [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
            ],
            'roles' => [],
            'effective_permissions' => [],
            'permission_sources' => [],
            'potential_conflicts' => [],
        ];

        foreach ($userRoles as $role) {
            $rolePermissions = $this->getEffectivePermissions($role);

            $analysis['roles'][] = [
                'id' => $role->id,
                'name' => $role->name,
                'hierarchy_level' => $role->hierarchy_level,
                'permissions' => $rolePermissions,
            ];

            // Track permission sources
            foreach ($rolePermissions as $permission) {
                if (!isset($analysis['permission_sources'][$permission])) {
                    $analysis['permission_sources'][$permission] = [];
                }
                $analysis['permission_sources'][$permission][] = $role->name;
            }
        }

        // Calculate effective permissions (union of all role permissions)
        $allPermissions = [];
        foreach ($analysis['roles'] as $roleData) {
            $allPermissions = array_merge($allPermissions, $roleData['permissions']);
        }
        $analysis['effective_permissions'] = array_unique($allPermissions);

        // Identify potential conflicts
        $analysis['potential_conflicts'] = $this->findUserPermissionConflicts($analysis['permission_sources']);

        return $analysis;
    }

    /**
     * Helper methods
     */
    private function getRoleColor(EnhancedRole $role): string
    {
        $colors = [
            0 => '#dc3545', // Super Admin - Red
            1 => '#fd7e14', // Admin - Orange
            2 => '#ffc107', // Business Owner - Yellow
            3 => '#28a745', // Manager - Green
            4 => '#17a2b8', // Staff - Cyan
            5 => '#6c757d', // Customer - Gray
        ];

        return $colors[$role->hierarchy_level] ?? '#6c757d';
    }

    private function getRoleSize(EnhancedRole $role): int
    {
        $baseSize = 30;
        $userCount = $role->users()->count();
        $permissionCount = $role->permissions()->count();

        return $baseSize + ($userCount * 2) + ($permissionCount * 0.5);
    }

    private function calculateNodePosition(EnhancedRole $role): array
    {
        // Calculate position based on hierarchy level
        $x = $role->hierarchy_level * 150;
        $y = $role->id * 80;

        return ['x' => $x, 'y' => $y];
    }

    private function calculateRelationshipStrength(EnhancedRole $role1, EnhancedRole $role2): float
    {
        $hierarchyDiff = abs($role1->hierarchy_level - $role2->hierarchy_level);
        return 1.0 / $hierarchyDiff; // Closer hierarchy levels have stronger relationships
    }

    private function getEdgeColor(EnhancedRole $from, EnhancedRole $to): string
    {
        $hierarchyDiff = $to->hierarchy_level - $from->hierarchy_level;

        if ($hierarchyDiff === 1) return '#28a745'; // Direct management - Green
        if ($hierarchyDiff <= 3) return '#ffc107'; // Indirect management - Yellow
        return '#6c757d'; // Distant relationship - Gray
    }

    private function categorizePermissions(array $permissions): array
    {
        $categories = [
            'System Administration' => [],
            'User Management' => [],
            'Business Management' => [],
            'Content Management' => [],
            'Reports & Analytics' => [],
            'Other' => [],
        ];

        foreach ($permissions as $permission) {
            $category = $this->getPermissionCategory($permission);
            $categories[$category][] = $permission;
        }

        return array_filter($categories); // Remove empty categories
    }

    private function getPermissionCategory(string $permission): string
    {
        if (str_contains($permission, 'system') || str_contains($permission, 'server') || str_contains($permission, 'backup')) {
            return 'System Administration';
        }
        if (str_contains($permission, 'user') || str_contains($permission, 'role')) {
            return 'User Management';
        }
        if (str_contains($permission, 'business') || str_contains($permission, 'service') || str_contains($permission, 'booking')) {
            return 'Business Management';
        }
        if (str_contains($permission, 'content') || str_contains($permission, 'page') || str_contains($permission, 'media')) {
            return 'Content Management';
        }
        if (str_contains($permission, 'report') || str_contains($permission, 'analytics') || str_contains($permission, 'view')) {
            return 'Reports & Analytics';
        }

        return 'Other';
    }

    private function getSensitivePermissions(array $permissions): array
    {
        $sensitiveKeywords = ['system', 'server', 'backup', 'audit', 'security', 'database', 'delete', 'manage'];

        return array_filter($permissions, function ($permission) use ($sensitiveKeywords) {
            foreach ($sensitiveKeywords as $keyword) {
                if (str_contains(strtolower($permission), $keyword)) {
                    return true;
                }
            }
            return false;
        });
    }

    private function getInheritanceRules(): array
    {
        return [
            'hierarchical_inheritance' => 'Higher hierarchy roles inherit permissions from lower hierarchy roles',
            'direct_assignment' => 'Roles can have directly assigned permissions',
            'effective_permissions' => 'User effective permissions = union of all role permissions',
            'conflict_resolution' => 'In case of conflicts, most permissive access is granted',
        ];
    }

    private function findConflictingPermissions(array $permissions): array
    {
        // Define conflicting permission pairs
        $conflicts = [
            [
                'permissions' => ['create users', 'delete users'],
                'description' => 'User creation and deletion should be separated'
            ],
            [
                'permissions' => ['manage system settings', 'view reports'],
                'description' => 'System management and reporting should be separated'
            ],
        ];

        $foundConflicts = [];
        foreach ($conflicts as $conflict) {
            $conflictPermissions = $conflict['permissions'];
            if (array_intersect($conflictPermissions, $permissions) === $conflictPermissions) {
                $foundConflicts[] = [
                    'permissions' => $conflictPermissions,
                    'description' => $conflict['description'],
                ];
            }
        }

        return $foundConflicts;
    }

    private function generateConflictRecommendations(array $conflicts, array $overlaps): array
    {
        $recommendations = [];

        if (!empty($conflicts)) {
            $recommendations[] = [
                'type' => 'conflict_resolution',
                'priority' => 'high',
                'message' => 'Review and resolve permission conflicts to maintain security',
                'actions' => ['Separate conflicting permissions into different roles', 'Review role assignments'],
            ];
        }

        if (!empty($overlaps)) {
            $recommendations[] = [
                'type' => 'role_consolidation',
                'priority' => 'medium',
                'message' => 'Consider consolidating roles with significant permission overlaps',
                'actions' => ['Merge similar roles', 'Create parent-child role relationships'],
            ];
        }

        return $recommendations;
    }

    private function findUserPermissionConflicts(array $permissionSources): array
    {
        $conflicts = [];

        foreach ($permissionSources as $permission => $sources) {
            if (count($sources) > 1) {
                $conflicts[] = [
                    'permission' => $permission,
                    'sources' => $sources,
                    'type' => 'multiple_sources',
                    'recommendation' => 'Consider consolidating roles to reduce permission redundancy',
                ];
            }
        }

        return $conflicts;
    }
}
