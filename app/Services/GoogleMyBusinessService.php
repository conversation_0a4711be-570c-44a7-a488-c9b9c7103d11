<?php

namespace App\Services;

use App\Models\Business;
use App\Models\BusinessBranch;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class GoogleMyBusinessService
{
    protected $apiKey;
    protected $baseUrl = 'https://mybusinessbusinessinformation.googleapis.com/v1';

    public function __construct()
    {
        $this->apiKey = config('services.google_my_business.api_key');
    }

    /**
     * Search for business locations using Google My Business API.
     */
    public function searchBusinessLocations(string $businessName, string $address = null): array
    {
        if (!$this->apiKey) {
            return [
                'success' => false,
                'message' => 'Google My Business API key not configured',
                'locations' => []
            ];
        }

        try {
            // For demo purposes, we'll simulate Google My Business data
            // In a real implementation, you would use the actual Google My Business API
            $mockLocations = $this->getMockGoogleBusinessData($businessName, $address);

            return [
                'success' => true,
                'message' => 'Locations found successfully',
                'locations' => $mockLocations
            ];

        } catch (\Exception $e) {
            Log::error('Google My Business API error: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Failed to fetch business locations: ' . $e->getMessage(),
                'locations' => []
            ];
        }
    }

    /**
     * Import selected locations into the business.
     */
    public function importLocations(Business $business, array $selectedLocations): array
    {
        $imported = 0;
        $skipped = 0;
        $errors = [];

        foreach ($selectedLocations as $locationData) {
            try {
                // Check if location already exists
                $exists = $business->branches()
                    ->where('name', $locationData['name'])
                    ->where('address', $locationData['address'])
                    ->exists();

                if ($exists) {
                    $skipped++;
                    continue;
                }

                // Create new location
                $location = $business->branches()->create([
                    'name' => $locationData['name'],
                    'description' => $locationData['description'] ?? null,
                    'address' => $locationData['address'],
                    'city' => $locationData['city'],
                    'state' => $locationData['state'],
                    'postal_code' => $locationData['postal_code'],
                    'country' => $locationData['country'],
                    'phone' => $locationData['phone'] ?? null,
                    'email' => $locationData['email'] ?? null,
                    'latitude' => $locationData['latitude'] ?? null,
                    'longitude' => $locationData['longitude'] ?? null,
                    'website' => $locationData['website'] ?? null,
                    'is_main_branch' => false,
                    'is_active' => true,
                ]);

                // Import operating hours if available
                if (isset($locationData['operating_hours'])) {
                    $this->importOperatingHours($location, $locationData['operating_hours']);
                }

                $imported++;

            } catch (\Exception $e) {
                $errors[] = "Failed to import {$locationData['name']}: " . $e->getMessage();
                Log::error('Location import error: ' . $e->getMessage());
            }
        }

        return [
            'success' => $imported > 0,
            'imported' => $imported,
            'skipped' => $skipped,
            'errors' => $errors,
            'message' => $this->getImportMessage($imported, $skipped, count($errors))
        ];
    }

    /**
     * Import operating hours for a location.
     */
    protected function importOperatingHours(BusinessBranch $location, array $operatingHours): void
    {
        $dayMapping = [
            'monday' => 1,
            'tuesday' => 2,
            'wednesday' => 3,
            'thursday' => 4,
            'friday' => 5,
            'saturday' => 6,
            'sunday' => 0,
        ];

        foreach ($operatingHours as $day => $hours) {
            if (isset($dayMapping[$day]) && $hours['is_open']) {
                $location->operatingHours()->updateOrCreate(
                    ['day_of_week' => $dayMapping[$day]],
                    [
                        'is_open' => true,
                        'open_time' => $hours['open_time'],
                        'close_time' => $hours['close_time'],
                    ]
                );
            }
        }
    }

    /**
     * Get mock Google My Business data for demonstration.
     */
    protected function getMockGoogleBusinessData(string $businessName, string $address = null): array
    {
        // This simulates what would come from Google My Business API
        return [
            [
                'id' => 'gmb_location_1',
                'name' => $businessName . ' - Main Location',
                'description' => 'Main business location',
                'address' => '123 Main Street',
                'city' => 'New York',
                'state' => 'NY',
                'postal_code' => '10001',
                'country' => 'United States',
                'phone' => '+****************',
                'email' => 'main@' . strtolower(str_replace(' ', '', $businessName)) . '.com',
                'website' => 'https://www.' . strtolower(str_replace(' ', '', $businessName)) . '.com',
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'operating_hours' => [
                    'monday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '17:00'],
                    'tuesday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '17:00'],
                    'wednesday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '17:00'],
                    'thursday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '17:00'],
                    'friday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '17:00'],
                    'saturday' => ['is_open' => true, 'open_time' => '10:00', 'close_time' => '16:00'],
                    'sunday' => ['is_open' => false, 'open_time' => null, 'close_time' => null],
                ]
            ],
            [
                'id' => 'gmb_location_2',
                'name' => $businessName . ' - Downtown Branch',
                'description' => 'Downtown branch location',
                'address' => '456 Broadway',
                'city' => 'New York',
                'state' => 'NY',
                'postal_code' => '10013',
                'country' => 'United States',
                'phone' => '+****************',
                'email' => 'downtown@' . strtolower(str_replace(' ', '', $businessName)) . '.com',
                'website' => 'https://www.' . strtolower(str_replace(' ', '', $businessName)) . '.com/downtown',
                'latitude' => 40.7589,
                'longitude' => -73.9851,
                'operating_hours' => [
                    'monday' => ['is_open' => true, 'open_time' => '08:00', 'close_time' => '18:00'],
                    'tuesday' => ['is_open' => true, 'open_time' => '08:00', 'close_time' => '18:00'],
                    'wednesday' => ['is_open' => true, 'open_time' => '08:00', 'close_time' => '18:00'],
                    'thursday' => ['is_open' => true, 'open_time' => '08:00', 'close_time' => '18:00'],
                    'friday' => ['is_open' => true, 'open_time' => '08:00', 'close_time' => '18:00'],
                    'saturday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '17:00'],
                    'sunday' => ['is_open' => true, 'open_time' => '10:00', 'close_time' => '15:00'],
                ]
            ]
        ];
    }

    /**
     * Generate import result message.
     */
    protected function getImportMessage(int $imported, int $skipped, int $errors): string
    {
        $messages = [];

        if ($imported > 0) {
            $messages[] = "Successfully imported {$imported} location" . ($imported > 1 ? 's' : '');
        }

        if ($skipped > 0) {
            $messages[] = "Skipped {$skipped} existing location" . ($skipped > 1 ? 's' : '');
        }

        if ($errors > 0) {
            $messages[] = "Failed to import {$errors} location" . ($errors > 1 ? 's' : '');
        }

        return implode('. ', $messages) . '.';
    }
}
