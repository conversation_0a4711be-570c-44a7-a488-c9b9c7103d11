<?php

namespace App\Services;

use App\Models\Business;
use App\Models\Service;
use App\Models\Resource;
use App\Models\Booking;
use App\Models\BusinessOperatingHour;
use App\Models\BusinessHoliday;
use App\Models\AvailabilityBlock;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class BusinessRulesEngine
{
    /**
     * Validate if a booking can be made according to business rules.
     */
    public function validateBooking(array $bookingData): array
    {
        $business = Business::find($bookingData['business_id']);
        $service = Service::find($bookingData['service_id']);
        $startDateTime = Carbon::parse($bookingData['start_datetime']);
        $endDateTime = Carbon::parse($bookingData['end_datetime']);

        $violations = [];

        // Check if business is active
        if (!$business->is_active) {
            $violations[] = 'Business is currently inactive and not accepting bookings.';
        }

        // Check if online booking is enabled
        if (!$business->online_booking_enabled && ($bookingData['booking_source'] ?? 'online') === 'online') {
            $violations[] = 'Online booking is currently disabled for this business.';
        }

        // Check advance booking limits
        $advanceBookingViolations = $this->validateAdvanceBooking($business, $service, $startDateTime);
        $violations = array_merge($violations, $advanceBookingViolations);

        // Check operating hours
        $operatingHoursViolations = $this->validateOperatingHours($business, $startDateTime, $endDateTime);
        $violations = array_merge($violations, $operatingHoursViolations);

        // Check holidays
        $holidayViolations = $this->validateHolidays($business, $startDateTime, $endDateTime);
        $violations = array_merge($violations, $holidayViolations);

        // Check service-specific rules
        $serviceViolations = $this->validateServiceRules($service, $bookingData);
        $violations = array_merge($violations, $serviceViolations);

        // Check resource availability
        if (!empty($bookingData['resources'])) {
            $resourceViolations = $this->validateResourceAvailability($bookingData['resources'], $startDateTime, $endDateTime);
            $violations = array_merge($violations, $resourceViolations);
        }

        // Check availability blocks
        $blockViolations = $this->validateAvailabilityBlocks($business, $startDateTime, $endDateTime);
        $violations = array_merge($violations, $blockViolations);

        // Check cancellation policy
        if (isset($bookingData['existing_booking_id'])) {
            $cancellationViolations = $this->validateCancellationPolicy($business, $startDateTime);
            $violations = array_merge($violations, $cancellationViolations);
        }

        return [
            'valid' => empty($violations),
            'violations' => $violations,
        ];
    }

    /**
     * Validate advance booking limits.
     */
    protected function validateAdvanceBooking(Business $business, Service $service, Carbon $startDateTime): array
    {
        $violations = [];
        $now = now();

        // Check minimum advance booking time
        $minAdvanceHours = $service->min_advance_booking_hours ?? $business->booking_advance_hours;
        if ($startDateTime->diffInHours($now) < $minAdvanceHours) {
            $violations[] = "Booking must be made at least {$minAdvanceHours} hours in advance.";
        }

        // Check maximum advance booking time
        $maxAdvanceDays = $service->max_advance_booking_days ?? $business->booking_advance_days;
        if ($startDateTime->diffInDays($now) > $maxAdvanceDays) {
            $violations[] = "Booking cannot be made more than {$maxAdvanceDays} days in advance.";
        }

        return $violations;
    }

    /**
     * Validate operating hours.
     */
    protected function validateOperatingHours(Business $business, Carbon $startDateTime, Carbon $endDateTime): array
    {
        $violations = [];
        $dayOfWeek = $startDateTime->dayOfWeek;

        $operatingHours = BusinessOperatingHour::where('business_id', $business->id)
            ->where('day_of_week', $dayOfWeek)
            ->first();

        if (!$operatingHours) {
            $violations[] = 'No operating hours defined for ' . $startDateTime->format('l') . '.';
            return $violations;
        }

        if ($operatingHours->is_closed) {
            $violations[] = 'Business is closed on ' . $startDateTime->format('l') . '.';
            return $violations;
        }

        $openTime = Carbon::parse($operatingHours->open_time);
        $closeTime = Carbon::parse($operatingHours->close_time);

        // Adjust times to the booking date
        $openTime->setDate($startDateTime->year, $startDateTime->month, $startDateTime->day);
        $closeTime->setDate($startDateTime->year, $startDateTime->month, $startDateTime->day);

        if ($startDateTime->lt($openTime) || $endDateTime->gt($closeTime)) {
            $violations[] = "Booking time is outside business hours ({$openTime->format('H:i')} - {$closeTime->format('H:i')}).";
        }

        // Check break times
        if ($operatingHours->break_times) {
            foreach ($operatingHours->break_times as $breakTime) {
                $breakStart = Carbon::parse($breakTime['start'])->setDate($startDateTime->year, $startDateTime->month, $startDateTime->day);
                $breakEnd = Carbon::parse($breakTime['end'])->setDate($startDateTime->year, $startDateTime->month, $startDateTime->day);

                if ($startDateTime->between($breakStart, $breakEnd) || $endDateTime->between($breakStart, $breakEnd)) {
                    $violations[] = "Booking conflicts with break time ({$breakStart->format('H:i')} - {$breakEnd->format('H:i')}).";
                }
            }
        }

        return $violations;
    }

    /**
     * Validate holidays.
     */
    protected function validateHolidays(Business $business, Carbon $startDateTime, Carbon $endDateTime): array
    {
        $violations = [];

        $holidays = BusinessHoliday::where('business_id', $business->id)
            ->where('is_active', true)
            ->where(function ($query) use ($startDateTime, $endDateTime) {
                $query->whereBetween('start_date', [$startDateTime->toDateString(), $endDateTime->toDateString()])
                      ->orWhereBetween('end_date', [$startDateTime->toDateString(), $endDateTime->toDateString()])
                      ->orWhere(function ($q) use ($startDateTime, $endDateTime) {
                          $q->where('start_date', '<=', $startDateTime->toDateString())
                            ->where('end_date', '>=', $endDateTime->toDateString());
                      });
            })
            ->get();

        foreach ($holidays as $holiday) {
            $violations[] = "Booking conflicts with holiday: {$holiday->name} ({$holiday->start_date->format('M d')} - {$holiday->end_date->format('M d')}).";
        }

        return $violations;
    }

    /**
     * Validate service-specific rules.
     */
    protected function validateServiceRules(Service $service, array $bookingData): array
    {
        $violations = [];

        // Check if service is active
        if (!$service->is_active) {
            $violations[] = 'Service is currently inactive.';
        }

        // Check if online booking is enabled for service
        if (!$service->online_booking_enabled && ($bookingData['booking_source'] ?? 'online') === 'online') {
            $violations[] = 'Online booking is disabled for this service.';
        }

        // Check participant count
        $participantCount = $bookingData['participant_count'] ?? 1;
        if ($participantCount > $service->max_participants) {
            $violations[] = "Maximum {$service->max_participants} participants allowed for this service.";
        }

        // Check service-specific booking rules
        if ($service->booking_rules) {
            $customViolations = $this->validateCustomRules($service->booking_rules, $bookingData);
            $violations = array_merge($violations, $customViolations);
        }

        return $violations;
    }

    /**
     * Validate resource availability.
     */
    protected function validateResourceAvailability(array $resources, Carbon $startDateTime, Carbon $endDateTime): array
    {
        $violations = [];

        foreach ($resources as $resourceData) {
            $resource = Resource::find($resourceData['resource_id']);

            if (!$resource) {
                $violations[] = "Resource not found.";
                continue;
            }

            if (!$resource->is_active) {
                $violations[] = "Resource '{$resource->name}' is currently inactive.";
                continue;
            }

            // Check capacity
            $requiredQuantity = $resourceData['quantity'] ?? 1;
            if ($requiredQuantity > $resource->capacity) {
                $violations[] = "Resource '{$resource->name}' capacity exceeded (required: {$requiredQuantity}, available: {$resource->capacity}).";
            }

            // Check for conflicting bookings
            $conflictingBookings = $resource->bookingServiceResources()
                ->where(function ($query) use ($startDateTime, $endDateTime) {
                    $query->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                          ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                          ->orWhere(function ($q) use ($startDateTime, $endDateTime) {
                              $q->where('start_datetime', '<=', $startDateTime)
                                ->where('end_datetime', '>=', $endDateTime);
                          });
                })
                ->sum('quantity');

            $availableQuantity = $resource->capacity - $conflictingBookings;
            if ($requiredQuantity > $availableQuantity) {
                $violations[] = "Resource '{$resource->name}' not available (required: {$requiredQuantity}, available: {$availableQuantity}).";
            }
        }

        return $violations;
    }

    /**
     * Validate availability blocks.
     */
    protected function validateAvailabilityBlocks(Business $business, Carbon $startDateTime, Carbon $endDateTime): array
    {
        $violations = [];

        $blocks = AvailabilityBlock::where('business_id', $business->id)
            ->where(function ($query) use ($startDateTime, $endDateTime) {
                $query->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                      ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                      ->orWhere(function ($q) use ($startDateTime, $endDateTime) {
                          $q->where('start_datetime', '<=', $startDateTime)
                            ->where('end_datetime', '>=', $endDateTime);
                      });
            })
            ->get();

        foreach ($blocks as $block) {
            $violations[] = "Time slot blocked: {$block->title} ({$block->start_datetime->format('M d, H:i')} - {$block->end_datetime->format('M d, H:i')}).";
        }

        return $violations;
    }

    /**
     * Validate cancellation policy.
     */
    protected function validateCancellationPolicy(Business $business, Carbon $startDateTime): array
    {
        $violations = [];
        $now = now();

        $hoursUntilBooking = $startDateTime->diffInHours($now);
        if ($hoursUntilBooking < $business->cancellation_hours) {
            $violations[] = "Cancellation must be made at least {$business->cancellation_hours} hours before the booking.";
        }

        return $violations;
    }

    /**
     * Validate custom business rules.
     */
    protected function validateCustomRules(array $rules, array $bookingData): array
    {
        $violations = [];

        foreach ($rules as $rule) {
            $ruleType = $rule['type'] ?? '';

            switch ($ruleType) {
                case 'max_bookings_per_day':
                    $violations = array_merge($violations, $this->validateMaxBookingsPerDay($rule, $bookingData));
                    break;
                case 'min_gap_between_bookings':
                    $violations = array_merge($violations, $this->validateMinGapBetweenBookings($rule, $bookingData));
                    break;
                case 'blackout_dates':
                    $violations = array_merge($violations, $this->validateBlackoutDates($rule, $bookingData));
                    break;
                // Add more custom rule types as needed
            }
        }

        return $violations;
    }

    /**
     * Validate maximum bookings per day rule.
     */
    protected function validateMaxBookingsPerDay(array $rule, array $bookingData): array
    {
        $violations = [];
        $maxBookings = $rule['value'] ?? 1;
        $startDate = Carbon::parse($bookingData['start_datetime'])->toDateString();

        $existingBookings = Booking::where('business_id', $bookingData['business_id'])
            ->whereDate('start_datetime', $startDate)
            ->whereIn('status', ['confirmed', 'in_progress'])
            ->count();

        if ($existingBookings >= $maxBookings) {
            $violations[] = "Maximum {$maxBookings} bookings per day limit reached.";
        }

        return $violations;
    }

    /**
     * Validate minimum gap between bookings rule.
     */
    protected function validateMinGapBetweenBookings(array $rule, array $bookingData): array
    {
        $violations = [];
        $minGapMinutes = $rule['value'] ?? 30;
        $startDateTime = Carbon::parse($bookingData['start_datetime']);
        $endDateTime = Carbon::parse($bookingData['end_datetime']);

        // Check for bookings before this one
        $beforeBooking = Booking::where('business_id', $bookingData['business_id'])
            ->where('end_datetime', '>', $startDateTime->copy()->subMinutes($minGapMinutes))
            ->where('end_datetime', '<=', $startDateTime)
            ->whereIn('status', ['confirmed', 'in_progress'])
            ->exists();

        if ($beforeBooking) {
            $violations[] = "Minimum {$minGapMinutes} minutes gap required before this booking.";
        }

        // Check for bookings after this one
        $afterBooking = Booking::where('business_id', $bookingData['business_id'])
            ->where('start_datetime', '<', $endDateTime->copy()->addMinutes($minGapMinutes))
            ->where('start_datetime', '>=', $endDateTime)
            ->whereIn('status', ['confirmed', 'in_progress'])
            ->exists();

        if ($afterBooking) {
            $violations[] = "Minimum {$minGapMinutes} minutes gap required after this booking.";
        }

        return $violations;
    }

    /**
     * Validate blackout dates rule.
     */
    protected function validateBlackoutDates(array $rule, array $bookingData): array
    {
        $violations = [];
        $blackoutDates = $rule['dates'] ?? [];
        $bookingDate = Carbon::parse($bookingData['start_datetime'])->toDateString();

        if (in_array($bookingDate, $blackoutDates)) {
            $violations[] = "Booking not allowed on blackout date: {$bookingDate}.";
        }

        return $violations;
    }

    /**
     * Get available time slots for a service on a given date.
     */
    public function getAvailableTimeSlots(Service $service, Carbon $date, int $duration = null): Collection
    {
        $duration = $duration ?? $service->duration_minutes;
        $business = $service->business;

        // Get operating hours for the day
        $operatingHours = BusinessOperatingHour::where('business_id', $business->id)
            ->where('day_of_week', $date->dayOfWeek)
            ->first();

        if (!$operatingHours || $operatingHours->is_closed) {
            return collect();
        }

        $openTime = Carbon::parse($operatingHours->open_time)->setDate($date->year, $date->month, $date->day);
        $closeTime = Carbon::parse($operatingHours->close_time)->setDate($date->year, $date->month, $date->day);

        // Generate potential time slots
        $slots = collect();
        $currentTime = $openTime->copy();

        while ($currentTime->copy()->addMinutes($duration)->lte($closeTime)) {
            $endTime = $currentTime->copy()->addMinutes($duration);

            // Validate this time slot
            $validation = $this->validateBooking([
                'business_id' => $business->id,
                'service_id' => $service->id,
                'start_datetime' => $currentTime->toISOString(),
                'end_datetime' => $endTime->toISOString(),
                'participant_count' => 1,
                'booking_source' => 'online',
            ]);

            if ($validation['valid']) {
                $slots->push([
                    'start_time' => $currentTime->format('H:i'),
                    'end_time' => $endTime->format('H:i'),
                    'start_datetime' => $currentTime->toISOString(),
                    'end_datetime' => $endTime->toISOString(),
                ]);
            }

            $currentTime->addMinutes(15); // 15-minute intervals
        }

        return $slots;
    }

    /**
     * Check availability for a booking request.
     */
    public function checkAvailability(Business $business, Carbon $startDateTime, Carbon $endDateTime, array $serviceIds = [], int $excludeBookingId = null): array
    {
        $conflicts = [];

        // Check operating hours
        $operatingHours = $business->operatingHours()
            ->where('day_of_week', $startDateTime->dayOfWeek)
            ->where('is_closed', false)
            ->first();

        if (!$operatingHours) {
            return [
                'available' => false,
                'message' => 'Business is closed on ' . $startDateTime->format('l'),
                'conflicts' => ['Business is closed on this day']
            ];
        }

        $openTime = Carbon::parse($operatingHours->open_time);
        $closeTime = Carbon::parse($operatingHours->close_time);

        if ($startDateTime->format('H:i') < $openTime->format('H:i') ||
            $endDateTime->format('H:i') > $closeTime->format('H:i')) {
            return [
                'available' => false,
                'message' => 'Booking time is outside business hours (' . $openTime->format('g:i A') . ' - ' . $closeTime->format('g:i A') . ')',
                'conflicts' => ['Outside business hours']
            ];
        }

        // Check for conflicting bookings
        $conflictingBookings = $business->bookings()
            ->where(function ($query) use ($startDateTime, $endDateTime) {
                $query->where(function ($q) use ($startDateTime, $endDateTime) {
                    $q->where('start_datetime', '<', $endDateTime)
                      ->where('end_datetime', '>', $startDateTime);
                });
            })
            ->whereNotIn('status', ['cancelled', 'no_show']);

        if ($excludeBookingId) {
            $conflictingBookings->where('id', '!=', $excludeBookingId);
        }

        $conflictCount = $conflictingBookings->count();
        if ($conflictCount > 0) {
            $conflicts[] = "There are {$conflictCount} conflicting bookings at this time";
        }

        // Check holidays
        $holiday = $business->holidays()
            ->whereDate('start_date', '<=', $startDateTime->toDateString())
            ->whereDate('end_date', '>=', $startDateTime->toDateString())
            ->where('is_active', true)
            ->first();

        if ($holiday) {
            $conflicts[] = 'Business is closed for holiday: ' . $holiday->name;
        }

        $available = empty($conflicts);
        $message = $available ? 'Time slot is available' : implode(', ', $conflicts);

        return [
            'available' => $available,
            'message' => $message,
            'conflicts' => $conflicts,
        ];
    }
}
