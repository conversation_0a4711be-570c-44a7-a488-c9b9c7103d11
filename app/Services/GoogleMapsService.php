<?php

namespace App\Services;

use App\Models\BusinessBranch;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class GoogleMapsService
{
    protected $apiKey;
    protected $baseUrl = 'https://maps.googleapis.com/maps/api';

    public function __construct()
    {
        $this->apiKey = config('services.google_maps.api_key');
    }

    /**
     * Geocode an address to get latitude and longitude.
     */
    public function geocodeAddress(string $address): array
    {
        if (!$this->apiKey) {
            return [
                'success' => false,
                'message' => 'Google Maps API key not configured',
            ];
        }

        $cacheKey = 'geocode_' . md5($address);
        
        // Check cache first
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $response = Http::get("{$this->baseUrl}/geocode/json", [
                'address' => $address,
                'key' => $this->api<PERSON><PERSON>,
            ]);

            $data = $response->json();

            if ($data['status'] === 'OK' && !empty($data['results'])) {
                $result = $data['results'][0];
                $location = $result['geometry']['location'];
                
                $geocodeResult = [
                    'success' => true,
                    'latitude' => $location['lat'],
                    'longitude' => $location['lng'],
                    'formatted_address' => $result['formatted_address'],
                    'place_id' => $result['place_id'],
                    'address_components' => $result['address_components'],
                ];

                // Cache for 24 hours
                Cache::put($cacheKey, $geocodeResult, 86400);
                
                return $geocodeResult;
            } else {
                return [
                    'success' => false,
                    'message' => 'Address not found or geocoding failed',
                    'error' => $data['status'],
                ];
            }
        } catch (\Exception $e) {
            Log::error('Google Maps geocoding error', [
                'address' => $address,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Geocoding service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get place details by place ID.
     */
    public function getPlaceDetails(string $placeId): array
    {
        if (!$this->apiKey) {
            return [
                'success' => false,
                'message' => 'Google Maps API key not configured',
            ];
        }

        $cacheKey = 'place_details_' . $placeId;
        
        // Check cache first
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $response = Http::get("{$this->baseUrl}/place/details/json", [
                'place_id' => $placeId,
                'fields' => 'name,formatted_address,geometry,formatted_phone_number,website,opening_hours,rating,reviews',
                'key' => $this->apiKey,
            ]);

            $data = $response->json();

            if ($data['status'] === 'OK') {
                $result = [
                    'success' => true,
                    'place_details' => $data['result'],
                ];

                // Cache for 24 hours
                Cache::put($cacheKey, $result, 86400);
                
                return $result;
            } else {
                return [
                    'success' => false,
                    'message' => 'Place details not found',
                    'error' => $data['status'],
                ];
            }
        } catch (\Exception $e) {
            Log::error('Google Maps place details error', [
                'place_id' => $placeId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Place details service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Search for places near a location.
     */
    public function searchNearbyPlaces(float $latitude, float $longitude, string $type = 'establishment', int $radius = 1000): array
    {
        if (!$this->apiKey) {
            return [
                'success' => false,
                'message' => 'Google Maps API key not configured',
            ];
        }

        try {
            $response = Http::get("{$this->baseUrl}/place/nearbysearch/json", [
                'location' => "{$latitude},{$longitude}",
                'radius' => $radius,
                'type' => $type,
                'key' => $this->apiKey,
            ]);

            $data = $response->json();

            if ($data['status'] === 'OK') {
                return [
                    'success' => true,
                    'places' => $data['results'],
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Nearby places search failed',
                    'error' => $data['status'],
                ];
            }
        } catch (\Exception $e) {
            Log::error('Google Maps nearby search error', [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Nearby search service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate a static map URL for a business branch.
     */
    public function getStaticMapUrl(BusinessBranch $branch, int $width = 400, int $height = 300, int $zoom = 15): string
    {
        if (!$this->apiKey || !$branch->latitude || !$branch->longitude) {
            return '';
        }

        $params = [
            'center' => "{$branch->latitude},{$branch->longitude}",
            'zoom' => $zoom,
            'size' => "{$width}x{$height}",
            'markers' => "color:red|{$branch->latitude},{$branch->longitude}",
            'key' => $this->apiKey,
        ];

        return "{$this->baseUrl}/staticmap?" . http_build_query($params);
    }

    /**
     * Generate an embedded map URL for a business branch.
     */
    public function getEmbedMapUrl(BusinessBranch $branch, int $zoom = 15): string
    {
        if (!$this->apiKey || !$branch->latitude || !$branch->longitude) {
            return '';
        }

        $params = [
            'q' => "{$branch->latitude},{$branch->longitude}",
            'zoom' => $zoom,
            'key' => $this->apiKey,
        ];

        return "https://www.google.com/maps/embed/v1/place?" . http_build_query($params);
    }

    /**
     * Get directions between two points.
     */
    public function getDirections(string $origin, string $destination, string $mode = 'driving'): array
    {
        if (!$this->apiKey) {
            return [
                'success' => false,
                'message' => 'Google Maps API key not configured',
            ];
        }

        try {
            $response = Http::get("{$this->baseUrl}/directions/json", [
                'origin' => $origin,
                'destination' => $destination,
                'mode' => $mode,
                'key' => $this->apiKey,
            ]);

            $data = $response->json();

            if ($data['status'] === 'OK') {
                return [
                    'success' => true,
                    'routes' => $data['routes'],
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Directions not found',
                    'error' => $data['status'],
                ];
            }
        } catch (\Exception $e) {
            Log::error('Google Maps directions error', [
                'origin' => $origin,
                'destination' => $destination,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Directions service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Update business branch with geocoded data.
     */
    public function updateBranchLocation(BusinessBranch $branch): array
    {
        $address = $branch->full_address;
        
        if (!$address) {
            return [
                'success' => false,
                'message' => 'No address provided for geocoding',
            ];
        }

        $geocodeResult = $this->geocodeAddress($address);

        if ($geocodeResult['success']) {
            $branch->update([
                'latitude' => $geocodeResult['latitude'],
                'longitude' => $geocodeResult['longitude'],
                'google_maps_data' => [
                    'formatted_address' => $geocodeResult['formatted_address'],
                    'place_id' => $geocodeResult['place_id'],
                    'address_components' => $geocodeResult['address_components'],
                    'geocoded_at' => now()->toISOString(),
                ],
            ]);

            return [
                'success' => true,
                'message' => 'Branch location updated successfully',
                'data' => $geocodeResult,
            ];
        }

        return $geocodeResult;
    }

    /**
     * Validate if Google Maps API is properly configured.
     */
    public function validateApiKey(): array
    {
        if (!$this->apiKey) {
            return [
                'success' => false,
                'message' => 'Google Maps API key not configured',
            ];
        }

        try {
            // Test with a simple geocoding request
            $response = Http::get("{$this->baseUrl}/geocode/json", [
                'address' => 'Google, Mountain View, CA',
                'key' => $this->apiKey,
            ]);

            $data = $response->json();

            if ($data['status'] === 'OK') {
                return [
                    'success' => true,
                    'message' => 'Google Maps API key is valid',
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Google Maps API key validation failed',
                    'error' => $data['status'],
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'API validation error: ' . $e->getMessage(),
            ];
        }
    }
}
