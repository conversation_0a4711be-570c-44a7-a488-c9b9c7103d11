<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\BookingReminder;
use App\Models\WaitingList;
use App\Services\SmsService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class NotificationService
{
    protected SmsService $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }
    /**
     * Send booking confirmation.
     */
    public function sendBookingConfirmation(Booking $booking, string $type, ?string $customMessage = null): array
    {
        try {
            $message = $customMessage ?? $this->getDefaultConfirmationMessage($booking);

            switch ($type) {
                case 'email':
                    return $this->sendEmail($booking->customer_email, 'Booking Confirmation', $message, $booking);
                case 'sms':
                    return $this->sendSMS($booking->customer_phone, $message);
                case 'push':
                    return $this->sendPushNotification($booking->customer_id, 'Booking Confirmed', $message);
                default:
                    return ['success' => false, 'message' => 'Invalid notification type'];
            }
        } catch (\Exception $e) {
            Log::error('Failed to send booking confirmation', [
                'booking_id' => $booking->id,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Send booking reminder.
     */
    public function sendReminder(BookingReminder $reminder): array
    {
        try {
            $booking = $reminder->booking;
            $message = $reminder->message ?? $this->getDefaultReminderMessage($booking, $reminder->hours_before);

            switch ($reminder->reminder_type) {
                case 'email':
                    $result = $this->sendEmail($booking->customer_email, 'Booking Reminder', $message, $booking);
                    break;
                case 'sms':
                    $result = $this->sendSMS($booking->customer_phone, $message);
                    break;
                case 'push':
                    $result = $this->sendPushNotification($booking->customer_id, 'Booking Reminder', $message);
                    break;
                case 'call':
                    $result = $this->makeCall($booking->customer_phone, $message);
                    break;
                default:
                    $result = ['success' => false, 'message' => 'Invalid reminder type'];
            }

            if ($result['success']) {
                $reminder->markAsSent($result['data'] ?? null);
            } else {
                $reminder->markAsFailed($result['data'] ?? null);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send booking reminder', [
                'reminder_id' => $reminder->id,
                'booking_id' => $reminder->booking_id,
                'error' => $e->getMessage()
            ]);

            $reminder->markAsFailed(['error' => $e->getMessage()]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Send waiting list notification.
     */
    public function sendWaitingListNotification(WaitingList $waitingList, string $type, string $availableDateTime, ?string $customMessage = null): array
    {
        try {
            $message = $customMessage ?? $this->getDefaultWaitingListMessage($waitingList, $availableDateTime);

            switch ($type) {
                case 'email':
                    return $this->sendEmail($waitingList->customer_email, 'Booking Available', $message);
                case 'sms':
                    return $this->sendSMS($waitingList->customer_phone, $message);
                case 'push':
                    return $this->sendPushNotification($waitingList->customer_id, 'Booking Available', $message);
                default:
                    return ['success' => false, 'message' => 'Invalid notification type'];
            }
        } catch (\Exception $e) {
            Log::error('Failed to send waiting list notification', [
                'waiting_list_id' => $waitingList->id,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Process due reminders.
     */
    public function processDueReminders(): int
    {
        $dueReminders = BookingReminder::due()->get();
        $processed = 0;

        foreach ($dueReminders as $reminder) {
            $result = $this->sendReminder($reminder);
            if ($result['success']) {
                $processed++;
            }
        }

        return $processed;
    }

    /**
     * Send test notification.
     */
    public function sendTestNotification(string $type, string $recipient, string $message): array
    {
        try {
            switch ($type) {
                case 'email':
                    return $this->sendEmail($recipient, 'Test Notification', $message);
                case 'sms':
                    return $this->sendSMS($recipient, $message);
                case 'push':
                    // For test push notifications, we'll just simulate success
                    return ['success' => true, 'message' => 'Test push notification sent'];
                default:
                    return ['success' => false, 'message' => 'Invalid notification type'];
            }
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Send email notification.
     */
    protected function sendEmail(string $to, string $subject, string $message, ?Booking $booking = null): array
    {
        try {
            // For now, we'll use a simple mail implementation
            // In production, you would use proper email templates and services

            Mail::raw($message, function ($mail) use ($to, $subject) {
                $mail->to($to)->subject($subject);
            });

            return [
                'success' => true,
                'message' => 'Email sent successfully',
                'data' => ['to' => $to, 'subject' => $subject]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage(),
                'data' => ['error' => $e->getMessage()]
            ];
        }
    }

    /**
     * Send SMS notification.
     */
    protected function sendSMS(string $to, string $message): array
    {
        return $this->smsService->sendSms($to, $message);
    }

    /**
     * Send push notification.
     */
    protected function sendPushNotification(?int $userId, string $title, string $message): array
    {
        try {
            // For now, we'll simulate push notification sending
            // In production, you would integrate with push notification services like FCM, APNS, etc.

            Log::info('Push notification sent', [
                'user_id' => $userId,
                'title' => $title,
                'message' => $message
            ]);

            return [
                'success' => true,
                'message' => 'Push notification sent successfully',
                'data' => ['user_id' => $userId, 'title' => $title]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to send push notification: ' . $e->getMessage(),
                'data' => ['error' => $e->getMessage()]
            ];
        }
    }

    /**
     * Make phone call.
     */
    protected function makeCall(string $to, string $message): array
    {
        try {
            // For now, we'll simulate phone call
            // In production, you would integrate with voice services like Twilio Voice, etc.

            Log::info('Phone call made', ['to' => $to, 'message' => $message]);

            return [
                'success' => true,
                'message' => 'Phone call initiated successfully',
                'data' => ['to' => $to, 'duration' => 'simulated']
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to make phone call: ' . $e->getMessage(),
                'data' => ['error' => $e->getMessage()]
            ];
        }
    }

    /**
     * Get default confirmation message.
     */
    protected function getDefaultConfirmationMessage(Booking $booking): string
    {
        return "Dear {$booking->customer_name},\n\n" .
               "Your booking has been confirmed!\n\n" .
               "Booking Details:\n" .
               "- Booking Number: {$booking->booking_number}\n" .
               "- Date & Time: {$booking->start_datetime->format('M d, Y \a\t g:i A')}\n" .
               "- Duration: {$booking->total_duration_minutes} minutes\n" .
               "- Total Amount: \${$booking->total_amount}\n\n" .
               "Thank you for choosing {$booking->business->name}!";
    }

    /**
     * Get default reminder message.
     */
    protected function getDefaultReminderMessage(Booking $booking, int $hoursBefore): string
    {
        return "Dear {$booking->customer_name},\n\n" .
               "This is a reminder that you have a booking in {$hoursBefore} hours.\n\n" .
               "Booking Details:\n" .
               "- Booking Number: {$booking->booking_number}\n" .
               "- Date & Time: {$booking->start_datetime->format('M d, Y \a\t g:i A')}\n" .
               "- Duration: {$booking->total_duration_minutes} minutes\n\n" .
               "We look forward to seeing you at {$booking->business->name}!";
    }

    /**
     * Get default waiting list message.
     */
    protected function getDefaultWaitingListMessage(WaitingList $waitingList, string $availableDateTime): string
    {
        $dateTime = Carbon::parse($availableDateTime);

        return "Dear {$waitingList->customer_name},\n\n" .
               "Great news! A slot has become available for your requested service.\n\n" .
               "Available Slot:\n" .
               "- Service: {$waitingList->service->name}\n" .
               "- Date & Time: {$dateTime->format('M d, Y \a\t g:i A')}\n" .
               "- Business: {$waitingList->business->name}\n\n" .
               "Please respond within 24 hours to secure this booking.";
    }
}
