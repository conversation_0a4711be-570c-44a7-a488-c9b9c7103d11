<?php

namespace App\Services;

use App\Models\Business;
use App\Models\BusinessLandingPage;
use App\Models\Service;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class LandingPageAnalyticsService
{
    const CACHE_TTL = 1800; // 30 minutes

    /**
     * Track page view.
     */
    public function trackPageView($businessSlug, $page = 'home', $userAgent = null, $ip = null): void
    {
        try {
            $landingPage = BusinessLandingPage::whereHas('business', function ($query) use ($businessSlug) {
                $query->where('landing_page_slug', $businessSlug);
            })->first();

            if (!$landingPage) {
                return;
            }

            // Store page view
            DB::table('landing_page_analytics')->insert([
                'business_id' => $landingPage->business_id,
                'landing_page_id' => $landingPage->id,
                'event_type' => 'page_view',
                'page' => $page,
                'user_agent' => $userAgent,
                'ip_address' => $this->hashIp($ip),
                'session_id' => session()->getId(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Update landing page stats
            $landingPage->increment('total_views');
            $landingPage->update(['last_viewed_at' => now()]);

            // Clear analytics cache
            $this->clearAnalyticsCache($landingPage->business_id);

        } catch (\Exception $e) {
            Log::error('Failed to track page view', [
                'slug' => $businessSlug,
                'page' => $page,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Track service view.
     */
    public function trackServiceView($businessSlug, $serviceId, $userAgent = null, $ip = null): void
    {
        try {
            $service = Service::whereHas('business', function ($query) use ($businessSlug) {
                $query->where('landing_page_slug', $businessSlug);
            })->where('id', $serviceId)->first();

            if (!$service) {
                return;
            }

            // Store service view
            DB::table('landing_page_analytics')->insert([
                'business_id' => $service->business_id,
                'service_id' => $serviceId,
                'event_type' => 'service_view',
                'user_agent' => $userAgent,
                'ip_address' => $this->hashIp($ip),
                'session_id' => session()->getId(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Update service stats
            $service->increment('landing_page_views');

            // Clear analytics cache
            $this->clearAnalyticsCache($service->business_id);

        } catch (\Exception $e) {
            Log::error('Failed to track service view', [
                'slug' => $businessSlug,
                'service_id' => $serviceId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Track booking attempt.
     */
    public function trackBookingAttempt($businessSlug, $serviceId = null, $userAgent = null, $ip = null): void
    {
        try {
            $business = Business::where('landing_page_slug', $businessSlug)->first();

            if (!$business) {
                return;
            }

            // Store booking attempt
            DB::table('landing_page_analytics')->insert([
                'business_id' => $business->id,
                'service_id' => $serviceId,
                'event_type' => 'booking_attempt',
                'user_agent' => $userAgent,
                'ip_address' => $this->hashIp($ip),
                'session_id' => session()->getId(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Clear analytics cache
            $this->clearAnalyticsCache($business->id);

        } catch (\Exception $e) {
            Log::error('Failed to track booking attempt', [
                'slug' => $businessSlug,
                'service_id' => $serviceId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Track successful booking.
     */
    public function trackBookingSuccess($businessSlug, $serviceId, $bookingId, $userAgent = null, $ip = null): void
    {
        try {
            $business = Business::where('landing_page_slug', $businessSlug)->first();

            if (!$business) {
                return;
            }

            // Store successful booking
            DB::table('landing_page_analytics')->insert([
                'business_id' => $business->id,
                'service_id' => $serviceId,
                'booking_id' => $bookingId,
                'event_type' => 'booking_success',
                'user_agent' => $userAgent,
                'ip_address' => $this->hashIp($ip),
                'session_id' => session()->getId(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Clear analytics cache
            $this->clearAnalyticsCache($business->id);

        } catch (\Exception $e) {
            Log::error('Failed to track booking success', [
                'slug' => $businessSlug,
                'service_id' => $serviceId,
                'booking_id' => $bookingId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get analytics dashboard data.
     */
    public function getDashboardData($businessId, $period = '30d'): array
    {
        $cacheKey = "analytics_dashboard_{$businessId}_{$period}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($businessId, $period) {
            $dateRange = $this->getDateRange($period);

            return [
                'overview' => $this->getOverviewStats($businessId, $dateRange),
                'page_views' => $this->getPageViewsData($businessId, $dateRange),
                'service_views' => $this->getServiceViewsData($businessId, $dateRange),
                'booking_funnel' => $this->getBookingFunnelData($businessId, $dateRange),
                'top_services' => $this->getTopServicesData($businessId, $dateRange),
                'traffic_sources' => $this->getTrafficSourcesData($businessId, $dateRange),
                'device_breakdown' => $this->getDeviceBreakdownData($businessId, $dateRange),
                'conversion_rate' => $this->getConversionRate($businessId, $dateRange),
            ];
        });
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats($businessId, $dateRange): array
    {
        $stats = DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->whereBetween('created_at', $dateRange)
            ->selectRaw('
                COUNT(*) as total_events,
                COUNT(CASE WHEN event_type = "page_view" THEN 1 END) as page_views,
                COUNT(CASE WHEN event_type = "service_view" THEN 1 END) as service_views,
                COUNT(CASE WHEN event_type = "booking_attempt" THEN 1 END) as booking_attempts,
                COUNT(CASE WHEN event_type = "booking_success" THEN 1 END) as successful_bookings,
                COUNT(DISTINCT session_id) as unique_visitors
            ')
            ->first();

        return [
            'total_events' => $stats->total_events ?? 0,
            'page_views' => $stats->page_views ?? 0,
            'service_views' => $stats->service_views ?? 0,
            'booking_attempts' => $stats->booking_attempts ?? 0,
            'successful_bookings' => $stats->successful_bookings ?? 0,
            'unique_visitors' => $stats->unique_visitors ?? 0,
            'conversion_rate' => $this->calculateConversionRate($stats->page_views ?? 0, $stats->successful_bookings ?? 0),
        ];
    }

    /**
     * Get page views data over time.
     */
    private function getPageViewsData($businessId, $dateRange): array
    {
        return DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->where('event_type', 'page_view')
            ->whereBetween('created_at', $dateRange)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as views')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get service views data.
     */
    private function getServiceViewsData($businessId, $dateRange): array
    {
        return DB::table('landing_page_analytics')
            ->join('services', 'landing_page_analytics.service_id', '=', 'services.id')
            ->where('landing_page_analytics.business_id', $businessId)
            ->where('landing_page_analytics.event_type', 'service_view')
            ->whereBetween('landing_page_analytics.created_at', $dateRange)
            ->selectRaw('services.name, COUNT(*) as views')
            ->groupBy('services.id', 'services.name')
            ->orderByDesc('views')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get booking funnel data.
     */
    private function getBookingFunnelData($businessId, $dateRange): array
    {
        $pageViews = DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->where('event_type', 'page_view')
            ->whereBetween('created_at', $dateRange)
            ->count();

        $serviceViews = DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->where('event_type', 'service_view')
            ->whereBetween('created_at', $dateRange)
            ->count();

        $bookingAttempts = DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->where('event_type', 'booking_attempt')
            ->whereBetween('created_at', $dateRange)
            ->count();

        $successfulBookings = DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->where('event_type', 'booking_success')
            ->whereBetween('created_at', $dateRange)
            ->count();

        return [
            'page_views' => $pageViews,
            'service_views' => $serviceViews,
            'booking_attempts' => $bookingAttempts,
            'successful_bookings' => $successfulBookings,
            'view_to_service_rate' => $this->calculateConversionRate($pageViews, $serviceViews),
            'service_to_booking_rate' => $this->calculateConversionRate($serviceViews, $bookingAttempts),
            'booking_success_rate' => $this->calculateConversionRate($bookingAttempts, $successfulBookings),
        ];
    }

    /**
     * Get top services data.
     */
    private function getTopServicesData($businessId, $dateRange): array
    {
        return DB::table('landing_page_analytics')
            ->join('services', 'landing_page_analytics.service_id', '=', 'services.id')
            ->where('landing_page_analytics.business_id', $businessId)
            ->whereBetween('landing_page_analytics.created_at', $dateRange)
            ->selectRaw('
                services.name,
                COUNT(CASE WHEN landing_page_analytics.event_type = "service_view" THEN 1 END) as views,
                COUNT(CASE WHEN landing_page_analytics.event_type = "booking_success" THEN 1 END) as bookings
            ')
            ->groupBy('services.id', 'services.name')
            ->orderByDesc('views')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get traffic sources data.
     */
    private function getTrafficSourcesData($businessId, $dateRange): array
    {
        // This would be enhanced with actual referrer tracking
        return [
            ['source' => 'Direct', 'visitors' => 45],
            ['source' => 'Google Search', 'visitors' => 30],
            ['source' => 'Social Media', 'visitors' => 15],
            ['source' => 'Referrals', 'visitors' => 10],
        ];
    }

    /**
     * Get device breakdown data.
     */
    private function getDeviceBreakdownData($businessId, $dateRange): array
    {
        return DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->whereBetween('created_at', $dateRange)
            ->selectRaw('
                CASE 
                    WHEN user_agent LIKE "%Mobile%" THEN "Mobile"
                    WHEN user_agent LIKE "%Tablet%" THEN "Tablet"
                    ELSE "Desktop"
                END as device_type,
                COUNT(*) as count
            ')
            ->groupBy('device_type')
            ->get()
            ->toArray();
    }

    /**
     * Get conversion rate.
     */
    private function getConversionRate($businessId, $dateRange): float
    {
        $pageViews = DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->where('event_type', 'page_view')
            ->whereBetween('created_at', $dateRange)
            ->count();

        $bookings = DB::table('landing_page_analytics')
            ->where('business_id', $businessId)
            ->where('event_type', 'booking_success')
            ->whereBetween('created_at', $dateRange)
            ->count();

        return $this->calculateConversionRate($pageViews, $bookings);
    }

    /**
     * Calculate conversion rate percentage.
     */
    private function calculateConversionRate($total, $conversions): float
    {
        if ($total == 0) {
            return 0.0;
        }

        return round(($conversions / $total) * 100, 2);
    }

    /**
     * Get date range for period.
     */
    private function getDateRange($period): array
    {
        switch ($period) {
            case '7d':
                return [Carbon::now()->subDays(7), Carbon::now()];
            case '30d':
                return [Carbon::now()->subDays(30), Carbon::now()];
            case '90d':
                return [Carbon::now()->subDays(90), Carbon::now()];
            case '1y':
                return [Carbon::now()->subYear(), Carbon::now()];
            default:
                return [Carbon::now()->subDays(30), Carbon::now()];
        }
    }

    /**
     * Hash IP address for privacy.
     */
    private function hashIp($ip): ?string
    {
        return $ip ? hash('sha256', $ip . config('app.key')) : null;
    }

    /**
     * Clear analytics cache.
     */
    private function clearAnalyticsCache($businessId): void
    {
        $periods = ['7d', '30d', '90d', '1y'];
        
        foreach ($periods as $period) {
            Cache::forget("analytics_dashboard_{$businessId}_{$period}");
        }
    }
}
