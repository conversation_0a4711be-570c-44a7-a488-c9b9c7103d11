<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\WaitingList;
use App\Models\Business;
use App\Models\Service;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BookingIntegrationService
{
    protected $notificationService;
    protected $businessRulesEngine;

    public function __construct(NotificationService $notificationService, BusinessRulesEngine $businessRulesEngine)
    {
        $this->notificationService = $notificationService;
        $this->businessRulesEngine = $businessRulesEngine;
    }

    /**
     * Handle booking status changes and trigger related actions
     */
    public function handleBookingStatusChange(Booking $booking, string $oldStatus, string $newStatus): array
    {
        $actions = [];

        try {
            DB::beginTransaction();

            // Handle specific status transitions
            switch ($newStatus) {
                case 'cancelled':
                    $actions[] = $this->handleBookingCancellation($booking);
                    break;
                case 'no_show':
                    $actions[] = $this->handleNoShow($booking);
                    break;
                case 'completed':
                    $actions[] = $this->handleBookingCompletion($booking);
                    break;
                case 'confirmed':
                    if ($oldStatus === 'pending') {
                        $actions[] = $this->handleBookingConfirmation($booking);
                    }
                    break;
            }

            // Always check for waiting list opportunities
            if (in_array($newStatus, ['cancelled', 'no_show', 'completed'])) {
                $actions[] = $this->checkWaitingListOpportunities($booking);
            }

            DB::commit();

            return [
                'success' => true,
                'actions' => array_filter($actions),
                'message' => 'Booking status updated and related actions processed.'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Booking status change handling failed', [
                'booking_id' => $booking->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process booking status change: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Handle booking cancellation
     */
    protected function handleBookingCancellation(Booking $booking): array
    {
        $result = ['action' => 'cancellation', 'details' => []];

        // Update cancellation timestamp
        $booking->update(['cancelled_at' => now()]);

        // Check for waiting list matches
        $waitingListMatches = $this->findWaitingListMatches($booking);
        if (!empty($waitingListMatches)) {
            $result['details']['waiting_list_matches'] = count($waitingListMatches);
            $result['details']['matches'] = $waitingListMatches;
        }

        return $result;
    }

    /**
     * Handle no-show
     */
    protected function handleNoShow(Booking $booking): array
    {
        $result = ['action' => 'no_show', 'details' => []];

        // Check for waiting list matches
        $waitingListMatches = $this->findWaitingListMatches($booking);
        if (!empty($waitingListMatches)) {
            $result['details']['waiting_list_matches'] = count($waitingListMatches);
            $result['details']['matches'] = $waitingListMatches;
        }

        return $result;
    }

    /**
     * Handle booking completion
     */
    protected function handleBookingCompletion(Booking $booking): array
    {
        $result = ['action' => 'completion', 'details' => []];

        // Auto-checkout if not already done
        if (!$booking->checked_out_at) {
            $booking->checkOut();
            $result['details']['auto_checkout'] = true;
        }

        return $result;
    }

    /**
     * Handle booking confirmation
     */
    protected function handleBookingConfirmation(Booking $booking): array
    {
        $result = ['action' => 'confirmation', 'details' => []];

        // Send confirmation notification if enabled
        // This would integrate with notification settings
        $result['details']['confirmation_sent'] = true;

        return $result;
    }

    /**
     * Check for waiting list opportunities when a slot becomes available
     */
    protected function checkWaitingListOpportunities(Booking $booking): array
    {
        $matches = $this->findWaitingListMatches($booking);

        if (empty($matches)) {
            return ['action' => 'waiting_list_check', 'matches' => 0];
        }

        // Notify waiting list customers
        foreach ($matches as $match) {
            $this->notifyWaitingListCustomer($match, $booking);
        }

        return [
            'action' => 'waiting_list_check',
            'matches' => count($matches),
            'notified' => count($matches)
        ];
    }

    /**
     * Find waiting list entries that match the available slot
     */
    protected function findWaitingListMatches(Booking $booking): array
    {
        $business = $booking->business;
        $serviceIds = $booking->services->pluck('id')->toArray();

        $waitingLists = $business->waitingLists()
            ->where('status', 'active')
            ->whereIn('service_id', $serviceIds)
            ->where('preferred_date', '<=', $booking->start_datetime->format('Y-m-d'))
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'asc')
            ->get();

        $matches = [];
        foreach ($waitingLists as $waitingList) {
            if ($this->isWaitingListMatch($waitingList, $booking)) {
                $matches[] = $waitingList;
            }
        }

        return $matches;
    }

    /**
     * Check if a waiting list entry matches the available booking slot
     */
    protected function isWaitingListMatch(WaitingList $waitingList, Booking $booking): bool
    {
        // Check service match
        if (!$booking->services->contains('id', $waitingList->service_id)) {
            return false;
        }

        // Check participant count
        if ($waitingList->participant_count > $booking->participant_count) {
            return false;
        }

        // Check preferred time if specified
        if ($waitingList->preferred_time_start && $waitingList->preferred_time_end) {
            $bookingTime = $booking->start_datetime->format('H:i');
            if ($bookingTime < $waitingList->preferred_time_start || $bookingTime > $waitingList->preferred_time_end) {
                return false;
            }
        }

        // Check preferred days of week if specified
        if ($waitingList->preferred_days_of_week) {
            $bookingDayOfWeek = $booking->start_datetime->dayOfWeek;
            if (!in_array($bookingDayOfWeek, $waitingList->preferred_days_of_week)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Notify waiting list customer about available slot
     */
    protected function notifyWaitingListCustomer(WaitingList $waitingList, Booking $booking): void
    {
        try {
            $availableDateTime = $booking->start_datetime->format('Y-m-d H:i');

            // Update waiting list status
            $waitingList->update([
                'status' => 'notified',
                'notified_at' => now(),
                'expires_at' => now()->addHours(2) // Give 2 hours to respond
            ]);

            // Send notification
            $this->notificationService->sendWaitingListNotification(
                $waitingList,
                'email', // Default to email, could be configurable
                $availableDateTime
            );

        } catch (\Exception $e) {
            Log::error('Failed to notify waiting list customer', [
                'waiting_list_id' => $waitingList->id,
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Convert waiting list entry to booking
     */
    public function convertWaitingListToBooking(WaitingList $waitingList, array $bookingData): array
    {
        try {
            DB::beginTransaction();

            // Validate availability
            $startDateTime = Carbon::parse($bookingData['start_datetime']);
            $service = $waitingList->service;
            $endDateTime = $startDateTime->copy()->addMinutes($service->duration_minutes);

            $availabilityCheck = $this->businessRulesEngine->checkAvailability(
                $waitingList->business,
                $startDateTime,
                $endDateTime,
                [$service->id]
            );

            if (!$availabilityCheck['available']) {
                return [
                    'success' => false,
                    'message' => $availabilityCheck['message']
                ];
            }

            // Create booking
            $booking = Booking::create([
                'business_id' => $waitingList->business_id,
                'customer_name' => $waitingList->customer_name,
                'customer_email' => $waitingList->customer_email,
                'customer_phone' => $waitingList->customer_phone,
                'start_datetime' => $startDateTime,
                'end_datetime' => $endDateTime,
                'total_duration_minutes' => $service->duration_minutes,
                'participant_count' => $waitingList->participant_count,
                'subtotal' => $service->base_price,
                'total_amount' => $service->base_price,
                'status' => 'confirmed',
                'payment_status' => 'pending',
                'notes' => $waitingList->notes,
            ]);

            // Attach service
            $booking->services()->attach($service->id, [
                'quantity' => 1,
                'unit_price' => $service->base_price,
                'total_price' => $service->base_price,
                'duration_minutes' => $service->duration_minutes,
                'start_datetime' => $booking->start_datetime,
                'end_datetime' => $booking->start_datetime->copy()->addMinutes($service->duration_minutes),
            ]);

            // Update waiting list status
            $waitingList->update([
                'status' => 'booked',
                'customer_id' => $booking->customer_id
            ]);

            DB::commit();

            return [
                'success' => true,
                'booking' => $booking,
                'message' => 'Waiting list entry successfully converted to booking.'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to convert waiting list to booking', [
                'waiting_list_id' => $waitingList->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to convert to booking: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get integrated dashboard stats
     */
    public function getDashboardStats(Business $business, Carbon $date = null): array
    {
        $date = $date ?? now();

        $bookings = $business->bookings()
            ->whereDate('start_datetime', $date)
            ->where('status', '!=', 'cancelled')
            ->get();

        $waitingLists = $business->waitingLists()
            ->where('status', 'active')
            ->get();

        return [
            'bookings' => [
                'total' => $bookings->count(),
                'confirmed' => $bookings->where('status', 'confirmed')->count(),
                'pending' => $bookings->where('status', 'pending')->count(),
                'in_progress' => $bookings->where('status', 'in_progress')->count(),
                'completed' => $bookings->where('status', 'completed')->count(),
                'no_show' => $bookings->where('status', 'no_show')->count(),
                'checked_in' => $bookings->whereNotNull('checked_in_at')->whereNull('checked_out_at')->count(),
                'waiting_to_check_in' => $bookings->where('status', 'confirmed')
                    ->whereNull('checked_in_at')
                    ->where('start_datetime', '<=', now()->addMinutes(15))
                    ->count(),
            ],
            'waiting_lists' => [
                'total' => $waitingLists->count(),
                'urgent' => $waitingLists->where('priority', '>=', 8)->count(),
                'notified' => $waitingLists->where('status', 'notified')->count(),
                'expired' => $waitingLists->where('status', 'expired')->count(),
            ],
            'opportunities' => [
                'cancelled_today' => $business->bookings()
                    ->whereDate('start_datetime', $date)
                    ->where('status', 'cancelled')
                    ->count(),
                'no_shows_today' => $business->bookings()
                    ->whereDate('start_datetime', $date)
                    ->where('status', 'no_show')
                    ->count(),
            ]
        ];
    }

    /**
     * Get cross-section notifications for the current user
     */
    public function getCrossSectionNotifications(Business $business): array
    {
        $notifications = [];

        // Check for urgent waiting list entries
        $urgentWaitingLists = $business->waitingLists()
            ->where('status', 'active')
            ->where('priority', '>=', 8)
            ->count();

        if ($urgentWaitingLists > 0) {
            $notifications[] = [
                'type' => 'urgent_waiting_list',
                'count' => $urgentWaitingLists,
                'message' => "You have {$urgentWaitingLists} urgent waiting list entries",
                'action_url' => route('owner.waiting-lists.index'),
                'icon' => 'fas fa-exclamation-triangle',
                'color' => 'warning'
            ];
        }

        // Check for customers waiting to check in
        $waitingToCheckIn = $business->bookings()
            ->where('status', 'confirmed')
            ->whereNull('checked_in_at')
            ->whereDate('start_datetime', now())
            ->where('start_datetime', '<=', now()->addMinutes(15))
            ->count();

        if ($waitingToCheckIn > 0) {
            $notifications[] = [
                'type' => 'waiting_check_in',
                'count' => $waitingToCheckIn,
                'message' => "{$waitingToCheckIn} customers ready for check-in",
                'action_url' => route('owner.check-in.index'),
                'icon' => 'fas fa-sign-in-alt',
                'color' => 'info'
            ];
        }

        // Check for expired waiting list notifications
        $expiredNotifications = $business->waitingLists()
            ->where('status', 'notified')
            ->where('expires_at', '<', now())
            ->count();

        if ($expiredNotifications > 0) {
            $notifications[] = [
                'type' => 'expired_notifications',
                'count' => $expiredNotifications,
                'message' => "{$expiredNotifications} waiting list notifications expired",
                'action_url' => route('owner.waiting-lists.index'),
                'icon' => 'fas fa-clock',
                'color' => 'danger'
            ];
        }

        return $notifications;
    }

    /**
     * Get related data for cross-section display
     */
    public function getRelatedData(string $section, $id, Business $business): array
    {
        switch ($section) {
            case 'booking':
                return $this->getBookingRelatedData($id, $business);
            case 'waiting_list':
                return $this->getWaitingListRelatedData($id, $business);
            case 'service':
                return $this->getServiceRelatedData($id, $business);
            default:
                return [];
        }
    }

    /**
     * Get related data for a booking
     */
    protected function getBookingRelatedData($bookingId, Business $business): array
    {
        $booking = $business->bookings()->with(['services', 'customer'])->find($bookingId);

        if (!$booking) {
            return [];
        }

        $serviceIds = $booking->services->pluck('id')->toArray();

        // Find related waiting list entries
        $relatedWaitingLists = $business->waitingLists()
            ->whereIn('service_id', $serviceIds)
            ->where('status', 'active')
            ->limit(5)
            ->get();

        // Find other bookings for the same customer
        $customerBookings = $business->bookings()
            ->where('customer_email', $booking->customer_email)
            ->where('id', '!=', $booking->id)
            ->orderBy('start_datetime', 'desc')
            ->limit(3)
            ->get();

        return [
            'waiting_lists' => $relatedWaitingLists,
            'customer_bookings' => $customerBookings,
            'can_check_in' => $booking->can_be_checked_in,
            'can_check_out' => $booking->can_be_checked_out,
        ];
    }

    /**
     * Get related data for a waiting list entry
     */
    protected function getWaitingListRelatedData($waitingListId, Business $business): array
    {
        $waitingList = $business->waitingLists()->with('service')->find($waitingListId);

        if (!$waitingList) {
            return [];
        }

        // Find available slots for this service
        $availableSlots = $this->findAvailableSlots($waitingList);

        // Find other waiting list entries for the same customer
        $customerWaitingLists = $business->waitingLists()
            ->where('customer_email', $waitingList->customer_email)
            ->where('id', '!=', $waitingList->id)
            ->get();

        return [
            'available_slots' => $availableSlots,
            'customer_waiting_lists' => $customerWaitingLists,
            'can_convert' => !empty($availableSlots),
        ];
    }

    /**
     * Get related data for a service
     */
    protected function getServiceRelatedData($serviceId, Business $business): array
    {
        $service = $business->services()->find($serviceId);

        if (!$service) {
            return [];
        }

        // Get today's bookings for this service
        $todayBookings = $business->bookings()
            ->whereHas('services', function ($query) use ($serviceId) {
                $query->where('services.id', $serviceId);
            })
            ->whereDate('start_datetime', now())
            ->get();

        // Get waiting list entries for this service
        $waitingLists = $business->waitingLists()
            ->where('service_id', $serviceId)
            ->where('status', 'active')
            ->get();

        return [
            'today_bookings' => $todayBookings,
            'waiting_lists' => $waitingLists,
            'next_available' => $this->getNextAvailableSlot($service, $business),
        ];
    }

    /**
     * Find available slots for a waiting list entry
     */
    protected function findAvailableSlots(WaitingList $waitingList, int $limit = 5): array
    {
        // This would integrate with the business rules engine to find actual available slots
        // For now, return a simplified version
        $slots = [];
        $startDate = Carbon::parse($waitingList->preferred_date);
        $endDate = $startDate->copy()->addDays(14); // Look ahead 2 weeks

        // This is a simplified implementation - in reality, this would use the business rules engine
        for ($date = $startDate; $date <= $endDate; $date->addDay()) {
            if ($waitingList->preferred_days_of_week &&
                !in_array($date->dayOfWeek, $waitingList->preferred_days_of_week)) {
                continue;
            }

            // Add some example slots (this would be replaced with actual availability checking)
            if (count($slots) < $limit) {
                $slots[] = [
                    'datetime' => $date->format('Y-m-d') . ' 10:00:00',
                    'formatted' => $date->format('M j, Y') . ' at 10:00 AM'
                ];
            }
        }

        return $slots;
    }

    /**
     * Get next available slot for a service
     */
    protected function getNextAvailableSlot(Service $service, Business $business): ?array
    {
        // This would integrate with the business rules engine
        // For now, return a simplified version
        $tomorrow = now()->addDay();

        return [
            'datetime' => $tomorrow->format('Y-m-d') . ' 09:00:00',
            'formatted' => $tomorrow->format('M j, Y') . ' at 9:00 AM'
        ];
    }
}
