<?php

namespace App\Services;

use App\Models\Business;
use App\Models\BusinessLandingPage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LandingPageCacheService
{
    const CACHE_TTL = 3600; // 1 hour
    const CACHE_PREFIX = 'landing_page:';
    const BUSINESS_CACHE_PREFIX = 'business:';
    const SERVICES_CACHE_PREFIX = 'services:';
    const REVIEWS_CACHE_PREFIX = 'reviews:';
    const SEO_CACHE_PREFIX = 'seo:';

    /**
     * Get cached landing page data.
     */
    public function getLandingPageData($businessSlug): ?array
    {
        $cacheKey = self::CACHE_PREFIX . $businessSlug;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($businessSlug) {
            return $this->buildLandingPageData($businessSlug);
        });
    }

    /**
     * Get cached business data.
     */
    public function getBusinessData($businessId): ?array
    {
        $cacheKey = self::BUSINESS_CACHE_PREFIX . $businessId;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($businessId) {
            $business = Business::with([
                'operatingHours',
                'seoSettings',
                'landingServiceSettings'
            ])->find($businessId);
            
            return $business ? $business->toArray() : null;
        });
    }

    /**
     * Get cached services data.
     */
    public function getServicesData($businessId): array
    {
        $cacheKey = self::SERVICES_CACHE_PREFIX . $businessId;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($businessId) {
            $business = Business::find($businessId);
            
            if (!$business) {
                return [];
            }
            
            return $business->services()
                ->with(['category', 'images'])
                ->where('is_active', true)
                ->where('is_public', true)
                ->orderBy('landing_display_order')
                ->get()
                ->toArray();
        });
    }

    /**
     * Get cached reviews data.
     */
    public function getReviewsData($businessId): array
    {
        $cacheKey = self::REVIEWS_CACHE_PREFIX . $businessId;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($businessId) {
            $business = Business::find($businessId);
            
            if (!$business) {
                return [];
            }
            
            return $business->reviews()
                ->where('is_approved', true)
                ->orderBy('is_featured', 'desc')
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get()
                ->toArray();
        });
    }

    /**
     * Get cached SEO data.
     */
    public function getSeoData($businessId): ?array
    {
        $cacheKey = self::SEO_CACHE_PREFIX . $businessId;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($businessId) {
            $business = Business::with('seoSettings')->find($businessId);
            
            return $business && $business->seoSettings ? $business->seoSettings->toArray() : null;
        });
    }

    /**
     * Build complete landing page data.
     */
    private function buildLandingPageData($businessSlug): ?array
    {
        try {
            $landingPage = BusinessLandingPage::with([
                'business.operatingHours',
                'business.seoSettings',
                'business.landingServiceSettings',
                'sections' => function ($query) {
                    $query->where('is_visible', true)->orderBy('sort_order');
                }
            ])->where('custom_slug', $businessSlug)
              ->where('is_published', true)
              ->first();

            if (!$landingPage) {
                return null;
            }

            $business = $landingPage->business;
            
            return [
                'landing_page' => $landingPage->toArray(),
                'business' => $business->toArray(),
                'services' => $this->getServicesData($business->id),
                'reviews' => $this->getReviewsData($business->id),
                'seo' => $this->getSeoData($business->id),
                'cached_at' => now()->toISOString()
            ];
            
        } catch (\Exception $e) {
            Log::error('Error building landing page data', [
                'slug' => $businessSlug,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Clear all cache for a business.
     */
    public function clearBusinessCache($businessId, $businessSlug = null): void
    {
        $keys = [
            self::BUSINESS_CACHE_PREFIX . $businessId,
            self::SERVICES_CACHE_PREFIX . $businessId,
            self::REVIEWS_CACHE_PREFIX . $businessId,
            self::SEO_CACHE_PREFIX . $businessId,
        ];
        
        if ($businessSlug) {
            $keys[] = self::CACHE_PREFIX . $businessSlug;
        }
        
        foreach ($keys as $key) {
            Cache::forget($key);
        }
        
        Log::info('Cleared cache for business', [
            'business_id' => $businessId,
            'slug' => $businessSlug,
            'keys_cleared' => count($keys)
        ]);
    }

    /**
     * Clear landing page cache.
     */
    public function clearLandingPageCache($businessSlug): void
    {
        $cacheKey = self::CACHE_PREFIX . $businessSlug;
        Cache::forget($cacheKey);
        
        Log::info('Cleared landing page cache', ['slug' => $businessSlug]);
    }

    /**
     * Clear services cache.
     */
    public function clearServicesCache($businessId): void
    {
        $cacheKey = self::SERVICES_CACHE_PREFIX . $businessId;
        Cache::forget($cacheKey);
        
        Log::info('Cleared services cache', ['business_id' => $businessId]);
    }

    /**
     * Clear reviews cache.
     */
    public function clearReviewsCache($businessId): void
    {
        $cacheKey = self::REVIEWS_CACHE_PREFIX . $businessId;
        Cache::forget($cacheKey);
        
        Log::info('Cleared reviews cache', ['business_id' => $businessId]);
    }

    /**
     * Warm up cache for a business.
     */
    public function warmUpCache($businessSlug): void
    {
        try {
            $this->getLandingPageData($businessSlug);
            
            Log::info('Cache warmed up successfully', ['slug' => $businessSlug]);
        } catch (\Exception $e) {
            Log::error('Failed to warm up cache', [
                'slug' => $businessSlug,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get cache statistics.
     */
    public function getCacheStats($businessId, $businessSlug): array
    {
        $keys = [
            'landing_page' => self::CACHE_PREFIX . $businessSlug,
            'business' => self::BUSINESS_CACHE_PREFIX . $businessId,
            'services' => self::SERVICES_CACHE_PREFIX . $businessId,
            'reviews' => self::REVIEWS_CACHE_PREFIX . $businessId,
            'seo' => self::SEO_CACHE_PREFIX . $businessId,
        ];
        
        $stats = [];
        
        foreach ($keys as $type => $key) {
            $stats[$type] = [
                'cached' => Cache::has($key),
                'key' => $key
            ];
        }
        
        return $stats;
    }

    /**
     * Preload critical data for faster page loads.
     */
    public function preloadCriticalData($businessSlug): array
    {
        $data = $this->getLandingPageData($businessSlug);
        
        if (!$data) {
            return [];
        }
        
        // Return only critical above-the-fold data
        return [
            'business' => [
                'name' => $data['business']['name'] ?? '',
                'description' => $data['business']['description'] ?? '',
                'phone' => $data['business']['phone'] ?? '',
                'email' => $data['business']['email'] ?? '',
            ],
            'landing_page' => [
                'page_title' => $data['landing_page']['page_title'] ?? '',
                'meta_title' => $data['landing_page']['meta_title'] ?? '',
                'meta_description' => $data['landing_page']['meta_description'] ?? '',
                'logo_url' => $data['landing_page']['logo_url'] ?? '',
                'theme' => $data['landing_page']['theme'] ?? 'default',
            ],
            'hero_section' => $this->getHeroSection($data['landing_page']['sections'] ?? []),
            'featured_services' => array_slice($data['services'] ?? [], 0, 3),
        ];
    }

    /**
     * Get hero section data.
     */
    private function getHeroSection($sections): ?array
    {
        foreach ($sections as $section) {
            if ($section['section_type'] === 'hero') {
                return $section;
            }
        }
        
        return null;
    }

    /**
     * Check if cache is healthy.
     */
    public function isCacheHealthy(): bool
    {
        try {
            $testKey = 'cache_health_check';
            $testValue = 'healthy';
            
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);
            
            return $retrieved === $testValue;
        } catch (\Exception $e) {
            Log::error('Cache health check failed', ['error' => $e->getMessage()]);
            return false;
        }
    }
}
