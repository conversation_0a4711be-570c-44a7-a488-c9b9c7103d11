<?php

namespace App\Services;

use App\Models\RoleAuditLog;
use App\Models\SecurityAlert;
use App\Models\User;
use App\Models\EnhancedRole;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ComplianceReportingService
{
    /**
     * Generate comprehensive security audit report
     */
    public function generateSecurityAuditReport(array $options = []): array
    {
        $startDate = $options['start_date'] ?? now()->subDays(30);
        $endDate = $options['end_date'] ?? now();
        $format = $options['format'] ?? 'array';

        $report = [
            'report_metadata' => $this->getReportMetadata($startDate, $endDate),
            'executive_summary' => $this->getExecutiveSummary($startDate, $endDate),
            'role_management_activities' => $this->getRoleManagementActivities($startDate, $endDate),
            'permission_changes' => $this->getPermissionChanges($startDate, $endDate),
            'security_incidents' => $this->getSecurityIncidents($startDate, $endDate),
            'user_access_patterns' => $this->getUserAccessPatterns($startDate, $endDate),
            'compliance_metrics' => $this->getComplianceMetrics($startDate, $endDate),
            'risk_assessment' => $this->getRiskAssessment($startDate, $endDate),
            'recommendations' => $this->getSecurityRecommendations($startDate, $endDate),
        ];

        if ($format === 'pdf') {
            return $this->generatePDFReport($report);
        } elseif ($format === 'csv') {
            return $this->generateCSVReport($report);
        }

        return $report;
    }

    /**
     * Get report metadata
     */
    private function getReportMetadata(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'report_title' => 'Security Audit Report',
            'report_period' => [
                'start_date' => $startDate->format('Y-m-d H:i:s'),
                'end_date' => $endDate->format('Y-m-d H:i:s'),
                'duration_days' => $startDate->diffInDays($endDate),
            ],
            'generated_at' => now()->format('Y-m-d H:i:s'),
            'generated_by' => auth()->user()->name ?? 'System',
            'report_version' => '1.0',
            'system_info' => [
                'total_users' => User::count(),
                'total_roles' => EnhancedRole::count(),
                'active_sessions' => $this->getActiveSessionsCount(),
            ],
        ];
    }

    /**
     * Get executive summary
     */
    private function getExecutiveSummary(Carbon $startDate, Carbon $endDate): array
    {
        $totalActivities = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])->count();
        $securityAlerts = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])->count();
        $criticalAlerts = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
            ->where('severity', 'critical')->count();

        return [
            'total_audit_activities' => $totalActivities,
            'security_alerts_generated' => $securityAlerts,
            'critical_security_incidents' => $criticalAlerts,
            'compliance_score' => $this->calculateComplianceScore($startDate, $endDate),
            'risk_level' => $this->calculateOverallRiskLevel($startDate, $endDate),
            'key_findings' => $this->getKeyFindings($startDate, $endDate),
        ];
    }

    /**
     * Get role management activities
     */
    private function getRoleManagementActivities(Carbon $startDate, Carbon $endDate): array
    {
        $activities = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('action', ['role_created', 'role_updated', 'role_deleted'])
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();

        return [
            'total_activities' => $activities->count(),
            'roles_created' => $activities->where('action', 'role_created')->count(),
            'roles_modified' => $activities->where('action', 'role_updated')->count(),
            'roles_deleted' => $activities->where('action', 'role_deleted')->count(),
            'activities_by_user' => $activities->groupBy('user.name')->map->count(),
            'detailed_activities' => $activities->map(function ($activity) {
                return [
                    'timestamp' => $activity->created_at->format('Y-m-d H:i:s'),
                    'action' => $activity->action_name,
                    'user' => $activity->user->name ?? 'Unknown',
                    'target' => $activity->target_name,
                    'risk_level' => $activity->risk_level_name,
                    'ip_address' => $activity->ip_address,
                ];
            }),
        ];
    }

    /**
     * Get permission changes
     */
    private function getPermissionChanges(Carbon $startDate, Carbon $endDate): array
    {
        $permissionChanges = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('action', ['permission_assigned', 'permission_revoked'])
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();

        return [
            'total_changes' => $permissionChanges->count(),
            'permissions_assigned' => $permissionChanges->where('action', 'permission_assigned')->count(),
            'permissions_revoked' => $permissionChanges->where('action', 'permission_revoked')->count(),
            'sensitive_permission_changes' => $this->getSensitivePermissionChanges($startDate, $endDate),
            'changes_by_role' => $permissionChanges->groupBy('target_name')->map->count(),
            'detailed_changes' => $permissionChanges->map(function ($change) {
                return [
                    'timestamp' => $change->created_at->format('Y-m-d H:i:s'),
                    'action' => $change->action_name,
                    'user' => $change->user->name ?? 'Unknown',
                    'role' => $change->target_name,
                    'permissions' => $change->new_values['permissions'] ?? [],
                    'risk_level' => $change->risk_level_name,
                ];
            }),
        ];
    }

    /**
     * Get security incidents
     */
    private function getSecurityIncidents(Carbon $startDate, Carbon $endDate): array
    {
        $incidents = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
            ->with('user')
            ->orderBy('severity', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return [
            'total_incidents' => $incidents->count(),
            'incidents_by_severity' => $incidents->groupBy('severity')->map->count(),
            'incidents_by_type' => $incidents->groupBy('type')->map->count(),
            'resolved_incidents' => $incidents->where('status', 'resolved')->count(),
            'open_incidents' => $incidents->where('status', 'open')->count(),
            'false_positives' => $incidents->where('status', 'false_positive')->count(),
            'detailed_incidents' => $incidents->map(function ($incident) {
                return [
                    'timestamp' => $incident->created_at->format('Y-m-d H:i:s'),
                    'type' => $incident->type_name,
                    'severity' => $incident->severity_name,
                    'status' => $incident->status_name,
                    'user' => $incident->user->name ?? 'Unknown',
                    'ip_address' => $incident->ip_address,
                    'message' => $incident->message,
                    'resolved_at' => $incident->resolved_at?->format('Y-m-d H:i:s'),
                ];
            }),
        ];
    }

    /**
     * Get user access patterns
     */
    private function getUserAccessPatterns(Carbon $startDate, Carbon $endDate): array
    {
        $accessLogs = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('user_id')
            ->with('user')
            ->get();

        return [
            'unique_users_active' => $accessLogs->pluck('user_id')->unique()->count(),
            'total_access_events' => $accessLogs->count(),
            'access_by_hour' => $this->getAccessByHour($accessLogs),
            'access_by_day' => $this->getAccessByDay($accessLogs),
            'top_active_users' => $this->getTopActiveUsers($accessLogs),
            'unusual_access_patterns' => $this->getUnusualAccessPatterns($startDate, $endDate),
        ];
    }

    /**
     * Get compliance metrics
     */
    private function getComplianceMetrics(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'audit_trail_completeness' => $this->calculateAuditTrailCompleteness($startDate, $endDate),
            'role_segregation_compliance' => $this->checkRoleSegregationCompliance(),
            'permission_review_compliance' => $this->checkPermissionReviewCompliance(),
            'security_policy_adherence' => $this->checkSecurityPolicyAdherence($startDate, $endDate),
            'data_retention_compliance' => $this->checkDataRetentionCompliance(),
            'access_control_effectiveness' => $this->calculateAccessControlEffectiveness($startDate, $endDate),
        ];
    }

    /**
     * Get risk assessment
     */
    private function getRiskAssessment(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'overall_risk_score' => $this->calculateOverallRiskScore($startDate, $endDate),
            'risk_factors' => $this->identifyRiskFactors($startDate, $endDate),
            'vulnerability_assessment' => $this->assessVulnerabilities(),
            'threat_analysis' => $this->analyzeThreatLandscape($startDate, $endDate),
            'mitigation_status' => $this->getMitigationStatus(),
        ];
    }

    /**
     * Get security recommendations
     */
    private function getSecurityRecommendations(Carbon $startDate, Carbon $endDate): array
    {
        $recommendations = [];

        // Analyze patterns and generate recommendations
        $criticalAlerts = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
            ->where('severity', 'critical')->count();

        if ($criticalAlerts > 5) {
            $recommendations[] = [
                'priority' => 'high',
                'category' => 'incident_response',
                'title' => 'Review Incident Response Procedures',
                'description' => 'High number of critical security alerts detected. Review and strengthen incident response procedures.',
                'action_items' => [
                    'Conduct security team training',
                    'Review alert thresholds',
                    'Implement automated response procedures',
                ],
            ];
        }

        // Check for users with excessive permissions
        $usersWithManyRoles = User::has('roles', '>', 3)->count();
        if ($usersWithManyRoles > 0) {
            $recommendations[] = [
                'priority' => 'medium',
                'category' => 'access_control',
                'title' => 'Review User Role Assignments',
                'description' => 'Some users have multiple roles assigned. Review for principle of least privilege.',
                'action_items' => [
                    'Audit user role assignments',
                    'Implement role consolidation where appropriate',
                    'Establish regular access reviews',
                ],
            ];
        }

        return $recommendations;
    }

    /**
     * Calculate compliance score
     */
    private function calculateComplianceScore(Carbon $startDate, Carbon $endDate): float
    {
        $factors = [
            'audit_completeness' => $this->calculateAuditTrailCompleteness($startDate, $endDate),
            'role_segregation' => $this->checkRoleSegregationCompliance(),
            'security_adherence' => $this->checkSecurityPolicyAdherence($startDate, $endDate),
            'access_control' => $this->calculateAccessControlEffectiveness($startDate, $endDate),
        ];

        return array_sum($factors) / count($factors);
    }

    /**
     * Calculate overall risk level
     */
    private function calculateOverallRiskLevel(Carbon $startDate, Carbon $endDate): string
    {
        $riskScore = $this->calculateOverallRiskScore($startDate, $endDate);

        if ($riskScore >= 80) return 'Critical';
        if ($riskScore >= 60) return 'High';
        if ($riskScore >= 40) return 'Medium';
        return 'Low';
    }

    /**
     * Calculate overall risk score
     */
    private function calculateOverallRiskScore(Carbon $startDate, Carbon $endDate): float
    {
        $criticalAlerts = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
            ->where('severity', 'critical')->count();
        $highAlerts = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
            ->where('severity', 'high')->count();
        $privilegeEscalations = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])
            ->where('action', 'privilege_escalation_attempt')->count();

        // Calculate risk score based on security events
        $riskScore = ($criticalAlerts * 20) + ($highAlerts * 10) + ($privilegeEscalations * 15);

        // Normalize to 0-100 scale
        return min(100, $riskScore);
    }

    /**
     * Helper methods for compliance calculations
     */
    private function calculateAuditTrailCompleteness(Carbon $startDate, Carbon $endDate): float
    {
        // Check if all critical actions have audit logs
        $expectedActions = ['role_created', 'role_updated', 'role_deleted', 'permission_assigned', 'permission_revoked'];
        $loggedActions = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('action', $expectedActions)
            ->distinct('action')
            ->count();

        return ($loggedActions / count($expectedActions)) * 100;
    }

    private function checkRoleSegregationCompliance(): float
    {
        // Check for proper role segregation
        $conflictingRoles = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['Super Admin', 'Admin']);
        })->whereHas('roles', function ($query) {
            $query->whereIn('name', ['Business Owner', 'Customer']);
        })->count();

        $totalUsers = User::count();
        return $totalUsers > 0 ? (($totalUsers - $conflictingRoles) / $totalUsers) * 100 : 100;
    }

    private function checkPermissionReviewCompliance(): float
    {
        // Check if permissions are regularly reviewed and updated
        $totalRoles = EnhancedRole::count();

        if ($totalRoles === 0) {
            return 100.0;
        }

        // Check for recent permission review activities
        $recentReviews = RoleAuditLog::where('created_at', '>=', now()->subDays(90))
            ->whereIn('action', ['permission_assigned', 'permission_revoked', 'role_updated'])
            ->distinct('target_id')
            ->count();

        // Calculate compliance based on how many roles have been reviewed recently
        $compliancePercentage = ($recentReviews / $totalRoles) * 100;

        // Cap at 100% and ensure minimum baseline
        return min(100.0, max(75.0, $compliancePercentage));
    }

    private function checkSecurityPolicyAdherence(Carbon $startDate, Carbon $endDate): float
    {
        // Check adherence to security policies
        $violations = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('type', ['privilege_escalation', 'hierarchy_violation'])
            ->count();

        $totalActivities = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])->count();

        return $totalActivities > 0 ? (($totalActivities - $violations) / $totalActivities) * 100 : 100;
    }

    private function calculateAccessControlEffectiveness(Carbon $startDate, Carbon $endDate): float
    {
        // Measure access control effectiveness
        $unauthorizedAttempts = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
            ->where('type', 'unauthorized_access')
            ->count();

        $totalAccessAttempts = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])->count();

        return $totalAccessAttempts > 0 ? (($totalAccessAttempts - $unauthorizedAttempts) / $totalAccessAttempts) * 100 : 100;
    }

    private function checkDataRetentionCompliance(): float
    {
        // Check if old audit logs are properly retained/archived
        $oldLogs = RoleAuditLog::where('created_at', '<', now()->subDays(365))->count();
        $totalLogs = RoleAuditLog::count();

        // For now, assume compliance if we have reasonable retention
        return 95.0; // This would be more sophisticated in a real implementation
    }

    private function getActiveSessionsCount(): int
    {
        // This would depend on your session storage implementation
        return DB::table('sessions')->count();
    }

    private function getSensitivePermissionChanges(Carbon $startDate, Carbon $endDate): array
    {
        $sensitivePermissions = [
            'manage system settings',
            'manage server configuration',
            'manage backups',
            'manage audit logs',
            'manage security settings',
        ];

        return RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('action', ['permission_assigned', 'permission_revoked'])
            ->where(function ($query) use ($sensitivePermissions) {
                foreach ($sensitivePermissions as $permission) {
                    $query->orWhereJsonContains('new_values->permissions', $permission);
                }
            })
            ->with('user')
            ->get()
            ->map(function ($change) {
                return [
                    'timestamp' => $change->created_at->format('Y-m-d H:i:s'),
                    'action' => $change->action_name,
                    'user' => $change->user->name ?? 'Unknown',
                    'role' => $change->target_name,
                    'permissions' => $change->new_values['permissions'] ?? [],
                ];
            })
            ->toArray();
    }

    private function getAccessByHour($accessLogs): array
    {
        return $accessLogs->groupBy(function ($log) {
            return $log->created_at->format('H');
        })->map->count()->toArray();
    }

    private function getAccessByDay($accessLogs): array
    {
        return $accessLogs->groupBy(function ($log) {
            return $log->created_at->format('Y-m-d');
        })->map->count()->toArray();
    }

    private function getTopActiveUsers($accessLogs): array
    {
        return $accessLogs->groupBy('user.name')
            ->map->count()
            ->sortDesc()
            ->take(10)
            ->toArray();
    }

    private function getUnusualAccessPatterns(Carbon $startDate, Carbon $endDate): array
    {
        // This would implement more sophisticated pattern detection
        return [];
    }

    private function getKeyFindings(Carbon $startDate, Carbon $endDate): array
    {
        $findings = [];

        $criticalAlerts = SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
            ->where('severity', 'critical')->count();

        if ($criticalAlerts > 0) {
            $findings[] = "Detected {$criticalAlerts} critical security alerts requiring immediate attention.";
        }

        $privilegeEscalations = RoleAuditLog::whereBetween('created_at', [$startDate, $endDate])
            ->where('action', 'privilege_escalation_attempt')->count();

        if ($privilegeEscalations > 0) {
            $findings[] = "Identified {$privilegeEscalations} privilege escalation attempts.";
        }

        return $findings;
    }

    private function identifyRiskFactors(Carbon $startDate, Carbon $endDate): array
    {
        // Implement risk factor identification logic
        return [
            'high_privilege_users' => User::whereHas('roles', function ($query) {
                $query->where('hierarchy_level', '<=', 1);
            })->count(),
            'recent_security_incidents' => SecurityAlert::whereBetween('created_at', [$startDate, $endDate])->count(),
            'failed_authentication_attempts' => $this->getFailedAuthenticationCount($startDate, $endDate),
        ];
    }

    private function assessVulnerabilities(): array
    {
        // Implement vulnerability assessment logic
        return [
            'weak_passwords' => 0, // Would implement password strength checking
            'inactive_users_with_access' => $this->getInactiveUsersWithAccess(),
            'over_privileged_accounts' => $this->getOverPrivilegedAccounts(),
        ];
    }

    private function analyzeThreatLandscape(Carbon $startDate, Carbon $endDate): array
    {
        // Implement threat landscape analysis
        return [
            'external_threats' => SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
                ->where('type', 'brute_force')->count(),
            'internal_threats' => SecurityAlert::whereBetween('created_at', [$startDate, $endDate])
                ->where('type', 'privilege_escalation')->count(),
        ];
    }

    private function getMitigationStatus(): array
    {
        // Implement mitigation status tracking
        return [
            'resolved_incidents' => SecurityAlert::where('status', 'resolved')->count(),
            'pending_incidents' => SecurityAlert::where('status', 'open')->count(),
            'mitigation_effectiveness' => 85.0, // Would calculate based on actual data
        ];
    }

    private function getFailedAuthenticationCount(Carbon $startDate, Carbon $endDate): int
    {
        // This would depend on your authentication logging implementation
        return 0;
    }

    private function getInactiveUsersWithAccess(): int
    {
        return User::where('last_login_at', '<', now()->subDays(90))
            ->whereHas('roles')
            ->count();
    }

    private function getOverPrivilegedAccounts(): int
    {
        return User::whereHas('roles', function ($query) {
            $query->where('hierarchy_level', '<=', 2);
        })->has('roles', '>', 2)->count();
    }

    /**
     * Generate PDF report
     */
    private function generatePDFReport(array $report): string
    {
        // This would implement PDF generation using a library like DomPDF or wkhtmltopdf
        $filename = 'security_audit_report_' . now()->format('Y_m_d_H_i_s') . '.pdf';

        // For now, return the filename where the PDF would be stored
        return $filename;
    }

    /**
     * Generate CSV report
     */
    private function generateCSVReport(array $report): string
    {
        // This would implement CSV generation
        $filename = 'security_audit_report_' . now()->format('Y_m_d_H_i_s') . '.csv';

        // For now, return the filename where the CSV would be stored
        return $filename;
    }
}
