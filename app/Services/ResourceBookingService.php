<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\BookingService;
use App\Models\Resource;
use App\Models\Service;
use App\Models\Business;
use App\Models\BookingServiceResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class ResourceBookingService
{
    /**
     * Assign resources to a booking automatically.
     */
    public function assignResourcesToBooking(Booking $booking): array
    {
        $results = [];
        $conflicts = [];

        DB::transaction(function () use ($booking, &$results, &$conflicts) {
            foreach ($booking->bookingServices as $bookingService) {
                $service = $bookingService->service;
                $requiredResources = $service->requiredResources;
                $optionalResources = $service->resources()->wherePivot('is_required', false)->get();

                // Assign required resources first
                foreach ($requiredResources as $resource) {
                    $assignment = $this->assignResourceToBookingService(
                        $bookingService,
                        $resource,
                        $resource->pivot->quantity_required
                    );

                    if ($assignment['success']) {
                        $results[] = $assignment;
                    } else {
                        $conflicts[] = $assignment;
                    }
                }

                // Try to assign optional resources if available
                foreach ($optionalResources as $resource) {
                    if ($this->isResourceAvailable($resource, $bookingService->start_datetime, $bookingService->end_datetime)) {
                        $assignment = $this->assignResourceToBookingService(
                            $bookingService,
                            $resource,
                            $resource->pivot->quantity_required
                        );

                        if ($assignment['success']) {
                            $results[] = $assignment;
                        }
                    }
                }
            }
        });

        return [
            'success' => empty($conflicts),
            'assignments' => $results,
            'conflicts' => $conflicts,
            'message' => empty($conflicts) 
                ? 'All resources assigned successfully' 
                : 'Some resources could not be assigned due to conflicts'
        ];
    }

    /**
     * Assign a specific resource to a booking service.
     */
    protected function assignResourceToBookingService(BookingService $bookingService, Resource $resource, int $quantity): array
    {
        // Check availability
        if (!$this->isResourceAvailable($resource, $bookingService->start_datetime, $bookingService->end_datetime, $quantity)) {
            return [
                'success' => false,
                'resource_id' => $resource->id,
                'resource_name' => $resource->name,
                'message' => "Resource '{$resource->name}' is not available for the requested time slot",
                'required_quantity' => $quantity,
                'available_quantity' => $this->getAvailableQuantity($resource, $bookingService->start_datetime, $bookingService->end_datetime)
            ];
        }

        // Calculate actual start/end times including setup and cleanup
        $setupTime = $resource->pivot->setup_time_minutes ?? 0;
        $cleanupTime = $resource->pivot->cleanup_time_minutes ?? 0;
        
        $actualStartTime = $bookingService->start_datetime->copy()->subMinutes($setupTime);
        $actualEndTime = $bookingService->end_datetime->copy()->addMinutes($cleanupTime);

        // Create the resource assignment
        $assignment = BookingServiceResource::create([
            'booking_service_id' => $bookingService->id,
            'resource_id' => $resource->id,
            'quantity' => $quantity,
            'start_datetime' => $actualStartTime,
            'end_datetime' => $actualEndTime,
        ]);

        return [
            'success' => true,
            'assignment_id' => $assignment->id,
            'resource_id' => $resource->id,
            'resource_name' => $resource->name,
            'quantity' => $quantity,
            'setup_time' => $setupTime,
            'cleanup_time' => $cleanupTime,
            'actual_start' => $actualStartTime,
            'actual_end' => $actualEndTime,
            'message' => "Resource '{$resource->name}' assigned successfully"
        ];
    }

    /**
     * Check if a resource is available for the given time period.
     */
    public function isResourceAvailable(Resource $resource, Carbon $startDateTime, Carbon $endDateTime, int $requiredQuantity = 1): bool
    {
        $availableQuantity = $this->getAvailableQuantity($resource, $startDateTime, $endDateTime);
        return $availableQuantity >= $requiredQuantity;
    }

    /**
     * Get available quantity for a resource at a given time.
     */
    public function getAvailableQuantity(Resource $resource, Carbon $startDateTime, Carbon $endDateTime): int
    {
        // Get conflicting bookings
        $conflictingBookings = $resource->bookingServiceResources()
            ->where(function ($query) use ($startDateTime, $endDateTime) {
                $query->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                      ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                      ->orWhere(function ($q) use ($startDateTime, $endDateTime) {
                          $q->where('start_datetime', '<=', $startDateTime)
                            ->where('end_datetime', '>=', $endDateTime);
                      });
            })
            ->sum('quantity');

        return max(0, $resource->capacity - $conflictingBookings);
    }

    /**
     * Find alternative resources for a service.
     */
    public function findAlternativeResources(Service $service, Carbon $startDateTime, Carbon $endDateTime): Collection
    {
        $business = $service->business;
        $requiredResourceTypes = $service->requiredResources->pluck('resource_type_id')->unique();

        $alternatives = collect();

        foreach ($requiredResourceTypes as $resourceTypeId) {
            $availableResources = $business->resources()
                ->where('resource_type_id', $resourceTypeId)
                ->where('is_active', true)
                ->get()
                ->filter(function ($resource) use ($startDateTime, $endDateTime) {
                    return $this->isResourceAvailable($resource, $startDateTime, $endDateTime);
                });

            $alternatives = $alternatives->merge($availableResources);
        }

        return $alternatives;
    }

    /**
     * Get resource utilization for a given period.
     */
    public function getResourceUtilization(Resource $resource, Carbon $startDate, Carbon $endDate): array
    {
        $totalMinutes = $startDate->diffInMinutes($endDate);
        $totalCapacityMinutes = $totalMinutes * $resource->capacity;

        $bookedMinutes = $resource->bookingServiceResources()
            ->whereBetween('start_datetime', [$startDate, $endDate])
            ->get()
            ->sum(function ($booking) {
                return $booking->start_datetime->diffInMinutes($booking->end_datetime) * $booking->quantity;
            });

        $utilizationRate = $totalCapacityMinutes > 0 ? ($bookedMinutes / $totalCapacityMinutes) * 100 : 0;

        return [
            'resource_id' => $resource->id,
            'resource_name' => $resource->name,
            'total_capacity_minutes' => $totalCapacityMinutes,
            'booked_minutes' => $bookedMinutes,
            'available_minutes' => $totalCapacityMinutes - $bookedMinutes,
            'utilization_rate' => round($utilizationRate, 2),
            'bookings_count' => $resource->bookingServiceResources()
                ->whereBetween('start_datetime', [$startDate, $endDate])
                ->count()
        ];
    }

    /**
     * Release resources from a cancelled booking.
     */
    public function releaseBookingResources(Booking $booking): array
    {
        $releasedResources = [];

        DB::transaction(function () use ($booking, &$releasedResources) {
            foreach ($booking->bookingServices as $bookingService) {
                $resourceAssignments = $bookingService->resources;

                foreach ($resourceAssignments as $assignment) {
                    $releasedResources[] = [
                        'resource_id' => $assignment->resource_id,
                        'resource_name' => $assignment->resource->name,
                        'quantity' => $assignment->quantity,
                        'start_datetime' => $assignment->start_datetime,
                        'end_datetime' => $assignment->end_datetime,
                    ];

                    $assignment->delete();
                }
            }
        });

        return [
            'success' => true,
            'released_resources' => $releasedResources,
            'message' => count($releasedResources) . ' resource assignment(s) released'
        ];
    }

    /**
     * Check for resource conflicts before booking.
     */
    public function checkResourceConflicts(array $serviceIds, Carbon $startDateTime, Carbon $endDateTime, Business $business, int $excludeBookingId = null): array
    {
        $conflicts = [];
        $services = $business->services()->whereIn('id', $serviceIds)->with('requiredResources')->get();

        foreach ($services as $service) {
            foreach ($service->requiredResources as $resource) {
                $requiredQuantity = $resource->pivot->quantity_required;
                
                if (!$this->isResourceAvailable($resource, $startDateTime, $endDateTime, $requiredQuantity)) {
                    $conflicts[] = [
                        'service_id' => $service->id,
                        'service_name' => $service->name,
                        'resource_id' => $resource->id,
                        'resource_name' => $resource->name,
                        'required_quantity' => $requiredQuantity,
                        'available_quantity' => $this->getAvailableQuantity($resource, $startDateTime, $endDateTime),
                        'message' => "Insufficient '{$resource->name}' available for '{$service->name}'"
                    ];
                }
            }
        }

        return [
            'has_conflicts' => !empty($conflicts),
            'conflicts' => $conflicts,
            'message' => empty($conflicts) 
                ? 'All required resources are available' 
                : 'Resource conflicts detected'
        ];
    }

    /**
     * Get next available time slot for a service considering resource availability.
     */
    public function getNextAvailableSlot(Service $service, Carbon $fromDateTime, int $durationMinutes): ?Carbon
    {
        $business = $service->business;
        $requiredResources = $service->requiredResources;

        if ($requiredResources->isEmpty()) {
            return $fromDateTime; // No resources required
        }

        $searchDateTime = $fromDateTime->copy();
        $maxSearchDays = 30; // Limit search to 30 days
        $searchEndDate = $fromDateTime->copy()->addDays($maxSearchDays);

        while ($searchDateTime->lte($searchEndDate)) {
            $endDateTime = $searchDateTime->copy()->addMinutes($durationMinutes);
            
            $allResourcesAvailable = true;
            foreach ($requiredResources as $resource) {
                if (!$this->isResourceAvailable($resource, $searchDateTime, $endDateTime, $resource->pivot->quantity_required)) {
                    $allResourcesAvailable = false;
                    break;
                }
            }

            if ($allResourcesAvailable) {
                return $searchDateTime;
            }

            // Move to next 15-minute slot
            $searchDateTime->addMinutes(15);
        }

        return null; // No available slot found within search period
    }
}
