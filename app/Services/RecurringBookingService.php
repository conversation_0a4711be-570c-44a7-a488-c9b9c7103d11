<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\Service;
use App\Models\Business;
use Carbon\Carbon;
use Illuminate\Support\Str;

class RecurringBookingService
{
    /**
     * Create recurring bookings based on recurrence pattern.
     */
    public function createRecurringBookings(array $bookingData, array $recurrenceData)
    {
        $createdBookings = [];
        $masterBooking = null;
        
        // Create the master booking first
        $masterBookingData = $bookingData;
        $masterBookingData['is_recurring'] = true;
        $masterBookingData['recurring_group_id'] = Str::uuid();
        $masterBookingData['recurrence_data'] = $recurrenceData;
        
        $masterBooking = Booking::create($masterBookingData);
        $createdBookings[] = $masterBooking;
        
        // Generate recurring instances
        $instances = $this->generateRecurringInstances(
            $masterBooking->start_datetime,
            $recurrenceData
        );
        
        foreach ($instances as $instanceDate) {
            if ($instanceDate->eq($masterBooking->start_datetime)) {
                continue; // Skip the master booking date
            }
            
            $instanceData = $bookingData;
            $instanceData['is_recurring'] = true;
            $instanceData['recurring_group_id'] = $masterBooking->recurring_group_id;
            $instanceData['recurrence_data'] = $recurrenceData;
            $instanceData['start_datetime'] = $instanceDate;
            $instanceData['end_datetime'] = $instanceDate->copy()->addMinutes($bookingData['total_duration_minutes']);
            $instanceData['booking_number'] = $this->generateBookingNumber();
            
            // Check availability for this instance
            if ($this->isSlotAvailable($instanceData)) {
                $instance = Booking::create($instanceData);
                $createdBookings[] = $instance;
            }
        }
        
        return $createdBookings;
    }
    
    /**
     * Generate recurring instances based on pattern.
     */
    public function generateRecurringInstances(Carbon $startDate, array $recurrenceData)
    {
        $instances = [];
        $pattern = $recurrenceData['pattern']; // daily, weekly, monthly, yearly
        $interval = $recurrenceData['interval'] ?? 1;
        $endType = $recurrenceData['end_type']; // never, after_occurrences, on_date
        $maxOccurrences = $recurrenceData['max_occurrences'] ?? null;
        $endDate = isset($recurrenceData['end_date']) ? Carbon::parse($recurrenceData['end_date']) : null;
        
        $currentDate = $startDate->copy();
        $occurrenceCount = 0;
        $maxInstances = 100; // Safety limit
        
        while ($occurrenceCount < $maxInstances) {
            $instances[] = $currentDate->copy();
            $occurrenceCount++;
            
            // Check end conditions
            if ($endType === 'after_occurrences' && $maxOccurrences && $occurrenceCount >= $maxOccurrences) {
                break;
            }
            
            if ($endType === 'on_date' && $endDate && $currentDate->gte($endDate)) {
                break;
            }
            
            // Generate next occurrence
            $currentDate = $this->getNextOccurrence($currentDate, $pattern, $interval, $recurrenceData);
            
            // Safety check - don't go beyond 2 years
            if ($currentDate->gt($startDate->copy()->addYears(2))) {
                break;
            }
        }
        
        return $instances;
    }
    
    /**
     * Get the next occurrence based on pattern.
     */
    private function getNextOccurrence(Carbon $date, string $pattern, int $interval, array $recurrenceData)
    {
        switch ($pattern) {
            case 'daily':
                return $date->addDays($interval);
                
            case 'weekly':
                $daysOfWeek = $recurrenceData['days_of_week'] ?? [$date->dayOfWeek];
                return $this->getNextWeeklyOccurrence($date, $interval, $daysOfWeek);
                
            case 'monthly':
                $monthlyType = $recurrenceData['monthly_type'] ?? 'day_of_month';
                if ($monthlyType === 'day_of_month') {
                    return $date->addMonths($interval);
                } else {
                    // day_of_week (e.g., 2nd Tuesday of each month)
                    return $this->getNextMonthlyByDayOfWeek($date, $interval, $recurrenceData);
                }
                
            case 'yearly':
                return $date->addYears($interval);
                
            default:
                return $date->addDays($interval);
        }
    }
    
    /**
     * Get next weekly occurrence.
     */
    private function getNextWeeklyOccurrence(Carbon $date, int $interval, array $daysOfWeek)
    {
        $currentDayOfWeek = $date->dayOfWeek;
        $nextDay = null;
        
        // Find next day in the same week
        foreach ($daysOfWeek as $dayOfWeek) {
            if ($dayOfWeek > $currentDayOfWeek) {
                $nextDay = $dayOfWeek;
                break;
            }
        }
        
        if ($nextDay !== null) {
            // Next occurrence is in the same week
            $daysToAdd = $nextDay - $currentDayOfWeek;
            return $date->addDays($daysToAdd);
        } else {
            // Move to next week(s) and find first day
            $weeksToAdd = $interval;
            $firstDayOfWeek = min($daysOfWeek);
            $daysToAdd = (7 * $weeksToAdd) - $currentDayOfWeek + $firstDayOfWeek;
            return $date->addDays($daysToAdd);
        }
    }
    
    /**
     * Get next monthly occurrence by day of week.
     */
    private function getNextMonthlyByDayOfWeek(Carbon $date, int $interval, array $recurrenceData)
    {
        $weekOfMonth = $recurrenceData['week_of_month'] ?? 1; // 1st, 2nd, 3rd, 4th, -1 (last)
        $dayOfWeek = $recurrenceData['day_of_week'] ?? $date->dayOfWeek;
        
        $nextMonth = $date->copy()->addMonths($interval)->startOfMonth();
        
        if ($weekOfMonth === -1) {
            // Last occurrence of the day in the month
            $nextMonth->endOfMonth();
            while ($nextMonth->dayOfWeek !== $dayOfWeek) {
                $nextMonth->subDay();
            }
        } else {
            // Nth occurrence of the day in the month
            $nextMonth->addDays(($weekOfMonth - 1) * 7);
            while ($nextMonth->dayOfWeek !== $dayOfWeek) {
                $nextMonth->addDay();
            }
        }
        
        // Set the time to match the original booking
        $nextMonth->setTime($date->hour, $date->minute, $date->second);
        
        return $nextMonth;
    }
    
    /**
     * Check if a time slot is available.
     */
    private function isSlotAvailable(array $bookingData)
    {
        $businessRulesEngine = app(BusinessRulesEngine::class);
        
        $business = Business::find($bookingData['business_id']);
        $service = Service::find($bookingData['service_id'] ?? null);
        $startDateTime = Carbon::parse($bookingData['start_datetime']);
        
        if (!$business || !$service) {
            return false;
        }
        
        $availability = $businessRulesEngine->checkAvailability(
            $business,
            $service,
            $startDateTime,
            $bookingData['participant_count'] ?? 1
        );
        
        return $availability['available'];
    }
    
    /**
     * Generate a unique booking number.
     */
    private function generateBookingNumber()
    {
        do {
            $number = 'BK' . date('Ymd') . strtoupper(Str::random(6));
        } while (Booking::where('booking_number', $number)->exists());
        
        return $number;
    }
    
    /**
     * Update recurring booking series.
     */
    public function updateRecurringSeries(Booking $masterBooking, array $updateData, string $updateScope = 'this_only')
    {
        switch ($updateScope) {
            case 'this_only':
                // Update only this instance
                $masterBooking->update($updateData);
                break;
                
            case 'this_and_future':
                // Update this instance and all future instances
                $this->updateThisAndFutureBookings($masterBooking, $updateData);
                break;
                
            case 'all_in_series':
                // Update all instances in the series
                $this->updateAllInSeries($masterBooking, $updateData);
                break;
        }
    }
    
    /**
     * Update this booking and all future bookings in the series.
     */
    private function updateThisAndFutureBookings(Booking $booking, array $updateData)
    {
        Booking::where('recurring_group_id', $booking->recurring_group_id)
            ->where('start_datetime', '>=', $booking->start_datetime)
            ->update($updateData);
    }
    
    /**
     * Update all bookings in the series.
     */
    private function updateAllInSeries(Booking $booking, array $updateData)
    {
        Booking::where('recurring_group_id', $booking->recurring_group_id)
            ->update($updateData);
    }
    
    /**
     * Cancel recurring booking series.
     */
    public function cancelRecurringSeries(Booking $booking, string $cancelScope = 'this_only', string $reason = null)
    {
        $cancelData = [
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancelled_by' => auth()->id(),
            'cancellation_reason' => $reason
        ];
        
        switch ($cancelScope) {
            case 'this_only':
                $booking->update($cancelData);
                break;
                
            case 'this_and_future':
                Booking::where('recurring_group_id', $booking->recurring_group_id)
                    ->where('start_datetime', '>=', $booking->start_datetime)
                    ->where('status', '!=', 'cancelled')
                    ->update($cancelData);
                break;
                
            case 'all_in_series':
                Booking::where('recurring_group_id', $booking->recurring_group_id)
                    ->where('status', '!=', 'cancelled')
                    ->update($cancelData);
                break;
        }
    }
    
    /**
     * Get recurring booking series information.
     */
    public function getSeriesInfo(Booking $booking)
    {
        if (!$booking->is_recurring || !$booking->recurring_group_id) {
            return null;
        }
        
        $seriesBookings = Booking::where('recurring_group_id', $booking->recurring_group_id)
            ->orderBy('start_datetime')
            ->get();
            
        return [
            'total_bookings' => $seriesBookings->count(),
            'completed_bookings' => $seriesBookings->where('status', 'completed')->count(),
            'cancelled_bookings' => $seriesBookings->where('status', 'cancelled')->count(),
            'upcoming_bookings' => $seriesBookings->where('start_datetime', '>', now())->count(),
            'first_booking' => $seriesBookings->first(),
            'last_booking' => $seriesBookings->last(),
            'next_booking' => $seriesBookings->where('start_datetime', '>', now())->first(),
            'recurrence_pattern' => $booking->recurrence_data
        ];
    }
    
    /**
     * Validate recurrence data.
     */
    public function validateRecurrenceData(array $recurrenceData)
    {
        $required = ['pattern', 'interval', 'end_type'];
        
        foreach ($required as $field) {
            if (!isset($recurrenceData[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }
        
        if (!in_array($recurrenceData['pattern'], ['daily', 'weekly', 'monthly', 'yearly'])) {
            throw new \InvalidArgumentException("Invalid pattern: {$recurrenceData['pattern']}");
        }
        
        if (!in_array($recurrenceData['end_type'], ['never', 'after_occurrences', 'on_date'])) {
            throw new \InvalidArgumentException("Invalid end_type: {$recurrenceData['end_type']}");
        }
        
        if ($recurrenceData['end_type'] === 'after_occurrences' && empty($recurrenceData['max_occurrences'])) {
            throw new \InvalidArgumentException("max_occurrences is required when end_type is 'after_occurrences'");
        }
        
        if ($recurrenceData['end_type'] === 'on_date' && empty($recurrenceData['end_date'])) {
            throw new \InvalidArgumentException("end_date is required when end_type is 'on_date'");
        }
        
        return true;
    }
}
