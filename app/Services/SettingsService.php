<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get(string $key, $default = null)
    {
        return Setting::getValue($key, $default);
    }

    /**
     * Set a setting value by key
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public function set(string $key, $value): bool
    {
        return Setting::setValue($key, $value);
    }

    /**
     * Get all settings by group
     *
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getGroup(string $group)
    {
        return Setting::getByGroup($group);
    }

    /**
     * Get all groups
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllGroups()
    {
        return Setting::getGroups();
    }

    /**
     * Clear all settings cache
     *
     * @return void
     */
    public function clearCache(): void
    {
        Setting::clearCache();
    }

    /**
     * Check if a setting exists
     *
     * @param string $key
     * @return bool
     */
    public function has(string $key): bool
    {
        return Setting::where('key', $key)->exists();
    }

    /**
     * Delete a setting
     *
     * @param string $key
     * @return bool
     */
    public function delete(string $key): bool
    {
        $setting = Setting::where('key', $key)->first();

        if ($setting) {
            return (bool) $setting->delete();
        }

        return false;
    }
}
