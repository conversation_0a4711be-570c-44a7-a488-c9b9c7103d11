<?php

namespace App\Services;

use App\Models\Business;
use App\Models\BusinessGalleryImage;
use App\Models\BusinessGalleryCategory;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class GalleryImageService
{
    /**
     * Upload and process gallery images.
     */
    public function uploadImages(Business $business, array $files, array $options = []): array
    {
        $uploadedImages = [];
        $errors = [];

        foreach ($files as $file) {
            try {
                $image = $this->uploadSingleImage($business, $file, $options);
                $uploadedImages[] = $image;
            } catch (\Exception $e) {
                $errors[] = [
                    'file' => $file->getClientOriginalName(),
                    'error' => $e->getMessage()
                ];
                Log::error('Gallery image upload failed', [
                    'business_id' => $business->id,
                    'file' => $file->getClientOriginalName(),
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'uploaded' => $uploadedImages,
            'errors' => $errors
        ];
    }

    /**
     * Upload a single gallery image.
     */
    public function uploadSingleImage(Business $business, UploadedFile $file, array $options = []): BusinessGalleryImage
    {
        // Validate file
        $this->validateImage($file);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        
        // Create business-specific directory
        $directory = "gallery/business-{$business->id}";
        
        // Store the original file
        $path = $file->storeAs($directory, $filename, 'public');
        
        // Get image dimensions
        $dimensions = $this->getImageDimensions($file);
        
        // Extract EXIF data
        $exifData = $this->extractExifData($file);
        
        // Create database record
        $image = BusinessGalleryImage::create([
            'business_id' => $business->id,
            'category_id' => $options['category_id'] ?? null,
            'filename' => $filename,
            'original_name' => $file->getClientOriginalName(),
            'path' => $path,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'width' => $dimensions['width'],
            'height' => $dimensions['height'],
            'title' => $options['title'] ?? null,
            'alt_text' => $options['alt_text'] ?? $file->getClientOriginalName(),
            'description' => $options['description'] ?? null,
            'tags' => $options['tags'] ?? [],
            'is_featured' => $options['is_featured'] ?? false,
            'is_active' => $options['is_active'] ?? true,
            'sort_order' => $options['sort_order'] ?? 0,
            'exif_data' => $exifData,
            'uploaded_at' => now(),
        ]);

        // Generate thumbnails
        $this->generateImageSizes($image);

        return $image;
    }

    /**
     * Update gallery image.
     */
    public function updateImage(BusinessGalleryImage $image, array $data): BusinessGalleryImage
    {
        $image->update($data);
        return $image;
    }

    /**
     * Delete gallery image.
     */
    public function deleteImage(BusinessGalleryImage $image): bool
    {
        try {
            $image->deleteFile();
            $image->delete();
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to delete gallery image', [
                'image_id' => $image->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Bulk update image order.
     */
    public function updateImageOrder(Business $business, array $imageOrders): bool
    {
        try {
            foreach ($imageOrders as $order) {
                BusinessGalleryImage::where('business_id', $business->id)
                    ->where('id', $order['id'])
                    ->update(['sort_order' => $order['sort_order']]);
            }
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update image order', [
                'business_id' => $business->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Create gallery category.
     */
    public function createCategory(Business $business, array $data): BusinessGalleryCategory
    {
        $category = new BusinessGalleryCategory($data);
        $category->business_id = $business->id;
        $category->slug = $category->generateUniqueSlug($data['name']);
        $category->save();

        return $category;
    }

    /**
     * Update gallery category.
     */
    public function updateCategory(BusinessGalleryCategory $category, array $data): BusinessGalleryCategory
    {
        if (isset($data['name']) && $data['name'] !== $category->name) {
            $data['slug'] = $category->generateUniqueSlug($data['name']);
        }
        
        $category->update($data);
        return $category;
    }

    /**
     * Delete gallery category.
     */
    public function deleteCategory(BusinessGalleryCategory $category): bool
    {
        try {
            // Move images to uncategorized
            $category->images()->update(['category_id' => null]);
            $category->delete();
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to delete gallery category', [
                'category_id' => $category->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Validate uploaded image.
     */
    private function validateImage(UploadedFile $file): void
    {
        $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 10 * 1024 * 1024; // 10MB

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
        }

        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException('File size too large. Maximum size is 10MB.');
        }
    }

    /**
     * Generate unique filename.
     */
    private function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        return Str::uuid() . '.' . $extension;
    }

    /**
     * Get image dimensions.
     */
    private function getImageDimensions(UploadedFile $file): array
    {
        try {
            $imageSize = getimagesize($file->getPathname());
            return [
                'width' => $imageSize[0] ?? null,
                'height' => $imageSize[1] ?? null,
            ];
        } catch (\Exception $e) {
            return ['width' => null, 'height' => null];
        }
    }

    /**
     * Extract EXIF data from image.
     */
    private function extractExifData(UploadedFile $file): ?array
    {
        try {
            if (function_exists('exif_read_data') && in_array($file->getMimeType(), ['image/jpeg', 'image/jpg'])) {
                $exif = exif_read_data($file->getPathname());
                return $exif ? array_filter($exif, function($value) {
                    return !is_array($value);
                }) : null;
            }
        } catch (\Exception $e) {
            Log::warning('Failed to extract EXIF data', ['error' => $e->getMessage()]);
        }
        
        return null;
    }

    /**
     * Generate different image sizes.
     */
    private function generateImageSizes(BusinessGalleryImage $image): void
    {
        try {
            $originalPath = Storage::disk('public')->path($image->path);
            $pathInfo = pathinfo($image->path);
            $directory = $pathInfo['dirname'];
            $filename = $pathInfo['filename'];
            $extension = $pathInfo['extension'];

            $sizes = [
                'thumbnail' => ['width' => 300, 'height' => 300],
                'medium' => ['width' => 800, 'height' => 600],
                'large' => ['width' => 1200, 'height' => 900],
            ];

            foreach ($sizes as $sizeName => $dimensions) {
                $sizeDirectory = $directory . '/' . $sizeName . 's';
                $sizePath = $sizeDirectory . '/' . $filename . '_' . $sizeName . '.' . $extension;
                $fullSizePath = Storage::disk('public')->path($sizePath);

                // Create directory if it doesn't exist
                Storage::disk('public')->makeDirectory($sizeDirectory);

                // Create resized image (this would require Intervention Image package)
                // For now, we'll just copy the original
                Storage::disk('public')->copy($image->path, $sizePath);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to generate image sizes', [
                'image_id' => $image->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
