<?php

namespace App\Services;

use App\Models\RoleAuditLog;
use App\Models\User;
use App\Models\SecurityAlert;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use App\Notifications\SecurityAlertNotification;
use Carbon\Carbon;

class SecurityMonitoringService
{
    /**
     * Monitor for suspicious activities and generate alerts
     */
    public function monitorSecurityEvents(): array
    {
        $alerts = [];

        // Check for privilege escalation attempts
        $alerts = array_merge($alerts, $this->detectPrivilegeEscalation());

        // Check for unusual access patterns
        $alerts = array_merge($alerts, $this->detectUnusualAccess());

        // Check for failed authentication attempts
        $alerts = array_merge($alerts, $this->detectFailedAuthentication());

        // Check for sensitive permission access
        $alerts = array_merge($alerts, $this->detectSensitivePermissionAccess());

        // Check for role hierarchy violations
        $alerts = array_merge($alerts, $this->detectRoleHierarchyViolations());

        // Process and store alerts
        foreach ($alerts as $alert) {
            $this->processSecurityAlert($alert);
        }

        return $alerts;
    }

    /**
     * Detect privilege escalation attempts
     */
    private function detectPrivilegeEscalation(): array
    {
        $alerts = [];
        $timeWindow = now()->subHours(1);

        $escalationAttempts = RoleAuditLog::where('action', 'privilege_escalation_attempt')
            ->where('created_at', '>=', $timeWindow)
            ->with('user')
            ->get();

        foreach ($escalationAttempts as $attempt) {
            $alerts[] = [
                'type' => 'privilege_escalation',
                'severity' => 'critical',
                'user_id' => $attempt->user_id,
                'message' => 'Privilege escalation attempt detected',
                'details' => $attempt->additional_data,
                'timestamp' => $attempt->created_at,
                'ip_address' => $attempt->ip_address,
                'user_agent' => $attempt->user_agent,
            ];
        }

        return $alerts;
    }

    /**
     * Detect unusual access patterns
     */
    private function detectUnusualAccess(): array
    {
        $alerts = [];
        $timeWindow = now()->subHours(24);

        // Check for access from multiple IPs
        $multipleIpUsers = RoleAuditLog::where('created_at', '>=', $timeWindow)
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->havingRaw('COUNT(DISTINCT ip_address) > 3')
            ->pluck('user_id');

        foreach ($multipleIpUsers as $userId) {
            $user = User::find($userId);
            if ($user) {
                $alerts[] = [
                    'type' => 'unusual_access',
                    'severity' => 'medium',
                    'user_id' => $userId,
                    'message' => 'User accessed from multiple IP addresses',
                    'details' => ['user_email' => $user->email],
                    'timestamp' => now(),
                ];
            }
        }

        // Check for off-hours access by high-privilege users
        $offHoursAccess = RoleAuditLog::where('created_at', '>=', $timeWindow)
            ->whereTime('created_at', '<', '06:00:00')
            ->orWhereTime('created_at', '>', '22:00:00')
            ->whereHas('user', function ($query) {
                $query->whereHas('roles', function ($roleQuery) {
                    $roleQuery->where('hierarchy_level', '<=', 2);
                });
            })
            ->with('user')
            ->get();

        foreach ($offHoursAccess as $access) {
            $alerts[] = [
                'type' => 'off_hours_access',
                'severity' => 'medium',
                'user_id' => $access->user_id,
                'message' => 'High-privilege user accessed system during off-hours',
                'details' => [
                    'user_email' => $access->user->email ?? 'Unknown',
                    'action' => $access->action,
                ],
                'timestamp' => $access->created_at,
                'ip_address' => $access->ip_address,
            ];
        }

        return $alerts;
    }

    /**
     * Detect failed authentication attempts
     */
    private function detectFailedAuthentication(): array
    {
        $alerts = [];
        $timeWindow = now()->subMinutes(30);

        // Check for multiple failed attempts from same IP
        $failedAttempts = Cache::get('failed_login_attempts', []);

        foreach ($failedAttempts as $ip => $attempts) {
            if (count($attempts) >= 5) {
                $recentAttempts = array_filter($attempts, function ($attempt) use ($timeWindow) {
                    return Carbon::parse($attempt['timestamp'])->gte($timeWindow);
                });

                if (count($recentAttempts) >= 5) {
                    $alerts[] = [
                        'type' => 'brute_force',
                        'severity' => 'high',
                        'user_id' => null,
                        'message' => 'Multiple failed login attempts detected',
                        'details' => [
                            'ip_address' => $ip,
                            'attempt_count' => count($recentAttempts),
                        ],
                        'timestamp' => now(),
                        'ip_address' => $ip,
                    ];
                }
            }
        }

        return $alerts;
    }

    /**
     * Detect sensitive permission access
     */
    private function detectSensitivePermissionAccess(): array
    {
        $alerts = [];
        $timeWindow = now()->subHours(1);

        $sensitiveAccess = RoleAuditLog::where('action', 'sensitive_permission_access')
            ->where('created_at', '>=', $timeWindow)
            ->with('user')
            ->get();

        foreach ($sensitiveAccess as $access) {
            $alerts[] = [
                'type' => 'sensitive_access',
                'severity' => 'high',
                'user_id' => $access->user_id,
                'message' => 'Sensitive permission accessed',
                'details' => $access->additional_data,
                'timestamp' => $access->created_at,
                'ip_address' => $access->ip_address,
            ];
        }

        return $alerts;
    }

    /**
     * Detect role hierarchy violations
     */
    private function detectRoleHierarchyViolations(): array
    {
        $alerts = [];
        $timeWindow = now()->subHours(1);

        $violations = RoleAuditLog::where('action', 'role_hierarchy_violation')
            ->where('created_at', '>=', $timeWindow)
            ->with('user')
            ->get();

        foreach ($violations as $violation) {
            $alerts[] = [
                'type' => 'hierarchy_violation',
                'severity' => 'critical',
                'user_id' => $violation->user_id,
                'message' => 'Role hierarchy violation detected',
                'details' => $violation->additional_data,
                'timestamp' => $violation->created_at,
                'ip_address' => $violation->ip_address,
            ];
        }

        return $alerts;
    }

    /**
     * Process and store security alert
     */
    private function processSecurityAlert(array $alertData): void
    {
        // Create security alert record
        $alert = SecurityAlert::create($alertData);

        // Log the alert
        Log::channel('security')->critical('Security Alert Generated', $alertData);

        // Send notifications based on severity
        $this->sendAlertNotifications($alert);

        // Take automated actions if necessary
        $this->takeAutomatedActions($alert);
    }

    /**
     * Send alert notifications
     */
    private function sendAlertNotifications(SecurityAlert $alert): void
    {
        // Get users who should receive security alerts
        $notificationUsers = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['Super Admin', 'Admin']);
        })->get();

        foreach ($notificationUsers as $user) {
            try {
                $user->notify(new SecurityAlertNotification($alert));
            } catch (\Exception $e) {
                Log::error('Failed to send security alert notification', [
                    'user_id' => $user->id,
                    'alert_id' => $alert->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Take automated security actions
     */
    private function takeAutomatedActions(SecurityAlert $alert): void
    {
        switch ($alert->type) {
            case 'brute_force':
                $this->blockSuspiciousIP($alert->ip_address);
                break;

            case 'privilege_escalation':
                $this->temporarilyLockUser($alert->user_id);
                break;

            case 'hierarchy_violation':
                $this->auditUserPermissions($alert->user_id);
                break;
        }
    }

    /**
     * Block suspicious IP address
     */
    private function blockSuspiciousIP(string $ipAddress): void
    {
        $blockedUntil = now()->addHours(24);

        // Get current blocked IPs and add the new one
        $allBlockedIPs = Cache::get('all_blocked_ips', []);
        $allBlockedIPs[$ipAddress] = $blockedUntil;

        // Store updated blocked IPs list
        Cache::put('all_blocked_ips', $allBlockedIPs, now()->addDays(7));

        // Also store individual entry for backward compatibility
        Cache::put("blocked_ip_{$ipAddress}", true, $blockedUntil);

        Log::warning('IP address blocked due to suspicious activity', [
            'ip_address' => $ipAddress,
            'blocked_until' => $blockedUntil,
        ]);
    }

    /**
     * Temporarily lock user account
     */
    private function temporarilyLockUser(int $userId): void
    {
        $user = User::find($userId);
        if ($user) {
            $lockedUntil = now()->addHours(2);

            // Get current locked users and add the new one
            $allLockedUsers = Cache::get('all_locked_users', []);
            $allLockedUsers[$userId] = $lockedUntil;

            // Store updated locked users list
            Cache::put('all_locked_users', $allLockedUsers, now()->addDays(7));

            // Also store individual entry for backward compatibility
            Cache::put("user_locked_{$userId}", true, $lockedUntil);

            Log::warning('User account temporarily locked', [
                'user_id' => $userId,
                'user_email' => $user->email,
                'locked_until' => $lockedUntil,
            ]);
        }
    }

    /**
     * Audit user permissions
     */
    private function auditUserPermissions(int $userId): void
    {
        $user = User::find($userId);
        if ($user) {
            // Log detailed permission audit
            RoleAuditLog::create([
                'action' => 'permission_audit_triggered',
                'target_type' => User::class,
                'target_id' => $userId,
                'target_name' => $user->name,
                'additional_data' => [
                    'roles' => $user->getRoleNames()->toArray(),
                    'permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
                    'triggered_by' => 'automated_security_system',
                ],
                'risk_level' => RoleAuditLog::RISK_LEVELS['high'],
            ]);
        }
    }

    /**
     * Get security dashboard data
     */
    public function getSecurityDashboardData(): array
    {
        $timeWindow = now()->subHours(24);

        return [
            'total_alerts' => SecurityAlert::where('created_at', '>=', $timeWindow)->count(),
            'critical_alerts' => SecurityAlert::where('severity', 'critical')
                ->where('created_at', '>=', $timeWindow)->count(),
            'high_alerts' => SecurityAlert::where('severity', 'high')
                ->where('created_at', '>=', $timeWindow)->count(),
            'recent_activities' => RoleAuditLog::where('created_at', '>=', $timeWindow)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->with('user')
                ->get(),
            'blocked_ips' => $this->getBlockedIPs(),
            'locked_users' => $this->getLockedUsers(),
        ];
    }

    /**
     * Get blocked IP addresses
     */
    private function getBlockedIPs(): array
    {
        $blockedIPs = [];

        // Since we can't use Redis-specific methods with database cache,
        // we'll store blocked IPs in a single cache entry
        $allBlockedIPs = Cache::get('all_blocked_ips', []);

        foreach ($allBlockedIPs as $ip => $blockedUntil) {
            // Check if the block is still active
            if (now()->lt($blockedUntil)) {
                $blockedIPs[] = [
                    'ip' => $ip,
                    'blocked_until' => $blockedUntil,
                ];
            }
        }

        return $blockedIPs;
    }

    /**
     * Get locked users
     */
    private function getLockedUsers(): array
    {
        $lockedUsers = [];

        // Since we can't use Redis-specific methods with database cache,
        // we'll store locked users in a single cache entry
        $allLockedUsers = Cache::get('all_locked_users', []);

        foreach ($allLockedUsers as $userId => $lockedUntil) {
            // Check if the lock is still active
            if (now()->lt($lockedUntil)) {
                $user = User::find($userId);
                if ($user) {
                    $lockedUsers[] = [
                        'user' => $user,
                        'locked_until' => $lockedUntil,
                    ];
                }
            }
        }

        return $lockedUsers;
    }
}
