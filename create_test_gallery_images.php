<?php

require_once 'vendor/autoload.php';

use App\Models\Business;
use App\Models\BusinessGalleryImage;
use App\Models\BusinessGalleryCategory;

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Creating Test Gallery Images\n";
echo "============================\n";

// Get the test business
$business = Business::where('slug', 'test-business')->first();
if (!$business) {
    echo "Test business not found. Please run TestDataSeeder first.\n";
    exit;
}

echo "Business found: {$business->name} (ID: {$business->id})\n";

// Create a test category
$category = BusinessGalleryCategory::firstOrCreate([
    'business_id' => $business->id,
    'slug' => 'test-category'
], [
    'name' => 'Test Category',
    'description' => 'Test category for gallery images',
    'color' => '#007bff',
    'is_active' => true,
    'sort_order' => 0
]);

echo "Category created/found: {$category->name} (ID: {$category->id})\n";

// Create some test gallery images
$testImages = [
    [
        'filename' => 'test-image-1.jpg',
        'original_name' => 'Test Image 1.jpg',
        'title' => 'Test Image 1',
        'description' => 'This is a test image for gallery functionality',
        'is_featured' => true,
    ],
    [
        'filename' => 'test-image-2.jpg',
        'original_name' => 'Test Image 2.jpg',
        'title' => 'Test Image 2',
        'description' => 'Another test image for gallery functionality',
        'is_featured' => false,
    ],
    [
        'filename' => 'test-image-3.jpg',
        'original_name' => 'Test Image 3.jpg',
        'title' => 'Test Image 3',
        'description' => 'Third test image for gallery functionality',
        'is_featured' => false,
    ]
];

foreach ($testImages as $index => $imageData) {
    $image = BusinessGalleryImage::firstOrCreate([
        'business_id' => $business->id,
        'filename' => $imageData['filename']
    ], [
        'category_id' => $category->id,
        'original_name' => $imageData['original_name'],
        'path' => "gallery/business-{$business->id}/{$imageData['filename']}",
        'url' => null,
        'mime_type' => 'image/jpeg',
        'file_size' => 1024000, // 1MB
        'width' => 800,
        'height' => 600,
        'title' => $imageData['title'],
        'alt_text' => $imageData['title'],
        'description' => $imageData['description'],
        'tags' => ['test', 'gallery'],
        'is_featured' => $imageData['is_featured'],
        'is_active' => true,
        'sort_order' => $index,
        'metadata' => [],
        'exif_data' => [],
        'uploaded_at' => now(),
    ]);
    
    echo "Image created/found: {$image->title} (ID: {$image->id})\n";
}

echo "\nTest gallery images created successfully!\n";
echo "You can now test the gallery functionality at: http://127.0.0.1:8000/owner/gallery\n";
echo "Login with: <EMAIL> / password\n";
